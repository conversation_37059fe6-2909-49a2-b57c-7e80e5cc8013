using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;


public abstract class TutorialGesture : MonoBehaviour {
	// -- public --
//	public string ToMarkerName { get { return m_toMarker.m_name; } }
	public float m_moveSpeed = 4f;
//	public TutorialGestureManager.GESTURE_TYPES m_gestureType;
	public Transform m_owner;

	public float m_rotation;
	public float m_scale;
	public Vector3 m_rotation3D;

	// -- protected --
//	protected TutorialMarker m_fromMarker;
//	protected TutorialMarker m_toMarker;
	protected Transform m_fromMarkerTransform;
	protected Transform m_toMarkerTransform;
	protected bool m_isTransformType = true;

	//---------------------------------------------------------------------------------------------------------------------------------
/*	public static TutorialGesture Create (Transform _owner, GameObject _prefab, Transform _parent, TutorialMarker _fromMarker, TutorialMarker _toMarker, float _rotation, float _scale, Vector3 _rotation3D) {
		var go = Instantiate (_prefab, _parent);
		go.SetLayerRecursively(GameManager.Me.CurrentLayer);
		var tg = go.GetComponent<TutorialGesture> ();
		if (tg) {
			tg.m_rotation = _rotation;
			tg.m_rotation3D = _rotation3D;
			tg.m_scale = _scale;
			tg.Activate (_fromMarker, _toMarker);
			tg.m_owner = _owner;
		}

		return tg;
	}*/
	
	public static TutorialGesture Create (GameObject _prefab, Transform _parent, Transform _fromMarker, Transform _toMarker, float _rotation, float _scale, Vector3 _rotation3D) {
		var go = Instantiate (_prefab, _parent);
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = new Vector3(1f, 1f, 1f);

		go.SetLayerRecursively(GameManager.Me.CurrentLayer);
		var tg = go.GetComponent<TutorialGesture> ();
		if (tg) {
			tg.m_rotation = _rotation;
			tg.m_rotation3D = _rotation3D;
			tg.m_scale = _scale;
			tg.Activate (_fromMarker, _toMarker);
			//tg.m_owner = _owner;
		}

		return tg;
	}

	//---------------------------------------------------------------------------------------------------------------------------------
	protected virtual void SetVisualsEnabled (bool _value) { }

	//---------------------------------------------------------------------------------------------------------------------------------
	/*private void Activate (TutorialMarker _fromMarker, TutorialMarker _toMarker)
	{
		m_isTransformType = false;
		m_fromMarker = _fromMarker;
		if (m_fromMarker) {
			m_fromMarker.OnActivate += UpdateActive;
			m_fromMarker.OnDeactivate += UpdateActive;
		}
		m_toMarker = _toMarker;
		if (m_toMarker) {
			m_toMarker.OnActivate += UpdateActive;
			m_toMarker.OnDeactivate += UpdateActive;
		}

		UpdateActive ();
		m_initialised = true;
	}*/

	private void Activate(Transform _fromMarker, Transform _toMarker)
	{
		m_isTransformType = true;
		m_fromMarkerTransform = _fromMarker;
		m_toMarkerTransform = _toMarker;
	}
	private void UpdateActive () {
        var showVisuals = true;// (m_fromMarker == null || m_fromMarker.gameObject.activeSelf) && (m_toMarker == null || m_toMarker.gameObject.activeSelf);
		if (!m_initialised || showVisuals != m_showVisuals) {
			SetVisualsEnabled (showVisuals);
			if (showVisuals) {
				Initialise ();
			}
		}
		m_showVisuals = showVisuals;
	}

	//---------------------------------------------------------------------------------------------------------------------------------
	bool m_initialised = false;
	bool m_showVisuals = false;
	private void Update () {
		if (!m_showVisuals) {
			return;
		}

		UpdateMove ();
	}

	//---------------------------------------------------------------------------------------------------------------------------------
	public virtual void Initialise () { }

	//---------------------------------------------------------------------------------------------------------------------------------
	public virtual void UpdateMove () { }
	//---------------------------------------------------------------------------------------------------------------------------------
	void OnDestroy () {
//		if (m_fromMarker)
//			Destroy (m_fromMarker.gameObject);
//		if (m_toMarker)
//			Destroy (m_toMarker.gameObject);
	}

}
