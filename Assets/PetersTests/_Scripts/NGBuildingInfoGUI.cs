using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
public class NGBuildingInfoGUI : NGBaseInfoGUI
{
    [System.Serializable] 
    public class NGBuildingInfoGUIComponents
    {
        public string m_name;
        public NGBuildingInfoGUIComponentBase m_prefab;
    }

    private const float c_width = 600;

    public MABuilding m_building;
    public List<NGBuildingInfoGUIComponents> m_componentInfos;
    public List<NGBuildingInfoGUIComponentBase> m_components;
    public NGScrollRect m_scrollRect;
    public TMP_Text m_description;
    public Transform m_content;
    private Vector3 m_startPos;
    private NGRename m_rename;
    private Coroutine m_waitingForConstructionToComplete = null;

    override protected string AudioHook_Close => "PlaySound_BuildingContextMenu_InfoScreenClose";
    
    private void OnDestroy()
    {
        s_currentInstance = null;
        
        if(m_waitingForConstructionToComplete != null)
        {
            StopCoroutine(m_waitingForConstructionToComplete);
            m_waitingForConstructionToComplete = null;
        }
        if (m_rename != null && m_rename.gameObject != null)
            m_rename.DestroyMe();
        if(m_buildingHighlight)
            m_buildingHighlight.DestroyMe();
    }

    override public void ClickedEditName()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        m_rename = NGRename.Create(m_building.m_title, GotNewName);
        base.ClickedEditName();
    }

    public float m_desiredHeight = 1400;
    private IEnumerator Resize()
    {
        yield return new WaitForEndOfFrame();

        var comps = GetComponentsInChildren<NGBuildingInfoGUIComponentBase>();
        var h = 0f;
        foreach(var c in comps)
            h += c.GetComponent<RectTransform>().GetHeight();
        
        if (h > m_desiredHeight)
        {
            h = m_desiredHeight;
            var vprt = m_scrollRect.GetComponent<RectTransform>();
            vprt.anchoredPosition = new Vector2(340, -200);
            vprt.sizeDelta = new Vector2(680, 400);

            vprt = m_scrollRect.viewport.GetComponent<RectTransform>();
            vprt.anchoredPosition = new Vector2(340, -200);
            vprt.sizeDelta = new Vector2(680, 370);
        }
        else if(h > 390)
        {
            var offset = h - 390;
            h += 90;

            var vprt = m_scrollRect.GetComponent<RectTransform>();
            vprt.anchoredPosition = new Vector2(340, -100 - (offset / 2f));
            vprt.sizeDelta = new Vector2(680, 200 + offset);

            vprt = m_scrollRect.viewport.GetComponent<RectTransform>();
            vprt.anchoredPosition = new Vector2(340, -100 - (offset / 2f));
            vprt.sizeDelta = new Vector2(680, 180 + offset);
        }
        else
        {
            h = 490;
        }
        var rt = GetComponent<RectTransform>();
        rt.sizeDelta = new Vector2(c_width, h);
        transform.position = m_startPos;
    }

    override public void GotNewName(string _newName)
    {
        m_building.m_title = _newName;
        m_building.m_stateData.m_title = _newName;
        base.GotNewName(_newName);
    }

    protected override Transform CreateComponent(string _name, Transform _parent)
    {
        var componentInfo = m_componentInfos.Find(o => o.m_name.Equals(_name, StringComparison.CurrentCultureIgnoreCase));
        if (componentInfo == null)
        {
            Debug.LogError($"No such component {_name}");
            return null;
        }
        var component = NGBuildingInfoGUIComponentBase.Create(m_building, componentInfo.m_prefab, _parent);
        m_components.Add(component);
        return component.transform;
    }

    private BuildingHighlight m_buildingHighlight;

    override protected float Activate(string _title)
    {
        var maBuilding = m_building as MABuilding;
        m_description.text = "";//maBuilding.GetDescription();
        m_title.text = _title;
        
        MABuildingButtonsPanel.Create(m_componentHolder, maBuilding);
        
        MABuildingInfoPanelSection.Create(m_componentHolder, new WarningInfoPanel(m_building as MABuilding));
        
        CreateWorkerPanel();
        
        List<BCUIPanel> panels = new();
        foreach(var component in maBuilding.ActionComponents)
        {
            component.GetOrCreatePanel(panels);
        }
        
        foreach(var component in maBuilding.m_components)
        {
            if(component.IsAction) continue;
            component.GetOrCreatePanel(panels);
        }
        
        panels.Sort((a, b) => b.Priority.CompareTo(a.Priority));
        
        foreach(var panel in panels)
        {
            MABuildingInfoPanelSection.Create(m_componentHolder, panel);
        }
        
        CreateBlocksPanel();
        return 0f;
    }
    
    private void CreateWorkerPanel()
    {
        var workerPanel = new BCWorkerPanel();
        
        foreach(var ac in m_building.ActionComponents)
        {
            if(ac as BCBedroom) continue;
            workerPanel.AddComponent(ac);
        }
        
        if(workerPanel.Quantity == 0)
            return;
        
        MABuildingInfoPanelSection.Create(m_componentHolder, workerPanel);
    }
    
    private void CreateBlocksPanel()
    {
        //MABuildingComponentInfo.Create(m_componentHolder, m_building as MABuilding, MABuildingComponentInfo.EType.Blocks);
        
        var blockPanel = new BCBlockInfoPanel(m_building as MABuilding);
        MABuildingInfoPanelSection.Create(m_componentHolder, blockPanel);
    }
    
    void Activate(NGCommanderBase _building)
    {
        //m_startPos = transform.position;
        //transform.position = new Vector3(-10000,0);
        m_building = _building as MABuilding;
        m_buildingHighlight = BuildingHighlight.Create(m_building);

        if (m_building is MABuilding)
        {
            Activate(m_building.GetBuildingTitle());
            //StartCoroutine(Resize());
            return;
        }
    }
    
    public static void DestroyCurrent()
    {
        if(s_currentInstance == null) return;
        
        s_currentInstance.DestroyMe();
    }
    
    private static NGBuildingInfoGUI s_currentInstance;
    
    public static NGBuildingInfoGUI Create(NGCommanderBase _building)
    {
        if(s_currentInstance != null)
        {
            s_currentInstance.DestroyMe();
            s_currentInstance = null;
        }
        
        var go = Instantiate(NGManager.Me.m_NGBuildingInfoGUIPrefab, NGManager.Me.NGInfoGUIHolder);
        var big = go.GetComponent<NGBuildingInfoGUI>();
        big.Activate(_building);
        s_currentInstance = big;
        return big;
    }

    override public void SetScale(float _scale)
    {
       // if (_scale > 1f)
        //    _scale = 1f;
        base.SetScale(_scale);
    }

    private static DebugConsole.Command s_debugEmptySelectedStock = new DebugConsole.Command("emptystock", _s =>
    {
        NGBuildingInfoGUI buildingInfo = FindObjectOfType<NGBuildingInfoGUI>();
        if(buildingInfo == null)
        {
            Debug.LogError("command \"emptyStock\" only works on individual buildings. please select a building and open its info panel, then use the command");
            return;
        }
    });
}
