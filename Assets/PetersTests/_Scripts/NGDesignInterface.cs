using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGDesignInterface
{
    public class DesignScoreInterface
    {
        public enum ScoreTypes
        {
            None = -1,
            Product,
            Building,
        }

        public class Part
        {
            public NGBlockInfo m_block;
            public float m_decorationSellingPriceMultiplier;
            public float m_decorationPartScoreMultiplier;
            public float m_stickerScore;
            public float m_usageModifier = 1f;
            public float m_scoreModifier = 1f;
            public float m_sellPriceMultiplier = 1f;
            
            public float Defence => CalculateScore(m_block.m_defence);
            public float Attack => CalculateScore(m_block.m_attack);
            public float Beauty => CalculateScore(m_block.m_beauty);
            public float Nutrition => CalculateScore(m_block.m_nutrition);
            
            public float Rarity => (float)m_block.Rarity / Enum.GetNames(typeof(NGBalance.RarityType)).Length;
            public float SellingPrice => (m_block.SellingPrice / m_usageModifier) * m_decorationSellingPriceMultiplier * m_sellPriceMultiplier;
            public float BaseSellingPrice => m_block.SellingPrice * m_decorationSellingPriceMultiplier;
            public float Cost => m_block.Price;
            
            private float CalculateScore(float _value)
            {
                return (_value / m_usageModifier) * m_scoreModifier * m_decorationPartScoreMultiplier + m_stickerScore;
            }
        }
        
        public DesignScoreInterface() {}
        
        public DesignScoreInterface(DesignUtilities.PartDescription[] _parts, MAOrder _order)
        {
            m_order = _order;
            
            if(_parts != null) m_partDescriptions = _parts;
            
            if(_order.IsNullOrEmpty() == false)
                m_productLine = NGProductInfo.GetInfo(_order.ProductLine);
            
            Activate();
        }
        
        public ProductTestingManager.EProductTestResult TesterReaction
        {
            get
            {
                if(m_order.IsNullOrEmpty()) return ProductTestingManager.EProductTestResult.Sad;
                var normalizedScore = ScoreDesignAgainstOrder();
                if(normalizedScore <= 0.1f) return ProductTestingManager.EProductTestResult.Sad;
                if(normalizedScore <= 0.2f) return ProductTestingManager.EProductTestResult.Unhappy;
                if(normalizedScore <= 0.6f) return ProductTestingManager.EProductTestResult.Disappointed;
                if(normalizedScore <= 0.8f) return ProductTestingManager.EProductTestResult.Surprised;
                if(normalizedScore <= 0.9f) return ProductTestingManager.EProductTestResult.Delighted;
                if(normalizedScore <= 1f) return ProductTestingManager.EProductTestResult.Sublime;
                return ProductTestingManager.EProductTestResult.Orgasmic;
            }
        }

        private float? m_salesPriceMultiplier;
        public float SalesPriceMultiplier
        {
            get
            {
                if(m_salesPriceMultiplier == null)
                {
                    if(m_order.IsNullOrEmpty()) m_salesPriceMultiplier = 1;
                    else
                    {
                        float normalizedScore = TotalScore / m_order.GetQualityCeiling();
                        if(normalizedScore <= 0.2f) m_salesPriceMultiplier = 1;
                        else if(normalizedScore <= 0.4f) m_salesPriceMultiplier = 1.25f;
                        else if(normalizedScore <= 0.6f) m_salesPriceMultiplier = 1.5f;
                        else if(normalizedScore <= 0.8f) m_salesPriceMultiplier = 1.75f;
                        else if(normalizedScore <= 1f) m_salesPriceMultiplier = 2;
                        else if(normalizedScore <= 2f) m_salesPriceMultiplier = 2.5f;
                        else m_salesPriceMultiplier = 3;
                    }
                }
                return m_salesPriceMultiplier.Value;
            }
        }
        
        private DesignUtilities.PartDescription[] m_partDescriptions = new DesignUtilities.PartDescription[0];
        private MAOrder m_order;
        public string ProductLineName => m_productLine==null ? "" : m_productLine.m_prefabName;
        private NGProductInfo m_productLine;
        private List<Part> m_parts = new List<Part>();
        private ScoreTypes m_scoreType = ScoreTypes.None;
        private float m_sellingPrice;
        private float m_totalRarity;
        public int m_designHash;
        private List<MADesignPriceSheetLine.Descriptor> m_priceSheetData = new List<MADesignPriceSheetLine.Descriptor>();
        
        public DesignUtilities.PartDescription[] PartDescriptions => m_partDescriptions;
        public ScoreTypes ScoreType => m_scoreType;
        public bool IsProduct => ScoreType.Equals(ScoreTypes.Product);
        public NGProductInfo ProductLine => m_productLine;
        public List<Part> Parts => m_parts;
        public List<MADesignPriceSheetLine.Descriptor> PriceSheetData { get { return m_priceSheetData; } }
        
        
        public float PlayerLevelScore => 1f+Mathf.Clamp((float)NGBusinessDecisionManager.Me.PlayerLevel / (float)NGManager.Me.m_playerLevelCap, 0f, 1f) * NGManager.Me.m_playerLevelMultiplier;

        public NGCarriableResource GetDominantResource()
        {
            Dictionary<NGCarriableResource, int> dic = new();
            foreach(var part in m_parts)
            {
                foreach(var resource in part.m_block.GetDefaultProductMaterials())
                {
                    if(dic.ContainsKey(resource) == false)
                        dic.Add(resource, 1);
                    else
                        dic[resource]++;
                }
            }
            int largest = 0;
            NGCarriableResource best = null;
            foreach(var kvp in dic)
            {
                if(kvp.Value > 0)
                {
                    best = kvp.Key;
                }
            }
            
            return best ?? NGCarriableResource.GetInfo(NGCarriableResource.c_none);
        }
        
        private float? m_totalScore; 
        public float TotalScore
        {
            get
            {
                if(m_totalScore == null)
                {
                    switch(ProductLine?.m_prefabName.ToLower())
                    {
                        case "armour": m_totalScore = TotalDefense; break;
                        case "ammo":
                        case "projectile":
                        case "weapons": m_totalScore = TotalAttack; break;
                        case "food": m_totalScore = TotalNutrition; break;
                        
                        default: m_totalScore = TotalBeauty; break;
                    }
                }
                return m_totalScore.Value;
            }
        }
        
        public float ReputationSellingPriceMultiplier => m_order.IsNullOrEmpty() ? 1f : m_order.ProductPriceMultiplier;
        public float SellingPrice => (IsProduct) ? m_sellingPrice * SalesPriceMultiplier * ReputationSellingPriceMultiplier : 0f;
        public float Rarity { get { return m_totalRarity; } }

        public float TotalBeauty
        {
            get
            {
                var totalScore = 0f;
                foreach (var p in Parts) { totalScore += p.Beauty; }
                if (NGManager.Me.m_scoreAccumulatorMethod.Equals("average",StringComparison.OrdinalIgnoreCase))
                    return totalScore/Parts.Count;
                return Mathf.Clamp01(totalScore);
            }
        }
        
        public float TotalNutrition
        {
            get
            {
                var totalScore = 0f;
                foreach (var p in Parts) { totalScore += p.Nutrition; }
                if (NGManager.Me.m_scoreAccumulatorMethod.Equals("average",StringComparison.OrdinalIgnoreCase))
                    return totalScore/Parts.Count;
                return Mathf.Clamp01(totalScore);
            }
        }
        
        public float TotalAttack
        {
            get
            {
                var total = 0f;
                foreach(var p in Parts) { total += p.Attack; }
                return total;
            }
        }
        
        public float TotalDefense
        {
            get
            {
                var total = 0f;
                foreach(var p in Parts) { total += p.Defence; }
                return total;
            }
        }
        
        public float AveragePartRarity
        {
            get
            {
                if(m_parts.Count > 0) return m_totalRarity / m_parts.Count;
                return 0;
            }
        }

        private Dictionary<NGCarriableResource, float> m_materialsRequired = null;
        public Dictionary<NGCarriableResource, float> MaterialsRequired 
        {
            get
            {
                if(m_materialsRequired == null)
                {
                    m_materialsRequired = new Dictionary<NGCarriableResource, float>();
                    // Add in design Parts
                    foreach (var p in Parts)
                    {
                        if (p.m_block.m_isInvisible) continue;
                        switch (ScoreType)
                        {
                            case ScoreTypes.Product:
                                if (p.m_block.m_materialCost.IsZero() == false)
                                    foreach (var m in p.m_block.GetProductMaterials(ProductLine))
                                        m_materialsRequired.AddCount(m, p.m_block.m_materialCost);
                                break;
                            case ScoreTypes.Building:
                                if (p.m_block.m_materialCost.IsZero() == false)
                                    foreach (var m in p.m_block.BuildingMaterials)
                                        m_materialsRequired.AddCount(m, p.m_block.m_materialCost);
                                break;
                        }
                    }
                    // Add in Paint Patterns and Stickers

                    foreach (var pd in PartDescriptions)
                    {
                        foreach (var i in pd.m_paint)
                        {
                            var paint = PaintPotData.s_entries[i];
                            if(paint.m_materialCost.IsZero() == false)
                                foreach(var m in paint.Materials)
                                    m_materialsRequired.AddCount(m, paint.m_materialCost);
                        }
                        foreach (var i in pd.m_pattern)
                        {
                            var pattern = PatternData.s_entries[i];
                            if(pattern.m_materialCost.IsZero() == false)
                                foreach(var m in pattern.Materials)
                                    m_materialsRequired.AddCount(m, pattern.m_materialCost);
                        }
                        foreach (var i in pd.m_stickers)
                        {
                            var sticker = StickerData.s_entries[i];
                            if(sticker.m_materialCost.IsZero() == false)
                                foreach(var m in sticker.Materials)
                                    m_materialsRequired.AddCount(m, sticker.m_materialCost);
                        }
                    }
                }
                return m_materialsRequired;
            }
        }
        
        public void RemoveComponentsFromCount(Dictionary<MAComponentInfo,int> _componentCounts)
        {
            foreach(var part in m_partDescriptions)
            {
                NGBlockInfo.GetInfo(part.m_blockID).RemoveComponentFromCount(_componentCounts);
            }
        }

        private float? m_tapsToBuild;
        public float TapsToBuild
        {
            get
            {
                if(m_tapsToBuild == null)
                {
                    m_tapsToBuild = 0f;
                    foreach (var p in Parts)
                        m_tapsToBuild += p.m_block.m_numTapsToMake;
                    foreach (var pd in PartDescriptions)
                    {
                        foreach (var i in pd.m_paint)
                            m_tapsToBuild += PaintPotData.s_entries[i].m_numTapsToMake;
                        foreach (var i in pd.m_pattern)
                            m_tapsToBuild += PatternData.s_entries[i].m_numTapsToMake;
                        foreach (var i in pd.m_stickers)
                            m_tapsToBuild += StickerData.s_entries[i].m_numTapsToMake;
                    }
                }
                return m_tapsToBuild.Value;
            }
        }

        private float? m_secondsForWorkerToBuild;
        public float SecondsForWorkerToBuild
        {
            get
            {
                if(m_secondsForWorkerToBuild == null)
                {
                    m_secondsForWorkerToBuild = 0f;
                    foreach (var p in Parts)
                        m_secondsForWorkerToBuild += p.m_block.m_workerTimeToMake;
                    foreach (var pd in PartDescriptions)
                    {
                        foreach (var i in pd.m_paint)
                            m_secondsForWorkerToBuild+= PaintPotData.s_entries[i].m_workerTimeToMake;
                        foreach (var i in pd.m_pattern)
                            m_secondsForWorkerToBuild+= PatternData.s_entries[i].m_workerTimeToMake;
                        foreach (var i in pd.m_stickers)
                            m_secondsForWorkerToBuild+= StickerData.s_entries[i].m_workerTimeToMake;
                    }
                }
                return m_secondsForWorkerToBuild.Value;
            }
        }
        
        public float GetPriceOfBlock(NGBlockInfo _block)
        {
            var price = _block.Price;
            
            if(IsProduct == false)
            {
                foreach(var c in _block.GetComponentInfos())
                {
                    var count = MABuilding.GetAllBlockComponentCount(c)+1;
                    price += c.GetPrice(count) * _block.MarketInfo.m_currentBlockCostMultiplier;
                }
            }
            return price;
        }
        
        public float ScoreDesignAgainstOrder()
        {
            if(m_order.IsNullOrEmpty()) return 0f;
            
            var tagMatches = new HashSet<string>();
            var requriedTags = m_order.OrderInfo.Tags;
            
            if(requriedTags.Count == 0)
                return 1f;
                
            foreach(var p in m_parts)
            {
                if(p.m_block == null || p.m_block.m_tagArray == null)
                    continue;
                    
                foreach(var tag in p.m_block.m_tagArray)
                {
                    if(requriedTags.Contains(tag))
                    {
                        tagMatches.Add(tag);
                    }
                }
            }
            return Mathf.Clamp01(tagMatches.Count / (float)requriedTags.Count);
        }
        
        public float GetPriceOfRemovingBlock(NGBlockInfo _block)
        {
            var price = _block.Price;

            if (IsProduct == false)
            {
                foreach (var c in _block.GetComponentInfos())
                {
                    var count = MABuilding.GetAllBlockComponentCount(c);
                    price += c.GetPrice(count) * _block.MarketInfo.m_currentBlockCostMultiplier;
                }
            }

            return price;
        }
        
        
        public float TotalCost(Dictionary<MAComponentInfo,int> _componentCounts = null)
        {
            float total = 0;
            
            foreach(var p in m_partDescriptions)
            {
                total += p.TotalCost(_componentCounts);
            }
            
            return total;
        }
        
        public string ReputationSalesPriceMultuiplierText => $"<b><voffset=2>x</voffset>{ReputationSellingPriceMultiplier}</b>";
        public string SalesPriceMultuiplierText => $"<b><voffset=2>x</voffset>{SalesPriceMultiplier}</b>";
        
        public Part Add(NGBlockInfo _part)
        {
            if(_part ==null || _part.id == null)
                return null;
            var part = new Part() {m_block = _part};
            
            Parts.Add(part);
            return part;
        }
        
        public void Activate()
        {
            if(GameManager.Me == null) return;
            
            m_scoreType = m_productLine == null ? ScoreTypes.Building : ScoreTypes.Product;
            
            m_parts.Clear();
            
            var uses = new Dictionary<NGBlockInfo, int>();
            Dictionary<string, List<Part>> m_uniqueParts = new ();
            
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            float sellingPriceMultuplier = MAUnlocks.Me.GetSalesPriceMarkup(ProductLineName);
#else
            float sellingPriceMultuplier = 1f;
#endif
            
            string designUId = "";
            //Find Duplicates and add parts
            foreach (var desc in PartDescriptions)
            {
                var blockInfo = NGBlockInfo.GetInfo(desc.m_blockID); 
                var part = Add(blockInfo);
                if (part == null) continue;
                
                if(m_order.IsNullOrEmpty() == false && m_order.OrderInfo != null)
                {
                    part.m_scoreModifier = m_order.OrderInfo.GetBlockScoreMultiplier(blockInfo);
                }
                
                part.m_sellPriceMultiplier = sellingPriceMultuplier;
                var uid = desc.UniqueID();
                designUId += uid;
                // Paints
                foreach(var paintIndex in desc.m_paint)
                {
                    var paint = PaintPotData.s_entries[paintIndex];
                    part.m_decorationPartScoreMultiplier += paint.m_partScoreMultiplier;
                    part.m_decorationSellingPriceMultiplier += paint.m_sellingPriceMultiplier;
                }

                // Patterns
                foreach(var patternIndex in desc.m_pattern)
                {
                    var pattern = PatternData.s_entries[patternIndex];
                    part.m_decorationPartScoreMultiplier += pattern.m_partScoreMultiplier;
                    part.m_decorationSellingPriceMultiplier += pattern.m_sellingPriceMultiplier;
                }
                
                // Take average of all paint and pattern modifiers
                if((desc.m_pattern.Count + desc.m_paint.Count) == 0)
                {
                    part.m_decorationSellingPriceMultiplier = 1;
                    part.m_decorationPartScoreMultiplier = 1;
                }
                else
                {
                    part.m_decorationSellingPriceMultiplier /= desc.m_pattern.Count + desc.m_paint.Count;
                    part.m_decorationPartScoreMultiplier /= desc.m_pattern.Count + desc.m_paint.Count;
                } 
                
                // Stickers
                desc.m_stickers.ForEach(o => part.m_stickerScore += StickerData.s_entries[o].m_score);
                
                if(m_uniqueParts.TryGetValue(uid, out var partGroup) == false)
                {
                    partGroup = new List<Part>();
                    m_uniqueParts[uid] = partGroup;
                }
                partGroup.Add(part);
            }
            
            m_designHash = designUId.GetHashCode();
            
            m_priceSheetData.Add(new MADesignPriceSheetLine.TitleDescriptor("<b>Part</b>", "<b>Takes</b>", "<b>Cost</b>", "<b>Sell For</b>"));
            
            foreach(var partGroup in m_uniqueParts.Values)
            {
                int usages = partGroup.Count;
                var groupBlockInfo = partGroup[0].m_block;
                if (groupBlockInfo.m_isInvisible) continue;
                bool overused = usages > groupBlockInfo.m_usageCap;
                int overuseCount = usages - groupBlockInfo.m_usageCap;
                string decoratedPrefix = (partGroup[0].m_decorationSellingPriceMultiplier != 1f) ? "<i>Tweaked</i> " : "";
                var partDescriptor = new MADesignPriceSheetLine.Descriptor($"{usages} {decoratedPrefix}{groupBlockInfo.m_displayName}");

                float normalPrice = 0;
                float penalisedPrice = 0;
                float materialsUsed = 0;
                
                for(int i = 0; i < usages; ++i)
                {
                    int overuseIndex = Mathf.Max(0, (i+1) - groupBlockInfo.m_usageCap);
                    partGroup[i].m_usageModifier = Mathf.Pow(groupBlockInfo.m_usesModifier, overuseIndex);
                    
                    partDescriptor.m_price += partGroup[i].BaseSellingPrice;
                    materialsUsed += partGroup[i].m_block.m_materialCost;
                    partDescriptor.m_cost += partGroup[i].Cost;
                    normalPrice += partGroup[i].BaseSellingPrice;
                    penalisedPrice += partGroup[i].SellingPrice;
                }
                 
                var materials = partGroup[0].m_block.GetProductMaterials(ProductLine);
                foreach(var mat in materials)
                {
                    partDescriptor.m_resources += $" {(int)materialsUsed} {mat.TextSprite}";
                }
                
                m_priceSheetData.Add(partDescriptor);
                if(overused)
                    m_priceSheetData.Add(new MADesignPriceSheetLine.Descriptor($"  <color=red>Part overused x{overuseCount}</color>", "", 0, (penalisedPrice - normalPrice), 0));
            }
            
            m_sellingPrice = 0;
            m_totalRarity = 0;

            foreach(var part in m_parts)
            {
                var groupBlockInfo = part.m_block;
                if (groupBlockInfo.m_isInvisible) continue;
                m_sellingPrice += part.SellingPrice;
                m_totalRarity += part.Rarity;
            }
            
            var matsRequired = GetMaterialsRequiredList();
            string totalMats = "";
            foreach(var mat in matsRequired)
            {
                totalMats += $" <b>{mat.Item2}</b> {mat.Item1.TextSprite}";
            }

            m_priceSheetData.Add(new MADesignPriceSheetLine.TitleDescriptor($"<b>Design Multiplier</b>", "", "", SalesPriceMultuiplierText));
            m_priceSheetData.Add(new MADesignPriceSheetLine.TitleDescriptor($"<b>Reputation Multiplier</b>", ReputationSalesPriceMultuiplierText));
            m_priceSheetData.Add(new MADesignPriceSheetLine.Descriptor("<b>Total</b>", totalMats, TotalCost(), SellingPrice, Rarity, true));
        }
        
        public List<(NGCarriableResource, int)> GetMaterialsRequiredList()
        {
            var mr = MaterialsRequired;
            var result = new List<(NGCarriableResource, int)>();
            foreach (var m in mr)
            {
                result.Add(new (m.Key, (int)m.Value));
            }

            return result;
        }
    }
    
    public static DesignScoreInterface Get() => Get(DesignTableManager.Me?.GetDesignOnTable(), DesignTableManager.Me?.Order);
    public static DesignScoreInterface Get(GameState_Product _product) => Get(_product.Design, _product.GetLinkedOrder());
    public static DesignScoreInterface Get(GameState_Design _design, MAOrder _order = null) => Get(_design?.m_design, _order);
    public static DesignScoreInterface Get(string _design, MAOrder _order = null) => Get(DesignUtilities.GetDesignData(_design), _order);
    public static DesignScoreInterface Get(DesignUtilities.PartDescription[] _parts, MAOrder _order) => new DesignScoreInterface(_parts, _order);
 
}
