using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class TestDelete : MonoMe<TestDelete>
{
    public TMP_Text m_testText;

    void Awake()
    {
        Debug.Log("Awakw");
    }
   void OnDestroy()
    {
        Debug.Log("Deleted Me");
    }
    override public void DestroyMe()
    {
        Debug.Log("DestroyMe");
        base.DestroyMe();
//        s_isActive = false;
    }
}
