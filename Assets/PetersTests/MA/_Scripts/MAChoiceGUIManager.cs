using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;

public class MAChoiceGUIManager : MonoSingleton<MAChoiceGUIManager>
{
   public static string m_lastChoice = "";
   public static bool m_hasAllBanchSectionsEnded = false;
   public class Choices
   {
      public string m_flow;
      public string m_advisor;
      public string m_explain;
      public string m_video;
      public MAChoiceGUI m_gui;
   }
   public List<Choices> m_choices = new List<Choices>();
   public string m_name;
   public bool m_showing = false;
   public TMP_Text m_title;
   public TMP_Text m_details;
   public Transform[] m_holders = new Transform[4];
   public int m_mustBeSection = -1;
   private Animator m_anim;
   private List<(string _flow, MAChoiceGUI _gui)> m_scripts = new ();
   public string[] m_branchSections = new string[4];
   void Awake()
   {
      m_anim = GetComponent<Animator>();
   }

   void OnDestroy()
   {
      s_Me = null;
   }
   void DestroyMe()
   {
      gameObject.SetActive(false);
      Destroy(gameObject);
   }
   public void OpenedPanel(int _panel)
   {
      m_scripts[_panel]._gui.ReadyToOpen();
   }
   public void ShowPanel(int _panelID)
   {
      // Enable the correct layer by setting its weight to 1
      switch (_panelID)
      {
         case 0:
            m_anim.SetLayerWeight(1, 1); // Layer 1: Left
            m_anim.SetTrigger("LeftOpen");
            break;

         case 1:
            m_anim.SetLayerWeight(2, 1); // Layer 2: Right
            m_anim.SetTrigger("RightOpen");
            break;

         case 2:
            m_anim.SetLayerWeight(3, 1); // Layer 3: Top
            m_anim.SetTrigger("BottomOpen");
            break;

         case 3:
            m_anim.SetLayerWeight(4, 1); // Layer 4: Bottom
            m_anim.SetTrigger("TopOpen");
            break;
      }
   }

   private void Update()
   {
      if (m_hasAllBanchSectionsEnded == false)
      {
         m_hasAllBanchSectionsEnded = true;
         for (var i = 0; i < m_branchSections.Length; i++)
         {
            var s = m_branchSections[i];
            if (s.IsNullOrWhiteSpace()) continue;
            if (MAParserManager.Me.IsSectionActive(s))
            {
               m_hasAllBanchSectionsEnded = false;
               break;
            }
         }
      }
   }

   public void SetCanvasGroup(bool _isActive)
   {
      m_showing = _isActive;
      var cg = GetComponent<CanvasGroup>();
      if (cg)
      {
         cg.interactable = _isActive;
         cg.blocksRaycasts = _isActive;
         cg.alpha = (_isActive ? 1 : 0);
      }
      
   }
   public void ClickChoice(int _id)
   {
      SetCanvasGroup(false);
      m_lastChoice = m_scripts[_id]._flow;
      for (var i = m_scripts.Count - 1; i >= 0; i--)
      {
         var s = m_scripts[i];
         s._gui.DestroyMe();
//         s._gui.Showing(false);
      }
   }
   
   private bool ScriptExists(string _name)
   {
      foreach(var script in m_scripts)
      {
         if(script._flow.Equals(_name))
         {
            return true;
         }
      }
      return false;
   }
   
   public void AddChoice(string _fromMOA, string _advisor, string _explain, string _video)
   {
      if(ScriptExists(_fromMOA)) return; 
      
      SetCanvasGroup(true);
      var id = m_scripts.Count;
      var choiceGUI = MAChoiceGUI.Create(id, _advisor, _explain, _video, m_holders[id]);
      ShowPanel(id);
      m_scripts.Add((_fromMOA, choiceGUI));
   }
   public void AddChoice(string _fromMOA, string _sprite, string _explain)
   {
      if(ScriptExists(_fromMOA)) return;
      
      SetCanvasGroup(true);
      var id = m_scripts.Count;
      var choiceGUI = MAChoiceGUIDialog.Create(id, _sprite, _explain, m_holders[0]);
      ShowPanel(id);
      m_scripts.Add((_fromMOA, choiceGUI));
   }

   public void ReplaceChoice(string _replaceWhat, string _replaceWith)
   {
      var foundScript = m_scripts.Find((x) => x._flow.Equals(_replaceWhat));
      var foundSection = m_branchSections.FindIndex((x) => x.Equals(_replaceWhat));
      if(foundScript._flow.IsNullOrWhiteSpace() == false)
         m_scripts.Remove(foundScript);
      if(foundSection >= 0)
         m_branchSections[foundSection] = _replaceWith;
      m_lastChoice = "";
      var section = MAParserManager.Me.ExecuteSection(_replaceWhat);
   }
   void Activate(string _title, string _details, string _section_1, string _section_2, string _section_3, string _section_4, int _mustBeSection)
   {
      _title = _title.Trim();
      m_name = _title;
      m_title.text = m_name;
      m_mustBeSection = _mustBeSection;
      m_branchSections = new []{_section_1, _section_2, _section_3, _section_4};
      if (_details != null && m_details != null)
      {
         m_details.text = _details;
         m_details.gameObject.SetActive(true);
      }
      m_lastChoice = null;
      foreach(var holder in m_holders)
      {
         holder.DestroyChildren();
      }
      bool alreadyActive = false;
      for (var i = 0; i < m_branchSections.Length; i++)
      {
         if (m_branchSections[i].IsNullOrWhiteSpace()) continue;
         m_branchSections[i] = m_branchSections[i].Trim();
         var s = m_branchSections[i];
         if (MAParserManager.Me.IsSectionActive(s))
         {
            SetCanvasGroup(false);
            alreadyActive = true;
            break;
         }
      }

      if (alreadyActive == false)
      {
         for (var i = 0; i < m_branchSections.Length; i++)
         {
            var s = m_branchSections[i];
            if(s.IsNullOrWhiteSpace()) continue;
            var section = MAParserManager.Me.ExecuteSection(s);
            if (section == null) m_branchSections[i] = null;
         }
      }
      m_hasAllBanchSectionsEnded = false;
   }
   public static MAChoiceGUIManager CreateChoice(string _title, string _section_1, string _section_2, string _section_3, string _section_4, int _mustBeSection = -1)
   {
      if(Me) Me.DestroyMe();
      var prefab = Resources.Load<MAChoiceGUIManager>("_Prefabs/Dialogs/MAChoiceGUIManager");
      var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
      instance.Activate(_title, null, _section_1, _section_2, _section_3, _section_4, _mustBeSection);
      return instance;
   }
   public static MAChoiceGUIManager CreateChoiceDialog(string _title, string _details, string _section_1, string _section_2, string _section_3, string _section_4, int _mustBeSection = -1)
   {
      if(Me) Me.DestroyMe();
      var prefab = Resources.Load<MAChoiceGUIManager>("_Prefabs/Dialogs/MAChoiceGUIManagerDialog");
      var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
      instance.Activate(_title, _details, _section_1, _section_2, _section_3, _section_4, _mustBeSection);
      return instance;
   }
}
