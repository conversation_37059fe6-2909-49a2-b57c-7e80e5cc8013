using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MAHelper : MonoBehaviour
{
    public static NGLegacyBase s_triedToPickupObjectInGrey;
    public enum HelperTypes
    {
        None,
        ClearVinesBlock,
        ClearVinesBuilding,
        TapDesign,
        DragOrder,
        Timed,
        WASD,
        GodHandMove,
        ProduceBuilding,
        GUIElement,
        GUIReseachButton,
        DragFrom,
        DragWall,
        Possess,
        PickupCharacter,
        SetPatrol,
        DragBlock,
        PossessDog,
        FollowTrail,
        TapGallows,
        AssignAccused,
        SpeedupToNight,
        DragWorkerFromTavern,
        DesignTable,
        PickupInGrey,
        NightChallenge,
        Last
    }
    public TMP_Text m_text;
    public GameObject m_background;

     
    public string m_name;
    public HelperTypes m_type;
    public float m_startTimer;
    public float m_endTimer;
    public int m_count = 1;
    public float m_backupEndTimer;
    public int m_buildingComponentCount;
    public int m_buildingStockCount;
    public float m_wallLength;
    public Transform m_arrowTransform;
    public PathManager.Path m_dragWall;
    public string m_GUIType;
    public bool m_GUIActive;
    public bool m_showing = true;

    void ActivateGUI(string _name, string _type, string _message, float _startAfterTimer, bool _active, string _GUIType, float _xOffset, float _yOffset, string _position, string _arrow)
    {
        m_GUIType = _GUIType;
        m_GUIActive = _active;
        SetPivotAndPosition(_position, _xOffset, _yOffset);
        Activate(_name, _type, _message, _startAfterTimer, -1, _arrow);
    }
    void Activate(string _name, string _type, string _message, float _startAfterTimer, float _lastForTimer, string _arrow, int _count = 1)
    {
        ShowMe(true);
        m_name = _name;
        m_text.text = ReplaceInputTokens(_message);
        m_startTimer = _startAfterTimer;
        m_endTimer = _lastForTimer;
        m_count = _count;
        var faceForward = GetComponent<AlwaysFaceCamera>();
        
        if (m_startTimer > 0)
        {
            m_startTimer = Time.time + m_startTimer;
            ShowMe(false);
        }
        else
        {
            m_startTimer = -1;
            if (m_endTimer > 0)
                m_endTimer = Time.time + m_endTimer;
            else
                m_endTimer = -1;
            ShowMe(true);
        }
        if (Enum.TryParse(_type.Trim(), out m_type) == false)
        {
            Debug.LogError(MAParserSupport.DebugColor($"No such HelperType: {_type}"));
        }

        if (m_arrowTransform != null)
        {
            _arrow = _arrow.Trim();
            m_arrowTransform.gameObject.SetActive(false);

            if (_arrow.IsNullOrWhiteSpace() == false)
            {
                var arrowNames = new string[] { "down", "up", "left", "right" };
                var index = arrowNames.FindIndex(o=> o.Equals(_arrow, StringComparison.InvariantCultureIgnoreCase));
                if (index >= 0)
                {
                    m_arrowTransform.gameObject.SetActive(true);
                    m_arrowTransform.GetChild(index).gameObject.SetActive(true);
                }
                switch(_arrow)
                {
                    case "down": (transform as RectTransform).pivot = new Vector2(0.5f, 0f); break;
                    case "up": (transform as RectTransform).pivot = new Vector2(0.5f, 1f); break;
                    case "left": (transform as RectTransform).pivot = new Vector2(0f, 0.5f); break;
                    case "right": (transform as RectTransform).pivot = new Vector2(1f, 0.5f); break;
                }
                
            }
        }

        switch (m_type)
        {
            case HelperTypes.TapDesign:
                var vineBuilding = GetComponentInParent<MABuilding>();
                if(vineBuilding)
                    m_buildingComponentCount = vineBuilding.GetBuildingComponentCount();
                break;
            case HelperTypes.DragFrom:
                var dragBuilding = GetComponentInParent<MABuilding>();
                if(dragBuilding)
                    m_buildingStockCount = dragBuilding.GetStockSpace<BCStockBase>(true);
                break;
            case HelperTypes.DragWall:
                m_dragWall = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(transform.position);
                if(m_dragWall != null)
                    m_wallLength = m_dragWall.TotalLength();
                break;
            case HelperTypes.WASD:
                transform.localScale*=0.1f;
                if(faceForward != null)
                    faceForward.m_pushForward = 0;
                break;
            case HelperTypes.GodHandMove:
                transform.localScale*=0.2f;
                transform.localPosition = new Vector3(0,1f,-1.5f);
                break;
            case HelperTypes.DragBlock:
            {
                var dragBlock = GetComponentInParent<MAWildBlock>();
                if(dragBlock == null || dragBlock.IsDisabledByPlants)
                    ShowMe(false);
                else
                    ShowMe(true);
                break;
            }
            case HelperTypes.PossessDog:
                transform.localScale*=0.6f;
                faceForward.m_pushForward = 0;
                break;
            case HelperTypes.FollowTrail:
                transform.localScale*=0.7f;
                if(faceForward != null)
                    faceForward.m_pushForward = 0;
                break;
            case HelperTypes.AssignAccused:
            case HelperTypes.TapGallows:
                transform.localScale*=0.6f;
                if(faceForward != null) 
                    faceForward.m_pushForward = 0;
                break;
            case HelperTypes.SpeedupToNight:
                if(DayNight.Me.m_isFullNight || DayNight.Me.m_isDusk)
                {
                    DestroyMe();
                    return;
                }
                break; 
            case HelperTypes.DesignTable:
                transform.localScale = Vector3.one*0.75f;
                transform.localPosition = Vector3.zero;
                break;
            case HelperTypes.PickupInGrey:
                s_triedToPickupObjectInGrey = null;
                m_backupEndTimer = _lastForTimer;
                m_endTimer = -1;
                ShowMe(false);
                break;
            
        }
        NGManager.Me.m_helpers.Add(this);
    }
    void ShowMe(bool _show)
    {
        m_showing = _show;
        m_background.SetActive(_show);
        m_text.gameObject.SetActive(_show);
    }
    void Update()
    {
        if(m_startTimer > 0f)
        {
            if (Time.time > m_startTimer)
            {
                m_startTimer = -1;
                ShowMe(true);
                if(m_endTimer > 0)
                    m_endTimer = Time.time + m_endTimer;
            }
        }
        else if(m_endTimer > 0f && Time.time > m_endTimer)
        {
            ShowMe(false);
            m_endTimer = -1;
            m_count--;
            if (m_count <= 0)
            {
                DestroyMe();
                return;
            }
        }
        
        switch (m_type)
        {
            case HelperTypes.ClearVinesBlock:
                var wildBlock = GetComponentInParent<MAWildBlock>();
                if(wildBlock == null || wildBlock.IsDisabledByPlants == false)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.DragBlock:
                var dragBlock = GetComponentInParent<MAWildBlock>();
                if(dragBlock == null|| dragBlock.GetComponentInParent<MABuilding>())
                {
                    DestroyMe();
                    return;
                }
                ShowMe(dragBlock.IsDisabledByPlants == false);
                break;
            case HelperTypes.ClearVinesBuilding:
                var vineBuilding = GetComponentInParent<MABuilding>();
                if(vineBuilding == null || vineBuilding.IsDisabledByPlants == false)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.TapDesign:
                var tabBuilding = GetComponentInParent<MABuilding>();
                if(tabBuilding == null || tabBuilding.GetBuildingComponentCount() != m_buildingComponentCount)
                {
                    DestroyMe();
                    return;
                }
                if (DesignTableManager.Me.m_isInDesignGlobally && DesignTableManager.Me.m_designGloballyFocusBuilding == tabBuilding)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.DragFrom:
                var dragBuilding = GetComponentInParent<MABuilding>();
                if(dragBuilding == null || m_buildingStockCount != dragBuilding.GetStockSpace<BCStockBase>(true))
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.DragOrder:
                var building = GetComponentInParent<MABuilding>();
                if(building == null || DesignTableManager.Me.IsInDesignInPlace)
                {
                    DestroyMe();
                }
                return;
                
            case HelperTypes.SetPatrol:
                if(InputUtilities.GetCurrentDragObject() == null || EKeyboardFunction.SetPatrolZone.IsDown())
                {
                    DestroyMe();
                }
                return;

            case HelperTypes.PickupCharacter:
            case HelperTypes.AssignAccused:
                var character = GetComponentInParent<MACharacterBase>();
                if(character == null || character.gameObject == InputUtilities.GetCurrentDragObject())
                {
                    DestroyMe();
                }
                return;
            case HelperTypes.Possess:
                if(GameManager.Me.IsPossessing)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.PossessDog:
                if(GameManager.Me.IsPossessing)
                {
                    if (GameManager.Me.PossessedCharacter is MADog)
                    {
                        DestroyMe();
                        return;
                    }
                }
                break;
            case HelperTypes.WASD:
                if (EKeyboardFunction.MoveCamera.AnyDown())
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.GodHandMove:
                if (Input.GetMouseButtonDown(1))
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.ProduceBuilding:
                var buildings = NGManager.Me.m_maBuildings;
                foreach (var b in buildings)
                {
                    var tap = b.GetComponentsInChildren<BCTap>();
                    foreach (var t in tap)
                    {
                        if(t.InLongPress)
                        {
                            DestroyMe();
                            return;
                        }
                    }
                }
                break;
            case HelperTypes.GUIReseachButton:
                var activeElement2 = GetHUDElement(m_GUIType);
                if (MAResearchManagerUI.m_isActive == true)
                {
                    DestroyMe();
                }
                ShowMe(true);
                break;
            case HelperTypes.GUIElement:
                var activeElement = GetHUDElement(m_GUIType);
                if (m_GUIActive)
                {
                    if(activeElement.holder == null || activeElement.active == false)
                    {
                        DestroyMe();
                        return;
                    }
                }
                else if (activeElement.active)
                { 
                    m_GUIActive = true;
                    ShowMe(m_showing);
                }
                break;
            case HelperTypes.DragWall:
                if (m_dragWall == null || m_dragWall.TotalLength() != m_wallLength)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.TapGallows:
                var quest = GetComponentInParent<MAQuestJudgement>();

                if(quest == null || quest.QuestObjectiveValue("GallowsTriggered") == 0)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.SpeedupToNight:
                if(DayNight.Me.m_isFullNight || DayNight.Me.m_isDusk)
                {
                    DestroyMe();
                    return;
                }
                break;
            case HelperTypes.DragWorkerFromTavern:
                if (NGManager.Me.m_MAWorkerList.Count > 0)
                {
                    DestroyMe();
                }
                break;
            case HelperTypes.PickupInGrey:
                if (s_triedToPickupObjectInGrey)
                {
                    transform.SetParent(s_triedToPickupObjectInGrey.transform, false);
                    m_endTimer = m_backupEndTimer + Time.time;
                    ShowMe(true);
                    s_triedToPickupObjectInGrey = null;
                }
                break;
            case HelperTypes.NightChallenge:
                if(MANightChallengeDialog.Me != null)
                {
                    DestroyMe();
                    return;
                }

                break;
        }
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public void SetPivotAndPosition(string _position, float _xOffset, float _yOffset)
    {
        var rectTransform = GetComponent<RectTransform>();
        var pivot = new Vector2(0.5f, 0.5f);
        var anchoredPosition = new Vector2(_xOffset, 0);
        var position = _position.ToLower().Trim().Replace(" ", "");
        switch (position)
        {
            case "topleft":
                pivot = new Vector2(0, 1);
                break;

            case "topmiddle":
                pivot = new Vector2(0.5f, 1);
                break;

            case "topright":
                pivot = new Vector2(1, 1);
                break;

            case "middleleft":
                pivot = new Vector2(0, 0.5f);
                break;

            case "center":
            case "centre":
                pivot = new Vector2(0.5f, 0.5f);
                break;

            case "middleright":
                pivot = new Vector2(1, 0.5f);
                break;

            case "bottomleft":
                pivot = new Vector2(0, 0);
                break;

            case "bottommiddle":
                rectTransform.pivot = new Vector2(0.5f, 0f);
                rectTransform.anchorMin = new Vector2(0.5f, 0f);
                rectTransform.anchorMax = new Vector2(0.5f, 0f);
                pivot = new Vector2(0.5f, 0);
                return;
                break;

            case "bottomright":
                pivot = new Vector2(1, 0);
                break;

            default:
                return;
        }

        rectTransform.pivot = pivot;
        rectTransform.anchoredPosition = new Vector2(_xOffset, _yOffset);  // Adjusts position if necessary
    }
    public static (Transform holder, bool active) GetHUDElement(string _type)
    {   // CalenderUIManager/buttonholder
        var split= _type.Split('/');

        if (split[0].Equals("DesignTable"))
        {
            
        }
        var holder = NGManager.Me.m_screenLocationsHolder.parent;
        var type = Type.GetType(split[0]);
        if (type == null)
        {
            MAParser.ParserError($"No such Type: {_type}");
            return (null, false);
        }
        var comp = holder.GetComponentInChildren(Type.GetType(split[0]));
        if (comp != null)
        {
            holder = comp.transform;
            if (split.Length > 1)
            {
                holder = comp.transform.Find(split[1]);
            }
            return (holder, true);
        }
        return (holder, false);
    }
    public static MAHelper CreateBuilding(string _name, MABuilding _overBuilding, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAHelper>("_Prefabs/Dialogs/MAHelperV2");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAHelperGUI");
            return null;
        }
        var instance = Instantiate(prefab, _overBuilding.m_balloonHolder);
        var afc = instance.GetComponent<AlwaysFaceCamera>();
        if(afc)
        {
            afc.m_pushForward = 0.01f; // Needs a value to allow for the pushdown
            afc.m_pushDown = -_heightAdjust;
            afc.Start();
        }
        // Old way of doing it below
        //var pos = _overBuilding.GetCentalPosition(true) + new Vector3(0, _heightAdjust, 0);
        //instance.transform.position = pos;
        instance.Activate(_name, _type, _message, _startAfterTimer, _lastForTimer, _arrow);
        return instance;
    }
    public static MAHelper CreateCardHolder(string _name, BuildingCardHolder _overCardHolder, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAHelper>("_Prefabs/Dialogs/MAHelperV2");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAHelperGUI");
            return null;
        }
        var instance = Instantiate(prefab, _overCardHolder.transform);
        var rt = instance.GetComponent<RectTransform>();
        rt.localScale = new Vector3(3, 3, 3);
        rt.anchoredPosition = new Vector2(0, 380);
        instance.gameObject.AddComponent<LayoutElement>().ignoreLayout = true;
       // Canvas.ForceUpdateCanvases();
        //instance.transform.localPosition = new Vector3(88, 111+_heightAdjust, 0);
        var afc = instance.GetComponent<AlwaysFaceCamera>();
        if(afc)
        {
            afc.m_freezeXZ = true;
            afc.m_pushForward = 0; // Needs a value to allow for the pushdown
            afc.m_pushDown = -_heightAdjust;
            afc.Start();
        }
        instance.Activate(_name, _type, _message, _startAfterTimer, _lastForTimer, _arrow);
        return instance;
    }
    public static MAHelper Create(string _name, GameObject _overGameObject, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count = 1  )
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAHelper>("_Prefabs/Dialogs/MAHelperV2");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAHelperGUI");
            return null;
        }

        MAHelper instance = null;
        if (_overGameObject == null)
            instance = Instantiate(prefab);
        else
            instance = Instantiate(prefab, _overGameObject.transform);
        var afc = instance.GetComponent<AlwaysFaceCamera>();
        if(afc)
        {
            var canvas = instance.transform.GetComponentInParent<Canvas>();
            
            if(canvas != null && canvas.renderMode == RenderMode.WorldSpace)
            {
                if(Mathf.Abs(_heightAdjust) > 0)
                {
                    afc.m_pushForward = 0.01f; // Needs a value to allow for the pushdown
                    afc.m_pushDown = -_heightAdjust;
                }
                else
                {
                    afc.m_pushForward = 0f;
                }
                afc.Start();
            }
            else
            {
                afc.enabled = false;
            }
        }
        //instance.transform.localPosition = new Vector3(0, _heightAdjust, 0);
        instance.Activate(_name, _type, _message, _startAfterTimer, _lastForTimer, _arrow, _count);
        return instance;
    }
    public static MAHelper CreateGUI(string _name, string _GUIType, string _type, string _message, float _startAfterTimer, float _xOffset, float _yOffset, string _position, string _arrow)
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAHelper>("_Prefabs/Dialogs/MAHelperGUIV2");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAHelperGUI");
            return null;
        }
        var holder = GetHUDElement(_GUIType);
        var instance = Instantiate(prefab, holder.holder);
        var rt = instance.GetComponent<RectTransform>();
        rt.anchoredPosition = new Vector2(_xOffset, _yOffset);
        instance.ActivateGUI(_name, _type, _message, _startAfterTimer, holder.active, _GUIType, _xOffset, _yOffset, _position, _arrow);
        return instance;
    }
    public static string ReplaceInputTokens(string input)
    {
        // Match anything between angle brackets
        var matches = Regex.Matches(input, @"<([^>]+)>");
        var foundTokens = new HashSet<string>();

        foreach (Match match in matches)
        {
            string fullTag = match.Value;       // e.g. "<LMB>"
            string token = match.Groups[1].Value; // e.g. "LMB"

            //foundTokens.Add(token); // collect if needed separately

            var replace = MAMessageManager.GetTMPSpriteString(token);
            if (replace.IsNullOrWhiteSpace() == false)
            {
                input = input.Replace(fullTag, replace);
            }
            else
            {
                // Optionally: replace with fallback or leave as-is
                input = input.Replace(fullTag, $"[Unknown:{token}]");
            }
        }

        return input;
    }
}
