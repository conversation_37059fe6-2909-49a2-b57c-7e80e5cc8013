using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

[Serializable]
public class MACalenderInfo
{
    public static List<MACalenderInfo> s_calenderList = new();
    public static List<MACalenderInfo> GetList=>s_calenderList;
    public string DebugDisplayName => $"Day: {m_dayNumber:D2} {m_dawnHeading}";
    public string id;
    public bool m_debugChanged;
    public int m_dayNumber;
    public string m_dawnHeading;
    public float m_dayLength;
    public float m_nightLength; 
    public string m_nightType;
    public string m_nightTypeData;
    public string m_nightShortText;
    public string m_nightTitle;
    public string m_nightDescription;
    public string m_nightHint;
    public string m_nightMapSprite;
    public string m_nightReward;
    public int m_numWorshipers;

    public static MACalenderInfo GetInfo(int _daynum) => s_calenderList.Find(o=> o.m_dayNumber == _daynum);

    public static bool PostImport(MACalenderInfo _what)
    {
        return true;
    }

    
    public static List<MACalenderInfo> LoadInfo()
    { 
        s_calenderList  = NGKnack.ImportKnackInto<MACalenderInfo>(PostImport);
        return s_calenderList;
    }
   
}
