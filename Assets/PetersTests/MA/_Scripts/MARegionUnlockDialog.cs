using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MARegionUnlockDialog : Mono<PERSON><PERSON><PERSON><MARegionUnlockDialog>
{
    public TMP_Text m_title;
    public Image m_image;
    public TMP_Text m_descriptiom;
    public GameObject m_rewardTitle;
    public Transform m_rewardHolder;
    public GameObject m_rewardLinePrefab;
    private ReactDistrictTable m_info;

    public void ClickedClose()
    {
        Destroy(gameObject);
    }
    
    public void AddReward(string _what)
    {
        m_rewardTitle.gameObject.SetActive(true);
        var line = Instantiate(m_rewardLinePrefab, m_rewardHolder);
        line.gameObject.SetActive(true);
        line.GetComponent<TMP_Text>().text = _what;
    }
    public void Activte(ReactDistrictTable _districtTable, Sprite _regionimage)
    {
        m_info = _districtTable;
        m_title.text = m_info.m_districtName;
        m_image.sprite = _regionimage;
        m_descriptiom.text = m_info.m_districtFlavourText;
        m_rewardHolder.DestroyChildren();
        m_rewardTitle.SetActive(false);
        if (m_info.m_regionUnlockScript.IsNullOrWhiteSpace() == false)
        {
            MAParserManager.Me.ExecuteSectionInternal($"Flows/RegionUnlocks/RegionUnlockScripts", m_info.m_regionUnlockScript.Trim());
            //MAParserManager.Me.ExecuteSection($"Flows/RegionUnlocks/RegionUnlockScripts/{m_info.m_regionUnlockScript}");
        }
    }
    
    public static MARegionUnlockDialog Create(ReactDistrictTable _districtTable, Sprite _regionimage)
    {
        if (_districtTable.m_regionUnlockScript.IsNullOrWhiteSpace()) return null;
        var prefab = Resources.Load<MARegionUnlockDialog>("_Prefabs/Dialogs/MARegionUnlockDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activte(_districtTable, _regionimage);
        return instance;
    }
}
