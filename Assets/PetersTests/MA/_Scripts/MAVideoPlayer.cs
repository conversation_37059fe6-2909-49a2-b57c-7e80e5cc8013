using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

public class MAVideoPlayer : MonoBehaviour
{
    public VideoPlayer videoPlayer; // Assign in Inspector
    public TMP_Text m_message;

    void Start()
    {
    }

    public void PlayVideo()
    {
        if (videoPlayer != null && videoPlayer.isPlaying)
        {
            videoPlayer.Pause();
        }
        else if (videoPlayer != null && !videoPlayer.isPlaying)
        {
            videoPlayer.Play();
        }
    }

    public void PauseVideo()
    {
        if (videoPlayer != null && videoPlayer.isPlaying)
        {
            videoPlayer.Pause();
        }
    }
 
    public void CloseButton()
    {
        Destroy(gameObject);
    }

    public void StopVideo()
    {
        if (videoPlayer != null)
        {
            videoPlayer.Stop();
        }
    }
    public void LoadAndPlayVideo(string videoName)
    {
        /*var name = $"Videos/{videoName}";
        // Load the VideoClip from Resources
        VideoClip clip = Resources.Load<VideoClip>(name);

        if (clip == null)
        {
            Debug.LogError($"VideoClip 'name' not found in Resources folder!");
            return;
        }*/
        videoPlayer.Stop(); // Stop any currently playing video
        videoPlayer.source = VideoSource.Url;
        videoPlayer.url = System.IO.Path.Combine(Application.streamingAssetsPath, $"Video/{videoName}.mp4");
        //videoPlayer.clip = clip; // Assign the new clip
        videoPlayer.Play(); // Play the new clip
    }
    void Activate(string _video, string _message)
    {
        LoadAndPlayVideo(_video);
        if(_message.IsNullOrWhiteSpace())
            m_message.gameObject.SetActive(false);
        else
            m_message.text = _message;
    }
    public static MAVideoPlayer Create(string _video, string _message)
    {
        var prefab = Resources.Load<MAVideoPlayer>("Videos/MAVideoPlayer");
        if(prefab == null)
        {
            MAParser.ParserError($"No such prefab: MAVideoPlayer");
            return null;
        }
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_video, _message);
        return instance;
    }
}