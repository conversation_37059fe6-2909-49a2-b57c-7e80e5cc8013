using System.Collections.Generic;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MAParserFlowGUI : MonoSingleton<MAParserFlowGUI>
{
    public TMP_Dropdown m_execute;
    public TMP_Dropdown m_skipToDay;
    public TMP_Text m_spawns;
    public Transform m_lineHolder;
    public MAParserFlowGUISection m_sectionPrefab;
    private Color m_defaultColor;
    private void Start()
    {
        var files = MAParserManager.GetAllFlowResources();
        m_lineHolder.DestroyChildren();
        ActivateExecute(files);
        ActivateSkipToDay(files);
        UpdateSpawns();
        m_defaultColor = GetComponent<Image>().color;
    }

    void ActivateExecute(List<string> files)
    {
        m_execute.ClearOptions();
        m_execute.AddOptions(files);
    }

    void ActivateSkipToDay(List<string> files)
    {
        const string directory = "Chapter1V2/Day";

        var days = files.FindAll(o => o.Contains(directory));
        if (days.Count == 0) return;
        days.Insert(0, "Skip To Day...");
        for (var i = 0; i < days.Count; i++)
        {
            days[i] = days[i].Replace(directory, "");
        }

        //days.Insert(0, "Select day to skip to:");
        m_skipToDay.ClearOptions();
        m_skipToDay.AddOptions(days);
    }

    private void Update()
    {
        UpdateSpawns();
        UpdateFlows();
    }
    public void OnPointerEnter(PointerEventData eventData)
    {
        GetComponent<Image>().color = new Color(1f,1f,1f,1f);
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        GetComponent<Image>().color = m_defaultColor;
    }
    
    void UpdateSpawns()
    {
        if (DayNight.Me == null) return;
        var day = DayNight.Me.CurrentWorkingDay;
        var text = "";
        MASpawnByDayInfo.s_creatureSpawnByDayInfos.FindAll(o => o.m_dayNum == day).ForEach(o =>
        {
            var txt = $"{o.m_spawnLocation}:{o.m_creature}[{o.m_spawnTimeSeconds}]:{o.m_count}";
            text += $"{txt}\n";
        });
        m_spawns.text = text;
    }

    List<MAParserFlowGUILine> m_lines = new List<MAParserFlowGUILine>();
    List<MAParserSection> m_activeSections = new List<MAParserSection>();
    void UpdateFlows()
    {
        var sectionAdded = false;
        var sectionRemoved = false;
        var sectionCount = MAParserManager.Me.m_sections.Count;
        foreach (var section in MAParserManager.Me.m_sections)
        {
            if (m_activeSections.Contains(section) == false)
            {
                sectionAdded = true;
            //    m_activeSections.Add(section);
            //    MAParserFlowGUISection.Create(m_sectionPrefab, m_lineHolder, section);
            }
        }

        for (var i = 0; i < m_activeSections.Count; i++)
        {
            var section = m_activeSections[i];
            if(MAParserManager.Me.m_sections.Contains(section) == false)
            {
                sectionRemoved = true;
              //  m_activeSections.RemoveAt(i);
            }
        }

        if (sectionAdded || sectionRemoved)
        {
            m_lineHolder.DestroyChildren();
            m_activeSections.Clear();
            foreach (var section in MAParserManager.Me.m_sections)
            {
                m_activeSections.Add(section);
                MAParserFlowGUISection.Create(m_sectionPrefab, m_lineHolder, section);
            }
        }
    }
    public void ClickedExecute()
    {
        var file = m_execute.options[m_execute.value].text;
        MAParserManager.Me.ExecuteSection(file);
        m_execute.value = 0;
        m_activeSections.Clear();
    }
    public void ClickedSkipToDay()
    {
        var file = m_skipToDay.options[m_skipToDay.value].text;
        if (file == "Skip To Day...") return;
        var selected =m_skipToDay.options[m_skipToDay.value].text;
        var split = selected.Split('_');
        if(int.TryParse(split[0], out var dayNumber))
        {
            MAParserManager.SkipToDay(dayNumber);
        }
        m_skipToDay.value = 0;
        m_activeSections.Clear();
    }
    public static MAParserFlowGUI   Create()
    {
        var prefab = Resources.Load<GameObject>("_Prefabs/Dialogs/MAParserFlowGUI");
        var instance = GameObject.Instantiate(prefab, NGManager.Me.m_screenLocationsHolder.parent.transform);
        return instance.GetComponent<MAParserFlowGUI>();
    }
}

