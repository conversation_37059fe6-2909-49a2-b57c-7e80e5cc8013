using System.Collections.Generic;
using UnityEngine;

public class MASpecialMABuilding : MABuilding
{
    //====
    
    private static Dictionary<string, MASpecialMABuilding> s_specialBuildings = new();
    private static void Register(MASpecialMABuilding _building) => s_specialBuildings[_building.Name.ToLower()] = _building;
    private static void Unregister(MASpecialMABuilding _building) => s_specialBuildings.Remove(_building.Name.ToLower());
    
    public static MASpecialMABuilding Find(string _name)
    {
        if (s_specialBuildings.TryGetValue(_name.ToLower(), out var building))
            return building;
        return null;
    }
    
    public static MASpecialMABuilding Crypt() => Find("TardisCrypt");
    
    //====
    
    public override float HealthNorm
    {
        get => m_stateData.m_healthNorm;
        set
        {
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            if (value < m_stateData.m_healthNorm) // damaged this building
            {
                VignetteManager.Me.AddToVignette(.4f);
                GameManager.Me.TrackBuildingDamage(this);
            }
            VignetteManager.Me.SetCrackLevel(1 - value);
#endif
            m_stateData.m_healthNorm = value;
        }
    }

    override protected void Awake()
    {
        base.Awake();
        Register(this);
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        Unregister(this);
    }

    // Update is called once per frame
    override protected void Update()
    {
        base.Update();
        
        if(HealthNorm <= 0)
        {
            FailSequenceController.Me.StartFailSequence(Transform);
        }
    }
    override public void TryShowContextMenu()
    {
        if (IsWithinOpenDistrict())
            ContextMenuManager.Me.ShowContextMenu(GetContextMenuData(), transform.position, this);
    }
    override protected List<ContextMenuData.ButtonData> GetContextMenuButtonData()
    {
        var buttonData = new List<ContextMenuData.ButtonData>
        {
            new ContextMenuData.ButtonData
            {
                m_label = Localizer.Get(TERM.GUI_INFO),
                m_audioHook = "InfoButton",
                m_onClick = ShowInfoPlaque
            }
        };
        
        if(BuildingComponents<BCActionCrypt>() != null)
        {
            buttonData.Add(new ContextMenuData.ButtonData
            {
                m_label = "Enter",
                m_onClick = IntroControl.Me.ReEnterCrypt,
                m_interactableCallback = () => IsWithinOpenDistrict()
            });
        }
        
        return buttonData;
    }

    protected override void OnBuildingDestroyed(Vector3 _forceDirection)
    {
        TargetObject targetObject = GetComponent<TargetObject>();
        if (targetObject != null)
        {
            foreach (var damageReceiver in targetObject.TargetedBy)
            {
                MACharacterBase character = damageReceiver as MACharacterBase;
                if (character != null &&
                    character.CanReachTargetWithAttack(character.CurrentComboState.GetNextAttack(), out var targetPos))
                {
                    if (m_componentsDict.TryGetValue(typeof(BCEntrance), out List<BCBase> outComps) && outComps.Count > 0)
                    {
                        character.Home = outComps[0];
                    }
                    MACharacterStateFactory.ApplyCharacterState(CharacterStates.GoingHome, character);
                }
            }
        }
    }
}
