using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class MAWorshipper : MATourist
{
    public enum WorshipperState
    {
        Idle,
        MoveToWorship,
        MoveToWorshipPosition,
        WorshipWaitForDrop,
        WorshipWaitForEnd,
        DecideWhatToDo,
        ReturnToDespawn,
        VisitBuilding,
        VisitDecoration,
    }
    public MAWorkerInfo m_info;
    public WorshipperState m_worshipperState;
    private float m_worshipTimer = 0f;
    public float m_worshipForBeforeDropTime = 5f;
    public float m_worshipHangOutAtWorshipTime = 2f;
    public Vector3 m_despawnPos;
    public class WorshipperDecisions
    {
        public enum DecisionType
        {
            VisitBuilding,
            VisitDecoration,
        }
		
        public DecisionType m_decisionType;
        public string m_lookFor;
        public string m_animation;
    }
    public int m_worshipperDecisionSessions;
    public WorshipperDecisions m_currentWorshipperDecision;

    public List<WorshipperDecisions> m_worshipperDecisions = new List<WorshipperDecisions>()
    {
        new WorshipperDecisions() { m_decisionType = WorshipperDecisions.DecisionType.VisitBuilding, m_lookFor = "ActionHouse", m_animation = "WorkerBrushOff" },
        new WorshipperDecisions() { m_decisionType = WorshipperDecisions.DecisionType.VisitDecoration, m_lookFor = "", m_animation = "" },

    };	
    public List<string> m_worshipperAnimationStringsToPlay = new List<string>();

    override protected void Update()
    {
        UpdateState();
        UpdateKinematic();
        UpdateVisuals();
    }
    override protected void UpdateState()
    {
        switch (m_worshipperState)
        {
            case WorshipperState.Idle:
                break;
            case WorshipperState.MoveToWorship:
                if(StateMoveToPosition())
                {
                    m_worshipperState = WorshipperState.MoveToWorshipPosition;
                    var pos = MAWorshipperManager.Me.GetWorshipPosition(this);
                    SetMoveToPosition(pos);
                }
                break;
            case WorshipperState.MoveToWorshipPosition:
                if(StateMoveToPosition())
                {
                    m_worshipperState = WorshipperState.WorshipWaitForDrop;
                    m_worshipTimer = Time.time + m_worshipForBeforeDropTime;
                }
                break;
            case WorshipperState.WorshipWaitForDrop:
                if(Time.time < m_worshipTimer) break;
                MAManaBall.Create(transform, m_info);
                m_worshipperState = WorshipperState.WorshipWaitForEnd;
                m_worshipTimer = Time.time + m_worshipHangOutAtWorshipTime;
                break;
            case WorshipperState.WorshipWaitForEnd:
                if(Time.time < m_worshipTimer) break;
                m_worshipperState = WorshipperState.DecideWhatToDo;
                MAWorshipperManager.Me.WorshipperStoppedWorshipping(this);
                break;
            case WorshipperState.DecideWhatToDo:
                WorshipperDecideWhatToDo();
                break;
            case WorshipperState.ReturnToDespawn:
                if (StateMoveToPosition())
                {
                    DestroyMe();
                }

                break;
            case WorshipperState.VisitBuilding:
                if (StateMoveToPosition())
                {
                    WorshipperDecideWhatToDo();
                }
                break;
            case WorshipperState.VisitDecoration:
                if (StateMoveToPosition())
                {
                    WorshipperDecideWhatToDo();
                }
                break;
            default:
                base.UpdateState();
                break;
        }
    }
    override public void DestroyMe()
    {
        NGManager.Me.m_MAHumanList.Remove(this);
        NGManager.Me.m_MACharacterList.Remove(this);
        NGManager.Me.m_MAWorshipperList.Remove(this);
        MAWorshipperManager.Me.WorshipperStoppedWorshipping(this);
        base.DestroyMe();
    }
    void WorshipperDecideWhatToDo()
    {
        if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
            return;
    	    
        if (m_worshipperDecisionSessions <= 0)
        {
            SetMoveToPosition(m_despawnPos, false, PeepActions.Idle);
            m_worshipperState = WorshipperState.ReturnToDespawn;
            return;
        }

        var randChoice = Random.Range(0, m_worshipperDecisions.Count);
        m_currentWorshipperDecision = m_worshipperDecisions[randChoice];
        switch (m_currentWorshipperDecision.m_decisionType)
        {
            case WorshipperDecisions.DecisionType.VisitBuilding:
                SetupWorshipperVisitBuilding();
                break;
            case WorshipperDecisions.DecisionType.VisitDecoration:
                SetupWorshipperVisitDecoration();
                break;
        }
    }    
    void SetupWorshipperVisitBuilding()
    {
        if (m_currentWorshipperDecision.m_lookFor.IsNullOrWhiteSpace())
        {
            m_worshipperDecisionSessions--;
            return;
        }
        MABuilding bestBuilding = null;
        var bestDistance = float.MaxValue;
		
        var cInfo = MAComponentInfo.GetInfo(m_currentWorshipperDecision.m_lookFor);
        var i = 0;
        for(int j = Random.Range(0, NGManager.Me.m_maBuildings.Count); i < NGManager.Me.m_maBuildings.Count; i++, j++)
        {
            if (j >= NGManager.Me.m_maBuildings.Count)
                j = 0;
            var building = NGManager.Me.m_maBuildings[j];
            if (building.HasBuildingComponent(cInfo.m_classType))
            {
                var distance = Vector3.Distance(transform.position, building.transform.position);
                if (distance < bestDistance)
                {
                    bestDistance = distance;
                    bestBuilding = building;
                    if(Random.value < 0.2f)
                        break;
                }
            }
        }
        if (bestBuilding == null)
            return;
        SetMoveToPosition(bestBuilding.DoorPosOuter, false, PeepActions.Working);
        m_worshipperState = WorshipperState.VisitBuilding;
        m_worshipperDecisionSessions--;
    }
    bool SetupWorshipperVisitDecoration()
    {
        if (MATouristManager.Me.m_decorations.Count == 0) return false;
        var bestDecoration = MATouristManager.Me.m_decorations[Random.Range(0, MATouristManager.Me.m_decorations.Count)]; 
        var info = NGDecorationInfoManager.NGDecorationInfo.GetInfo(bestDecoration.Name);
        if (info == null) return false;
        ParseAnimString(info.m_touristAnim);
        var pos = GetPositionOnCircle(bestDecoration.transform.position, 1, Random.Range(0, 360));
        SetMoveToPosition(bestDecoration.transform.position, false, PeepActions.Working);
        m_worshipperState = WorshipperState.VisitDecoration;
        m_worshipperDecisionSessions--;
        return true;
    }
    void ParseAnimString(string _animString)
    {
        m_animationStringsToPlay.Clear();
        if (_animString.IsNullOrWhiteSpace()) return;
        var anims = _animString.Split('\n',',');
        foreach(var a in anims)
        {
            var choice = a.Split('|');
            var choiceIndex = 0;
            if(choice.Length > 1)
            {
                choiceIndex = Random.Range(0, choice.Length);
            }
            m_animationStringsToPlay.Add(choice[choiceIndex]);
        }
    }
    public void SetMoveToWorkship(Vector3 _position)
    {
        m_worshipperState = WorshipperState.MoveToWorship;
        SetMoveToPosition(_position);
    }
    public void ActivateWorshipper(MAWorkerInfo _info)
    {
        m_info = _info;
        m_worshipperDecisionSessions = Random.Range(0, 3);
        SetSpeed(_info.GetRandomWorkerSpeed());
    }

    override public void UpdateComponentReferences()
    {
        Debug.Log("UpdateComponentReferences");
    }

    public override void PostLoad()
    {
        enabled = true;
        
        var holder = GlobalData.Me.m_characterHolder.Find("Worshippers");
        if(holder == null) holder = GlobalData.Me.m_characterHolder;
        transform.SetParent(holder, true);
        base.PostLoad();
    }
    public static MAWorshipper Create(MAWorkerInfo _info, Transform _holder, Vector3 _pos)
    {
        var worshipper = MAWorker.Create(_info, _pos) as MAWorshipper;
        worshipper.transform.SetParent(_holder, true);
        worshipper.m_despawnPos = _pos;
        _pos = _pos.GroundPosition();
        worshipper.ActivateWorshipper(_info);
        return worshipper;
    }
}
