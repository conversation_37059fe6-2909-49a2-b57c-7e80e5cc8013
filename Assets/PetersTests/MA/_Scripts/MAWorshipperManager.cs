using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class MAWorshipperManager : Mono<PERSON>ingleton<MAWorshipperManager>
{
    public class WorshipPosition
    {
        public Transform m_position;
        public MAWorshipper m_worshipper = null;
    }
    public List<MAWorshipper> m_allWorshippers = new List<MAWorshipper>();
    public Dictionary<string, List<MAWorkerInfo>> m_worshipperInfos = new ();
    public MARoadPoint m_spawnPoint;
    public MARoadPoint m_despawnPoint;
    public Vector3 m_worshipEntryPosition; // Position where worshippers will move to worship
    public float m_worshipRadius = 5f; // Radius around the worship position where worshippers can gather
    public Transform m_worshipperHolder;
    public MAMovingInfoBase m_dropInfo;
    public enum StateType
    {
        WaitingToSpawn,
        Spawning,
        MovingToWorship,
        Worshipping,
        BeingTourists,
        MovingToDespawn,
    }
    public StateType m_state = StateType.WaitingToSpawn;
    public float m_spawnTimeAfterDayStart = 5f; // Time after day start to spawn worshippers
    public int m_numToSpawn;
    public float m_distanceFromCrypt = 10f; 
    public float m_spawnTime = 1.5f; // Time between each worshipper spawn
    public string m_spawnTOD = "10.00"; // Time of day to start spawning worshippers
    private float m_spawnTimer;
    private List<WorshipPosition> m_worshipPositions = new();
    void Start()
    {
        StartCoroutine(WaitForActivate());
    }
    private System.Collections.IEnumerator WaitForActivate()
    {
        yield return new WaitUntil(() => GameManager.Me != null && GameManager.Me.LoadComplete);
        m_worshipperHolder = GlobalData.Me.m_characterHolder.Find("Worshippers");
        if (m_worshipperHolder == null) m_worshipperHolder = GlobalData.Me.m_characterHolder;
        
        ActivateWorshipInfo();
        ActivateWorshipPositions();
        m_state = StateType.WaitingToSpawn;
    }
    void ActivateWorshipInfo()
    {
        m_worshipperInfos.Clear();
        var worshippers = MAWorkerInfo.GetInfoByType("Worshipper");
        foreach (var worshipper in worshippers)
        {
            if (m_worshipperInfos.ContainsKey(worshipper.m_subType))
                m_worshipperInfos[worshipper.m_subType].Add(worshipper);
            else
                m_worshipperInfos.Add(worshipper.m_subType, new List<MAWorkerInfo>() { worshipper });
        }
    }
    void ActivateWorshipPositions()
    {
        m_worshipPositions.Clear();
        m_spawnPoint = NGManager.Me.m_moaRoadStart;
        m_despawnPoint = NGManager.Me.m_moaRoadEnd;
        
        var crypt = MASpecialMABuilding.Crypt();
        var holder = crypt.transform.Find("Holder/WorshipperEntryPoint");
        if (holder)
        {
            m_worshipEntryPosition = holder.position;
            foreach (Transform t in holder)
            {
                if (t.name.Contains("Position"))
                    m_worshipPositions.Add(new WorshipPosition() { m_position = t.transform });
            }
        }
        else
        {
            var fwd = (crypt.DoorPosInner - crypt.transform.position).normalized;
            Vector3 p0 = crypt.DoorPosInner + fwd * m_distanceFromCrypt;
            m_worshipEntryPosition = p0.GroundPosition();
        }
    }
    bool StateSpawnWorshippers()
    {
        var calenderInfo = MACalenderInfo.GetInfo(DayNight.Me.CurrentWorkingDay);
        if (calenderInfo == null)
        {
            Debug.LogError("No calendar info found for day: " + DayNight.Me.CurrentWorkingDay);
            return false;
        }
        m_numToSpawn = calenderInfo.m_numWorshipers;
        //m_numToSpawn = 10; // For testing purposes, spawn 10 worshippers
        if(m_numToSpawn <= 0) return false;
        m_state = StateType.Spawning;
        m_spawnTimer= 0f;

        m_numToSpawn = Mathf.Max(m_numToSpawn - NGManager.Me.m_MAWorshipperList.Count, 0);
       return true; 
    }

    bool StateSpawning()
    {
        if (m_numToSpawn <= 0)
        {
            m_state = StateType.Worshipping;
            return false;
        }
        if (Time.time < m_spawnTimer) return false;
        if (MAParser.WaitForTimeOfDay(m_spawnTOD) == false) return false;
        Vector3 destPos;

/*
        var rp = Random.insideUnitCircle * m_worshipRadius;
        destPos = m_worshipEntryPosition + new Vector3(rp.x, 0, rp.y);
        destPos = destPos.GroundPosition();
        if (GlobalData.Me.IsNavigableAtPoint(destPos) == false) return false;
*/
        var worshipper = SpawnWorshipper();
        worshipper.SetMoveToPosition(m_worshipEntryPosition);
        worshipper.m_worshipperState = MAWorshipper.WorshipperState.MoveToWorship;
        m_numToSpawn--;
        if (m_numToSpawn <= 0)
        {
            m_state = StateType.MovingToWorship;
        };
        m_spawnTimer= m_spawnTime + Time.time;
        return true;
    }

    void Update()
    {
        if (GameManager.Me.LoadComplete == false) return;
        switch (m_state)
        {
            case StateType.WaitingToSpawn:
                StateSpawnWorshippers();
                break;
            case StateType.Spawning:
                StateSpawning();
                break;
            case StateType.Worshipping:
                StateWorshipping();
                break;
        }
    }
    void StateWorshipping()
    {
        // Check if all worshippers have reached their worship positions
        bool allWorshippersReady = true;
        foreach (var w in NGManager.Me.m_MAWorshipperList)
        {
            var worshipper = w as MAWorshipper;
            if (worshipper == null) continue;
            if(worshipper.m_worshipperState != MAWorshipper.WorshipperState.WorshipWaitForEnd)
            {
                allWorshippersReady = false;
                break;
            }
        }
        
        if (allWorshippersReady == false) return;
        foreach (var w in NGManager.Me.m_MAWorshipperList)
        {
            var worshipper = w as MAWorshipper;
            if (worshipper == null) continue;
            worshipper.m_worshipperState = MAWorshipper.WorshipperState.DecideWhatToDo;
        }
    }

    public Vector3 GetWorshipPosition(MAWorshipper _worshiper)
    {
        foreach (var wp in m_worshipPositions)
        {
            if (wp.m_worshipper == null)
            {
                wp.m_worshipper = _worshiper;
                return wp.m_position.position;
            }
        }
        return m_worshipPositions[Random.Range(0, m_worshipPositions.Count)].m_position.position;
    }

    public void WorshipperStoppedWorshipping(MAWorshipper _worshiper)
    {
        var wp = m_worshipPositions.Find(w => w.m_worshipper == _worshiper);
        if (wp != null)
        {
            wp.m_worshipper = null;
        }
    }
    MAWorshipper SpawnWorshipper()
    {
        var a = AlignmentManager.Me.Alignment;
        string alighmentToSubType;
        if(AlignmentManager.Me.IsEvil) alighmentToSubType = "Evil";
        else if(AlignmentManager.Me.IsNeutral) alighmentToSubType = "Neutral";
        else alighmentToSubType = "Good";
        var infos = m_worshipperInfos[alighmentToSubType];
        var info = infos[Random.Range(0, infos.Count)];
        var worshipper = MAWorshipper.Create(info, m_worshipperHolder, m_spawnPoint.transform.position);
        
        return worshipper;
    }
}
