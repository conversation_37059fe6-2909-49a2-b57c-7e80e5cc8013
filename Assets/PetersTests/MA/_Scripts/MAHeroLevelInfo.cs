using System;
using System.Collections.Generic;
using UnityEngine;
[Serializable]
public class MAHeroLevelInfo
{
    public static List<MAHeroLevelInfo> s_heroLevelInfoList = new();
    public static List<MAHeroLevelInfo> GetList=>s_heroLevelInfoList;
    public string DebugDisplayName => $"{m_heroName}:{m_level}";
    public bool m_debugChanged;
    public string id;

    public string m_indexer;
    public string m_heroName;
    public string m_index;
    public int m_level;
    public int m_experienceRequired;
    public string m_reward1;
    public string m_reward2;
    public string m_reward3;
    public string m_levelDescription;
    
    public static MAHeroLevelInfo GetInfo(string _name) => s_heroLevelInfoList.Find(o=> o.m_indexer == _name);
    public static List<MAHeroLevelInfo> GetInfoList(string _name) => s_heroLevelInfoList.FindAll(o=> o.m_heroName == _name);

    public static bool PostImport(MAHeroLevelInfo _what)
    {
        return true;
    }

    
    public static List<MAHeroLevelInfo> LoadInfo()
    { 
        s_heroLevelInfoList  = NGKnack.ImportKnackInto<MAHeroLevelInfo>(PostImport);
        return s_heroLevelInfoList;
    }
}
