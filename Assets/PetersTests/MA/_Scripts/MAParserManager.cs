using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.Serialization;

using Cans.Analytics;

public class MAParserManager : MonoSingleton<MAParserManager>
{
    [System.Serializable] public class ParserMemory
    {
        public string m_key;
        public string m_value;
    }
    public static string c_fileWritePath => Application.dataPath + "/Resources/";
    public const string c_parserPath = "Flows/";
    public static bool m_pauseAllFlows = false;
    public string m_startOfGameSection = "Chapter1/SetupChapter1";
    public List<MAParserSection> m_sections = new();
    public static MAParserSection m_updatingMaParserSection = null;
    public List<MAGodBeam> m_godBeams = new();
    public string m_nightFailSectionName = "Chapter1V2/NightFail";
    public List<ParserMemory> m_memories = new();
    [System.Serializable] public class ParserCommand
    {
        public string m_functionCommand;
        
        public ParserCommand(string _functionCommand)
        {
            m_functionCommand = _functionCommand.Trim();
        }
        
        public void Execute()
        {
            MAParserSupport.TryParse(m_functionCommand, out var result);
        }
    }
    

    public static MAFeedbackConditions GetCurrentFeedbackConditions()
    {
        foreach(var s in Me.m_sections)
        {   
            if(s.m_currentFeedbackConditions != null)
            {
                return s.m_currentFeedbackConditions;
            }
        }
        return null;
    }
    public static MAMessage CurrentMessage
    {
        get=> m_updatingMaParserSection.m_currentMessage;
        set=> m_updatingMaParserSection.m_currentMessage = value;
    }
    public static bool CurrentDisplayingMessage
    {
        get  => m_updatingMaParserSection.m_currentDisplayingMessage;
        set => m_updatingMaParserSection.m_currentDisplayingMessage = value;
    }
    public static NGBusinessDecision CurrentDecision
    {
        get => m_updatingMaParserSection.m_currentDecision;
        set => m_updatingMaParserSection.m_currentDecision = value;
    }
    public static MAInspection CurrentInspection
    {
        get => m_updatingMaParserSection.m_currentInspection;
        set => m_updatingMaParserSection.m_currentInspection = value;
    }

    public static MAGameFlowDialog CurrentDialog
    {
        get=> m_updatingMaParserSection.m_currentDialog;
        set=> m_updatingMaParserSection.m_currentDialog = value;
    }
    public static MAParserSection CurrentCall
    {
        get=> m_updatingMaParserSection.m_currentCall;
        set=> m_updatingMaParserSection.m_currentCall = value;
    }

    public static float CurrentParserTimer
    {
        get => m_updatingMaParserSection.m_currentParserTimer;
        set => m_updatingMaParserSection.m_currentParserTimer = value;
    }

    public static MAQuestCreateScroll CurrentQuestScroll
    {
        get => (m_updatingMaParserSection != null) ? m_updatingMaParserSection.m_currentQuestScroll : null;
        set
        {
            if(m_updatingMaParserSection != null) m_updatingMaParserSection.m_currentQuestScroll = value;
        }
    }
    public static MAQuestBase CurrentQuest
    {
        get
        {
            if(m_updatingMaParserSection == null)
            {
                return null;
            }

            //KW: try to call SetCurrentQuest if m_currentQuest is null
            if (m_updatingMaParserSection.m_currentQuest == null)
            {
                bool foundSetCurrentQuest = false;
                
                foreach (var line in m_updatingMaParserSection.m_lines)
                {
                    if (line.m_line.Contains("SetCurrentQuest"))
                    {
                        foundSetCurrentQuest = true;

                        if(!MAParserSupport.TryParse(line.m_line, out var result))
                        {
                            Debug.LogError("m_currentQuest is null and failed to parse SetCurrentQuest");
                        }

                        break;
                    }
                }

                if(!foundSetCurrentQuest)
                {
                    Debug.LogWarning("m_currentQuest is null and could not find SetCurrentQuest");
                }
            }

            return m_updatingMaParserSection.m_currentQuest;
        }

        set => m_updatingMaParserSection.m_currentQuest = value;
    }

    /*public static List<string> CurrentGifts
    {
        get => m_updatingMaParserSection.m_currentGifts;
        set => m_updatingMaParserSection.m_currentGifts = value;
    }*/

    public static MAQuestTrigger CurrentQuestTrigger
    {
        get => m_updatingMaParserSection.m_currentQuestTrigger;
        set => m_updatingMaParserSection.m_currentQuestTrigger = value;
    }
    public static string CurrentData
    {
        get => m_updatingMaParserSection.m_currentData;
        set => m_updatingMaParserSection.m_currentData = value;
    }
    public static List<(string id, float value)> GetCurrentTrackValues
    {
        get => m_updatingMaParserSection.m_currentTrackFloat;
    }
    public static float GetOrSetTrackValue(float _value)
    {
        var id = $"{m_updatingMaParserSection.m_name}|{m_updatingMaParserSection.m_currentLine}";
        var f = m_updatingMaParserSection.m_currentTrackFloat.Find(x => x.id == id);
        if (f.id == null)
        {
            m_updatingMaParserSection.m_currentTrackFloat.Add((id, _value));
            return _value;
        }
        return f.value;
    }
    public MAParserSection GetSection(string _section)
    {
        foreach(var s in m_sections)
        {
            if(s.m_name == _section)
                return s;
        }
        return null;
    }
    public static void RemoveTrackedValue()
    {
        var id = $"{m_updatingMaParserSection.m_name}|{m_updatingMaParserSection.m_currentLine}";
        m_updatingMaParserSection.m_currentTrackFloat.RemoveAll(x => x.id == id);
    }
    void Update()
    {
        if (GameManager.Me == null || GameManager.Me.LoadCompletePreFadeIn == false) return;
        if(m_pauseAllFlows) return;
        for (var i = 0; i < m_sections.Count; i++)
        {
            var s = m_sections[i];
            m_updatingMaParserSection = s;
            UpdateSection(s);
            m_updatingMaParserSection = null;
        }
    }

    public bool NextLine(MAParserSection _maParserSection)
    {
        AnalyticsEvent flowStepCompletedEvent = AnalyticsManager.Me.Events.FlowStepCompleted;
        flowStepCompletedEvent.AddParameter(EventParams.flowStepFile, _maParserSection.m_name);
        flowStepCompletedEvent.AddParameter(EventParams.flowStepLine, _maParserSection.m_currentLine);
        flowStepCompletedEvent.AddParameter(EventParams.timeActive, GameManager.Me.m_state.m_gameTime.m_secondsActive);
        AnalyticsManager.Me.LogEvent(flowStepCompletedEvent);

        _maParserSection.m_currentLine++;
        _maParserSection.m_currentFeedbackConditions = null;
        if(_maParserSection.m_currentLine >= _maParserSection.Lines.Count)
        {
            m_sections.Remove(_maParserSection);
            return false;
        }

        return true;
    }
    public void UpdateSection(MAParserSection _maParserSection)
    {
        var line = _maParserSection.Lines[_maParserSection.m_currentLine].m_line;
        if (line == "" || line.StartsWith('{') || line.StartsWith('}'))
        {
            if(NextLine(_maParserSection) == false)
                return;
            line = _maParserSection.Lines[_maParserSection.m_currentLine].m_line;
        }
        if (_maParserSection.m_isStepping)
        {
            if (_maParserSection.m_nextStep == false)
                return;
            _maParserSection.m_nextStep = false; 
        } 
        if(MAParserSupport.TryParse(line, out var result, $"MAParserFile[{_maParserSection.m_name}]:{_maParserSection.Lines[_maParserSection.m_currentLine].m_lineNumber}>{_maParserSection.Lines[_maParserSection.m_currentLine].m_line}") == false)
        {
            //Debug.LogError(MAParserSupport.DebugColor($"MAParserFile[{_section.m_name}]:{_section.m_currentLine} failed to parse: {line}"));
        }
        
        _maParserSection.UpdateDescisionState();
        _maParserSection.UpdateChecks();
        if(result == null || result)
        {
            NextLine(_maParserSection);
        }
    }

    public bool IsSectionActive(string _section)
    {
        var name = _section;
        if(_section.Contains('/'))
        {
            var pathSplit = _section.Split('/');
            name = pathSplit[pathSplit.Length - 1];
        }
        foreach(var s in m_sections)
        {
            if(s.m_name == name)
                return true;
        }
        return false;
    }
    public ParserCommand SaveFlowStep(string _tag, string _functionCommand)
    {
        if(GameManager.Me.m_state.m_parserCommands.ContainsKey(_tag))
        {
            Debug.LogError($"Trying to save step that already exists Tag: {_tag} FunctionCommand: {_functionCommand}");
            return null;
        }
        var pc = new ParserCommand (_functionCommand);
        GameManager.Me.m_state.m_parserCommands[_tag] = pc;
        if (m_updatingMaParserSection != null)
        {
            m_updatingMaParserSection.m_currentSavedTags.Add(_tag);
        }
        return pc;
    }
    
    public void RemoveFlowStep(string _tag)
    {
        if(GameManager.Me.m_state.m_parserCommands.ContainsKey(_tag) == false)
            return;
        GameManager.Me.m_state.m_parserCommands.RemoveKey(_tag);

        if(m_updatingMaParserSection != null && m_updatingMaParserSection.m_currentSavedTags.Contains(_tag))
        {
            m_updatingMaParserSection.m_currentSavedTags.Remove(_tag);
        }
    }
    
    public MAParserSection FindByFileName(string _fileName)
    {
        foreach(var s in m_sections)
        {
            if(s.m_fileName == _fileName)
                return s;
        }
        return null;
    }
    
    public MAParserSection FindByName(string _name)
    {
        foreach(var s in m_sections)
        {
            if(s.m_name == _name)
                return s;
        }
        return null;
    }
    
    public static (string fileName, string sectionName) GetSectionFileName(string _section)
    {
        var pathSplit = _section.Split('/');
        var sectionName = "";
        var path = "";
        for(int i = 0; i < pathSplit.Length; i++)
        {
            if(i == pathSplit.Length - 1)
            {
                sectionName = pathSplit[i];
                break;
            }
            path += $"{pathSplit[i]}/";
        }

        var fileName = "";
        if (path.Contains(c_parserPath))
            fileName = $"{path}{sectionName}";
        else
            fileName = $"{c_parserPath}{path}{sectionName}";
        return (fileName, sectionName);
    }

    public MAParserSection ExecuteSectionInSection(string _section, MAParserSection _execuiteSection)
    {
        var oldSection = m_updatingMaParserSection;
        m_updatingMaParserSection = _execuiteSection;
        var ret = ExecuteSection(_section);
        m_updatingMaParserSection = oldSection;
        return ret;
    }

    public MAParserSection ExecuteSection(string _section, int _fromLine = 0, MAParserSection _fromSave = null, bool _addToSectionList = true)
    {
        var name = GetSectionFileName(_section);

        if (_fromSave != null)
        {
            name = (_fromSave.m_fileName, _fromSave.m_name);
        }
        else if (_section.Contains('/') == false && m_updatingMaParserSection != null)
        {
            name = new(m_updatingMaParserSection.m_fileName, _section);
        }
        return ExecuteSectionInternal(name.fileName, name.sectionName,_fromLine, _fromSave, _addToSectionList);
    }

    public MAParserSection  ExecuteSectionInternal(string _filename, string _sectionName, int _fromLine = 0, MAParserSection _fromSave = null, bool _addToSectionList = true)
    { 
        var textAsset = Resources.Load<TextAsset>(_filename);
        if(textAsset == null)
        {
            Debug.LogError(MAParserSupport.DebugColor($"Flow File Resources/{_filename} not found"));
            return null;
        }
        var lines = textAsset.text.Split(new[] { "\r\n", "\r", "\n" }, System.StringSplitOptions.None);
        //var lines = RegexSplit(textAsset.text);
        int section = -1;
        for (var i = 0; i < lines.Length; i++)
        {
            var line = lines[i].Trim();
            if (line.ToLower().StartsWith("section") && line.EndsWith(_sectionName))
            {
                section = i;
                break;
            }
        }

        if (section == -1)
        {
            Debug.LogError(MAParserSupport.DebugColor($"Section {_sectionName} not found in file {_filename}"));
            return null;
        }

        var newSection = new MAParserSection(){m_name = _sectionName, m_fileName = _filename};
        if(_fromSave != null)
        {
            newSection.m_decisionState = _fromSave.m_decisionState;
        }
        
        var braceCount = 0;
        for (int i = section; i < lines.Length; i++)
        {
            var line = lines[i].Trim();
            if (line.StartsWith("{"))
            {
                braceCount++;
                if(braceCount == 1)
                    continue;
            }
            if (line.StartsWith("//"))
                continue;
            if (line.IsNullOrWhiteSpace())
                continue;
            if (line.StartsWith("section"))
                continue;
            if (line.StartsWith("}"))
            {
                braceCount--;
                if(braceCount == 0)
                    break;
            }
            newSection.Lines.Add(new MAParserSection.SectionLine(i, line));
        }

        if (newSection.Lines.Count <= 0)
        {
            Debug.LogError(MAParserSupport.DebugColor($"Section {_sectionName} no }} found in file {_filename}"));
            return null;            
        }
        if(newSection.Lines.Count <= _fromLine)
        {
            Debug.LogError(MAParserSupport.DebugColor($"Section {_sectionName} no line {_fromLine} found in file {_filename} {_sectionName}"));
            _fromLine = 0;
        }
        newSection.m_currentLine = _fromLine;
        if(_addToSectionList)
            m_sections.Add(newSection);
        return newSection;
    }
    public static List<string> RegexSplit(string _text)
    {

        // Regular expression to match lines while ignoring newlines within quotes
        var pattern = @"(?<=^|[\n\r])(([^""\n\r]|""([^""\\]*(?:\\.[^""\\]*)*)"")*)";
        var matches = Regex.Matches(_text, pattern);

        // Collect the split lines
        var lines = new List<string>();
        foreach (Match match in matches)
        {
            if (!string.IsNullOrEmpty(match.Value.Trim()))
            {
                lines.Add(match.Value.Trim());
            }
        }

        return lines;
    }
    public static void StartOfGame()
    {
        DayNight.Me.RegisterRestartDayCallback(DayNighFailCallback);

        Me.ExecuteSection(Me.m_startOfGameSection);
    }

    private const string DesignTableReactionsSectionName = "Chapter1V2/DesignTableReactions";
    public void KillDesignTableReactions()
    {
        List<MAParserSection> sections = new();
        
        foreach(var section in m_sections)
        {
            if(section.m_fileName.EndsWith(DesignTableReactionsSectionName))
            {
                sections.Add(section);
            }
        }
        
        foreach(var section in sections)
        {
            section.ClearCurrents();
            MAParserManager.Me.m_sections.Remove(section);
        }
    }
    
    public void ExecuteDesignTableReactions()
    {
        KillDesignTableReactions();
        ExecuteSection(DesignTableReactionsSectionName);
    }
    
    static void DayNighFailCallback()
    {
        if (Me.m_nightFailSectionName.IsNullOrWhiteSpace()) return;
        Me.ExecuteSection(Me.m_nightFailSectionName);
    }
    static public void ChangeLine(MAParserSection _maParserSection, MAParserSection.SectionLine _line, string _changedString)
    {
#if UNITY_EDITOR
        var lines = File.ReadAllLines($"{c_fileWritePath}{_maParserSection.m_fileName}.MOA");
        lines[_line.m_lineNumber] = $"\t\t{_changedString}";
        //File.WriteAllLines($"{Me.m_fileWritePath}{_section.m_fileName}.MOA", lines);
#endif
    }

    public static string ConvertFlowToText(Dictionary<string, List<NGBusinessFlow>> s_flowsDict)
    {
        StringBuilder sb = new StringBuilder();

        foreach (var section in s_flowsDict)
        {
            sb.Append($"section\t{section.Key}()\n{{\n");
            foreach (var flow in section.Value)
            {
                foreach (var c1 in flow.m_enterWaitForTrigger.Split('\n', '|', ';'))
                {
                    if (c1.IsNullOrWhiteSpace() == false)
                        sb.Append($"\t\tWaitForTrigger({c1})\n");
                }

                foreach (var c1 in flow.m_enterFunctions.Split('\n', '|', ';'))
                {
                    if (c1.IsNullOrWhiteSpace() == false)
                        sb.Append($"\t\t{c1}\n");
                }

                if (flow.m_message.IsNullOrWhiteSpace() == false)
                {
                    var message = flow.m_message.Trim('"');
                    sb.Append($"\t\tMessage({flow.m_businessAdvisor}, {flow.m_messageType}, {flow.m_staticPose}, \"{message}\", {flow.m_audioID})\n");
                }
                foreach (var c1 in flow.m_businessDecision.Split('\n', '|', ';'))
                {
                    if (c1.IsNullOrWhiteSpace() == false)
                        sb.Append($"\t\tDecision({flow.m_businessAdvisor}, {c1})\n");
                }
                foreach(var c1 in flow.m_takeAllGifts.Split('\n', '|', ';'))
                {
                    if (c1.IsNullOrWhiteSpace() == false)
                        sb.Append($"\t\tTakeGift({c1})\n");
                }
                foreach(var c1 in flow.m_chooseGifts.Split('\n', '|', ';'))
                {
                    if (c1.IsNullOrWhiteSpace() == false)
                        sb.Append($"\t\tChooseGift({c1}, {flow.m_chooseMaxGifts})\n");
                }
                foreach (var c1 in flow.m_exitFunctions.Split('\n', '|', ';'))
                {
                    if (c1.IsNullOrWhiteSpace() == false)
                        sb.Append($"\t\t{c1}\n");
                }

    
            }

            sb.Append("}\n");
        }
#if UNITY_EDITOR
        File.WriteAllText($"{c_fileWritePath}{c_parserPath}ConvertFlowToText.MOA", sb.ToString());
#endif
        return sb.ToString();
    }

    private static string[] flowFolders = {"Chapter1V2", "Quests", "Helpers"};

    public static List<string> GetAllFlowResources()
    {
        var flowFiles = new List<string>();
        foreach (var folder in flowFolders)
        {
            var fullFolder = $"{c_parserPath}{folder}/";
            var flows = Resources.LoadAll<TextAsset>(fullFolder);
            foreach (var flow in flows)
                flowFiles.Add($"{folder}/{flow.name}");
        }
        return flowFiles;
    }

    public static List<string> GetAllFlowFiles()
    {
        string folderPath = $"{c_fileWritePath}{c_parserPath}".TrimEnd('/');
        var flowFiles = new List<string>();
        flowFiles.Add("Select a flow file");
        
        if (Directory.Exists(folderPath))
        {
            // Get all .MOA files in the directory and subdirectories
            var files = Directory.GetFiles(folderPath, "*.MOA", SearchOption.AllDirectories);

            foreach (var file in files)
            {
                // Get the relative path from the Flows folder and remove the .MOA extension
                string relativePath = Path.GetRelativePath(folderPath, file);
                string fileWithoutExtension = Path.Combine(
                    Path.GetDirectoryName(relativePath), 
                    Path.GetFileNameWithoutExtension(relativePath)
                );

                // Add the result to the list
                flowFiles.Add(fileWithoutExtension.Replace("\\", "/"));
            }
        }
        else
        {
            Console.WriteLine($"Directory '{folderPath}' not found.");
        }

        return flowFiles;
    }

    public static void WriteDialogToFile(string _outputType, string _advisor)
    {
        var dialogDict = new Dictionary<string, List<string>>();
        var allFiles = GetAllFlowFiles();
        foreach(var file in allFiles)
        {
            if(file.StartsWith("Old") || file.Contains("/") == false) continue;
            var name =GetSectionFileName(file);
            var textAsset = Resources.Load<TextAsset>(name.fileName);
            if(textAsset == null)
            {
                continue;
            }
            //Message(Keeper, Popup:TopLeft, KeeperBall, "I sense a great force awakening, be on your guard.", )
            var lines = textAsset.text.Split(new[] { "\r\n", "\r", "\n" }, System.StringSplitOptions.None);
            for (var lineNum = 0; lineNum < lines.Length; lineNum++)
            {
                var line = lines[lineNum].Trim();
                if (line.StartsWith("//")) continue;
                if (line == "" || line.StartsWith('{') || line.StartsWith('}')) continue;
                var pline = MAParserSupport.ParseString(line);
                if (pline == null || pline.Count == 0) continue;
                var fpass = pline[0];
                if (fpass.m_functionName.Equals("Message") || fpass.m_functionName.Equals("MessageHold"))
                {
                    if(_advisor != null && fpass.m_args[0].Trim().Equals(_advisor) == false) continue;
                    var who = $"{name.fileName},{pline[0].m_args[0].Trim(' ', '"', '(', ')')}";
                    var message = "";
                    switch (pline[0].m_args.Count)
                    {
                        case 3:
                            message = $"{lineNum},\"{pline[0].m_args[1].Trim(' ')}\"";
                            break;
                        case 5:
                            message = $"{lineNum},\"{pline[0].m_args[3].Trim(' ')}\"";
                            break;
                        default:
                            continue;
                    }
                    if (dialogDict.ContainsKey(who))
                        dialogDict[who].Add(message);
                    else
                        dialogDict[who] = new List<string>() {message};
                }

                lineNum++;
            }
        }
        var dialog = new StringBuilder();
        dialog.Append($"File,Who,Line,Message\n");
        foreach(var d in dialogDict)
        {
            foreach(var m in d.Value)
            {
                dialog.Append($"{d.Key},{m}\n");
            }
        }

        if (dialog.Length <= 0) return;
        var path = Application.dataPath;
        var fname = $"{c_fileWritePath}{c_parserPath}Output/";
        if(_advisor != null)
            fname = $"{c_fileWritePath}{c_parserPath}Output/{_advisor}";
        switch (_outputType)
        {
            case "csv":
                fname = $"{fname}Dialog.csv";
                File.WriteAllText($"{fname}", dialog.ToString().TrimEnd('\n'));
                break;
            case "script":
                fname = $"{fname}Script.rtf";
                var script = GenerateScriptRTF(dialog.ToString());
                File.WriteAllText(fname, script);
#if UNITY_EDITOR
                System.Diagnostics.Process.Start(fname);
#endif
                break;
            
        }
    }
    static string GenerateScriptRTF(string csvContent, bool _groupLines = true)
    {
        var output = new List<string>();

        // RTF Header with color table
        output.Add(@"{\rtf1\ansi\deff0");
        output.Add(@"{\fonttbl{\f0 Arial;}}");
        output.Add(@"{\colortbl ;\red169\green169\blue169;}"); // Color 1 = light grey (dark grey actually)

        output.Add(@"\fs28"); // Font size 14pt (RTF sizes are double points)

        var lines = csvContent.Split('\n');
        string lastFileName = "";
        string lastAdvisorName = "";
        for (int i = 1; i < lines.Length; i++) // Skip header
        {
            var line = lines[i].Trim();
            if (string.IsNullOrWhiteSpace(line)) continue;

            var parts = line.Split(',');

            if (parts.Length >= 4)
            {
                string fileName = parts[0].Trim();
                string who = parts[1].Trim();
                string lineNum = parts[2].Trim();
                string message = "";
                for(int j = 3; j < parts.Length; j++)
                {
                    message += $"{parts[j]},";
                }
                message = message.TrimEnd(',');
                // When file name changes, insert a left-justified, light grey header
                if (fileName != lastFileName)
                {
                    output.Add(@"\pard\ql\cf1 " + EscapeRtf(fileName) + @"\cf0\par");
                    output.Add(@"\par"); // Blank line after file name
                    lastFileName = fileName;
                    lastAdvisorName = "";
                }

                if (who != lastAdvisorName || _groupLines == false)
                {
                    //output.Add(@"\pard\ql\cf1 " + EscapeRtf(who) + @"\cf0\par"); // Character name, light grey
                    output.Add(@"\pard\qc\b " + EscapeRtf(who.ToUpper()) + @"\b0\par"); // Character name, bolded and centered
                    lastAdvisorName = who;
                }
               // output.Add(@"\pard\ql\cf1 " + EscapeRtf(lineNum) + @"\cf0\par");
                output.Add(@"\pard\qc " + EscapeRtf(message) + @"\par");             // Message, centered
                output.Add(@"\par"); // Blank line between dialogue entries
            }
        }

        output.Add("}"); // End of RTF document
        return string.Join("\n", output);
    }
    static string EscapeRtf(string text)
    {
        return text
            .Replace(@"\", @"\\")
            .Replace("{", @"\{")
            .Replace("}", @"\}")
            .Replace("\n", @"\par "); // newlines into \par
    }
    public static List<string> GetAllFlowFiles2()
    {
        string folderPath = $"{c_fileWritePath}{c_parserPath}".TrimEnd('/');
        var flowFiles = new List<string>();
        flowFiles.Add("Select a flow file");
        if (Directory.Exists(folderPath))
        {
            // Search for all .MOA files in the directory and subdirectories
            var files = Directory.GetFiles(folderPath, "*.MOA", SearchOption.AllDirectories);

            // Add each file name (without the extension) to the list
            flowFiles.AddRange(files.Select(file => Path.GetFileNameWithoutExtension(file)));
        }
        return flowFiles;
    }
    public static void SaveAll(ref SaveContainers.SaveCountryside _s)
    {
        var json = JsonHelper.ToJson(Me.m_sections.ToArray());
        _s.m_activeParseFlows = json;
        _s.m_activeParseMemories = Me.m_memories;
        _s.m_failSequence = JsonUtility.ToJson(Me.m_failWhatToDo);
        _s.m_lastChoice = MAChoiceGUIManager.m_lastChoice;
    }
    public static void LoadAll(SaveContainers.SaveCountryside _l)
    {
        var sectionsJson = JsonHelper.FromJson<MAParserSection>(_l.m_activeParseFlows, true);
        Me.m_memories = _l.m_activeParseMemories;
        if(_l.m_failSequence.IsNullOrWhiteSpace() == false)
            Me.m_failWhatToDo = JsonUtility.FromJson<FailWhatToDo>(_l.m_failSequence);
        var sections = new List<MAParserSection>(sectionsJson);
        foreach(var s in sections)
        {
            Me.ExecuteSection(s.m_fileName, s.m_currentLine, s);

            /*
            //Load the current gifts
            var takeAllGifts = new List<NGBusinessGift>();
            foreach (var g in s.m_currentGifts)
            {
                var giftInfo = NGBusinessGift.GetInfo(g);
                if (giftInfo == null) continue;
                takeAllGifts.Add(giftInfo);
            }
            if(takeAllGifts.Count > 0)
            {
                NGBusinessGiftsPanel.CreateOrCall(takeAllGifts, null, 0, MAParserManager.m_updatingMaParserSection);
            }*/
        }
    }

    public class FailWhatToDo
    {
        public FailWhatToDo()
        {
            m_failCrypt = null;
            m_failTasks = null;
            m_failFileName = null;
        }
        public FailWhatToDo(string _failCrypt, string _failTasks)
        {
            m_failCrypt = _failCrypt;
            m_failTasks = _failTasks;
            m_failFileName = m_updatingMaParserSection.m_fileName;;
        }
        public string m_failCrypt = null;
        public string m_failTasks = null;
        public string m_failFileName = null;
        public void Clear()
        {
            m_failCrypt = null;
            m_failTasks = null;
            m_failFileName = null;
        }
    }
    public FailWhatToDo m_failWhatToDo = new();
    public string m_failCryptWhatToDo = null; 
    public string m_failTasksWhatToDo = null;
    public string m_failSetSectionFileName = null;
    public  bool FailDayCheck()
    {
        var whatToDo = "";
        switch (FailSequenceController.Me.CurrentFailType)
        {
            case FailSequenceController.EFailType.Crypt:
                whatToDo = m_failWhatToDo.m_failCrypt;
            break;

            case FailSequenceController.EFailType.Tasks: 
                whatToDo= m_failWhatToDo.m_failTasks;
            break;
        }

        if (whatToDo.IsNullOrWhiteSpace() == false)
        {
            if (whatToDo.Contains('/') == false)
            {
                ExecuteSectionInternal(m_failWhatToDo.m_failFileName, whatToDo);
            }
            else
            {
                ExecuteSection(whatToDo);
            }
            return true;
        }

        m_failWhatToDo.Clear();
        m_failCryptWhatToDo = null;
        m_failTasksWhatToDo = null;
        m_failSetSectionFileName = "";
        return false;
    }
    public static void SkipToDay(int _day)
    {
        foreach(var s in Me.m_sections)
        {
            s.ClearCurrents();
        }
        MAParserManager.Me.m_sections.Clear();
        MAParserManager.Me.ExecuteSection($"Skip");
        DayNight.Me.SetGameDay(_day);
        DayNight.Me.SetTimeOfDay(8.0f, false);
        var searchFor = $"Chapter1V2/Day{_day:d2}_";
        var files = GetAllFlowFiles();
        var found = files.Find(o => o.StartsWith(searchFor));
        var section = MAParserManager.Me.ExecuteSection($"Chapter1V2/Chapter1Flow", 12);
        for(int i =0; i < section.Lines.Count; i++)
        {
            if(section.Lines[i].m_line.Contains(searchFor))
            {
                section.m_currentLine = i;
                break;
            }
        }
    }
    private static DebugConsole.Command s_cmdSkipToDay = new DebugConsole.Command("SkipToDay", _s =>
    {
        if (int.TryParse(_s, out var day) == false) return;
        SkipToDay(day);
    });
}
