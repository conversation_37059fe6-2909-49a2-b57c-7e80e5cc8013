using System.Collections.Generic;
using System.Linq;
using MACharacterStates;
using UnityEngine;

public class MAInspection : MonoBehaviour
{
    public enum InspectionState
    {
        None,
        MoveToBuilding,
        Inspecting,
        Reporting,
        Finished
    }
    private bool m_isFinished = false;
    private MAFlowCharacter m_character;
    private MAComponentInfo m_component;
    private MAParserSection m_calledFrom;
    private MABuilding m_gotoBuilding;
    private float m_maxRange;
    private InspectionState m_state = InspectionState.None;
    private List<string> m_inspectionReports = new List<string>();
    private string m_reportingExecuite = "";
    private bool m_isExecuting = false;
    [ShowInInspector] private List<MABuilding> m_buildingVisited = new();
    public bool IsFinished => m_isFinished;
    void InspectBuilding()
    {
        if (m_character.IsIdle == false && m_character.IsDecideWhatToDo == false) return;
        if (m_gotoBuilding ==null) return;
        
        var score = m_gotoBuilding.GetInspectionScore();
        int index = (int)(score * (float)m_inspectionReports.Count);
        m_reportingExecuite = m_inspectionReports[index].Trim();
        m_state = InspectionState.Inspecting;
    }
    void Inspecting()
    {
        if (m_isExecuting == false)
        {
            if (MAParserManager.Me.IsSectionActive(m_reportingExecuite))
            {
                MAParser.ParserError($"{m_reportingExecuite} Already executing.");
            }
            else
            {
                MAParserManager.Me.ExecuteSectionInSection(m_reportingExecuite, m_calledFrom);
            }
            m_isExecuting = true;
        }

        if (MAParserManager.Me.IsSectionActive(m_reportingExecuite)) return;
        m_isExecuting = false;
        m_state = InspectionState.None;
    }   

    void FindNextInspection()
    {
        var bestDistance = m_maxRange;
        MABuilding bestBuilding = null;
        foreach (var building in NGManager.Me.m_maBuildings)
        {
            if (building.HasBuildingComponent(m_component.m_classType) == false) continue;
            if (m_buildingVisited.Contains(building)) continue;
            var distance = Vector3.Distance(building.transform.position, m_character.transform.position);
            if (distance < bestDistance)
            {
                bestDistance = distance;
                bestBuilding = building;
            }
        }

        if (bestBuilding == null)
        {
            m_isFinished = true;
            return;
        }
        m_buildingVisited.Add(bestBuilding);
        m_gotoBuilding = bestBuilding;
        m_state = InspectionState.MoveToBuilding;
        m_character.NavigateToPosition(bestBuilding.DoorPosOuter);
        //MAParser.MoveCharacterToBuilding(m_character, m_gotoBuilding);

        //m_character.SetMoveToBuilding(bestBuilding);
    }
    void Update()
    {
        if (IsFinished) return;
        switch (m_state)
        {
            case InspectionState.None:
                FindNextInspection();
                break;
            case InspectionState.MoveToBuilding:
                if (MAParser.WaitForCharacterState(m_character, "Idle"))
                {
                    InspectBuilding();
                }
                break;
            case InspectionState.Inspecting:
                Inspecting();
                break;
                
        }
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    void Activate(MAComponentInfo _component, MAFlowCharacter _character, float _maxRange, string _goodExecute, string _averageFeedback, string _poorFeedback)
    {
        m_component = _component;
        m_character = _character;
        m_calledFrom = MAParserManager.m_updatingMaParserSection;
        m_maxRange = _maxRange;
        m_inspectionReports.Add(_poorFeedback);
        m_inspectionReports.Add(_averageFeedback);
        m_inspectionReports.Add(_goodExecute);
        m_state = InspectionState.None;
    }
    public static MAInspection Create(MAComponentInfo _component, MAFlowCharacter _character, float _maxRange, string _goodExecute, string _averageFeedback, string _poorFeedback)
    {
        GameObject _gameObject = new GameObject("Inspection");
        _gameObject.transform.SetParent(_character.transform);
        var inspection = _gameObject.AddComponent<MAInspection>();
        inspection.Activate(_component, _character, _maxRange,_goodExecute, _averageFeedback, _poorFeedback);
        return inspection;
    }
}
