[{"id": "67d02a7189c8e00314b6a788", "m_name": "ActionAnimalHouse", "m_title": "Animal Shelter", "m_buildingTitle": "", "m_class": "BCActionAnimalHouse", "m_field": "", "m_blockSprite": "ComponentAnimalHouse", "m_showInInfo": "False", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "A cozy haven for feathered friends", "m_usageMultiplier": "1.00", "m_basePrice": "1.00"}, {"id": "68135c3057e65602cf1077bd", "m_name": "ActionAnimation", "m_title": "Animate Working", "m_buildingTitle": "", "m_class": "BCAnimateWorking", "m_field": "", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "0.00"}, {"id": "66c3435245c3dd028577c39e", "m_name": "ActionArmourer", "m_title": "<PERSON><PERSON><PERSON>", "m_buildingTitle": "", "m_class": "BCActionArmourer", "m_field": "m_workerSellSpeed=10\nm_orderType=armourer", "m_blockSprite": "ComponentShop", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Armourer:Entrances", "m_description": "Design & Equip Armour", "m_usageMultiplier": "1.50", "m_basePrice": "100.00"}, {"id": "64a421cc49796a002a5e2e39", "m_name": "ActionClayMine", "m_title": "Clay Mine", "m_buildingTitle": "", "m_class": "BCActionGatherer", "m_field": "m_input =None\nm_output = RawResourceClay\nm_inputQuantity = 1\nm_energyRequiedToMake=2\nm_energyRequiedToHarvest = 1\nm_workerHarvestRate=0.1\nm_animationSpeedMultiplier=1\nm_maxWorkers=1", "m_blockSprite": "ComponentConvertor", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "2.00", "m_basePrice": "50.00"}, {"id": "657710809305b90025e630e4", "m_name": "ActionCottonFarm", "m_title": "Cloth Farm Workstation", "m_buildingTitle": "Cloth Farm", "m_class": "BCActionGatherer", "m_field": "m_input =Cotton\nm_output = RefinedCotton\nm_inputQuantity = 1\nm_energyRequiedToMake=2\nm_energyRequiedToHarvest = 1\nm_workerHarvestRate=0.05\nm_treeRadius=31\nm_treeHolderType=Cotton\nm_harvestRegrowDawns=-1\nm_animationSpeedMultiplier=1\nm_minResourceFlyTime=0.7\nm_maxResourceFlyTime=2\nm_maxWorkers=1", "m_blockSprite": "ComponentConvertor", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Farm:Action", "m_description": "Harvests raw cotton from the fields, refines them into cotton bales ready for the Spinner.", "m_usageMultiplier": "1.20", "m_basePrice": "50.00"}, {"id": "65771198faf0f4002880c3f6", "m_name": "ActionCottonSpinner", "m_title": "Cloth Mill Workstation", "m_buildingTitle": "Cloth Mill", "m_class": "BCActionProducer", "m_field": "m_input = RefinedCotton\nm_output = Fabric\nm_inputQuantity = 1\nm_energyRequiedToMake=1.5\nm_animationSpeedMultiplier=1\nm_maxWorkers=1", "m_blockSprite": "ComponentConvertor", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Farm Cotton:Action", "m_description": "Spins cotton bales into fabric, ready for the Factory.", "m_usageMultiplier": "1.20", "m_basePrice": "50.00"}, {"id": "64ccfb8cc994b10029a993e6", "m_name": "ActionCrypt", "m_title": "Crypt", "m_buildingTitle": "", "m_class": "BCActionCrypt", "m_field": "", "m_blockSprite": "ComponentCrypt", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Monsters:New Blocks", "m_description": "The Last Line Of Defence", "m_usageMultiplier": "10.00", "m_basePrice": "9999.00"}, {"id": "67e6ddbce7becb02f97a448f", "m_name": "ActionFabricDispatch", "m_title": "Cotton Dispatch", "m_buildingTitle": "", "m_class": "BCActionDispatch", "m_field": "m_orderType=dispatch\nm_orderSequenceType=fabric\nm_cardLockedDuration=30", "m_blockSprite": "ComponentDispatch", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Dispatch:Entrances", "m_description": "Supplies & Delivers Fabric Orders", "m_usageMultiplier": "3.00", "m_basePrice": "99.00"}, {"id": "647098a8f967a506e5125610", "m_name": "ActionFactory", "m_title": "Factory Workstation", "m_buildingTitle": "Factory", "m_class": "BCFactory", "m_field": "m_animationSpeedMultiplier=1\nm_energyRequiredToMakeMultiplier=1\nm_maxWorkers=1", "m_blockSprite": "ComponentFactory", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Factory:Entrances", "m_description": "The hub of all manufacturing. Takes refined resources and turns them into final product, ready for Dispatch.", "m_usageMultiplier": "1.05", "m_basePrice": "35.00"}, {"id": "647098a8f967a506e5125625", "m_name": "ActionFlourMill", "m_title": "Food Mill Workstation", "m_buildingTitle": "Food Mill", "m_class": "BCActionProducer", "m_field": "m_input = RefinedWheat\nm_output = Flour\nm_inputQuantity = 1\nm_energyRequiedToMake=1.25\nm_animationSpeedMultiplier=1\nm_maxWorkers=1", "m_blockSprite": "ComponentConvertor", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Mill Produce:Action", "m_description": "Grinds wheat kernels into flour, ready for the factory.", "m_usageMultiplier": "1.04", "m_basePrice": "25.00"}, {"id": "64abdf4f692e8f002606e45f", "m_name": "ActionFoodDispatch", "m_title": "Food Dispatch", "m_buildingTitle": "", "m_class": "BCActionDispatch", "m_field": "m_orderType=dispatch\nm_orderSequenceType=food\nm_cardLockedDuration=30", "m_blockSprite": "ComponentDispatch", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "Dispatch:Entrances", "m_description": "Supplies & Delivers Food Orders", "m_usageMultiplier": "3.00", "m_basePrice": "99.00"}, {"id": "65154cc7091e9b00274dd3ce", "m_name": "ActionGraveyard", "m_title": "Graveyard", "m_buildingTitle": "", "m_class": "BCActionGraveyard", "m_field": "", "m_blockSprite": "ComponentGraveyard", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "Monsters:New Blocks", "m_description": "", "m_usageMultiplier": "10.00", "m_basePrice": "9999.00"}, {"id": "670e396a40940102cc03f514", "m_name": "ActionHeroesGuild", "m_title": "Heroes' Guild", "m_buildingTitle": "", "m_class": "BCActionHeroesGuild", "m_field": "m_healMultiplier=1.1\nm_trailMultiplier=1.1", "m_blockSprite": "ComponentTavern", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "Heroes Guild:Entrances", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "10.00"}, {"id": "647098a8f967a506e512561f", "m_name": "ActionHouse", "m_title": "House", "m_buildingTitle": "", "m_class": "BCActionHouse", "m_field": "m_restMultiplier=1.2", "m_blockSprite": "ComponentHouse", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "5.00"}, {"id": "6620376f6581be00274c54a1", "m_name": "ActionLumberMill", "m_title": "Lumber Mill", "m_buildingTitle": "", "m_class": "BCActionGatherer", "m_field": "m_inputQuantity=1\nm_input=Wood\nm_output=Timber\nm_treeRadius=81\nm_treeHolderType=Trees\nm_harvestRegrowDawns=-1\nm_energyRequiedToMake=.2\nm_energyRequiedToHarvest = 1\nm_workerHarvestRate=0.05\nm_animationSpeedMultiplier=1\nm_minResourceFlyTime=0.6\nm_maxResourceFlyTime=2\nm_maxWorkers=1", "m_blockSprite": "ComponentLumberMill", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Lumber Mill:Action", "m_description": "Harvests wood from trees, refines them into timber.", "m_usageMultiplier": "2.00", "m_basePrice": "25.00"}, {"id": "67e6de0e6b5c8a02daf9f9d2", "m_name": "ActionMetalDispatch", "m_title": "Metal Dispatch", "m_buildingTitle": "", "m_class": "BCActionDispatch", "m_field": "m_orderType=dispatch\nm_orderSequenceType=metal\nm_cardLockedDuration=30", "m_blockSprite": "ComponentDispatch", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Dispatch Metal:Entrances", "m_description": "Supplies & Delivers Metal Orders", "m_usageMultiplier": "3.00", "m_basePrice": "99.00"}, {"id": "656f316abe16880028c9931a", "m_name": "ActionMetalSmelter", "m_title": "Metal Smelter Workstation", "m_buildingTitle": "Metal Smelter", "m_class": "BCActionProducer", "m_field": "m_input = RefinedOre\nm_output = Metal\nm_inputQuantity = 1\nm_energyRequiedToMake=1.75\nm_animationSpeedMultiplier=1\nm_maxWorkers=1", "m_blockSprite": "ComponentConvertor", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Metal Smelter:Entrances", "m_description": "Smelts refined ore into Metal, ready for the Factory.", "m_usageMultiplier": "1.20", "m_basePrice": "25.00"}, {"id": "647098a8f967a506e5125629", "m_name": "ActionMineMetal", "m_title": "Metal Mine Workstation", "m_buildingTitle": "Metal Mine", "m_class": "BCActionGatherer", "m_field": "m_inputQuantity=1\nm_input=Ore\nm_output=RefinedOre\nm_treeRadius=21\nm_treeHolderType=Metal\nm_harvestRegrowDawns=1\nm_energyRequiedToMake=2\nm_energyRequiedToHarvest = 1\nm_workerHarvestRate=0.05\nm_animationSpeedMultiplier=1\nm_minResourceFlyTime=0.5\nm_maxResourceFlyTime=2\nm_maxWorkers=1", "m_blockSprite": "ComponentConvertor", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Metal Mine:Entrances", "m_description": "Mines ore from the world, refines it ready for the smelter.", "m_usageMultiplier": "2.00", "m_basePrice": "25.00"}, {"id": "64ad30e26e795b002913535a", "m_name": "ActionPub", "m_title": "Pub", "m_buildingTitle": "", "m_class": "BCRestPlace", "m_field": "m_workerRestsPerSecond=0.25", "m_blockSprite": "ComponentRestPlace", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "2.00", "m_basePrice": "50.00"}, {"id": "64c277ffcf4391002620de1a", "m_name": "ActionRestPlace", "m_title": "Rest Place", "m_buildingTitle": "", "m_class": "BCActionRestPlace", "m_field": "m_workerRestsPerSecond = 0.025", "m_blockSprite": "ComponentBedroom", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "15.00"}, {"id": "661d10e5350f190027da53af", "m_name": "ActionShop", "m_title": "Clothing Shop", "m_buildingTitle": "", "m_class": "BCActionClothesShop", "m_field": "m_workerSellSpeed=10\nm_orderType=shop", "m_blockSprite": "ComponentShop", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Shop:Entrances", "m_description": "The Clothing Shop", "m_usageMultiplier": "2.00", "m_basePrice": "50.00"}, {"id": "6499b4b3a0093f002a8dd103", "m_name": "ActionTavern", "m_title": "Tavern", "m_buildingTitle": "", "m_class": "BCActionTavern", "m_field": "m_refreshTime=0", "m_blockSprite": "ComponentTavern", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "Hire Workers & Heroes", "m_usageMultiplier": "5.00", "m_basePrice": "99.00"}, {"id": "661a59317d60fc00264dd013", "m_name": "ActionTurret", "m_title": "<PERSON>", "m_buildingTitle": "", "m_class": "BCActionTurret", "m_field": "m_targets=Troll:Zombie:ZombieLurker:Werewolf\nm_defaultAmmo=MA_Turret_Ammo_Boulder\nm_timeBetweenShots=6\nm_damageMultiplier=80\nm_payloadSpeedXZ=40\nm_explosionRadius=10\nm_explosionForce=200\nm_turnSpeed=80\nm_minPitch=-20\nm_maxPitch=25\nm_fireAnimationSpeedModifier=1\nm_recoilAmount=1\nm_ammoSizeMultiplier=1\nm_defaultAmmoSizeMultiplier=0.2\nm_reloadGUIHeight=4\nm_barrelForwardType=down\nm_barrelEndDist=3\nm_barrelEndUp=0.5\nm_pitchOffset=0\nm_aimAdjustment=90\nm_rotateOnX=true\nm_controlledVisualName=ControlledVisual\nm_ammoVisualName=AmmoPos\nm_barrelVisualName=Cannon", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "AmmoStockIn", "m_recommended": "", "m_defaultDrawerSet": "Turret:Action", "m_description": "Fully Automatic Cannon", "m_usageMultiplier": "1.30", "m_basePrice": "10.00"}, {"id": "66c3424e305150027f2a0c7d", "m_name": "ActionWeaponsmith", "m_title": "Weaponsmith", "m_buildingTitle": "", "m_class": "BCActionWeaponsmith", "m_field": "m_workerSellSpeed=10\nm_orderType=weaponsmith", "m_blockSprite": "ComponentShop", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Weaponsmith:Entrances", "m_description": "Design & Equip Weapons", "m_usageMultiplier": "1.50", "m_basePrice": "100.00"}, {"id": "66c375ee5af7ba027f5f7ea7", "m_name": "ActionWheatFarm", "m_title": "Food Farm Workstation", "m_buildingTitle": "Food Farm", "m_class": "BCActionGatherer", "m_field": "m_inputQuantity=1\nm_outputQuantity=1\nm_input=Wheat\nm_output=RefinedWheat\nm_treeRadius=31\nm_treeHolderType=Wheat\nm_harvestRegrowDawns=1\nm_energyRequiedToMake=.2\nm_energyRequiedToHarvest = .2\nm_workerHarvestRate=0.1\nm_animationSpeedMultiplier=1\nm_minResourceFlyTime=0.45\nm_maxResourceFlyTime=2\nm_maxWorkers=1", "m_blockSprite": "ComponentProduceFarm", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "Farm:Action", "m_description": "Harvests wheat bushel from the fields, refines them into wheat kernels ready for the Mill.", "m_usageMultiplier": "1.04", "m_basePrice": "25.00"}, {"id": "66c70ef23e26580282d7f6c5", "m_name": "Aesthetic", "m_title": "Aesthetic", "m_buildingTitle": "", "m_class": "BCAesthetic", "m_field": "", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "3.00"}, {"id": "682c6d412e003802ecb69862", "m_name": "Ammo", "m_title": "Ammo Slot", "m_buildingTitle": "", "m_class": "BCAmmo", "m_field": "", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "AmmoStockIn", "m_recommended": "<span class=\"661a59317d60fc00264dd013\" data-kn=\"connection-value\">ActionTurret</span>", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "50.00"}, {"id": "682c83182e003802ecb6d77b", "m_name": "AmmoStockIn", "m_title": "Ammo Pad", "m_buildingTitle": "", "m_class": "BCAmmoStock", "m_field": "", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "", "m_recommended": "<span class=\"661a59317d60fc00264dd013\" data-kn=\"connection-value\">ActionTurret</span>", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.20", "m_basePrice": "5.00"}, {"id": "64805de782cf780028eb2ffc", "m_name": "Automator", "m_title": "Automator", "m_buildingTitle": "", "m_class": "BCAutomator", "m_field": "m_producesPerSecond=0.025", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "5.00"}, {"id": "6880b74ee3ef2e02e3dc7336", "m_name": "Ballista", "m_title": "Ballista", "m_buildingTitle": "", "m_class": "BCActionTurret", "m_field": "m_targets=Troll:Zombie:ZombieLurker:Werewolf\nm_defaultAmmo=MA_Turret_Ammo_Bolt_Wood\nm_timeBetweenShots=3\nm_damageMultiplier=160\nm_payloadSpeedXZ=70\nm_explosionRadius=5\nm_explosionForce=100\nm_turnSpeed=100\nm_minPitch=-30\nm_maxPitch=5\nm_fireAnimationSpeedModifier=2\nm_recoilAmount=0.5\nm_ammoSizeMultiplier=1\nm_defaultAmmoSizeMultiplier=0.7\nm_reloadGUIHeight=4\nm_barrelForwardType=up\nm_barrelEndDist=1.8\nm_barrelEndUp=0\nm_pitchOffset=-90\nm_aimAdjustment=0\nm_rotateOnX=false\nm_invertPitch=true\nm_controlledVisualName=ControlledVisual\nm_ammoVisualName=Ammo_Holder\nm_barrelVisualName=Bone_Base", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "AmmoStockIn", "m_recommended": "", "m_defaultDrawerSet": "Turret:Action", "m_description": "Fully Automatic Ballista", "m_usageMultiplier": "1.50", "m_basePrice": "20.00"}, {"id": "6733368cb32a5502d0e3ed1e", "m_name": "BCActionQuestOrder", "m_title": "Quest Order", "m_buildingTitle": "", "m_class": "BCActionQuestOrder", "m_field": "m_orderType=quest", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "0.00"}, {"id": "67bdd68a914687030d2f6573", "m_name": "Beacon", "m_title": "Beacon", "m_buildingTitle": "", "m_class": "BCBeacon", "m_field": "m_manaToFill=20\nm_localUnlockRadius=8", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "0.00"}, {"id": "64a5427349796a002a654736", "m_name": "Bedroom", "m_title": "Bedroom ", "m_buildingTitle": "House", "m_class": "BCBedroom", "m_field": "m_maxWorkers = 1\nm_restPerSecond = 0.2", "m_blockSprite": "ComponentBedroom", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "<span class=\"647098a8f967a506e512561f\" data-kn=\"connection-value\">ActionHouse</span>", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.06", "m_basePrice": "20.00"}, {"id": "67e2e6b9d59eb402e46d2d60", "m_name": "BedroomDouble", "m_title": "Double Bedroom", "m_buildingTitle": "House", "m_class": "BCBedroom", "m_field": "m_maxWorkers = 2\nm_restPerSecond = 0.3", "m_blockSprite": "ComponentBedroom", "m_showInInfo": "False", "m_required": "Entrance", "m_recommended": "<span class=\"647098a8f967a506e512561f\" data-kn=\"connection-value\">ActionHouse</span>", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "5.00"}, {"id": "647098a8f967a506e512563b", "m_name": "Capacity", "m_title": "Capacity", "m_buildingTitle": "", "m_class": "BCCapacity", "m_field": "m_maxVisitors=1", "m_blockSprite": "ComponentCapacity", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "5.00"}, {"id": "68813f8d9390fe0304fda76b", "m_name": "Catapult", "m_title": "Catapult", "m_buildingTitle": "", "m_class": "BCActionTurret", "m_field": "m_targets=Troll:Zombie:ZombieLurker:Werewolf\nm_defaultAmmo=MA_Turret_Ammo_Boulder\nm_timeBetweenShots=6\nm_damageMultiplier=200\nm_payloadSpeedXZ=50\nm_explosionRadius=10\nm_explosionForce=100\nm_turnSpeed=50\nm_minPitch=-10\nm_maxPitch=10\nm_fireAnimationSpeedModifier=2\nm_recoilAmount=1\nm_ammoSizeMultiplier=1\nm_defaultAmmoSizeMultiplier=0.7\nm_reloadGUIHeight=6\nm_barrelForwardType=forward\nm_barrelEndDist=0\nm_barrelEndUp=0\nm_pitchOffset=0\nm_aimAdjustment=0\nm_rotateOnX=true\nm_invertPitch=false\nm_controlledVisualName=ControlledVisual\nm_ammoVisualName=Ammo_Holder\nm_barrelVisualName=ReleasePoint", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "AmmoStockIn", "m_recommended": "", "m_defaultDrawerSet": "Turret:Action", "m_description": "Fully Automatic Catapult", "m_usageMultiplier": "1.50", "m_basePrice": "30.00"}, {"id": "64746d429f5c73002859dd9f", "m_name": "<PERSON><PERSON><PERSON>", "m_title": "<PERSON><PERSON><PERSON>", "m_buildingTitle": "", "m_class": "BC<PERSON><PERSON>ney", "m_field": "", "m_blockSprite": "ComponentChimney", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.50", "m_basePrice": "1.50"}, {"id": "647098a8f967a506e5125609", "m_name": "Drag", "m_title": "Drag", "m_buildingTitle": "", "m_class": "BCDrag", "m_field": "", "m_blockSprite": "ComponentDrag", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "0.00"}, {"id": "647098a8f967a506e512562e", "m_name": "Empty", "m_title": "", "m_buildingTitle": "", "m_class": "", "m_field": "", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "5.00"}, {"id": "647098a8f967a506e5125636", "m_name": "Entrance", "m_title": "Entrance ", "m_buildingTitle": "", "m_class": "BCEntrance", "m_field": "", "m_blockSprite": "ComponentEntrance", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "Gives workers access to the building.", "m_usageMultiplier": "1.00", "m_basePrice": "1.00"}, {"id": "6880ff0b8e392002c27ea3b0", "m_name": "GiantBallista", "m_title": "Giant Ballista", "m_buildingTitle": "", "m_class": "BCActionTurret", "m_field": "m_targets=Troll:Zombie:ZombieLurker:Werewolf\nm_defaultAmmo=MA_Turret_Ammo_Bolt_Iron\nm_timeBetweenShots=4\nm_damageMultiplier=200\nm_payloadSpeedXZ=100\nm_explosionRadius=7\nm_explosionForce=100\nm_turnSpeed=70\nm_minPitch=-30\nm_maxPitch=3\nm_fireAnimationSpeedModifier=2\nm_recoilAmount=0.7\nm_ammoSizeMultiplier=1\nm_defaultAmmoSizeMultiplier=0.7\nm_reloadGUIHeight=4\nm_barrelForwardType=up\nm_barrelEndDist=1.8\nm_barrelEndUp=0\nm_pitchOffset=-90\nm_aimAdjustment=0\nm_rotateOnX=false\nm_invertPitch=true\nm_controlledVisualName=ControlledVisual\nm_ammoVisualName=Ammo_Holder\nm_barrelVisualName=Bone_Base", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "AmmoStockIn", "m_recommended": "", "m_defaultDrawerSet": "Turret:Action", "m_description": "Giant Full Automatic Ballista", "m_usageMultiplier": "2.00", "m_basePrice": "100.00"}, {"id": "64b2950b2b01b600288d2ffe", "m_name": "GUIClock", "m_title": "Clock", "m_buildingTitle": "", "m_class": "BCGUIClock", "m_field": "", "m_blockSprite": "ComponentGUIInfo", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "10.00"}, {"id": "670e89299786f202f6ea46e9", "m_name": "GuildBedroom", "m_title": "Hero Bedroom", "m_buildingTitle": "", "m_class": "BCGuildBedroom", "m_field": "m_maxWorkers=1", "m_blockSprite": "ComponentBedroom", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "<span class=\"670e396a40940102cc03f514\" data-kn=\"connection-value\">ActionHeroesGuild</span>", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "5.00"}, {"id": "670e9aa4987ff203183cea70", "m_name": "GuildHealing", "m_title": "<PERSON>", "m_buildingTitle": "", "m_class": "BCGuildHealing", "m_field": "m_heroHealingPerSecond=1\nm_heroesToHeal=1", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "Entrance|GuildBedroom", "m_recommended": "<span class=\"670e396a40940102cc03f514\" data-kn=\"connection-value\">ActionHeroesGuild</span>", "m_defaultDrawerSet": "", "m_description": "Increases the rate your Hero will heal while at home resting.", "m_usageMultiplier": "2.00", "m_basePrice": "5.00"}, {"id": "670e9acec818ab02f1a4108a", "m_name": "GuildTraining", "m_title": "Hero Training", "m_buildingTitle": "", "m_class": "BCGuildTraining", "m_field": "m_heroTrainingPerSecond=1\nm_heroesToTrain=1", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "Entrance|GuildBedroom", "m_recommended": "<span class=\"670e396a40940102cc03f514\" data-kn=\"connection-value\">ActionHeroesGuild</span>", "m_defaultDrawerSet": "", "m_description": "Train and level up your hero.", "m_usageMultiplier": "2.00", "m_basePrice": "5.00"}, {"id": "65f1e988c4965000285cf4ce", "m_name": "GUIOrderInfo", "m_title": "Order Info", "m_buildingTitle": "", "m_class": "BCOrderInfo", "m_field": "", "m_blockSprite": "ComponentGUIInfo", "m_showInInfo": "True", "m_required": "", "m_recommended": "<span class=\"647098a8f967a506e5125610\" data-kn=\"connection-value\">ActionFactory</span>", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "10.00"}, {"id": "647483972dba6610b3b243a0", "m_name": "GUIStock", "m_title": "Stock", "m_buildingTitle": "", "m_class": "BCGUIStock", "m_field": "", "m_blockSprite": "ComponentGUIInfo", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "15.00"}, {"id": "64746d6e6300b10028c98119", "m_name": "Happiness", "m_title": "Happiness", "m_buildingTitle": "", "m_class": "BCHappiness", "m_field": "", "m_blockSprite": "ComponentHeart", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "10.00"}, {"id": "6475fac3959abb002651b822", "m_name": "None", "m_title": "", "m_buildingTitle": "", "m_class": "", "m_field": "", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "", "m_basePrice": ""}, {"id": "64e5c8e3d68c1900285a4817", "m_name": "<PERSON><PERSON><PERSON>", "m_title": "<PERSON><PERSON><PERSON>", "m_buildingTitle": "", "m_class": "<PERSON><PERSON><PERSON><PERSON>", "m_field": "m_pushForce=5", "m_blockSprite": "ComponentPusher", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "5.00"}, {"id": "682326d6006ddf02cb449730", "m_name": "QuestActionGatherer", "m_title": "Wood Collection Point", "m_buildingTitle": "", "m_class": "BCQuestActionGatherer", "m_field": "m_inputQuantity=1\nm_input=Wood\nm_output=Wood\nm_treeRadius=81\nm_treeHolderType=Trees\nm_harvestRegrowDawns=-1\nm_energyRequiedToMake=.0\nm_energyRequiedToHarvest = 0\nm_workerHarvestRate=0.05\nm_animationSpeedMultiplier=1\nm_minResourceFlyTime=0.75\nm_maxResourceFlyTime=2", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "Entrance|QuestStockIn", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "2.00", "m_basePrice": "0.00"}, {"id": "6733477a50b98b030ef0ed09", "m_name": "QuestStockIn", "m_title": "Stock In", "m_buildingTitle": "", "m_class": "BCQuestStockIn", "m_field": "", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "1.00"}, {"id": "673c9c4ed41b4802b2d48642", "m_name": "QuestStockOut", "m_title": "Stock Out", "m_buildingTitle": "", "m_class": "BCQuestStockOut", "m_field": "m_maxStock=50", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "1.00"}, {"id": "66c87d0f8be6f60283c0afd6", "m_name": "ResourceNeededRepair", "m_title": "Repair", "m_buildingTitle": "", "m_class": "BCResourceNeededRepair", "m_field": "", "m_blockSprite": "ComponentLumberMill", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "10.00"}, {"id": "6814c5be6ca81802dd88e452", "m_name": "<PERSON><PERSON>", "m_title": "<PERSON><PERSON>", "m_buildingTitle": "", "m_class": "BCRoof", "m_field": "", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "1.00"}, {"id": "64ca3df0a9c5020027ea0dbb", "m_name": "Stables", "m_title": "Stables", "m_buildingTitle": "", "m_class": "BCStables", "m_field": "", "m_blockSprite": "ComponentStables", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.10", "m_basePrice": "15.00"}, {"id": "6476204cae6e1d00270c0c69", "m_name": "StockIn", "m_title": "Stock In Pad", "m_buildingTitle": "", "m_class": "BCStockIn", "m_field": "m_maxStock=999\nm_stockPadCapacity = -1", "m_blockSprite": "ComponentInputStock", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "10.00"}, {"id": "6476206925e269002839b8e7", "m_name": "StockOut", "m_title": "Stock Out Pad", "m_buildingTitle": "", "m_class": "BCStockOut", "m_field": "m_maxStock=999\nm_stockPadCapacity = -1", "m_blockSprite": "ComponentOutputStock", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "10.00"}, {"id": "647098a8f967a506e5125615", "m_name": "Tap", "m_title": "Tap", "m_buildingTitle": "", "m_class": "BCTap", "m_field": "m_clickAddsHowMuch = 0.1\nm_clickHoldAddsHowMuch = 0.025\nm_workerSpeedMultiplier = 1", "m_blockSprite": "ComponentTap", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "0.00"}, {"id": "6790dcc00016f302aa2151d4", "m_name": "TardisCrypt", "m_title": "Crypt", "m_buildingTitle": "", "m_class": "BCTardisCrypt", "m_field": "", "m_blockSprite": "", "m_showInInfo": "False", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.00", "m_basePrice": "1.00"}, {"id": "688246190d5ae102d7fdb753", "m_name": "Trebuchet", "m_title": "Trebuchet", "m_buildingTitle": "", "m_class": "BCActionTurret", "m_field": "m_targets=Troll:Zombie:ZombieLurker:Werewolf\nm_defaultAmmo=MA_Turret_Ammo_Boulder\nm_timeBetweenShots=6\nm_damageMultiplier=200\nm_payloadSpeedXZ=50\nm_explosionRadius=15\nm_explosionForce=100\nm_turnSpeed=50\nm_minPitch=-10\nm_maxPitch=10\nm_fireAnimationSpeedModifier=2\nm_recoilAmount=1\nm_ammoSizeMultiplier=1.2\nm_defaultAmmoSizeMultiplier=0.7\nm_reloadGUIHeight=6\nm_barrelForwardType=forward\nm_barrelEndDist=0\nm_barrelEndUp=0\nm_pitchOffset=0\nm_aimAdjustment=0\nm_rotateOnX=true\nm_invertPitch=false\nm_controlledVisualName=ControlledVisual\nm_ammoVisualName=Ammo_Holder\nm_barrelVisualName=ReleasePoint", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "AmmoStockIn", "m_recommended": "", "m_defaultDrawerSet": "Turret:Action", "m_description": "Full Automatic Trebuchet", "m_usageMultiplier": "2.00", "m_basePrice": "100.00"}, {"id": "682b5a2a6e1a7e02d1739e8c", "m_name": "TurretCooldownEnhancement", "m_title": "<PERSON><PERSON>ack<PERSON>", "m_buildingTitle": "", "m_class": "BCTurretEnhancement", "m_field": "m_explosionRadiusMultiplier=1\nm_explosionForceMultiplier=1\nm_damageMultiplier=1\nm_cooldownMultiplier=0.9", "m_blockSprite": "", "m_showInInfo": "True", "m_required": "", "m_recommended": "", "m_defaultDrawerSet": "Turret:Shafts", "m_description": "Reduces reload time between shots.", "m_usageMultiplier": "1.20", "m_basePrice": "10.00"}, {"id": "647098a8f967a506e512561a", "m_name": "Worker", "m_title": "Worker ", "m_buildingTitle": "", "m_class": "BCWorkers", "m_field": "m_maxWorkers=1", "m_blockSprite": "ComponentWorker", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "3.00"}, {"id": "67e2eb6804ccbb03173bb9e8", "m_name": "WorkerDouble", "m_title": "Double Worker ", "m_buildingTitle": "", "m_class": "BCWorkers", "m_field": "m_maxWorkers=2", "m_blockSprite": "ComponentWorker", "m_showInInfo": "True", "m_required": "Entrance", "m_recommended": "", "m_defaultDrawerSet": "", "m_description": "", "m_usageMultiplier": "1.75", "m_basePrice": "3.00"}]