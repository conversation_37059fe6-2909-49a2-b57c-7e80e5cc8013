[{"id": "6492c9ffb360e10027858ef9", "m_name": "002 First Order Peoples", "m_type": "Order", "m_giftTitle": "Peoples Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Commoners, 3)\nOrderReward(Cash, 175)\nOrderNutrition=0.2\nOrderQuantity=2\nOrderScore=0.3\nOrderBonusScore=0.2", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Test Description Peoples Order 1", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "1|Cake_v1_Topping_CheerWoman@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64c236e35100b90028072efa", "m_name": "A Variable Block stock", "m_type": "Building", "m_giftTitle": "to test blocks quickly", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "for block testing ", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "2|<PERSON>er_Door@1^-66^3^2=p37;^3=p37;^5=p37;^|Harvester_Roof_Pigment@1^-66^3^1=p37;^4=p37;^5=p23;^|1|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68247c9f92ff2e02fc928acf", "m_name": "BagOfLordsFavours", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag of Lords Favours", "m_cardTitle": "Expansion", "m_cardPower": "Lords", "m_cardPrice": 0, "m_quantity": 5, "m_spritePath": "LordsFavourBag", "m_description": "A bag of Lords Favours", "m_power": "Lords", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67ba3877b9df840a6df0c828", "m_name": "BagOfMysticFavours", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag of Mystic Favours", "m_cardTitle": "Expansion", "m_cardPower": "Mystic", "m_cardPrice": 0, "m_quantity": 5, "m_spritePath": "MysticFavourBag", "m_description": "A bag of Mystic Favours", "m_power": "Mystic", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac7db287c36644b0cb2", "m_name": "BagOfPeoplesFavours", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag of Peoples Favours", "m_cardTitle": "Expansion", "m_cardPower": "Commoners", "m_cardPrice": 0, "m_quantity": 5, "m_spritePath": "PeoplesFavourBag", "m_description": "A bag of Peoples Favours", "m_power": "Commoners", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68247ec7babf5902c483f4db", "m_name": "BagOfRoyalFavours", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag of Royal Favours", "m_cardTitle": "Expansion", "m_cardPower": "Royal", "m_cardPrice": 0, "m_quantity": 5, "m_spritePath": "RoyalsFavourBag", "m_description": "A bag of Royal Favours", "m_power": "Royal", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67bdcc4eae21df032981c049", "m_name": "Beacon Base", "m_type": "Building", "m_giftTitle": "Beacon Base", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Beacon base", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase1x1Beacon|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67ed558c971d6d02ed89a692", "m_name": "Briar Lake Factory", "m_type": "Building", "m_giftTitle": "Factory Base", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A shiny new factory base", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "2|MABase4x2@|MA_Factory_Action@|1|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64c10a121b26fa0025325a89", "m_name": "Build Factory Plot MA", "m_type": "Building", "m_giftTitle": "Build A Factory", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Build A Factory", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "11|MABase5x4@|MA_Tom_Factory_Base@|MA_Tom_Factory_Base@|MA_Tom_Factory_Base@|MA_Tom_Factory_Door_A@|MA_Tom_Factory_Window_C@|MA_Tom_Factory_Base@|MA_Tom_Factory_Roof_Chimney@|Harvester_Roof_Stock@|Factory_Input@|Factory_Output@|10|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|********.-90|*******.0|*******.0|********.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66cdb743a49ba502866a6f50", "m_name": "Build Moat Armourer", "m_type": "Building", "m_giftTitle": "<PERSON><PERSON><PERSON>", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Equip your heroes with new Armour", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase3x2@|Factory_Input@|MA_Armourer_Window@|MA_Armourer_Door@|MA_Armourer_Shade@|MA_Armourer_RoofSide@|MA_Weaponsmith_RoofWindow@|MA_Armourer_Sign@|MA_Armourer_Roof_Chimney@|8|*******.0|*******.0|*******.0|*******.90|*******.0|*******.180|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649d6701d9d865002804db01", "m_name": "Build Moat Clay Mine", "m_type": "Building", "m_giftTitle": "Placeholder Clay mine", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder Clay Mine", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase3x2@|Harvester_Clay_Rubble@|Harvester_Clay_Tap@|Harvester_Clay_Processor@|Harvester_Door@|Harvester_Clay_Topper@|MA_Produce_Mine_Rustic_Roof@|Factory_Output@|Harvester_Clay_Hotspot@|8|*******.0|*******.0|*******.90|*******.0|*******.-90|*******.90|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "65783a1599f77c00287caba2", "m_name": "Build Moat Cloth Mine", "m_type": "Building", "m_giftTitle": "Cloth region test mine", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder Cloth mine for testing ", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "10|MABase5x4@|MA_Farm_Door_CM@|MA_Farm_Window@|MA_Farm_Window@|Harvester_Cotton_Processor@|Harvester_Cotton_Hotspot@|MA_Farm_RoofTop@|Factory_Output@|Harvester_Cotton_Conveyor@|Harvester_Cotton_Topper@|9|*******.0|*******0.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.90|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "65783a27900a2a00287d9498", "m_name": "Build Moat Cloth Smelter", "m_type": "Building", "m_giftTitle": "Cloth region test smelter", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder Cloth mine for testing ", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase5x4@|MA_Produce_Mill_Door_CS@|Factory_Output@|MA_Produce_Mill_Block@|Harvester_Cotton_Conveyor@|Factory_Input@|MA_Produce_Mill_Thatch@|Harvester_Cotton_Processor_Smelter@|Harvester_Cotton_Topper_Smelter@|8|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.-180|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "666c1af73803e0026bac44cc", "m_name": "Build Moat Combi Metal Smelter", "m_type": "Building", "m_giftTitle": "Combined Metal mine & Smelter", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder Metal Mine & Smelter", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "12|MABase4x3@|Factory_Output@|MA_MetalMine_MetalMine_A@|MA_MetalMine_MetalSmelter_A@|MA_MetalMine_MetalSmelter_C@|MA_MetalMine_Door_A@|MA_MetalMine_Window_A@|MA_MetalMine_MetalMine_B@|MA_MetalMine_MetalSmelter_B@|MA_MetalMine_Window_A@|MA_MetalMine_Roof_B@|MA_MetalMine_Roof_A@|11|*******.0|*******.-90|*******.90|*******.90|********.0|*******.0|*******.180|*******.-90|*******.0|********.-180|********.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67d9602988de20031cbd4694", "m_name": "Build Moat Cotton Mill", "m_type": "Building", "m_giftTitle": "Cotton Mill", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Cotton mill/spinner", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase3x2@|MA_CottonMill_Window@|MA_CottonMill_Door@|MA_CottonMill_Window@|MA_CottonMill_Action@|MA_CottonFarm_Roof@|MA_CottonFarm_Roof@|MA_CottonMill_DomeRoof@|7|*******.-180|*******.180|*******.180|*******.0|*******.0|*******.0|*******.-180|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67daa8a9769f6a02cd57abf7", "m_name": "Build Moat Cotton Tavern", "m_type": "Building", "m_giftTitle": "Cotton Tavern", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Cotton Tavern", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase3x1@|MA_Tavern_BriarLake_Arch@|MA_Tavern_BriarLake_Door@|MA_Tavern_BriarLake_Door_Stairwell@|MA_Tavern_BriarLake_Window_A@|MA_Tavern_BriarLake_Window_B@|MA_Tavern_BriarLake_Roof_B@|MA_Tavern_BriarLake_Roof_A@|MA_Tavern_BriarLake_Sign@|8|*******.-180|*******.-180|*******.-180|*******.-90|*******.-180|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6511869847beda0027bdf849", "m_name": "Build Moat Crypt", "m_type": "Building", "m_giftTitle": "A Scary Crypt (Boo!)", "m_cardTitle": "Expansion", "m_cardPower": "NGBank", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A crypt that spawns Creatures to pillage your town", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "2|MABase1x1@|MA_CryptDoor@|1|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "63415d533843030021a6924b", "m_name": "Build Moat Dispatch", "m_type": "Building", "m_giftTitle": "Oakridge Delivery Stable", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "For delivering orders", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "10|MABase3x1@|MA_Dispatch_Foundation_Stair_B@|MA_Dispatch_Foundation_Stair_A@|MA_Dispatch_Foundation_Stair_B@|Factory_Input@|MA_Dispatch_Door@|MA_OrderBoard@|MA_Dispatch_Window@|MA_Dispatch_Clock@|MA_Dispatch_RoofTileTop@|9|*******.90|*******.0|*******.-90|*******.0|*******.0|*******.90|*******.-90|*******.0|*******.180|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67ea6eea09960d02d2e559ab", "m_name": "Build Moat Dispatch Cotton", "m_type": "Building", "m_giftTitle": "Briar Lakes Dispatch", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Dispatch For Cloth Based Products", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase3x1@|Factory_Input@|MA_Dispatch_DoorCotton@|MA_OrderBoard_Cotton@|MA_Tavern_BriarLake_Window_B@|MA_Dispatch_Clock@|MA_Tavern_BriarLake_Roof_B@|MA_Dispatch_Clock@|6|*******.0|*******.0|*******.0|*******.0|*******.0|*******.180|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6793a9b197659c031460e1d0", "m_name": "Build Moat Dispatch Metal", "m_type": "Building", "m_giftTitle": "Wrymscar Delivery Stable", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "For delivering metal orders", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase3x1@|Factory_Input@|MA_Dispatch_Metal_Shade@|MA_Dispatch_Metal_Door@|MA_Dispatch_Metal_Window@|MA_OrderBoard_Metal@|MA_Dispatch_Clock@|MA_Dispatch_Metal_Roof@|7|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6793a9e20016f302aa25d9fc", "m_name": "Build Moat Dispatch Swamp", "m_type": "Building", "m_giftTitle": "Briarwood Delivery Stable", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "For delivering orders", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase3x1@|MA_Dispatch_Swamp_Foundation_Stair@|MA_Dispatch_Swamp_Door@|MA_Dispatch_Swamp_Foundation@|MA_Dispatch_Swamp_Window@|MA_OrderBoard@|MA_Dispatch_Clock@|MA_Dispatch_Swamp_Roof@|7|*******.90|*******.0|*******.0|*******.0|*******.0|*******.0|*******.-90|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649d636513f475002ab50308", "m_name": "Build Moat Factory", "m_type": "Building", "m_giftTitle": "Placeholder Factory", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "the big factory from oakvale", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "24|MABase4x3@|MA_FactoryWindowVents@|Factory_Output@|MA_FactoryVents@|MA_FactoryVents@|MA_FactoryWindows_Half@|MA_FactoryWindows_Half@|Factory_Input@|MA_FactoryDooArchBase@|MA_FactoryWindows_8@|MA_FactoryWindowArches_Worker@|MA_FactoryWindowArches_Worker@|MA_FactoryRoofSlantHalf_A1@|MA_FactoryRoofSlantHalf_A1@|MA_FactoryDoorArchTop@|MA_FactoryWindows_8@|MA_FactoryWindowArches_Worker@|MA_FactoryWindowArches_Worker@|MA_FactoryWindowArches@|MA_FactoryRoofSlant_B1@|MA_FactoryRoofSemi_A@|MA_FactoryRoofSemi_A@|MA_FactoryRoofSlant_B1@|MA_FactoryChimA@|23|*******.0|*******.0|*******.0|*******.0|********.-90|*******.0|*******.0|*******.0|*******.0|********.0|********.0|********.0|********.0|********.0|********.0|*********.0|*********.0|*********.0|*********.90|*********.0|*********.0|*********.90|*********.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64930c54b360e10027880cc2", "m_name": "Build Moat Factory latest", "m_type": "Building", "m_giftTitle": " Factory Latest version", "m_cardTitle": "Expansion", "m_cardPower": "NGFactory", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Factory latest", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase4x2@|Factory_Input@|MA_Factory_Action@|MA_FactoryDoorArchBase@|Factory_Output@|MA_FactoryRoofSemi_A@|MA_FactoryRoofSemi_AWkr@|MA_FactoryChimB@|InfoBoardOrder@|8|*******.0|*******.0|*******.0|*******.0|5.*******|6.*******80|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649d5f53d4469c00299f799d", "m_name": "Build Moat Farm", "m_type": "Building", "m_giftTitle": "Farm placeholder", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Farm placeholder", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "13|MABase5x4@|MA_Farm_Door@|MA_Farm_Shade@|MA_Farm_Hotspot@|MA_Farm_Hotspot_Vegs@|MA_Farm_RoofWindowBig@|MA_Farm_Window@|Factory_Output@|MA_Farm_Storage@|MA_Farm_Hotspot@|MA_Farm_RoofTop@|MA_Farm_SideRoof@|MA_Farm_Shade@|12|*******.0|*******.0|*********.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|********.0|********.180|********.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "651558cc7bde260028d6f945", "m_name": "Build Moat Graveyard", "m_type": "Building", "m_giftTitle": "A Scary Graveyard", "m_cardTitle": "Expansion", "m_cardPower": "NGBank", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A graveyard that spawns C<PERSON>tures to eat your workers", "m_power": "Build(NGBank)", "m_componentsToUpgrade": "", "m_buildingDesign": "2|MABase5x5@|MA_CryptGraveyard@|1|*******3.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6728bcfe4d9be802bf20041f", "m_name": "Build Moat Heroes Guild", "m_type": "Building", "m_giftTitle": "Heroes guild", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Heroes Guild", "m_power": "Build(NGHouse)", "m_componentsToUpgrade": "", "m_buildingDesign": "6|MABase2x2@|MA_HeroesGuild_Door@|MA_HeroesGuild_Healing@|MA_HeroesGuild_Bedroom@|MA_HeroesGuild_Training@|MA_HeroesGuild_Roof@|5|*******.0|*******.0|*******.0|*******.0|*******.180|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "63415cb00a9eaf002429409e", "m_name": "Build Moat House ", "m_type": "Building", "m_giftTitle": "Oakridge Cottage", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A Traditional Oakridge Cottage", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "4|MABase1x1@|MA_WorkerHouse_Door_E@|MA_WorkerHouse_RoofWindow_A@|MA_WorkerHouse_ThatchedRoofTop@|3|*******.0|*******.0|*******.-90|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6542304ae82ce80027253fe5", "m_name": "Build Moat House 2", "m_type": "Building", "m_giftTitle": "Oakridge Cottage 2", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Another Traditional Oakridge Cottage", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "5|MABase2x1@|MA_WorkerHouse_Shade@|MA_WorkerHouse_Door_A@|MA_WorkerHouse_RoofWindow_A@|MA_WorkerHouse_RoofTileTopChimney_B@|4|*******.180|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "65422df9ea1c490029a1cc56", "m_name": "Build Moat Hungalow", "m_type": "Building", "m_giftTitle": "Oakridge Bungalow", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A Traditional Oakridge Bungalow", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "5|MABase2x1@|MA_WorkerHouse_Door_C@|MA_WorkerHouse_Window_A@|MA_WorkerHouse_RoofTileTop_B@|MA_WorkerHouse_RoofTileTopChimney_A@|4|*******.0|*******.0|*******.-90|*******.-90|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "65439f44cb5a9b0029d02468", "m_name": "Build Moat Hungalow 2", "m_type": "Building", "m_giftTitle": "Another Oakridge Bungalow", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Another Traditional Oakridge Bungalow", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "5|MABase2x1@|MA_WorkerHouse_Door_B@|MA_WorkerHouse_ThatchedRoofChimney@|MA_WorkerHouse_Window_A@|MA_WorkerHouse_ThatchedRoofTop@|4|*******.0|*******.-90|*******.0|*******.-90|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66a385ac72b0910027ff9e9d", "m_name": "Build Moat LumberMill", "m_type": "Building", "m_giftTitle": "Moat Lumbermill", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "moat lumbermill", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase4x1@|MA_LumberMill_Door@|MA_LumberMill_Storage@|MA_LumberMill_Wood_Processor@|Factory_Output@|MA_LumberMill_Window@|MA_LumberMill_RoofSide@|MA_LumberMill_RoofTop@|7|*******.180|*******.-90|*******.180|*******.0|*******.0|*******.-90|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6548e3a3b030af0026f8668f", "m_name": "Build Moat Metal Mine", "m_type": "Building", "m_giftTitle": "Metal region test mine", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder metal mine for testing worker travel distances", "m_power": "Build(NGHouse)", "m_componentsToUpgrade": "", "m_buildingDesign": "11|MABase2x2@|MA_Tavern_Door_A@|MA_WorkerHouse_Metal_Window_A@|MA_WorkerHouse_Metal_Door_B@|MA_WorkerHouse_Metal_Roof_A@|MA_WorkerHouse_Metal_Roof_D@|MA_WorkerHouse_Metal_Door_A@|MA_WorkerHouse_Metal_Roof_D@|MA_Tavern_Cellar@|MA_Tavern_Sign@|MA_Tavern_Sign@|8|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "656f3e95eeb88f0028c2a5c1", "m_name": "Build Moat Metal Smelter", "m_type": "Building", "m_giftTitle": "Placeholder Metal Smelter", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder metal smelter ", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase3x2@|Factory_Output@|MA_MetalMine_MetalSmelter_A@|Factory_Input@|MA_MetalMine_MetalSmelter_C@|MA_MetalMine_Window_A@|MA_MetalMine_MetalSmelter_B@|MA_MetalMine_Roof_A@|7|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6553809711be4100280a2b65", "m_name": "Build Moat Mill", "m_type": "Building", "m_giftTitle": "Oakridge Mill", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder Produce Mine for testing worker travel distances", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "12|MABase5x2@|MA_Produce_Mill_Door@|MA_Produce_Mill_Block@|Factory_Input@|MA_Produce_Mill_Mid@|MA_Produce_Mill_Dispatch@|MA_Produce_Mill_Thatch@|MA_Produce_Smelter_Rustic_WaterWheel_New@|MA_Produce_Mill_Top@|Factory_Output@|MA_Produce_Mill_Deck@|MA_Produce_Mill_Deck@|11|*******.0|*******.0|*******.0|*******.-180|*******.0|*******.0|*******.0|*******.180|*******.0|********.0|*********.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66697eceb5229600285793ab", "m_name": "Build Moat Shop", "m_type": "Building", "m_giftTitle": "Shop placeholder", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Moat shop placeholder", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase3x2@|Factory_Input@|MA_Shop_Window@|MA_Shop_Door@|MA_Shop_Bazaar@|MA_Shop_RoofWindow@|MA_Shop_Sign_Food@|MA_Shop_RoofSide@|MA_Shop_Roof_Chimney@|8|*******.0|*******.0|*******.0|*******.-90|*******.-180|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6717ceaf0df3a8031339f280", "m_name": "Build Moat Small Factory", "m_type": "Building", "m_giftTitle": "A small Factory", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A small factory", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase4x1@|Factory_Output@|MA_FactoryDoorArchBase@|MA_Factory_Action@|Factory_Input@|MA_FactoryRoofSemi_AWkr@|MA_FactoryRoofSemi_AWkr@|MA_FactoryChimB@|InfoBoardOrder@|8|*******.0|*******.0|*******.0|*******.0|*******.0|6.*******80|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "63415b7a0a9eaf0024293dec", "m_name": "Build Moat Smelter", "m_type": "Building", "m_giftTitle": "Oakridge Mill", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Refines raw produce from the farm for use in the factory.", "m_power": "Build(NGProduceSmelter)", "m_componentsToUpgrade": "", "m_buildingDesign": "11|MABase5x1@|MA_Produce_Mill_Door@|MA_Produce_Mill_Block@|Factory_Input@|MA_Produce_Mill_Mid@|MA_Produce_Mill_Dispatch@|MA_Produce_Mill_Thatch@|MA_Produce_Smelter_Rustic_WaterWheel_New@|MA_Produce_Mill_Top@|MA_Produce_Mill_Deck@|Factory_Output@|10|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|********.-180|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "63176c9f11054f0021a50ba8", "m_name": "Build Moat Tavern Farm", "m_type": "Building", "m_giftTitle": "Moat FarmTavern", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Farm Tavern", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "11|MABase2x2@|MA_Tavern_Door_B@|MA_Tavern_Door_A@|MA_Tavern_Cellar@|MA_Tavern_RoofTileTop_A@|MA_Tavern_Door_B@|MA_Tavern_RoofWindowBig@|MA_Tavern_Sign@|MA_Tavern_RoofTileTop_B@|MA_Tavern_RoofTileTop_Chimney@|MA_Tavern_Sign@|10|*******.90|*******.0|*******.0|4.*******80|*******.0|*******.-90|*******.0|*******.90|*******.90|********.0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6696409fb434da0028c137cb", "m_name": "Build Moat Tavern Metal", "m_type": "Building", "m_giftTitle": "Moat Metal Tavern", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Metal Tavern", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase2x2@|MA_Tavern_Cellar@|MA_Tavern_Metal_Window_A@|MA_Tavern_Metal_Door_A@|MA_Tavern_Metal_Roof_A@|MA_Tavern_Metal_Window_A@|MA_Tavern_Metal_Roof_A@|MA_Tavern_Metal_Sign@|6|*******.-180|*******.180|*******.180|*******.180|*******.180|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "679772ab50866a031f3b0edc", "m_name": "Build Moat Tavern Swamp", "m_type": "Building", "m_giftTitle": "Moat Swamp Tavern", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Swamp Tavern", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "13|MABase2x2@|MA_Tavern_Swamp_Door@|MA_Tavern_Cellar@|MA_Tavern_Swamp_Wndow@|MA_Tavern_Swamp_Wndow@|MA_Tavern_Swamp_Wndow@|MA_Tavern_Swamp_RoofSide@|MA_Tavern_Swamp_RoofSide@|MA_Tavern_Swamp_Sign@|MA_Tavern_Swamp_RoofTop@|MA_Tavern_Swamp_Sign@|MA_Tavern_Sign@|MA_Tavern_Sign@|10|*******.-180|*******.-180|*******.-180|*******.-180|*******.-180|*******.0|*******.-180|*******.0|*******.180|********.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66bf5a2512994502851ee901", "m_name": "Build Moat Weaponsmith", "m_type": "Building", "m_giftTitle": "Weaponsmith", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Equip your heroes with new weapons.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase4x1@|MA_Weaponsmith_Window@|MA_Weaponsmith_Door@|MA_Weaponsmith_Shade@|Factory_Input@|MA_Weaponsmith_RoofWindow@|MA_Weaponsmith_RoofSide@|MA_Weaponsmith_Roof_Chimney@|MA_Weaponsmith_Sign@|8|*******.90|*******.0|*******.0|*******.0|*******.180|*******.0|*******.90|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6578382e99fdc20027bec45f", "m_name": "Build Moat Wood Mine", "m_type": "Building", "m_giftTitle": "Wood Region Test Mine", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder wood mine for testing ", "m_power": "Build(NGHouse)", "m_componentsToUpgrade": "", "m_buildingDesign": "8|MABase5x4@|MA_Produce_Mill_Block@|MA_Farm_Door_WM@|Harvester_Wood_Processor_Mine@|Harvester_Wood_Hotspot@|Factory_Output@|MA_Farm_RoofTop@|Harvester_Wood_Topper_Mine@|7|*******.0|*******.0|*******.0|*******.0|*******.0|*******.-180|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "65783928dba78f0026adbb87", "m_name": "Build Moat Wood Smelter", "m_type": "Building", "m_giftTitle": "Wood Region Test Smelter", "m_cardTitle": "Expansion", "m_cardPower": "NGHouse", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Placeholder wood smalter for testing ", "m_power": "Build(NGHouse)", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase5x4@|MA_Produce_Mill_Door_WS@|Factory_Output@|MA_Produce_Mill_Block@|Harvester_Wood_Processor@|MA_Produce_Mill_Mid@|Factory_Input@|MA_Produce_Mill_Thatch@|Harvester_Wood_Topper_Mine@|8|*******.0|2.0.0.5.0|*******.0|*******.0|5.4.2.2.0|6.3.3.4.0|7.3.3.0.90|8.0.4.0.0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "687cdbfd0c4fbb02d46d5ec2", "m_name": "CashGift", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "A wad of [Value] cash;", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 100, "m_spritePath": "MA_Gold_Coins", "m_description": "A wad of cash", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+100", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67fbd9567577bf02d5e95d40", "m_name": "Clive<PERSON>ineWorkerKey", "m_type": "ParserCall", "m_giftTitle": "Crystal Cave Key", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "659d2b36b585f50026c39c30", "m_name": "Cotter Plot 2x1", "m_type": "Building", "m_giftTitle": "<PERSON><PERSON>'s 2x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "House|Entrances", "m_cardPrice": 150, "m_quantity": 1, "m_spritePath": "MABase2x1", "m_description": "A 2x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase2x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "659d2b45d7a73e0028cf08b0", "m_name": "Cotter Plot 3x1", "m_type": "Building", "m_giftTitle": "<PERSON><PERSON>'s 3x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "House|Entrances", "m_cardPrice": 100, "m_quantity": 1, "m_spritePath": "MABase3x1", "m_description": "A 3x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase3x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "659e90c9b585f50026d3ac68", "m_name": "CottersLandDeed", "m_type": "Unlock", "m_giftTitle": "Mining Land Deed", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 500, "m_quantity": 1, "m_spritePath": "landdeed", "m_description": "The deed to the land in which the Metal Mine resides", "m_power": "Unlock(District14)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "673b4ef5c5b50502f7614f92", "m_name": "CottersMealDeal", "m_type": "Unlock", "m_giftTitle": "Expansion Land Deed", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "landdeed", "m_description": "Gain access to more of Oakridge, uncover the ramshackle home of Thomas & Edith Cotter.", "m_power": "UnlockRegion(District14)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac6db287c36644b0c93", "m_name": "CurrencyMoneyMed", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "A lot of cash", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": "", "m_quantity": 150, "m_spritePath": "MA_Gold_Coins", "m_description": "Plenty money.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+150", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac6db287c36644b0c98", "m_name": "C<PERSON>rencyMoneySmall", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "A wad of cash", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": "", "m_quantity": 50, "m_spritePath": "MA_Gold_Coins", "m_description": "Well wadda ya know, cash money doesn't grow.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+50", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67eff911eacaea02cf924a2a", "m_name": "dig1000", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Buried Treasure!", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1000, "m_spritePath": "treasurechest", "m_description": "How long was that there waiting to be stole...discovered?", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+1000", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67efff2ceacaea02cf925db6", "m_name": "dig1500", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Buried Treasure!", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1500, "m_spritePath": "treasurechest", "m_description": "How long was that there waiting to be stole...discovered?", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+1500", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649bf5ef5ff0510028ae5db6", "m_name": "dig500", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Buried Treasure!", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 500, "m_spritePath": "treasurechest", "m_description": "How long was that there waiting to be stole...discovered?", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+150", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67eff514d5412002c72ef3c9", "m_name": "dig750", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Buried Treasure!", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 750, "m_spritePath": "treasurechest", "m_description": "How long was that there?", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+750", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67c5a133cdfb9402c526b7d6", "m_name": "EvilRing", "m_type": "Unlock", "m_giftTitle": "Evil Ring", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "ring", "m_description": "", "m_power": "GiveRing(Evil)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac7db287c36644b0cac", "m_name": "Fill Mine Input Small", "m_type": "UpgradeBuilding", "m_giftTitle": "Small Materials Delivery", "m_cardTitle": "Investment", "m_cardPower": "Fill_Input", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Get a helping hand filling up your mines, farms, forests and all other raw resource producers.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "(Number)NumOutputs+10", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64afe3cf1f1a12002a09df11", "m_name": "First Order Royal", "m_type": "Order", "m_giftTitle": "Royal Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=Weapons\nOrderReward(Royal, 3)\nOrderReward(Cash, 1000)\nOrderNutrition=0.2\nOrderQuantity=10\nOrderScore=0.3\nOrderBonusScore=0.2", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Test Description Royal Order swords", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "1|Cake_Topping_Crown@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67f688c6bd92da02c8969729", "m_name": "GarrickFrescoPt1", "m_type": "ParserCall", "m_giftTitle": "A Meeting of Two Minds", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "", "m_description": "<PERSON><PERSON><PERSON>, the scourge, gives information about <PERSON><PERSON> and his movements.", "m_power": "ShowFresco(\"Bandit_Intro\")", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f85a02b66ebb0027ddb5ac", "m_name": "Gemina's Great Feat", "m_type": "Order", "m_giftTitle": "Royal Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Royal, 3)\nOrderReward(Cash, 350)\nOrderNutrition=0.3\nOrderQuantity=100\nOrderScore=0.4\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f84de3d76152002bc5c406", "m_name": "<PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON>", "m_type": "Order", "m_giftTitle": "Lords Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(<PERSON>, 3)\nOrderReward(Cash, 250)\nOrderNutrition=0.2\nOrderQuantity=32\nOrderScore=0.3\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67c5a1cfcdfb9402c526bd1c", "m_name": "GoodRing", "m_type": "Unlock", "m_giftTitle": "Good Ring", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "ring", "m_description": "", "m_power": "GiveRing(Good)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f85eb7ce58f4002900d0e7", "m_name": "Hansard's Harvest Hamper", "m_type": "Order", "m_giftTitle": "Royal Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Royal, 3)\nOrderReward(Cash, 410)\nOrderNutrition=0.3\nOrderQuantity=45\nOrderScore=0.5\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "682364908b08f303020013b3", "m_name": "Hero Chest 01", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Hero Treasure!", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 150, "m_spritePath": "treasurechest", "m_description": "Treasure Fit For A Hero", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68236a286a9dbd02fbbbd68f", "m_name": "Hero Chest 02", "m_type": "Block", "m_giftTitle": "Hero Treasure!", "m_cardTitle": "Expansion", "m_cardPower": "MA_HeroesGuild_Training\nMA_HeroesGuild_Healing", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "treasurechest", "m_description": "", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6826099fee75e102be32bb9d", "m_name": "HeroChest100", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 100, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68248166babf5902c483ff0c", "m_name": "HeroChest1000", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 1000, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+1000", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6824817bcf25ce0303a72684", "m_name": "HeroChest1500", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 1500, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+1500", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6824818bd9dfcb02bd7652ff", "m_name": "HeroChest2000", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 2000, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+2000", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6826097102ad8e0acef103f3", "m_name": "HeroChest250", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 250, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac6db287c36644b0c8f", "m_name": "HeroChest500", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 500, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+500", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68248157f8255a030fd01817", "m_name": "HeroChest750", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Bag Of Money", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 750, "m_spritePath": "MA_Gold_Coins", "m_description": "More cash than your wallet can handle.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+750", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67d09884515c1402f7781e7c", "m_name": "HippyRevelation1", "m_type": "ParserCall", "m_giftTitle": "The Unsanitary Truth", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "", "m_description": "knowledge from the piss pot boy", "m_power": "ShowFresco(“Malmus_Intro”)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f84e916a1ca20028516b54", "m_name": "Humbolt's Hunger Hash", "m_type": "Order", "m_giftTitle": "Lords Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(<PERSON>, 3)\nOrderReward(Cash, 400)\nOrderNutrition=0.2\nOrderQuantity=40\nOrderScore=0.3 \nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f8598cdbeb2600284898c2", "m_name": "<PERSON><PERSON><PERSON>'s Imbibable Delicacy", "m_type": "Order", "m_giftTitle": "Lords Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(<PERSON>, 3)\nOrderReward(Cash, 200)\nOrderNutrition=0.4\nOrderQuantity=15\nOrderScore=0.4\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f85ef15cd80c0027114c3e", "m_name": "india's Indecent Eats", "m_type": "Order", "m_giftTitle": "Royal Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Royal, 3)\nOrderReward(Cash, 460)\nOrderNutrition=0.3\nOrderQuantity=98\nOrderScore=0.4\nOrderBonusScore=0.2", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6391ed5dbe0bc40021949fee", "m_name": "Input Stock 2", "m_type": "Block", "m_giftTitle": "Input Stock 2", "m_cardTitle": "Expansion", "m_cardPower": "FactoryChimney_Medium", "m_cardPrice": 5, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "FactoryChimney_Medium", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6391dbbe48ec4500214c4539", "m_name": "InputStockBlock", "m_type": "Block", "m_giftTitle": "Input Stock Block", "m_cardTitle": "Expansion", "m_cardPower": "FactoryWindow_Clothing", "m_cardPrice": 100, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "FactoryWindow_Clothing", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f859a7c42932002848f288", "m_name": "Jaunt<PERSON>'s <PERSON><PERSON><PERSON>", "m_type": "Order", "m_giftTitle": "Lords Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Lords, 3)\nOrderReward(Cash, 500)\nOrderNutrition=0.4\nOrderQuantity=45\nOrderScore=0.4\nOrderBonusScore=0.2", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f85f02d73d920026c26680", "m_name": "<PERSON><PERSON><PERSON>'s Just <PERSON>", "m_type": "Order", "m_giftTitle": "Royal Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Royal, 3)\nOrderReward(Cash, 666)\nOrderNutrition=0.5\nOrderQuantity=120\nOrderScore=0.7\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f85f27dbeb26002848ddd5", "m_name": "Knewton's <PERSON><PERSON><PERSON>bbles", "m_type": "Order", "m_giftTitle": "Royal Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Royal, 3)\nOrderReward(Cash, 1200)\nOrderNutrition=0.8\nOrderQuantity=200\nOrderScore=0.9\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f859cbee084000274aa9ae", "m_name": "<PERSON><PERSON><PERSON>'s <PERSON><PERSON>", "m_type": "Order", "m_giftTitle": "Lords Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(<PERSON>, 3)\nOrderReward(Cash, 1050)\nOrderNutrition=0.7\nOrderQuantity=27\nOrderScore=0.6\nOrderBonusScore=0.2\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67d095770e71ac02c4f47447", "m_name": "LostBoySecret", "m_type": "ParserCall", "m_giftTitle": "Lost Boy's Secret", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": "", "m_spritePath": "", "m_description": "Deep knowledge of the evils", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649992b913f475002a93d891", "m_name": "MA 1x1 Plot", "m_type": "Building", "m_giftTitle": "1x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase1x1", "m_description": "A 1x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase1x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64afbf781060ba00275c3a42", "m_name": "MA 1x2 Plot", "m_type": "Building", "m_giftTitle": "1x2 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase1x2", "m_description": "A 1x2 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase1x2|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649993dc13f475002a93ec8f", "m_name": "MA 2x1 Plot", "m_type": "Building", "m_giftTitle": "2x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase2x1", "m_description": "A 2x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase2x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649994699fb60e0029c92bb5", "m_name": "MA 2x2 Plot", "m_type": "Building", "m_giftTitle": "2x2 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase2x2", "m_description": "A 2x2 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase2x2|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6500441d16088e0027c3b03f", "m_name": "MA 3x1 Plot", "m_type": "Building", "m_giftTitle": "3x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase3x1", "m_description": "A 3x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase3x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6499954775769b00269352f7", "m_name": "MA 3x2 Plot", "m_type": "Building", "m_giftTitle": "3x2 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase3x2", "m_description": "A 3x2 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase3x2|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "649995bb6509730029cb0704", "m_name": "MA 3x3 Plot", "m_type": "Building", "m_giftTitle": "3x3 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase3x3", "m_description": "A 3x3 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase3x3|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66c4c10902eb660280f42371", "m_name": "MA 4x1 Plot", "m_type": "Building", "m_giftTitle": "4x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase4x1", "m_description": "A 4x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase4x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f9a9f763d49c0028916889", "m_name": "MA 4x2 Plot", "m_type": "Building", "m_giftTitle": "4x2 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase4x2", "m_description": "A 4x2 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase4x2|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "648b240238f17c0027e83195", "m_name": "MA 4x3 Plot", "m_type": "Building", "m_giftTitle": "4x3 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase4x3", "m_description": "A 4x3 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase4x3|0|", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "682f17050d568602f3d14eae", "m_name": "MA 4x4 Plot", "m_type": "Building", "m_giftTitle": "4x4 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase4x4", "m_description": "A 4x4 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase4x4|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f9a9a0f43542002762cd83", "m_name": "MA 5x1 Plot", "m_type": "Building", "m_giftTitle": "5x1 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase5x1", "m_description": "A 5x1 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase5x1|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f9a9ea360f7900273c5115", "m_name": "MA 5x2 Plot", "m_type": "Building", "m_giftTitle": "5x2 Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase5x2", "m_description": "A 5x2 Building Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase5x2|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "662a701ae5a10300260ed6cd", "m_name": "MA 5x5 Plot", "m_type": "Building", "m_giftTitle": "5x5 Graveyard Plot", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase5x5", "m_description": "A 5x5 Graveyard Plot", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase5x5|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "661516bd996b550026a22f1f", "m_name": "MA_Bread_LongLoaf", "m_type": "Block", "m_giftTitle": "Baguette", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 100, "m_quantity": 1, "m_spritePath": "", "m_description": "Rustic and versatile. This continental crust makes a fine accompaniment to many a stew and casserole. Plus, once stale, it doubles up as a fine baton for crowd control.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": "UnlockBlock(MA_Bread_LongLoaf)"}, {"id": "6723f02001760403031f4155", "m_name": "MalmusIntro", "m_type": "ParserCall", "m_giftTitle": "Know your enemy", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": "", "m_spritePath": "fresco2", "m_description": "This will show you a vision of your Enemy", "m_power": "ShowFresco(“Malmus_Intro”)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67f679f48f292802e9582624", "m_name": "MalmusIntroPt2", "m_type": "ParserCall", "m_giftTitle": "Round Two", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "fresco2", "m_description": "The end of the cleric's story.", "m_power": "ShowFresco(\"Malmus_Intro_Part2\")", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64a2c99986973900273af1dc", "m_name": "MA_Paints, Patterns & Stickers", "m_type": "Unlock", "m_giftTitle": "Paints, Patterns & Stickers", "m_cardTitle": "Investment", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "businessreward_paints", "m_description": "Unlock Paints, Patterns & Stickers", "m_power": "<PERSON>lock(Paints)|<PERSON><PERSON>(Patterns)|<PERSON>lock(Stickers)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6840064160bf570300d1036c", "m_name": "MA_PaintStarterPack", "m_type": "Unlock", "m_giftTitle": "Paint Pack", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MA_Gold_Coins", "m_description": "Unlock a pack of paints", "m_power": "UnlockProductPack(Moat Starter Pack)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68400b960652fd03132d07ec", "m_name": "MA_PatternStarterPack", "m_type": "Unlock", "m_giftTitle": "Pattern Pack", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MA_Gold_Coins", "m_description": "Unlock of a pattern package", "m_power": "UnlockProductPack(01_Patterns_Moat_Pack)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66d0982597374b03007a41f9", "m_name": "MA_RL_Armourer_Parts1", "m_type": "Block", "m_giftTitle": "Armourer Blocks", "m_cardTitle": "Expansion", "m_cardPower": "MA_Armourer_RoofSide\nMA_Armourer_Window\nMA_Armourer_Shade\nMA_Armourer_Roof_Chimney", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Armourer Building Blocks", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase3x2@|Factory_Input@|MA_Armourer_Window@|MA_Armourer_Door@|MA_Armourer_Shade@|MA_Weaponsmith_RoofWindow@|MA_Armourer_Sign@|MA_Armourer_RoofSide@|MA_Armourer_Roof_Chimney@|8|*******.0|*******.0|*******.0|*******.90|*******.180|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "670d2c107de9da02ec224ea3", "m_name": "MA_RL_BreadPack", "m_type": "Block", "m_giftTitle": "Bread Pack", "m_cardTitle": "Expansion", "m_cardPower": "MA_SlicedBread_A\nMA_Bread_LongLoaf\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Pack with different bread types.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Bread_LongLoaf@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "670d2c17a654b402f5bb7611", "m_name": "MA_RL_DairyPack", "m_type": "Block", "m_giftTitle": "Dairy Pack", "m_cardTitle": "Expansion", "m_cardPower": "MA_Dairy_Cheese\nMA_Dairy_CheeseSlice\nMA_Dairy_Egg\nMA_Dairy_StinkingCheese\nMA_Dairy_MilkJar", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Pack with different dairy products.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Dairy_StinkingCheese@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6380b419a239740021524ca7", "m_name": "MA_RL_FactoryBlock1", "m_type": "Block", "m_giftTitle": "Factory Worker Block", "m_cardTitle": "Expansion", "m_cardPower": "MA_FactoryWindowArches", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Factory block for research lab", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_FactoryWindowArches_Worker@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "63d3b39368ae4300112fa312", "m_name": "MA_RL_FarmBlock", "m_type": "Block", "m_giftTitle": "Factory blocks", "m_cardTitle": "Expansion", "m_cardPower": "MA_Farm_RoofWindowBig", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A Farm Block for the research lab", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Farm_RoofWindowBig@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6380b44899ea910021091f44", "m_name": "MA_RL_Houseblocks1", "m_type": "Block", "m_giftTitle": "House Blocks", "m_cardTitle": "Expansion", "m_cardPower": "MA_WorkerHouse_Door_C\nMA_WorkerHouse_Window_B\nMA_WorkerHouse_ThatchedRoofChimney", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "House blocks for the research lab", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "3|MA_WorkerHouse_Door_C@|MA_WorkerHouse_Window_B@|MA_WorkerHouse_ThatchedRoofChimney@|2|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "63d3b3bbcf419d0012be54c0", "m_name": "MA_RL_MillBlocks", "m_type": "Block", "m_giftTitle": "Mill Blocks", "m_cardTitle": "Expansion", "m_cardPower": "MA_Produce_Mill_Block\nMA_Produce_Mill_Dispatch", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Mill blocks for research lab", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Produce_Mill_Block@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "670d2c1e1b4fcf02adee21fd", "m_name": "MA_RL_PiesAndTartsPack", "m_type": "Block", "m_giftTitle": "Pies & Tarts Pack", "m_cardTitle": "Expansion", "m_cardPower": "MA_Pies_PieLid", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Pack with different pie and tarts parts.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Pies_PieLid@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "670d2c23c32001030478769a", "m_name": "MA_RL_SlicesPack", "m_type": "Block", "m_giftTitle": "Slices Pack", "m_cardTitle": "Expansion", "m_cardPower": "MA_SlicedBeef\nMA_Stock_Beef\nMA_SlicedWerewolf\nMA_SlicedOnion\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Pack with different slices.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_SlicedWerewolf@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b7de24de13702efc43722", "m_name": "MA_RL_Turret_Ballista", "m_type": "Building", "m_giftTitle": "Turret Weapon Upgrade Ballista", "m_cardTitle": "Expansion", "m_cardPower": "MA_Turret_Ballista\nMA_Turret_Ammo_Bolt_Wood", "m_cardPrice": 1, "m_quantity": 1, "m_spritePath": "", "m_description": "A ballista is a weapon with the combined elements of a large crossbow and a catapult", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Turret_Ballista@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b7f0c30bda802bf5bbb87", "m_name": "MA_RL_Turret_BallistaGiant", "m_type": "Building", "m_giftTitle": "<PERSON>rret Weapon Upgrade Giant Ballista", "m_cardTitle": "Expansion", "m_cardPower": "MA_Turret_BallistaGiant\nMA_Turret_Ammo_Bolt_Iron", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A Giant ballista is a ballista but gigantic (still fits on a turret)", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Turret_BallistaGiant@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6380b45c0bfc630022ed4dab", "m_name": "MA_RL_TurretBlocks", "m_type": "Block", "m_giftTitle": "Turret Blocks", "m_cardTitle": "Expansion", "m_cardPower": "MA_Turret_Cannon\nMA_Turret_Structure\nMA_Turret_Window", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Turret blocks for research lab", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "3|MA_Turret_Structure@|MA_Turret_Window@|MA_Turret_Cannon@|2|*******.180|*******.64|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b7fc3b15fa802d4875ea5", "m_name": "MA_RL_Turret_Catapult", "m_type": "", "m_giftTitle": "Turret Weapon Upgrade Catapult", "m_cardTitle": "Expansion", "m_cardPower": "MA_Turret_Catapult", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A catapult is a ballistic device used to launch a projectile at a great distance without the aid of gunpowder or other propellants ", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Turret_Catapult@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b806261db9702c4a94374", "m_name": "MA_RL_Turret_Trebuchet", "m_type": "", "m_giftTitle": "Turret Weapon Upgrade Trebuchet", "m_cardTitle": "Expansion", "m_cardPower": "MA_Turret_Trebuchet", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A trebuchet is a type of catapult that uses a hinged arm with a sling attached to the tip to launch a projectile. It was a common powerful siege engine until the advent of gunpowder.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Turret_Trebuchet@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "66d0983197374b03007a4204", "m_name": "MA_RL_Weaponary_Parts1", "m_type": "Block", "m_giftTitle": "Weaponsmith Sign", "m_cardTitle": "Expansion", "m_cardPower": "MA_Weaponsmith_Sign\nMA_Weaponsmith_Shade", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "A sign for your Weaponsmith.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MA_Weaponsmith_Sign@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "68400d35d3a37e02d11aa885", "m_name": "MA_StickerStarterPack", "m_type": "Unlock", "m_giftTitle": "<PERSON><PERSON> Starter Pack", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MA_Gold_Coins", "m_description": "Unlock a set of magical runes", "m_power": "UnlockProductPack(01_Stickers_Moat_Pack)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67486892c3d7e002d578763b", "m_name": "MA_UnlockCottersHouseBlocks", "m_type": "Block", "m_giftTitle": "Cotters House Pack", "m_cardTitle": "Expansion", "m_cardPower": "MA_WorkerHouse_Window_A\nMA_WorkerHouse_Door_B\nMA_WorkerHouse_RoofWindow_A\nMA_WorkerHouse_Door_A\nMA_WorkerHouse_Door_C\nMA_WorkerHouse_Door_E\nMA_WorkerHouse_Window_B\nMA_WorkerHouse_RoofTileTop_C\nMA_WorkerHouse_ThatchedRoofChimney\nMA_WorkerHouse_RoofTileTop_A\nMA_WorkerHouse_RoofWindow_B\nMA_WorkerHouse_ShadeThatched\nMA_WorkerHouse_Shade\n", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "unseen card", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "659c2b9378b11500289cdd53", "m_name": "Metal Region Land Deed", "m_type": "Unlock", "m_giftTitle": "Land Deed", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 1000, "m_quantity": 1, "m_spritePath": "", "m_description": "Use this to unlock the Metal Mine region!", "m_power": "UnlockRegion(Metal)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": "ShowLandDeed(Region[District11])"}, {"id": "685a6c266a339402ce0406d0", "m_name": "MH_Dispatch", "m_type": "Building", "m_giftTitle": "Oakridge Dispatch", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Oakridge Dispatch", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "10|MABase3x1@|MA_Dispatch_Foundation_Stair_B@|MA_Dispatch_Foundation_Stair_A@|MA_Dispatch_Foundation_Stair_B@|Factory_Input@|MA_Dispatch_Door@|MA_OrderBoard@|MA_Dispatch_Window@|MA_Dispatch_RoofTileTop@|MA_Dispatch_Clock@|9|*******.90|*******.0|*******.-90|*******.-90|*******.0|*******.90|*******.-90|*******.-180|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "685a6c005e68a71aa6b12a84", "m_name": "MH_Factory", "m_type": "Building", "m_giftTitle": "Oakridge Factory", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Oakridge Factory", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "9|MABase4x2@|InfoBoardOrder@|Factory_Output@|MA_FactoryDoorArchBase@|MA_Factory_Action@|Factory_Input@|MA_FactoryWindowArches_Worker@|MA_FactoryRoofSemi_A@|MA_FactoryChimA@|8|*******.0|*******.0|*******.0|*******.0|*******.0|6.*******|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "685a6ba85e68a71aa6b128db", "m_name": "MH_Farm", "m_type": "Building", "m_giftTitle": "Oakridge Farm", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Oakridge Farm", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "10|MABase4x2@|MA_Farm_Action@|MA_Farm_RoofWindowBig_NW@|MA_Produce_Smelter_Rustic_WaterWheel_New@|Factory_Output@|MA_Farm_Hotspot_Vegs@|MA_Farm_Door@|Factory_Input@|MA_Farm_SideRoof@|MA_Farm_RoofTop@|9|*******.-90|*******.0|*******.0|*******.0|*******.180|*******.0|*******.0|*******.0|*******.-180|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "685a6bce531ffe1ab6c5de49", "m_name": "MH_Mill", "m_type": "Building", "m_giftTitle": "Oakridge Mill", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Oakridge Mill", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "7|MABase4x2@|Factory_Output@|MA_Produce_Mill_Door@|MA_Produce_Mill_Action@|Factory_Input@|MA_Produce_Mill_Mid@|MA_Produce_Mill_Top@|6|*******.0|*******.0|*******.0|*******.0|*******.0|*******.0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67fbadde057ca3957028b8c3", "m_name": "MineWorkersKey", "m_type": "ParserCall", "m_giftTitle": "Mine Key", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "", "m_description": "", "m_power": "GiveKey(MineWorkersKey)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67c5a1a7c7ec770303443833", "m_name": "NeutralRing", "m_type": "Unlock", "m_giftTitle": "Neutral Ring", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "ring", "m_description": "", "m_power": "GiveRing(Neutral)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6617cffdb946e20028b6ac66", "m_name": "New Giant Hero", "m_type": "Hero", "m_giftTitle": "Giant Hero", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 1000, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "Giant", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64a691d5ec2ef40027b25d7c", "m_name": "New Worker", "m_type": "Worker", "m_giftTitle": "New Worker", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 1000, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "Worker", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b995aa3fb8b02f5c5a4e1", "m_name": "NightRewardCashGift", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Night Reward of [Value] cash.", "m_cardTitle": "Expansion", "m_cardPower": "Money", "m_cardPrice": 0, "m_quantity": 100, "m_spritePath": "MA_Gold_Coins", "m_description": "A wad of cash from completing a night time reward.", "m_power": "Cash", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "Money+100", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b9b8e61db9702c4a988a6", "m_name": "NightRewardLordsFavourGift", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Lords Favours from night rewards.", "m_cardTitle": "Expansion", "m_cardPower": "Lords", "m_cardPrice": 0, "m_quantity": 5, "m_spritePath": "LordsFavourBag", "m_description": "A bag of Lords Favours from night rewards.", "m_power": "Lords", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "688b9a6f30bda802bf5c04c0", "m_name": "NightRewardMA 2x2 Plot", "m_type": "Building", "m_giftTitle": "A 2x2 Plot; from a night reward", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "MABase2x2", "m_description": "A 2x2 Building Plot from a night reward.", "m_power": "", "m_componentsToUpgrade": "", "m_buildingDesign": "1|MABase2x2|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "689088f7c62d8d02cb78a8ce", "m_name": "NightRewardMysticFavourGift", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Mystic Favours from night rewards.", "m_cardTitle": "Expansion", "m_cardPower": "Mystic", "m_cardPrice": "", "m_quantity": 1, "m_spritePath": "MysticFavourBag", "m_description": "A bag of Mystic Favours from night rewards.", "m_power": "Mystic", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6890876c8f2d2e02faaf505b", "m_name": "NightRewardPeoplesFavourGift", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Peoples Favours from night rewards.", "m_cardTitle": "Expansion", "m_cardPower": "Commoners", "m_cardPrice": "", "m_quantity": 1, "m_spritePath": "PeoplesFavourBag", "m_description": "A bag of Peoples Favours from night rewards.", "m_power": "Commoners", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "689087e27cbbe302c4ee872a", "m_name": "NightRewardRoyalFavourGift", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_giftTitle": "Royal Favours from night rewards.", "m_cardTitle": "Expansion", "m_cardPower": "Royal", "m_cardPrice": "", "m_quantity": 1, "m_spritePath": "RoyalFavourBag", "m_description": "A bag of Royal Favours from night rewards.", "m_power": "Royal", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "67ee89e29c4f6e02c925d9f8", "m_name": "OrkenStoneFresco", "m_type": "ParserCall", "m_giftTitle": "A History of Albion", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": "", "m_spritePath": "HistoryOfAlbion", "m_description": "A revelation unearthed in an ancient tome.", "m_power": "ShowFresco(“OrkenStoneFresco”)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f705dbe837330026678391", "m_name": "<PERSON>ep<PERSON>'s <PERSON><PERSON><PERSON>ck ", "m_type": "Order", "m_giftTitle": "Peoples Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Commoners, 1)\nOrderReward(Cash, 250)\nOrderNutrition=0.2\nOrderQuantity=2\nOrderScore=0.1\nOrderBonusScore=0.2", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "Test Description Peoples Order 1a", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "1|Cake_v1_Topping_CheerWoman@|0|", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "64f756109004bd00275d3cf9", "m_name": "Qualag's Hot Supper", "m_type": "Order", "m_giftTitle": "Peoples Council Order", "m_cardTitle": "Expansion", "m_cardPower": "OrderType=WorkerFood\nOrderReward(Commoners, 2)\nOrderReward(Cash, 270)\nOrderNutrition=0.4\nOrderQuantity=10\nOrderScore=0.5\nOrderBonusScore=0.2", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "", "m_description": "", "m_power": "", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "False", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac7db287c36644b0cb7", "m_name": "Resource Gift", "m_type": "Unlock", "m_giftTitle": "Produce ", "m_cardTitle": "Investment", "m_cardPower": "", "m_cardPrice": "", "m_quantity": "", "m_spritePath": "", "m_description": "A bulk of raw resources.", "m_power": "Resource(RawMaterialProduce,15)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "6570562bf429670028131f83", "m_name": "UnlockProductLine (Weapons)", "m_type": "Unlock", "m_giftTitle": "Create Weapons", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 0, "m_quantity": 1, "m_spritePath": "businessreward_productline", "m_description": "Unlock a New product Line!", "m_power": "UnlockProductLine(Weapons)", "m_componentsToUpgrade": "ActionFactory", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": ""}, {"id": "62c41ac8db287c36644b0d06", "m_name": "Unlock Roads", "m_type": "Unlock", "m_giftTitle": "Unlock Road building", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 200, "m_quantity": "", "m_spritePath": "businessreward_road", "m_description": "New roads means new slots for your buildings to inhabit.", "m_power": "<PERSON>lock(Roads)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": "RoadBuilderTutorial"}, {"id": "62c41ac8db287c36644b0d0c", "m_name": "Unlock Stickers", "m_type": "Unlock", "m_giftTitle": "Unlock Stickers", "m_cardTitle": "Expansion", "m_cardPower": "", "m_cardPrice": 250, "m_quantity": 1, "m_spritePath": "businessreward_stickers", "m_description": "With this card you can use stickers in your designs.", "m_power": "<PERSON><PERSON>(Stickers)", "m_componentsToUpgrade": "", "m_buildingDesign": "", "m_upgradeImports": "", "m_canBePutInVault": "True", "m_giftFunction": "", "m_rewardFunction": "Stickers"}]