[{"id": "663264e45a356500279765d5", "m_indexer": "Dig1", "m_name": "Dig", "m_level": 1, "m_baseDamage": "5.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 0, "m_manaCostPerSecond": "", "m_baseCooldownTime": "1.00", "m_title": "Dig Level 1", "m_description": "Allows to dig out hidden treasures."}, {"id": "663265bb22d3d4002861c7d6", "m_indexer": "Dig2", "m_name": "Dig", "m_level": 2, "m_baseDamage": "10.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 0, "m_manaCostPerSecond": "", "m_baseCooldownTime": "1.00", "m_title": "Dig Level 2", "m_description": "Allows to dig out hidden treasures."}, {"id": "667156e01b64e20027ab95c0", "m_indexer": "Dig3", "m_name": "Dig", "m_level": 3, "m_baseDamage": "20.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 0, "m_manaCostPerSecond": "", "m_baseCooldownTime": "1.00", "m_title": "Dig Level 3", "m_description": "Allows to dig out hidden treasures."}, {"id": "667156eacd573c0028818422", "m_indexer": "Dig4", "m_name": "Dig", "m_level": 4, "m_baseDamage": "25.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 0, "m_manaCostPerSecond": "", "m_baseCooldownTime": "1.00", "m_title": "Dig Level 4", "m_description": "Allows to dig out hidden treasures."}, {"id": "667156f1f4f46300284efe68", "m_indexer": "Dig5", "m_name": "Dig", "m_level": 5, "m_baseDamage": "50.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 0, "m_manaCostPerSecond": "", "m_baseCooldownTime": "1.00", "m_title": "Dig Level 5", "m_description": "Allows to dig out hidden treasures."}, {"id": "663264b12d8ca300284476f0", "m_indexer": "Fireball1", "m_name": "Fireball", "m_level": 1, "m_baseDamage": "85.00", "m_minDamage": 10, "m_knockbackPower": "", "m_numOfHits": 2, "m_aoeRadius": "4.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 25, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "Fireball Level 1", "m_description": "Shoots a Fireball dealing AoE damage."}, {"id": "663265ec9d06d50028faadcd", "m_indexer": "Fireball2", "m_name": "Fireball", "m_level": 2, "m_baseDamage": "125.00", "m_minDamage": 20, "m_knockbackPower": "", "m_numOfHits": 3, "m_aoeRadius": "6.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 35, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "Fireball Level 2", "m_description": "Shoots a Fireball dealing AoE damage."}, {"id": "667157098fd53000282c90da", "m_indexer": "Fireball3", "m_name": "Fireball", "m_level": 3, "m_baseDamage": "150.00", "m_minDamage": 30, "m_knockbackPower": "", "m_numOfHits": 4, "m_aoeRadius": "8.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 45, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "Fireball Level 3", "m_description": "Shoots a Fireball dealing AoE damage."}, {"id": "6671570e5b6efe00260e2451", "m_indexer": "Fireball4", "m_name": "Fireball", "m_level": 4, "m_baseDamage": "200.00", "m_minDamage": 40, "m_knockbackPower": "", "m_numOfHits": 5, "m_aoeRadius": "10.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 50, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "Fireball Level 4", "m_description": "Shoots a Fireball dealing AoE damage."}, {"id": "6671571412df140027cefbf8", "m_indexer": "Fireball5", "m_name": "Fireball", "m_level": 5, "m_baseDamage": "225.00", "m_minDamage": 65, "m_knockbackPower": "", "m_numOfHits": 5, "m_aoeRadius": "15.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 75, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "Fireball Level 5", "m_description": "Shoots a Fireball dealing AoE damage."}, {"id": "66326368f041fa0029f07b21", "m_indexer": "Flamethrower1", "m_name": "Flamethrower", "m_level": 1, "m_baseDamage": "35.00", "m_minDamage": "", "m_knockbackPower": "1.00", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "3.00", "m_attackLength": "6.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "30.00", "m_baseCooldownTime": "0.01", "m_title": "Flamethrower Level 1", "m_description": ""}, {"id": "663263945b3d260028af9fb1", "m_indexer": "Flamethrower2", "m_name": "Flamethrower", "m_level": 2, "m_baseDamage": "45.00", "m_minDamage": "", "m_knockbackPower": "1.00", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "4.00", "m_attackLength": "8.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "40.00", "m_baseCooldownTime": "0.01", "m_title": "Flamethrower Level 2", "m_description": ""}, {"id": "6671572521702400274d1865", "m_indexer": "Flamethrower3", "m_name": "Flamethrower", "m_level": 3, "m_baseDamage": "60.00", "m_minDamage": "", "m_knockbackPower": "1.00", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "5.00", "m_attackLength": "10.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "50.00", "m_baseCooldownTime": "0.01", "m_title": "Flamethrower Level 3", "m_description": ""}, {"id": "6671572a9c3da80027faa805", "m_indexer": "Flamethrower4", "m_name": "Flamethrower", "m_level": 4, "m_baseDamage": "80.00", "m_minDamage": "", "m_knockbackPower": "1.00", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "6.00", "m_attackLength": "12.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "75.00", "m_baseCooldownTime": "0.01", "m_title": "Flamethrower Level 4", "m_description": ""}, {"id": "66715730e9a12a00290347b2", "m_indexer": "Flamethrower5", "m_name": "Flamethrower", "m_level": 5, "m_baseDamage": "100.00", "m_minDamage": "", "m_knockbackPower": "1.00", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "7.00", "m_attackLength": "14.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "100.00", "m_baseCooldownTime": "0.01", "m_title": "Flamethrower Level 5", "m_description": ""}, {"id": "6632633ab39d400027eb157e", "m_indexer": "Lightning1", "m_name": "Lightning", "m_level": 1, "m_baseDamage": "30.00", "m_minDamage": "", "m_knockbackPower": "2.00", "m_numOfHits": 2, "m_aoeRadius": "0.00", "m_attackWidth": "0.00", "m_attackLength": "0.00", "m_bonusAttackChance": "0.25", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "30.00", "m_baseCooldownTime": "0.01", "m_title": "Lightning Level 1", "m_description": "Hand lighting will target enemies"}, {"id": "663265da539137002815b35d", "m_indexer": "Lightning2", "m_name": "Lightning", "m_level": 2, "m_baseDamage": "45.00", "m_minDamage": "", "m_knockbackPower": "2.00", "m_numOfHits": 3, "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "0.35", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "35.00", "m_baseCooldownTime": "0.01", "m_title": "Lightning Level 2", "m_description": "Hand lighting will target enemies"}, {"id": "6671573bf4f46300284efed4", "m_indexer": "Lightning3", "m_name": "Lightning", "m_level": 3, "m_baseDamage": "70.00", "m_minDamage": "", "m_knockbackPower": "2.00", "m_numOfHits": 4, "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "0.50", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "40.00", "m_baseCooldownTime": "0.01", "m_title": "Lightning Level 3", "m_description": "Hand lighting will target enemies"}, {"id": "6671574178791a00268df32e", "m_indexer": "Lightning4", "m_name": "Lightning", "m_level": 4, "m_baseDamage": "100.00", "m_minDamage": "", "m_knockbackPower": "2.00", "m_numOfHits": 5, "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "0.75", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "45.00", "m_baseCooldownTime": "0.01", "m_title": "Lightning Level 4", "m_description": "Hand lighting will target enemies"}, {"id": "667157470b0bab0028b2806b", "m_indexer": "Lightning5", "m_name": "Lightning", "m_level": 5, "m_baseDamage": "150.00", "m_minDamage": "", "m_knockbackPower": "2.00", "m_numOfHits": 6, "m_aoeRadius": "", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "1.00", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "50.00", "m_baseCooldownTime": "0.01", "m_title": "Lightning Level 5", "m_description": "Hand lighting will target enemies"}, {"id": "6671b6f3f7e9610028b46575", "m_indexer": "WaterBlob1", "m_name": "WaterBlob", "m_level": 1, "m_baseDamage": "85.00", "m_minDamage": 10, "m_knockbackPower": "", "m_numOfHits": 2, "m_aoeRadius": "2.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 25, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "WaterBlob Level 1", "m_description": ""}, {"id": "6671b6f960f77e002869715c", "m_indexer": "WaterBlob2", "m_name": "WaterBlob", "m_level": 2, "m_baseDamage": "125.00", "m_minDamage": 20, "m_knockbackPower": "", "m_numOfHits": 3, "m_aoeRadius": "4.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 35, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "WaterBlob Level 2", "m_description": ""}, {"id": "6671b6ff8d05660027872582", "m_indexer": "WaterBlob3", "m_name": "WaterBlob", "m_level": 3, "m_baseDamage": "150.00", "m_minDamage": 30, "m_knockbackPower": "", "m_numOfHits": 4, "m_aoeRadius": "6.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 45, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "WaterBlob Level 3", "m_description": ""}, {"id": "6671b706e678f90028c2c1c3", "m_indexer": "WaterBlob4", "m_name": "WaterBlob", "m_level": 4, "m_baseDamage": "200.00", "m_minDamage": 40, "m_knockbackPower": "", "m_numOfHits": 5, "m_aoeRadius": "8.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 50, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "WaterBlob Level 4", "m_description": ""}, {"id": "6671b70bf5572000267c3f43", "m_indexer": "WaterBlob5", "m_name": "WaterBlob", "m_level": 5, "m_baseDamage": "225.00", "m_minDamage": 50, "m_knockbackPower": "", "m_numOfHits": 6, "m_aoeRadius": "10.00", "m_attackWidth": "", "m_attackLength": "", "m_bonusAttackChance": "", "m_tickSpeed": "", "m_manaCost": 100, "m_manaCostPerSecond": "", "m_baseCooldownTime": "2.00", "m_title": "WaterBlob Level 5", "m_description": ""}, {"id": "6671b6cbf09fd900260f393a", "m_indexer": "WaterSpout1", "m_name": "WaterSpout", "m_level": 1, "m_baseDamage": "35.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "3.00", "m_attackLength": "6.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "30.00", "m_baseCooldownTime": "0.01", "m_title": "WaterSpout Level 1", "m_description": ""}, {"id": "6671b6d4fc57ae0027b1e787", "m_indexer": "WaterSpout2", "m_name": "WaterSpout", "m_level": 2, "m_baseDamage": "45.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "4.00", "m_attackLength": "8.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "40.00", "m_baseCooldownTime": "0.01", "m_title": "WaterSpout Level 2", "m_description": ""}, {"id": "6671b6dacb13a60028a7fb0b", "m_indexer": "WaterSpout3", "m_name": "WaterSpout", "m_level": 3, "m_baseDamage": "60.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "5.00", "m_attackLength": "10.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "50.00", "m_baseCooldownTime": "0.01", "m_title": "WaterSpout Level 3", "m_description": ""}, {"id": "6671b6e03de50d002757b0c5", "m_indexer": "WaterSpout4", "m_name": "WaterSpout", "m_level": 4, "m_baseDamage": "80.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "6.00", "m_attackLength": "12.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "75.00", "m_baseCooldownTime": "0.01", "m_title": "WaterSpout Level 4", "m_description": ""}, {"id": "6671b6e5e9a12a0029050398", "m_indexer": "WaterSpout5", "m_name": "WaterSpout", "m_level": 5, "m_baseDamage": "100.00", "m_minDamage": "", "m_knockbackPower": "", "m_numOfHits": "", "m_aoeRadius": "", "m_attackWidth": "7.00", "m_attackLength": "15.00", "m_bonusAttackChance": "", "m_tickSpeed": "0.25", "m_manaCost": 10, "m_manaCostPerSecond": "100.00", "m_baseCooldownTime": "0.01", "m_title": "WaterSpout Level 5", "m_description": ""}]