[{"id": "67a60312b76c6802e14e8b57", "m_prefabName": "MA_Axe", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "", "m_starterPack": "False", "m_displayName": "MA_Axe", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "6511835e9c71200028b8d1ce", "m_prefabName": "MA_BaleOfWheat", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Bale Of Wheat", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "65032580ecbedd0028c502e9", "m_prefabName": "MA_Barrel", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Barrel", "m_description": "Full of honeymead. If you're lucky.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "BuildHammerFloor", "m_throwExplosionDamageBase": "10.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682f114aba6f7202d6ee214d", "m_prefabName": "MA_BarrelBomb", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Barrel Bomb", "m_description": "Bomb!", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "80.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "65.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651174927f0235002805407f", "m_prefabName": "MA_Broom", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Broom", "m_description": "Nice and sturdy, good for chasing away scallywags.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d200e26638e02c3b87419", "m_prefabName": "MA_BurnerBin", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Burner<PERSON>in", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d2001d953db02dc18949b", "m_prefabName": "MA_Canarycage", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Canarycage", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a6034453bd7a031d6acbc2", "m_prefabName": "MA_CookingStove", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_CookingStove", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67aa1fcdda165a24f61aa17e", "m_prefabName": "MA_Cutting_Horse", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_Cutting_Horse", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67ea77ffddf5f802ddcbc11c", "m_prefabName": "MA_Dock_Oakridge", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "True", "m_displayName": "Dock", "m_description": "If you catch anything can you let minnow?", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651175493b5916002a7277e1", "m_prefabName": "MA_DunkingStool", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Dunking Stool", "m_description": "Scared of having a bath? Try this!", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "65d7687cd0513300274066bf", "m_prefabName": "MA_Farm_Storage_Decoration", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "True", "m_displayName": "Farm Tool Storage", "m_description": "Store your rakes and hoes here.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "RGHandBoreKnock", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a6036153bd7a031d6acbd7", "m_prefabName": "MA_FirePlate", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_FirePlate", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "650325a53b58300028cdb168", "m_prefabName": "MA_FireTorchPole", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Fire Torch Pole", "m_description": "Lights paths and attracts mosquitos", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "ReactPillerInto\nReactPillerLoop\nReactPillerOut", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651175740aa24500295502fc", "m_prefabName": "MA_FireWoodLogPile_A", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Fire Wood Pile A", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "15.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "650325b8ecbedd0028c5040b", "m_prefabName": "MA_Gallows", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Gallows", "m_description": "A great place to hang about.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "NoticeReadExamine", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651175b39c71200028b87390", "m_prefabName": "MA_GrindStonePile", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Grind <PERSON> Pile", "m_description": "Medieval Grindr.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651175ef6b516c0029154839", "m_prefabName": "MA_GStone_A", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Grave Stone A ", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "6511760fcdeef600271c91b5", "m_prefabName": "MA_GStone_B", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Gave Stone B", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651176317f023500280549b6", "m_prefabName": "MA_GStone_C", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Grave Stone C", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "6511765f47beda0027bd53d3", "m_prefabName": "MA_GStone_D", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Grave Stone D", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651176810aa24500295507b8", "m_prefabName": "MA_HandCart", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Hand Cart", "m_description": "What a hand-some cart! Pulled by hands apparently. ", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "672bae2ed96f5202f8a19924", "m_prefabName": "MA_HeroLog", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Legendary", "m_starterPack": "False", "m_displayName": "Hero's Magic Log", "m_description": "Hero can spawn this to rest on.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651176a7cdeef600271c94a3", "m_prefabName": "MA_HorseTrough", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Horse Trough", "m_description": "A bed for drunkards.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a604a3b76c6802e14e96f1", "m_prefabName": "MA_LampPost_Swamp", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "", "m_starterPack": "False", "m_displayName": "MA_LampPost_Swamp", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a6038632830a031c2e69be", "m_prefabName": "MA_LifeSaver", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_LifeSaver", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651176c83faba10027e5e6fa", "m_prefabName": "MA_lilBucket", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Little Bucket", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651176e46b516c0029157dea", "m_prefabName": "MA_LongStool", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Long Stool", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d1fead953db02dc18946e", "m_prefabName": "MA_LunchPail", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "LunchPail", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a5e982f6fe5f02d6bb64ae", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Kit", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Kit", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "679b68c582f5ce02cccf9284", "m_prefabName": "MA_OldLogA_Bark", "m_location": "_Art/Decorations/Throwable", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Old Log", "m_description": "Old Log", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "687658d0b5593f02e1184f96", "m_prefabName": "MA_OpenBarrel", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Open barrel", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "687658e31f1bd602e1f644a5", "m_prefabName": "MA_OpenCrate1", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Crate A", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "687658f0f5a13802d6d5a16f", "m_prefabName": "MA_OpenCrate2", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Crate B", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "687658f897d95b02cf7e4152", "m_prefabName": "MA_OpenCrate3", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Crate C", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67aa1fed6231030303556089", "m_prefabName": "MA_OutDoorShower", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_OutDoorShower", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "654d01844bc217002756c76c", "m_prefabName": "MA_Outhouse", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Outhouse", "m_description": "Get your 'me' time here...", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "FloorSitInto\nFloorSitOut", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "6503260f0e39410028db7678", "m_prefabName": "MA_PilloryStock", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "PilloryStock", "m_description": "Enjoy being hit in the face with rotten food? Line up now!", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a603a5f6fe5f02d6bbc6e5", "m_prefabName": "MA_RockingChair", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_RockingChair", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67a6032953bd7a031d6acb96", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d1fd2efa9ec02c9e65972", "m_prefabName": "MA_RubbleCart", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "RubbleCart", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651184888775110026e16a0f", "m_prefabName": "MA_Sack_Bend_A", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Sack Bent A", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651184a73faba10027e65fab", "m_prefabName": "MA_Sack_Bend_B", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Sack Bent B", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651184c27f0235002805e7f7", "m_prefabName": "MA_Sack_Flat", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Sack Flat", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651184db3b5916002a735a92", "m_prefabName": "MA_Sack_Open", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Sack Open", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651184fc0aa2450029559fff", "m_prefabName": "MA_Sack_Tied", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Sack Tied", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "651185493b5916002a735cb9", "m_prefabName": "MA_Scarecrow", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Scarecrow", "m_description": "Has no brain. Or legs.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d1fc7efa9ec02c9e6592e", "m_prefabName": "MA_Shovel", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "<PERSON><PERSON><PERSON>", "m_description": "Good for shovelin'", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "65032634d423cd002a7eb350", "m_prefabName": "MA_Signpost", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Signpost", "m_description": "Are you lost? \n", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "ReactWorkerPickUpInto01\nReactWorkerPickUpLoop01\nReactWorkerPickUpOut01", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "6880b7b4a23b5102d1204fa9", "m_prefabName": "MA_StigmataBall", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Luckrite", "m_description": "The stigmata trap lure", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "25.00", "m_throwExplosionPowerBase": "20.00", "m_throwExplosionRadiusBase": "3.00", "m_throwExplosionSpeedBase": "2.00", "m_canPickupAndThrow": "True", "m_throwableMass": "250.00", "m_maxImpactCount": ""}, {"id": "67aa1faa8278b102cfd689fb", "m_prefabName": "MA_SwampToolTable_A", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_SwampToolTable_A", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67911fef7eb978030567f9bc", "m_prefabName": "MA_TableSaw", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Table Saw", "m_description": "Mind your fingers.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "66e1853e05e110031e2cb8ff", "m_prefabName": "MA_Throwable_Boulder1", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder1", "m_description": "Boulder1", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f76ff7f5d02ceb1e0a1", "m_prefabName": "MA_Throwable_Boulder10", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder10", "m_description": "Boulder10", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9fa9f512f202e3cc95bf", "m_prefabName": "MA_Throwable_Boulder11", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder11", "m_description": "Boulder11", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9faef512f202e3cc95ed", "m_prefabName": "MA_Throwable_Boulder12", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder12", "m_description": "Boulder12", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "66e1885db0e2e402ef9d9de0", "m_prefabName": "MA_Throwable_Boulder2", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder2", "m_description": "Boulder2", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "66e1889ac86ce6030d957a8a", "m_prefabName": "MA_Throwable_Boulder3", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder3", "m_description": "Boulder3", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f50a680b502e08249b1", "m_prefabName": "MA_Throwable_Boulder4", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder4", "m_description": "Boulder4", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f59c289c002c790a4f7", "m_prefabName": "MA_Throwable_Boulder5", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder5", "m_description": "Boulder5", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f659f610b030c38c2c9", "m_prefabName": "MA_Throwable_Boulder6", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder6", "m_description": "Boulder6", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f68a680b502e0824a40", "m_prefabName": "MA_Throwable_Boulder7", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder7", "m_description": "Boulder7", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f6da25fd302ebd63a5f", "m_prefabName": "MA_Throwable_Boulder8", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder8", "m_description": "Boulder8", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682c9f71e66ce702d5d06bb6", "m_prefabName": "MA_Throwable_Boulder9", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Boulder9", "m_description": "Boulder9", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "50.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "4.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682db6af3d5bfa031256ce3c", "m_prefabName": "MA_Throwable_GiantRubberDuck", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "<PERSON><PERSON><PERSON>", "m_description": "Quack!", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "100.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "8.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "671247f8c5a7a30323e82293", "m_prefabName": "MA_Throwable_Large_Boulder1", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Large Boulder", "m_description": "A Large Throwable Boulder", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "75.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "8.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "5000.00", "m_maxImpactCount": ""}, {"id": "67124870c5a7a30323e82349", "m_prefabName": "MA_Throwable_Large_Boulder2", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_Throwable_Large_Boulder2", "m_description": "MA_Throwable_Large_Boulder2", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "75.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "8.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "5000.00", "m_maxImpactCount": ""}, {"id": "6712489a1921fd02fa3a80cc", "m_prefabName": "MA_Throwable_Large_Boulder3", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_Throwable_Large_Boulder3", "m_description": "MA_Throwable_Large_Boulder3", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "75.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "8.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "5000.00", "m_maxImpactCount": ""}, {"id": "671248eb94eba002f185bdac", "m_prefabName": "MA_Throwable_Large_Boulder4", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "MA_Throwable_Large_Boulder4", "m_description": "MA_Throwable_Large_Boulder4", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "75.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "8.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "5000.00", "m_maxImpactCount": ""}, {"id": "66e188d928663c02e185f3c4", "m_prefabName": "MA_Throwable_Log1", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Log1", "m_description": "Log1", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "25.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "6.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "66e188e773e83103199942fc", "m_prefabName": "MA_Throwable_Log2", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Log2", "m_description": "Log2", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "25.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "6.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "66e1890673e831031999437d", "m_prefabName": "MA_Throwable_Log3", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Log3", "m_description": "Log3", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "25.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "6.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "66e188f751e4ad02bbdddd33", "m_prefabName": "MA_Throwable_Log4", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Log4", "m_description": "Log4", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "25.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "6.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "682db6d99bf6de02c492c3a7", "m_prefabName": "MA_Throwable_SpikyBall", "m_location": "_Art/Decorations/Throwable", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Spiky Ball", "m_description": "Ow!", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "150.00", "m_throwExplosionPowerBase": "100.00", "m_throwExplosionRadiusBase": "8.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "True", "m_throwableMass": "250.00", "m_maxImpactCount": ""}, {"id": "677d1fe2efa9ec02c9e659d2", "m_prefabName": "MA_Tools", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Tools", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "65084c317ec1e8002716a91e", "m_prefabName": "MA_TreeStump", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Tree Stump", "m_description": "Well I'm stumped.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "BenchSitIdle", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "654cea80d780d200277cc89d", "m_prefabName": "MA_VegsGround", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Vegetable Plot", "m_description": "Grow your own veg, then eat it or throw it at people.", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "LabDeskStandLook", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "6876590ae85cb602e864c1c6", "m_prefabName": "MA_WashingPole", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 0, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Rustic Washing Line", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "67aa23f484734f513d56000c", "m_prefabName": "MA_WaterPump", "m_location": "_Art/Decorations/MA_Decorations", "m_price": "", "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Water Pump", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "", "m_throwExplosionPowerBase": "", "m_throwExplosionRadiusBase": "", "m_throwExplosionSpeedBase": "", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "650436b84555f70029561bf8", "m_prefabName": "MA_Well", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Well", "m_description": "Yes I am, how are you?", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "IdleVomit", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d1fbad953db02dc1893e8", "m_prefabName": "MA_Well_Metal", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "Metal Well", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}, {"id": "677d1fa846696c02b944ee8d", "m_prefabName": "MA_Wheel<PERSON><PERSON>row", "m_location": "_Art/Decorations/MA_Decorations", "m_price": 100, "m_rarity": "Common", "m_starterPack": "False", "m_displayName": "WheelBarrow", "m_description": "", "m_townSatisfactionEffect": "0.01", "m_spritePath": "_Art/Sprites/Decorations/Decoration_LogShed.png", "m_touristAnim": "", "m_throwExplosionDamageBase": "0.00", "m_throwExplosionPowerBase": "40.00", "m_throwExplosionRadiusBase": "10.00", "m_throwExplosionSpeedBase": "6.00", "m_canPickupAndThrow": "False", "m_throwableMass": "200.00", "m_maxImpactCount": ""}]