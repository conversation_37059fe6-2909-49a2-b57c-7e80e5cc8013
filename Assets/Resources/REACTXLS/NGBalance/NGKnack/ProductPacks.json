[{"id": "6141c0878f63aa002239c5d0", "m_packName": "01_Patterns_Moat_Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "", "m_stickerData": "", "m_patternData": "10|9|8|7|6|5|4|3|2|1", "m_starterPack": "True"}, {"id": "665f291f9fbae700282bab3e", "m_packName": "01_Stickers_Moat_Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "", "m_stickerData": "257|193", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bc8f4133470020d22324", "m_packName": "Amethyst Peel Colours", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Pink Glitter|Violet|Tangerine", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6144906bc6bef8001e039d8f", "m_packName": "Avatar Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Lipstick Red|Natural Cream|Gentle Pink|Vibrant Green|Beige|Mid Pink|Terracotta|Tan Tan|Sepia|Purple Jewel|Light Brown|Mid Brown|Bright Cyan|Dark Brown|Umber|Ebony", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140b7d54e1796001f635690", "m_packName": "<PERSON><PERSON> Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Rare", "m_nGBlocks": "", "m_paintPotData": "Iridescent Eggshell|Tan|Brown", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140aa8d86eff5001f406783", "m_packName": "Basic Colour Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Earth Brown|Sunshine Yellow|Lush Green|Blue|Red", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bd90d0089e001e469a70", "m_packName": "Blossoming <PERSON>ts", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Legendary", "m_nGBlocks": "", "m_paintPotData": "Iridescent Tan|Blossom Pink|Pure Pink", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bde405e6ab001e49fbde", "m_packName": "Blueberry Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Epic", "m_nGBlocks": "", "m_paintPotData": "Mint|Light Blueberry|Blueberry", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140be62c872c000204f4ae5", "m_packName": "Bold and <PERSON><PERSON>y <PERSON>ts", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Epic", "m_nGBlocks": "", "m_paintPotData": "Lilac|Blood Orange|Rose Glitter", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bea6e4faa2001ebcd467", "m_packName": "Camouflage Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Iridescent Camo Green|Light Camo Green|Dark Camo Green", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140ab56d0089e001e46681e", "m_packName": "Candy Colour Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Candy Pink|Light Blue", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bef0c657f7001e94dc51", "m_packName": "Chocolate Cream Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Latte|Dark Chocolate|Milk Chocolate", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bf614d94b3001e0bd206", "m_packName": "Clear Blue Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Epic", "m_nGBlocks": "", "m_paintPotData": "Lightest Blue|Clear White|Deep Sky Blue", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140bfbb86eff5001f40803a", "m_packName": "<PERSON><PERSON><PERSON><PERSON>", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Rare", "m_nGBlocks": "", "m_paintPotData": "Dark Green Glitter|Dark Red|Dark Cyan", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c0144d94b3001e0bd2ae", "m_packName": "Crimson Leaves Colours", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "White Glitter|Soft Red|Lush Green", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c0c3c872c000204f4d8f", "m_packName": "Crisp Dark Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Even Teal|Iridescent Black|Red Orange", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c1074e1796001f636105", "m_packName": "Cupped Cloud Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Epic", "m_nGBlocks": "", "m_paintPotData": "Silver Glitter|Cloud White|Cyan", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140acb3c657f7001e94b06d", "m_packName": "Dark Colour Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Dark Blue|Blood Red", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c1c667bf8b001e2f8959", "m_packName": "<PERSON><PERSON>", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Epic", "m_nGBlocks": "", "m_paintPotData": "Violet Glitter|Light Blue Denim|Dark Blue Denim", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c23de9f0e90021681182", "m_packName": "Fiery Lemon Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Iridescent Red|Lemon Yellow|Burnt Orange", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140b3b33db9c1001e12f6f2", "m_packName": "Fire Glow Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Yellow Glitter|Fire Orange|Bright Red", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c31c216279001e861ff7", "m_packName": "Fresh Fruit Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Raspberry|Orange Juice|Lemon", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c41eb2e08500202f5d4a", "m_packName": "Fresh Spring Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Legendary", "m_nGBlocks": "", "m_paintPotData": "Lavender|Spring Green|Royal Purple", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c4725d11e200212e6acd", "m_packName": "Golden Glow Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Yellow Gold|White Gold|Golden Orange", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c50fed14e3001e14d2d0", "m_packName": "Gothic Greyscale Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Dark Grey|Light Grey|Gothic Black", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140b709329140001f56b5b2", "m_packName": "Idle Glen Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Purple Glitter|Fae Blue|Light Green", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c63dece38e001f0857e2", "m_packName": "Idle Glen Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Late Spring Green|Air Blue|Purple Glitter", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140acd4329140001f56aa9a", "m_packName": "Light Colour Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Pale Blue|Pale Pink", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c6a66d7185001e0e092c", "m_packName": "Minty Fresh Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Legendary", "m_nGBlocks": "", "m_paintPotData": "Mint Glitter|Blackberry|Lime", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e444d7ce84100280369c8", "m_packName": "Moat Black Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Gothic Black|Punk Black|Blackberry|Iridescent Black|Shaded Black|Black", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e3dcafa79cd0028010437", "m_packName": "Moat Blues Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Royal Blue|Bright Cyan|Air Blue|Blue Denim|Pastel Blue|Light Blueberry|Lightest Blue|Deep Sky Blue|Light Blue Denim|Dark Blue Denim|Indigo Glitter|Cyan|Dark Cyan|Blue Black|Iridescent Blue|Navy Blue|Sky Blue|Blue Glitter|Fae Blue|Pale Blue|Dark Blue|Light Blue|Blue", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e44d53870a600274be09a", "m_packName": "Moat Browns Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Beige|Tan Tan|Sepia|Light Brown|Mid Brown|Dark Brown|Umber|Sand|Earth Brown|Rich Brown|Latte|Dark Chocolate|Milk Chocolate|Iridescent Tan|Chocolate|Tan|Brown|Mocha|Ebony|Foundation Beige", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e41c87a077a0027e01e36", "m_packName": "Moat Greens Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Seaweed Green|Turquoise|Even Teal|Vibrant Green|Bright Cyan|Alpine Green|Late Spring Green|Mint Glitter|Lime|Mint|Spring Green|Teal Glitter|Light Teal|Dark Teal|Pastel Green Glitter|Leaf Green|Knoll Green|Dark Green Glitter|Iridescent Camo Green|Light Camo Green|Dark Camo Green|Upper Teal|Lush Green|Iridescent Green|Light Green|Pale Green|Lower Teal|Spooky Green|Warm Green|Cold Green|Forest Green|Dark Green|Green", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e5f3c5431b60028ca11d7", "m_packName": "Moat Greys Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Dark Grey|Light Grey|Iridescent Grey|Soft Grey|Shadow Grey|Smooth Grey", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e5ae9afa47d00263642af", "m_packName": "Moat Oranges Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Bright Orange|Orange Earth|Orange Juice|Golden Orange|Light Orange|Blood Orange|Burnt Orange|Bold Orange|Red Orange|Tangerine|Peach|Fire Orange|Orange", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e4baf7a5c0e00285e101a", "m_packName": "Moat Pinks Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Bright Pink|Pink|Gentle Pink|Mid Pink|Pastel Pink|Pale Rose|Hot Pink|Ash Pink|Blossom Pink|Pure Pink|Rose Glitter|Light Pink|Pink Glitter|Sky Pink|Pale Pink|Candy Pink", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e46fea0459a00286e1a71", "m_packName": "Moat Purple Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Purple Jewel|Pastel Purple|Blueberry|Royal Purple|Ash Purple|Violet Glitter|Iridescent Maroon|Violet|Purple Glitter|Plum|Ooze Purple|Purple", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e4a2611a43a002896603b", "m_packName": "Moat Reds Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Royal Red|Lipstick Red|Rich Red|Strawberry|Raspberry|Bold Red|Dark Red|Iridescent Red|Red Orange|Soft Red|Bright Red|Blood Red|Red", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e64a311a43a002896a5e1", "m_packName": "Moat Starter Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Turquoise|Royal Blue|Dark Grey|Light Grey|Gentle Pink|Vibrant Green|Beige|Terracotta|Light Brown|Mid Brown|Dark Brown|Pastel Purple|Lightest Blue|Deep Sky Blue|Hot Pink|White Gold|Blossom Pink|Silver Glitter|Dark Red|Navy Blue|Light Yellow|Dark Silver Glitter|Sky Blue|Brown|Fire Orange|Ooze Purple|Gold|Black|Natural White|Forest Green|Dark Green|Yellow|Red", "m_stickerData": "", "m_patternData": "", "m_starterPack": "True"}, {"id": "669e5f8f0197ee0027a89a29", "m_packName": "Moat Whites Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Sunshine White|Natural Cream|Pastel White|Vanilla|Double Cream|Clear White|White Gold|Iridescent White|Cream|Cloud White|Soft Cream|Iridescent Cream|White Glitter|Cream Sponge|Shaded White|Ivory|Natural White", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "669e5ca215a7fa002610156e", "m_packName": "Moat Yellows Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Tennis Yellow|Golden Sunshine|Natural Cream|Sunshine Yellow|Yellow Gold|White Gold|Golden Orange|Iridescent Yellow|Lemon Yellow|Soft Cream|Light Yellow|Warm Yellow|Baked Yellow|Yellow Glitter|Pale yellow|Mustard|Gold|Yellow", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c6e95d11e200212e6ebd", "m_packName": "Moonlight Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Legendary", "m_nGBlocks": "", "m_paintPotData": "Iridescent Grey|Soft Cream|Blue Black", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c826216279001e862671", "m_packName": "Morning Glow Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Iridescent Yellow|Cream|Light Orange", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6397063e8d9e7a0024c41f6f", "m_packName": "Paints_Starter_Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Pink|Beige|Soft Grey|Brown|Purple|Black|Natural White|Yellow|Orange|Blue|Green|Red", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c8ae6020360023584eb7", "m_packName": "Palette <PERSON>", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Pastel Pink|Pastel Blue|Pastel White", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140ac27d0089e001e4668fa", "m_packName": "Panda Colour Pack", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Black|Natural White", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140c9806020360023584f94", "m_packName": "Pastel Punk Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Legendary", "m_nGBlocks": "", "m_paintPotData": "Sand|Pastel Purple|Punk Black", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140cbff6020360023585229", "m_packName": "Pastel Punk Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Common", "m_nGBlocks": "", "m_paintPotData": "Iridescent Lilac|Cream Sponge|Peach", "m_stickerData": "", "m_patternData": "", "m_starterPack": "False"}, {"id": "6140ccc3ece38e001f085bd7", "m_packName": "Pastel Punk Paints", "m_nGProductInfo": "Food|Weapons", "m_rarity": "Rare", "m_nGBlocks": "", "m_paintPotData": "Iridescent Rose|Pale Rose|Hot Pink", "m_stickerData": "", "m_patternData": "", "m_starterPack": "True"}]