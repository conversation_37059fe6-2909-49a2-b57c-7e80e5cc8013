using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Object = UnityEngine.Object;

public class MADestinationSign : Mono<PERSON><PERSON>aviour, IBatchPartitioner
{
    [Header("Examples: Components[ActionTavern], Pos[-50;20], Building[Tavern], Building[205], Decoration[MA_Tardis_Crypt]")]
    [SerializeField] private string m_target;
    [Header("If target is Empty, can use reference")]
    [SerializeField] private Object m_targetObject = null;
    [Header("If m_targetObject is null, can use position")]
    [SerializeField] private Vector3 m_position = Vector3.zero;
    [SerializeField] private string m_signText = "";

    [SerializeField] private float m_updateInterval = 0.5f;
    
    private bool m_setByString = true;
    private MATimer m_poll = new MATimer();
    private MeshRenderer m_renderer = null;
    private List<TextMeshPro> m_signTextMeshes = new();
    
    private void Awake()
    {
        m_renderer = GetComponent<MeshRenderer>();
        GetComponentsInChildren<TextMeshPro>(true, m_signTextMeshes);
    }
    
    private void Start()
    {
        m_poll = new MATimer(m_updateInterval);
        RefreshTarget();
    }

    private void Update()
    {
        if (m_poll.IsFinished)
        {
            RefreshTarget();
        
            if (m_position != Vector3.zero)
            {
                if(m_renderer != null) m_renderer.enabled = true;
                Transform trSelf = transform;
                trSelf.LookAt(new Vector3(m_position.x, trSelf.position.y, m_position.z));
                
                for (int i = m_signTextMeshes.Count - 1; i >= 0; i--)
                {
                    if (m_signTextMeshes[i] == null)
                    {
                        m_signTextMeshes.RemoveAt(i);
                        continue;
                    }
                    m_signTextMeshes[i].text = m_signText;
                    m_signTextMeshes[i].enabled = true;
                }
            }
            else
            {   
                if(m_renderer != null) m_renderer.enabled = false;
                for (int i = m_signTextMeshes.Count - 1; i >= 0; i--)
                {
                    if (m_signTextMeshes[i] == null)
                    {
                        m_signTextMeshes.RemoveAt(i);
                        continue;
                    }
                    m_signTextMeshes[i].enabled = false;
                }
            }
        }
    }
    
    private void RefreshTarget()
    {
        bool res = InputUtilities.GetObjectFromLocationString(m_target, out object obj);
        if (res && obj != null)
        {
            m_targetObject = obj as Object;
            m_setByString = true;
            if (m_targetObject == null)
            {
                List<BCBase> comps = null;
                if (obj is HashSet<MABuilding> bs)
                {
                    comps = new();
                    foreach (var b in bs)
                    {
                        if (b.m_components.Count > 0)
                        {
                            comps.Add(b.m_components[0]);//just pick any component, we care about the building pos only.
                        }
                    }
                }
                else if (obj is List<BCBase> bc)
                {
                    comps = bc;
                }

                if (comps != null)
                {
                    if (comps.Count == 0)
                    {
                        m_position = Vector3.zero;
                        m_signText = "";
                        return;
                    }
                    else
                    {
                        float bestDistSq = Single.MaxValue;
                        MABuilding bestB = null;
                        BCBase bestC = null;
                        Vector3 bestP = Vector3.zero;
                        foreach (var comp in comps)
                        {
                            Vector3 buildingPos = comp.transform.position;
                            float newDistSq = (buildingPos - transform.position).sqrMagnitude;
                            if (bestDistSq > newDistSq)
                            {
                                bestDistSq = newDistSq;
                                bestB = comp.Building;
                                bestC = comp;
                                bestP = buildingPos;
                            }
                        }

                        m_targetObject = bestB;
                        if (m_targetObject != null)
                        {
                            m_position = bestP;
                            if(bestC != null) m_signText = bestC.m_info.DisplayTitle;
                        }
                    }
                }
                else if (obj is Vector3 pos)
                {
                    m_targetObject = null;
                    m_position = pos;
                }
            }
        }
        else if (m_setByString)
        {
            m_targetObject = null;
            m_position = Vector3.zero;
            m_signText = "";
            m_setByString = false;
        }
        
        if (m_targetObject != null)
        {
            if (m_targetObject is MonoBehaviour mb)
                m_position = mb.transform.position;
            else if (m_targetObject is GameObject go)
                m_position = go.transform.position;
        }

        m_poll.Set();
    }

    public Component Component()
    {
        return this;
    }

    public List<List<Transform>> GetExcludedTransforms()
    {
        return new List<List<Transform>> { new() { transform } };
    }

    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }
}
