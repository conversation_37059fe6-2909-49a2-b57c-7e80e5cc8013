using UnityEngine;

[RequireComponent(typeof(MeshFilter))]
public class MeshScaleToRectWidth : MonoBehaviour
{
    public RectTransform targetRectTransform;

    private float lastWidth = -1f;

    void LateUpdate() // LateUpdate is better for layout-based updates
    {
        if (targetRectTransform == null)
            return;

        float currentWidth = targetRectTransform.rect.width;

        // Only update if the width changed
        if (!Mathf.Approximately(currentWidth, lastWidth))
        {
            lastWidth = currentWidth;
            UpdateScale(currentWidth);
        }
    }

    void UpdateScale(float width)
    {
        float newXScale = width / 100f / 3.75f; // Adjust divisor as needed
        Vector3 currentScale = transform.localScale;
        transform.localScale = new Vector3(newXScale, currentScale.y, currentScale.z);
    }
}
