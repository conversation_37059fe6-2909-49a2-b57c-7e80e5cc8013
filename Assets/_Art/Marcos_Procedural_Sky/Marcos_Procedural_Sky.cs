using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.HighDefinition;


[ExecuteAlways]
public class Marcos_Procedural_Sky : MonoSingleton<Marcos_Procedural_Sky>
{
    [Header("________________________________________________________________________________________________________________")]
    [Header("DAY NIGHT CYCLE SETTINGS")]
    [Space(8)]
    [Range(0f, 1.0f)]
    public float dayNightCycle;

    [Tooltip("Maximum strength shadows will never get too, (0.5 is a good value for most cases)")]
    public float ShadowStrengthMax = 0.5f;
    
    [Tooltip("Turn this on to allow manual control of day nght slider for editing/tweaking purposes when game is running!")]
    public bool overideDayNightCycleControl;

    [Tooltip("Update lighting settings and test animated cycle in editor mode")]
    public bool updateInEditor;
    
    public bool animateDayNightCycle;

    [Range(-1f, 1f)]
    public float dayNightCycleSpeed;

    public bool renderFog;

    [Range(0.0f, 0.1f)]
    public float maxFogDensity;
    
    [Tooltip("Interior light colour")] public Color m_interiorLightColour = new Color(1, .9f, .6f);
    [Tooltip("Interior light temperature")] [Range(1500.0f, 20000.0f)] public float m_interiorLightTemperature = 6500;
    [Tooltip("Interior light direction (Euler angles)")] public Vector3 m_interiorLightDirection = new Vector3(60, 0, 0);

    [Header("________________________________________________________________________________________________________________")]
    [Header("SUN RELATIVE TO CAMERA OPTIONS")]
    [Space(8)]

    [Tooltip("Sun position will move with camera so front of buildings get more sunlight on them (God Mode)")]
    public bool SunPositionRelativeToCamera;

    [Tooltip("Different version of Sun position will move with camera so front of buildings get more sunlight on them (God Mode)")]
    public bool Option2SunPositionRelativeToCamera;

    [Tooltip("Sun position will move with camera so front of buildings get more sunlight on them (Possession Mode (Only if God Mode also on though)")]
    public bool PossessionSunPositionRelativeToCamera;

    [Tooltip("How quickly the sun position changes relative to camera to kleep fronts of buildings in more sunlight")]
    [Range(0.001f, 1.0f)]
    public float CatchupSpeedSunPositionRelativeToCamera = 0.001f;


    [Header("________________________________________________________________________________________________________________")]
    [Header("MIDDAY SETTINGS (URP)")]
    [Space(8)]
    [Tooltip("Colour of Sun and the light it shines at Midday (Alpha = Opacity of Sun Sprite)")]
    public Color sun_Color_Midday;

    [Tooltip("Temperature of Sun in Kelvins at Midday")]
    [Range(1500.0f, 20000.0f)]
    public float sun_Temperature_Midday;

    [Tooltip("Intensity of Sun at Midday")]
    [Range(0f, 10.0f)]
    public float sun_Intensity_Midday;

    [Tooltip("Colour of Moon and the light it shines at Midday (Alpha = Opacity of Moon Sprite) - Can be used to get some fill lighting during day if desired")]
    public Color moon_Color_Midday;

    [Tooltip("Temperature of Moon in Kelvins at Midday (Can be used to get some fill lighting during day if desired)")]
    [Range(1500.0f, 20000.0f)]
    public float moon_Temperature_Midday;

    [Tooltip("Intensity of Moon at Midday (Can be used to get some fill lighting during day if desired)")]
    [Range(0f, 10.0f)]
    public float moon_Intensity_Midday;

    [ColorUsageAttribute(false)]
    public Color ambientSkyColorMidday;
    [ColorUsageAttribute(false)]
    public Color ambientEquatorColorMidday;
    [ColorUsageAttribute(false)]
    public Color ambientGroundColorMidday;

    [Tooltip("Fog Colour, Alpha = density multplier")]
    public Color fogColorMidday;

    [Tooltip("Post Colour Adjust Exposure at Midday")]
    [Range(-10f, 10.0f)]
    public float post_CA_exposure_Midday;

    [Tooltip("Post Colour Adjust Contrast at Midday")]
    [Range(-100f, 100f)]
    public float post_CA_contrast_Midday;

    [Tooltip("Post Colour Adjust Saturation at Midday")]
    [Range(-100f, 100f)]
    public float post_CA_saturation_Midday;

    [Tooltip("Post Bloom Threshold at Midday")]
    [Range(0, 1f)]
    public float post_BLM_threshold_Midday;

    [Tooltip("Post Bloom Intensity at Midday")]
    [Range(0, 1f)]
    public float post_BLM_intensity_Midday;

    public Color DistrictColourMidday = new Color(.54f, .54f, .54f, .2f);

    [Space(8)]
    [Header("MIDDAY SETTINGS (HDRP)")]
    [Space(8)]

    [Tooltip("(HDRP) Colour of Sun and the light it shines at Midday (Alpha = Opacity of Sun Sprite)")]
    public Color HDRP_Sun_Color_Midday;

    [Tooltip("(HDRP) Intensity of Sun at Midday")]
    [Range(0f, 100.0f)]
    public float HDRP_Sun_Intensity_Midday;

    [Tooltip("(HDRP) Temperature of Sun at Midday")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Sun_Temperature_Midday;

    [Tooltip("(HDRP) Colour of Moon and the light it shines at Midday (Alpha = Opacity of Moon Sprite) - Can be used to get some fill lighting during day if desired")]
    public Color HDRP_Moon_Color_Midday;

    [Tooltip("(HDRP) Intensity of Moon at Midday (Can be used to get some fill lighting during day if desired)")]
    [Range(0f, 100.0f)]
    public float HDRP_Moon_Intensity_Midday;

    [Tooltip("(HDRP) Temperature of Moon at Midday")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Moon_Temperature_Midday;

    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Top_Midday;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Middle_Midday;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Bottom_Midday;

    [Tooltip("(HDRP) Colour Of the cLouds - effects ambient lighting!")]
    [ColorUsageAttribute(false,false)]
    public Color HDRP_Cloud_Colour_Midday;

    [Tooltip("(HDRP) Fog Attenutation Distance")]
    [Range(50f, 2500.0f)]
    public float HDRP_FogAttenuationDistance_Midday;

    [Tooltip("(HDRP) Post Colour Adjust Exposure at Midday")]
    [Range(-10f, 10.0f)]
    public float HDRP_Post_CA_exposure_Midday;

    [Tooltip("(HDRP) Post Colour Adjust Contrast at Midday")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_contrast_Midday;

    [Tooltip("(HDRP) Post Colour Adjust Saturation at Midday")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_saturation_Midday;

    [Tooltip("(HDRP) Post Bloom Threshold at Midday")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_threshold_Midday;

    [Tooltip("(HDRP) Post Bloom Intensity at Midday")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_intensity_Midday;
    public Color HDRPDistrictColourMidday = new Color(.54f, .54f, .54f, .2f);


    [Header("________________________________________________________________________________________________________________")]
    [Header("SUNSET/SUNRISE SETTINGS (URP)")]
    [Space(8)]
    [Tooltip("Colour of Sun and the Sunlight it shines at Sunset and Sunrise (Alpha = Opacity of Sun Sprite)")]
    public Color sun_Color_SunSet;

    [Tooltip("Temperature of Sun (& Moon) in Kelvins at Sunset and Sunrise")]
    [Range(1500.0f, 20000.0f)]
    public float sun_Temperature_SunSet;

    [Tooltip("Intensity of Sun at Sent and Sunrise")]
    [Range(0f, 10.0f)]
    public float sun_Intensity_SunSet;

    [Tooltip("Colour of Moon and the light it shines at SunSet (Alpha = Opacity of Moon Sprite)")]
    public Color moon_Color_SunSet;

    [Tooltip("Temperature of Moon in Kelvins at SunSet")]
    [Range(1500.0f, 20000.0f)]
    public float moon_Temperature_SunSet;

    [Tooltip("Intensity of Moon at SunSet")]
    [Range(0f, 10.0f)]
    public float moon_Intensity_SunSet;

    [ColorUsageAttribute(false)]
    public Color ambientSkyColorSunSet;
    [ColorUsageAttribute(false)]
    public Color ambientEquatorColorSunSet;
    [ColorUsageAttribute(false)]
    public Color ambientGroundColorSunSet;

    [Tooltip("Fog Colour, Alpha = density multplier")]
    public Color fogColorSunSet;

    [Tooltip("Post Colour Adjust Exposure at Sun Set and Sun Rise")]
    [Range(-10f, 10.0f)]
    public float post_CA_exposure_SunSet;

    [Tooltip("Post Colour Adjust Contrast at Sun Set and Sun Rise")]
    [Range(-100f, 100f)]
    public float post_CA_contrast_SunSet;

    [Tooltip("Post Colour Adjust Saturation at Sun Set and Sun Rise")]
    [Range(-100f, 100f)]
    public float post_CA_saturation_SunSet;

    [Tooltip("Post Bloom Threshold at Sun Set and Sun Rise")]
    [Range(0, 1f)]
    public float post_BLM_threshold_SunSet;

    [Tooltip("Post Bloom Intensity at Sun Set and Sun Rise")]
    [Range(0, 1f)]
    public float post_BLM_intensity_SunSet;
    public Color DistrictColourSunSet = new Color(.15f, .15f, .25f, .3f);

    [Space(8)]
    [Header("SUNSET/SUNRISE SETTINGS (HDRP)")]
    [Space(8)]

    [Tooltip("(HDRP) Colour of Sun and the light it shines at SunSet/SunRise (Alpha = Opacity of Sun Sprite)")]
    public Color HDRP_Sun_Color_SunSet;

    [Tooltip("(HDRP) Intensity of Sun at SunSet/SunRise")]
    [Range(0f, 100.0f)]
    public float HDRP_Sun_Intensity_SunSet;

    [Tooltip("(HDRP) Temperature of Sun at SunSet/SunRise")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Sun_Temperature_SunSet;

    [Tooltip("(HDRP) Colour of Moon and the light it shines at SunSet/SunRise (Alpha = Opacity of Moon Sprite)")]
    public Color HDRP_Moon_Color_SunSet;

    [Tooltip("(HDRP) Intensity of Moon at SunSet/SunRise")]
    [Range(0f, 100.0f)]
    public float HDRP_Moon_Intensity_SunSet;

    [Tooltip("(HDRP) Temperature of Moon at SunSet/SunRise")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Moon_Temperature_SunSet;

    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Top_SunSet;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Middle_SunSet;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Bottom_SunSet;

    [Tooltip("(HDRP) Colour Of the cLouds - effects ambient lighting!")]
    [ColorUsageAttribute(false, false)]
    public Color HDRP_Cloud_Colour_SunSet;

    [Tooltip("(HDRP) Fog Attenutation Distance")]
    [Range(50f, 2500.0f)]
    public float HDRP_FogAttenuationDistance_SunSet;

    [Tooltip("(HDRP) Post Colour Adjust Exposure at SunSet/SunRise")]
    [Range(-10f, 10.0f)]
    public float HDRP_Post_CA_exposure_SunSet;

    [Tooltip("(HDRP) Post Colour Adjust Contrast at SunSet/SunRise")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_contrast_SunSet;

    [Tooltip("(HDRP) Post Colour Adjust Saturation at SunSet/SunRise")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_saturation_SunSet;

    [Tooltip("(HDRP) Post Bloom Threshold atSunSet/SunRise")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_threshold_SunSet;

    [Tooltip("(HDRP) Post Bloom Intensity at SunSet/SunRise")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_intensity_SunSet;
    public Color HDRPDistrictColourSunSet = new Color(.15f, .15f, .25f, .3f);


    [Header("________________________________________________________________________________________________________________")]
    [Header("MIDNIGHT SETTINGS (URP)")]
    [Space(8)]
    [Tooltip("Colour of Sun and the Sunlight it shines at Night (Alpha = Opacity of Sun Sprite) - Can be used to get some fill lighting at night if desired")]
    public Color sun_Color_Night;

    [Tooltip("Temperature of Sun (& Moon) in Kelvins at Night (Can be used to get some fill lighting at night if desired)")]
    [Range(1500.0f, 20000.0f)] 
    public float sun_Temperature_Night;

    [Tooltip("Intensity of Sun at Night (Can be used to get some fill lighting at night if desired)")]
    [Range(0f, 10.0f)]
    public float sun_Intensity_Night;

    [Tooltip("Colour of Moon and the light it shines at Night (Alpha = Opacity of Moon Sprite)")]
    public Color moon_Color_Night;

    [Tooltip("Temperature of Moon in Kelvins at Night")]
    [Range(1500.0f, 20000.0f)]
    public float moon_Temperature_Night;

    [Tooltip("Intensity of Moon at Night")]
    [Range(0f, 10.0f)]
    public float moon_Intensity_Night;

    [ColorUsageAttribute(false)]
    public Color ambientSkyColorNight;
    [ColorUsageAttribute(false)]
    public Color ambientEquatorColorNight;
    [ColorUsageAttribute(false)]
    public Color ambientGroundColorNight;

    [Tooltip("Fog Colour, Alpha = density multplier")]
    public Color fogColorNight;

    [Tooltip("Post Colour Adjust Exposure at Night")]
    [Range(-10f, 10.0f)]
    public float post_CA_exposure_Night;

    [Tooltip("Post Colour Adjust Contrast at Night")]
    [Range(-100f, 100f)]
    public float post_CA_contrast_Night;

    [Tooltip("Post Colour Adjust Saturation at Night")]
    [Range(-100f, 100f)]
    public float post_CA_saturation_Night;

    [Tooltip("Post Bloom Threshold at Night")]
    [Range(0, 1f)]
    public float post_BLM_threshold_Night;

    [Tooltip("Post Bloom Intensity at Night")]
    [Range(0, 1f)]
    public float post_BLM_intensity_Night;
    public Color DistrictColourNight = new Color(.05f, .05f, .05f, .5f);

    [Space(8)]
    [Header("MIDNIGHT SETTINGS (HDRP)")]
    [Space(8)]

    [Tooltip("(HDRP) Colour of Sun and the light it shines at Night (Alpha = Opacity of Sun Sprite) - Can be used to get some fill lighting at night if desired")]
    public Color HDRP_Sun_Color_Night;

    [Tooltip("(HDRP) Intensity of Sun at Night - Can be used to get some fill lighting at night if desired")]
    [Range(0f, 100.0f)]
    public float HDRP_Sun_Intensity_Night;

    [Tooltip("(HDRP) Temperature of Sun at Night")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Sun_Temperature_Night;

    [Tooltip("(HDRP) Colour of Moon and the light it shines at Night (Alpha = Opacity of Moon Sprite)")]
    public Color HDRP_Moon_Color_Night;

    [Tooltip("(HDRP) Intensity of Moon at Night")]
    [Range(0f, 100.0f)]
    public float HDRP_Moon_Intensity_Night;

    [Tooltip("(HDRP) Temperature of Moon at Night")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Moon_Temperature_Night;

    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Top_Night;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Middle_Night;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Bottom_Night;

    [Tooltip("(HDRP) Colour Of the cLouds - effects ambient lighting!")]
    [ColorUsageAttribute(false, false)]
    public Color HDRP_Cloud_Colour_Night;

    [Tooltip("(HDRP) Fog Attenutation Distance")]
    [Range(50f, 2500.0f)]
    public float HDRP_FogAttenuationDistance_Night;

    [Tooltip("(HDRP) Post Colour Adjust Exposure at Night")]
    [Range(-10f, 10.0f)]
    public float HDRP_Post_CA_exposure_Night;

    [Tooltip("(HDRP) Post Colour Adjust Contrast at Night")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_contrast_Night;

    [Tooltip("(HDRP) Post Colour Adjust Saturation at Night")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_saturation_Night;

    [Tooltip("(HDRP) Post Bloom Threshold at Night")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_threshold_Night;

    [Tooltip("(HDRP) Post Bloom Intensity at Night")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_intensity_Night;
    public Color HDRPDistrictColourNight = new Color(.05f, .05f, .05f, .5f);


    [Header("________________________________________________________________________________________________________________")]
    [Header("RESULTS OF MIXED SETTINGS (URP - READ ONLY)")]
    [Space(8)]

    [Tooltip("Final Colour of Sun and the Sunlight it shines")]
    public Color sun_Color_Mixed;

    [Tooltip("Final Temperature of Sun")]
    [Range(1500.0f, 20000.0f)]
    public float sun_Temperature_Mixed;

    [Tooltip("Final Intensity of Sun")]
    [Range(0f, 10.0f)]
    public float sun_Intensity_Mixed;

    [Tooltip("Final Colour of Moon and the light it shines")]
    public Color moon_Color_Mixed;

    [Tooltip("Final Temperature")]
    [Range(1500.0f, 20000.0f)]
    public float moon_Temperature_Mixed;

    [Tooltip("Final Intensity of Moon at Night")]
    [Range(0f, 10.0f)]
    public float moon_Intensity_Mixed;

    [ColorUsageAttribute(false)]
    public Color ambientSkyColorMixed;
    [ColorUsageAttribute(false)]
    public Color ambientEquatorColorMixed;
    [ColorUsageAttribute(false)]
    public Color ambientGroundColorMixed;
    public Color DistrictColourMixed;

    [Tooltip("Final Fog Colour, Alpha = density multplier")]

    public Color fogColorMixed;

    [Tooltip("Final Post Colour Adjust Exposure")]
    [Range(-10f, 10.0f)]
    public float post_CA_exposure_Mixed;

    [Tooltip("Final Post Colour Adjust Contrast")]
    [Range(-100f, 100f)]
    public float post_CA_contrast_Mixed;

    [Tooltip("Final Post Colour Adjust Saturation")]
    [Range(-100f, 100f)]
    public float post_CA_saturation_Mixed;

    [Tooltip("Final Post Bloom Threshold")]
    [Range(0, 1f)]
    public float post_BLM_threshold_Mixed;

    [Tooltip("Final Post Bloom Intensity")]
    [Range(0, 1f)]
    public float post_BLM_intensity_Mixed;

    [Space(8)]
    [Header("RESULTS OF MIXED SETTINGS (HDRP - READ ONLY)")]
    [Space(8)]

    [Tooltip("(HDRP) Final Colour of Sun and the light it shines (Alpha = Opacity of Sun Sprite)")]
    public Color HDRP_Sun_Color_Mixed;

    [Tooltip("(HDRP) Final Intensity of Sun")]
    [Range(0f, 100.0f)]
    public float HDRP_Sun_Intensity_Mixed;

    [Tooltip("(HDRP) Final Temperature of Sun")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Sun_Temperature_Mixed;

    [Tooltip("(HDRP) Final Colour of Moon and the light it shines (Alpha = Opacity of Moon Sprite)")]
    public Color HDRP_Moon_Color_Mixed;

    [Tooltip("(HDRP) Final Intensity of Moon")]
    [Range(0f, 100.0f)]
    public float HDRP_Moon_Intensity_Mixed;

    [Tooltip("(HDRP) Final Temperature of Moon")]
    [Range(1500.0f, 20000.0f)]
    public float HDRP_Moon_Temperature_Mixed;

    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Top_Mixed;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Middle_Mixed;
    [ColorUsageAttribute(false, true)]
    public Color HDRP_Sky_Gradient_Color_Bottom_Mixed;
    public Color HDRPDistrictColourMixed;

    [Tooltip("(HDRP) Final Colour Of the cLouds - effects ambient lighting!")]
    [ColorUsageAttribute(false, false)]
    public Color HDRP_Cloud_Colour_Mixed;

    [Tooltip("(HDRP) Final Fog Attenutation Distance")]
    [Range(50f, 500.0f)]
    public float HDRP_FogAttenuationDistance_Mixed;

    [Tooltip("(HDRP) Final Post Colour Adjust Exposure")]
    [Range(-10f, 10.0f)]
    public float HDRP_Post_CA_exposure_Mixed;

    [Tooltip("(HDRP) Final Post Colour Adjust Contrast")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_contrast_Mixed;

    [Tooltip("(HDRP) Final Post Colour Adjust Saturation")]
    [Range(-100f, 100f)]
    public float HDRP_Post_CA_saturation_Mixed;

    [Tooltip("(HDRP) Final Post Bloom Threshold")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_threshold_Mixed;

    [Tooltip("(HDRP) Final Post Bloom Intensity")]
    [Range(0, 1f)]
    public float HDRP_Post_BLM_intensity_Mixed;


    [Header("________________________________________________________________________________________________________________")]
    [Header("USEFUL VALUES (READ ONLY)")]
    [Space(8)]

    [Range(0f, 1.0f)]
    public float NightnessDayness;

    [Range(0f, 1.0f)]
    public float DayNightMix;

    [Range(0f, 1.0f)]
    public float SunSetMix;

    [Range(0f, 1.0f)]
    public float NightMix;

    [Range(0f, 360.0f)]
    public float sunAngle;

    [Range(0, Mathf.PI * 2)]
    public float sunAngleRadians;

    [Header("________________________________________________________________________________________________________________")]
    [Header("OBJECTS USED BY THIS SCRIPT")]
    [Space(8)]
    public Camera mainCamera;
    public Volume globalVolume;
    public GameObject sunRotater;
    public GameObject sunObject;
    public Light sunDirectionLight;
    public GameObject moonObject;
    public Light moonDirectionLight;
    public Material SkyBoxMaterial;
    public GameObject earthAngle;

    private UniversalAdditionalLightData lightData;
    private HDAdditionalLightData HDLightData;

    private float m_sunMoonBlend = 0;

    private float fogModifier = 1;  // This changes fog settings depending on if we are in possessed mode or not - i.e when possessing I make fog attenuation shorter, longer for God mode so less washed out at normal playing zoom

    private Dictionary<MonoBehaviour, float> m_darkenSources = new();
    private float m_darkenSmoothed = 0;
    
    public void SetDarkening(MonoBehaviour _source, float _darken)
    {
        if (_darken > 0)
            m_darkenSources[_source] = _darken;
        else
            m_darkenSources.Remove(_source);
    }

    private float GetMaxDarkening()
    {
        float maxDarkening = 0;
        foreach (var kvp in m_darkenSources)
            if (kvp.Value > maxDarkening)
                maxDarkening = kvp.Value;
        m_darkenSmoothed = Mathf.Lerp(m_darkenSmoothed, maxDarkening, .05f.TCLerp());
        return m_darkenSmoothed;
    }

    //-----------------------------------------------------------------------------------------------------------------------
    // Start is called before the first frame update
    //-----------------------------------------------------------------------------------------------------------------------
    void Start()
    {
        //Get Camera
        mainCamera = Camera.main;

        //Initialise Ambient Lighting Settings
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;

        //Initialise Fog Settings
        RenderSettings.fogMode = FogMode.ExponentialSquared;

        //Get additional light data
        lightData = sunDirectionLight.GetComponent<UniversalAdditionalLightData>();
        HDLightData = sunDirectionLight.GetComponent<HDAdditionalLightData>();
    }

    //-----------------------------------------------------------------------------------------------------------------------
    // Update is called once per frame
    //-----------------------------------------------------------------------------------------------------------------------
    void Update()
    {
        UpdateSky();
    }

    public void UpdateSky(bool _forceInterior = false)
    {

        //Only run in editor mode if correct toggle is on
        if (Application.isEditor && updateInEditor == false && Application.isPlaying == false)
        {
            return;
        }

        var isPossessing = GameManager.Me.IsPossessing && PlayerHandManager.Me.IsInPossessSequence == false;
        //Experiment make angle of earth rotate with camera so shadows are always falling away from players view
        if (SunPositionRelativeToCamera && earthAngle != null && mainCamera != null && (!isPossessing || (isPossessing && PossessionSunPositionRelativeToCamera)))
        {
            Vector3 newEarthAngle = earthAngle.transform.eulerAngles;
            newEarthAngle.y = Mathf.LerpAngle(newEarthAngle.y, mainCamera.transform.eulerAngles.y + 90, CatchupSpeedSunPositionRelativeToCamera);
            earthAngle.transform.eulerAngles = newEarthAngle; ;
        }

        //Experiment2 make angle of earth rotate with camera so shadows are always falling away from players view
        if (Option2SunPositionRelativeToCamera && earthAngle != null && mainCamera != null && (!isPossessing || (isPossessing && PossessionSunPositionRelativeToCamera)))
        {
            Vector3 newEarthAngle = earthAngle.transform.eulerAngles;

            //Work out if we facing more North or more South
            float yaw = Mathf.DeltaAngle(0, mainCamera.transform.eulerAngles.y); // Put in range [-180, 180]
            yaw = Mathf.Abs(yaw);
            if (Mathf.Abs(yaw) <= 90)
            {
                //North
                //newEarthAngle.y = Mathf.LerpAngle(newEarthAngle.y, -20, CatchupSpeedSunPositionRelativeToCamera);
                float lerpAmount = yaw / 90;
                lerpAmount = 1f - Mathf.Cos(lerpAmount * Mathf.PI * 0.5f); //Ease in
                newEarthAngle.z = Mathf.LerpAngle(30, 0 , lerpAmount);
            }
            else
            {
                //South
                //newEarthAngle.y = Mathf.LerpAngle(newEarthAngle.y, -60, CatchupSpeedSunPositionRelativeToCamera);
                float lerpAmount = (yaw - 90) / 90;
                lerpAmount = Mathf.Sin(lerpAmount * Mathf.PI * 0.5f);//Ease out
                newEarthAngle.z = Mathf.LerpAngle(0, -30, lerpAmount);
            }
            earthAngle.transform.eulerAngles = newEarthAngle;
        }

        //Follow camera so we are always in middle of Moon Sun Rotation (So they move properly with Sky box, i.e. no parralax)
        transform.position = mainCamera.transform.position;
        
        //Handle Day Night cycle (gets from other script that controls time of day, unlessoveride is on in which case can be conrolled manually by slider in editor)
        if (GlobalData.Me != null && overideDayNightCycleControl == false)
        {
            dayNightCycle = DayNight.Me.m_timeStage / DayNight.c_timeStages;
        }
        else if (animateDayNightCycle)
        {
            //Move and Loop Day Night value by Speed of Sun
            dayNightCycle += (dayNightCycleSpeed / 16) * Time.deltaTime;
            if (dayNightCycle > 1) { dayNightCycle -= 1; }
            if (dayNightCycle < 0) { dayNightCycle += 1; }
        }
        if (GameManager.Me.IsIn3DTitleScreen()) dayNightCycle = 0;


        //Convert Night Day value to Sun Angle from horizon
        sunAngle = (dayNightCycle * 360) - 90;
        if (sunAngle > 360) { sunAngle -= 360; }
        if (sunAngle < 0) { sunAngle += 360; }


        //Calculate amount of 'Dayness - nightness value to match current game read value)
        if (dayNightCycle >= 0 && dayNightCycle < 0.5f)
        {
            NightnessDayness = dayNightCycle * 2;
        }

        if (dayNightCycle >= 0.5f && dayNightCycle <= 1)
        {
            NightnessDayness = 1 - ((dayNightCycle - 0.5f) * 2);
        }
        
        //Work out current Ambient lighting and Fog colours
        //-----------------------------------------------------------------------------------------------------------------
        //Convert angle of Sun to Radians and work out Mix values
        sunAngleRadians = (sunAngle / 360f) * Mathf.PI * 2;
        DayNightMix = (1 + Mathf.Cos(sunAngleRadians + (Mathf.PI / 2))) / 2;
        SunSetMix = 1 - Mathf.Abs(Mathf.Sin(sunAngleRadians));


        var isInterior = (GameManager.Me?.IsInterior ?? false) || (TerrainPopulation.Me?.IsInMapMode ?? false) || _forceInterior;
        if (isInterior)
        {
            DayNightMix = 0;
            SunSetMix = 0;
        }

        if (DayNightMix <= 0.5f)
        {
            NightMix = 0;

            //Mix Colours that are between Day and Sunset/rise URP
            sun_Color_Mixed = Color.Lerp(sun_Color_Midday, sun_Color_SunSet, SunSetMix);
            sun_Temperature_Mixed = Mathf.Lerp(sun_Temperature_Midday, sun_Temperature_SunSet, SunSetMix);
            sun_Intensity_Mixed = Mathf.Lerp(sun_Intensity_Midday, sun_Intensity_SunSet, SunSetMix);

            moon_Color_Mixed = Color.Lerp(moon_Color_Midday, moon_Color_SunSet, SunSetMix);
            moon_Temperature_Mixed = Mathf.Lerp(moon_Temperature_Midday, moon_Temperature_SunSet, SunSetMix);
            moon_Intensity_Mixed = Mathf.Lerp(moon_Intensity_Midday, moon_Intensity_SunSet, SunSetMix);

            ambientSkyColorMixed = Color.Lerp(ambientSkyColorMidday, ambientSkyColorSunSet, SunSetMix);
            ambientEquatorColorMixed = Color.Lerp(ambientEquatorColorMidday, ambientEquatorColorSunSet, SunSetMix);
            ambientGroundColorMixed = Color.Lerp(ambientGroundColorMidday, ambientGroundColorSunSet, SunSetMix);
            fogColorMixed = Color.Lerp(fogColorMidday, fogColorSunSet, SunSetMix);

            post_CA_exposure_Mixed = Mathf.Lerp(post_CA_exposure_Midday, post_CA_exposure_SunSet, SunSetMix);
            post_CA_contrast_Mixed = Mathf.Lerp(post_CA_contrast_Midday, post_CA_contrast_SunSet, SunSetMix);
            post_CA_saturation_Mixed = Mathf.Lerp(post_CA_saturation_Midday, post_CA_saturation_SunSet, SunSetMix);
            post_BLM_threshold_Mixed = Mathf.Lerp(post_BLM_threshold_Midday, post_BLM_threshold_SunSet, SunSetMix);
            post_BLM_intensity_Mixed = Mathf.Lerp(post_BLM_intensity_Midday, post_BLM_intensity_SunSet, SunSetMix);

            //Mix Colours that are between Day and Sunset/rise HDRP
            HDRP_Sun_Color_Mixed = Color.Lerp(HDRP_Sun_Color_Midday, HDRP_Sun_Color_SunSet, SunSetMix);
            HDRP_Sun_Temperature_Mixed = Mathf.Lerp(HDRP_Sun_Temperature_Midday, HDRP_Sun_Temperature_SunSet, SunSetMix);
            HDRP_Sun_Intensity_Mixed = Mathf.Lerp(HDRP_Sun_Intensity_Midday, HDRP_Sun_Intensity_SunSet, SunSetMix);

            HDRP_Moon_Color_Mixed = Color.Lerp(HDRP_Moon_Color_Midday, HDRP_Moon_Color_SunSet, SunSetMix);
            HDRP_Moon_Temperature_Mixed = Mathf.Lerp(HDRP_Moon_Temperature_Midday, HDRP_Moon_Temperature_SunSet, SunSetMix);
            HDRP_Moon_Intensity_Mixed = Mathf.Lerp(HDRP_Moon_Intensity_Midday, HDRP_Moon_Intensity_SunSet, SunSetMix);

            HDRP_Sky_Gradient_Color_Top_Mixed = Color.Lerp(HDRP_Sky_Gradient_Color_Top_Midday, HDRP_Sky_Gradient_Color_Top_SunSet, SunSetMix);
            HDRP_Sky_Gradient_Color_Middle_Mixed = Color.Lerp(HDRP_Sky_Gradient_Color_Middle_Midday, HDRP_Sky_Gradient_Color_Middle_SunSet, SunSetMix);
            HDRP_Sky_Gradient_Color_Bottom_Mixed = Color.Lerp(HDRP_Sky_Gradient_Color_Bottom_Midday, HDRP_Sky_Gradient_Color_Bottom_SunSet, SunSetMix);

            HDRP_Cloud_Colour_Mixed = Color.Lerp(HDRP_Cloud_Colour_Midday, HDRP_Cloud_Colour_SunSet, SunSetMix);

            HDRP_FogAttenuationDistance_Mixed = Mathf.Lerp(HDRP_FogAttenuationDistance_Midday, HDRP_FogAttenuationDistance_SunSet, SunSetMix);
            HDRPDistrictColourMixed = Color.Lerp(HDRPDistrictColourMidday, HDRPDistrictColourSunSet, SunSetMix);

            HDRP_Post_CA_exposure_Mixed = Mathf.Lerp(HDRP_Post_CA_exposure_Midday, HDRP_Post_CA_exposure_SunSet, SunSetMix);
            HDRP_Post_CA_contrast_Mixed = Mathf.Lerp(HDRP_Post_CA_contrast_Midday, HDRP_Post_CA_contrast_SunSet, SunSetMix);
            HDRP_Post_CA_saturation_Mixed = Mathf.Lerp(HDRP_Post_CA_saturation_Midday, HDRP_Post_CA_saturation_SunSet, SunSetMix);
            HDRP_Post_BLM_threshold_Mixed = Mathf.Lerp(HDRP_Post_BLM_threshold_Midday, HDRP_Post_BLM_threshold_SunSet, SunSetMix);
            HDRP_Post_BLM_intensity_Mixed = Mathf.Lerp(HDRP_Post_BLM_intensity_Midday, HDRP_Post_BLM_intensity_SunSet, SunSetMix);
        }
        else
        {
            NightMix = 1 - SunSetMix;
            SunSetMix = 1;

            //Mix Colours that are between SunSet/Rise and Midnight URP
            sun_Color_Mixed = Color.Lerp(sun_Color_SunSet, sun_Color_Night, NightMix);
            sun_Temperature_Mixed = Mathf.Lerp(sun_Temperature_SunSet, sun_Temperature_Night, NightMix);
            sun_Intensity_Mixed = Mathf.Lerp(sun_Intensity_SunSet, sun_Intensity_Night, NightMix);

            moon_Color_Mixed = Color.Lerp(moon_Color_SunSet, moon_Color_Night, NightMix);
            moon_Temperature_Mixed = Mathf.Lerp(moon_Temperature_SunSet, moon_Temperature_Night, NightMix);
            moon_Intensity_Mixed = Mathf.Lerp(moon_Intensity_SunSet, moon_Intensity_Night, NightMix);

            ambientSkyColorMixed = Color.Lerp(ambientSkyColorSunSet, ambientSkyColorNight, NightMix);
            ambientEquatorColorMixed = Color.Lerp(ambientEquatorColorSunSet, ambientEquatorColorNight, NightMix);
            ambientGroundColorMixed = Color.Lerp(ambientGroundColorSunSet, ambientGroundColorNight, NightMix);
            fogColorMixed = Color.Lerp(fogColorSunSet, fogColorNight, NightMix);
            DistrictColourMixed = Color.Lerp(DistrictColourSunSet, DistrictColourNight, NightMix);

            post_CA_exposure_Mixed = Mathf.Lerp(post_CA_exposure_SunSet, post_CA_exposure_Night, NightMix);
            post_CA_contrast_Mixed = Mathf.Lerp(post_CA_contrast_SunSet, post_CA_contrast_Night, NightMix);
            post_CA_saturation_Mixed = Mathf.Lerp(post_CA_saturation_SunSet, post_CA_saturation_Night, NightMix);
            post_BLM_threshold_Mixed = Mathf.Lerp(post_BLM_threshold_SunSet, post_BLM_threshold_Night, NightMix);
            post_BLM_intensity_Mixed = Mathf.Lerp(post_BLM_intensity_SunSet, post_BLM_intensity_Night, NightMix);

            //Mix Colours that are between SunSet/Rise and Midnight HDRP
            HDRP_Sun_Color_Mixed = Color.Lerp(HDRP_Sun_Color_SunSet, HDRP_Sun_Color_Night, NightMix);
            HDRP_Sun_Temperature_Mixed = Mathf.Lerp(HDRP_Sun_Temperature_SunSet, HDRP_Sun_Temperature_Night, NightMix);
            HDRP_Sun_Intensity_Mixed = Mathf.Lerp(HDRP_Sun_Intensity_SunSet, HDRP_Sun_Intensity_Night, NightMix);

            HDRP_Moon_Color_Mixed = Color.Lerp(HDRP_Moon_Color_SunSet, HDRP_Moon_Color_Night, NightMix);
            HDRP_Moon_Temperature_Mixed = Mathf.Lerp(HDRP_Moon_Temperature_SunSet, HDRP_Moon_Temperature_Night, NightMix);
            HDRP_Moon_Intensity_Mixed = Mathf.Lerp(HDRP_Moon_Intensity_SunSet, HDRP_Moon_Intensity_Night, NightMix);

            HDRP_Sky_Gradient_Color_Top_Mixed = Color.Lerp(HDRP_Sky_Gradient_Color_Top_SunSet, HDRP_Sky_Gradient_Color_Top_Night, NightMix);
            HDRP_Sky_Gradient_Color_Middle_Mixed = Color.Lerp(HDRP_Sky_Gradient_Color_Middle_SunSet, HDRP_Sky_Gradient_Color_Middle_Night, NightMix);
            HDRP_Sky_Gradient_Color_Bottom_Mixed = Color.Lerp(HDRP_Sky_Gradient_Color_Bottom_SunSet, HDRP_Sky_Gradient_Color_Bottom_Night, NightMix);

            HDRP_Cloud_Colour_Mixed = Color.Lerp(HDRP_Cloud_Colour_SunSet, HDRP_Cloud_Colour_Night, NightMix);

            HDRP_FogAttenuationDistance_Mixed = Mathf.Lerp(HDRP_FogAttenuationDistance_SunSet, HDRP_FogAttenuationDistance_Night, NightMix);
            HDRPDistrictColourMixed = Color.Lerp(HDRPDistrictColourSunSet, HDRPDistrictColourNight, NightMix);

            HDRP_Post_CA_exposure_Mixed = Mathf.Lerp(HDRP_Post_CA_exposure_SunSet, HDRP_Post_CA_exposure_Night, NightMix);
            HDRP_Post_CA_contrast_Mixed = Mathf.Lerp(HDRP_Post_CA_contrast_SunSet, HDRP_Post_CA_contrast_Night, NightMix);
            HDRP_Post_CA_saturation_Mixed = Mathf.Lerp(HDRP_Post_CA_saturation_SunSet, HDRP_Post_CA_saturation_Night, NightMix);
            HDRP_Post_BLM_threshold_Mixed = Mathf.Lerp(HDRP_Post_BLM_threshold_SunSet, HDRP_Post_BLM_threshold_Night, NightMix);
            HDRP_Post_BLM_intensity_Mixed = Mathf.Lerp(HDRP_Post_BLM_intensity_SunSet, HDRP_Post_BLM_intensity_Night, NightMix);
        }

        var lightDirectionEulers = new Vector3(sunAngle, 0, 0);
        if (isInterior)
        {
            sun_Color_Mixed = m_interiorLightColour;
            sun_Temperature_Mixed = m_interiorLightTemperature;
            sun_Intensity_Mixed = sun_Intensity_Midday;
            moon_Intensity_Mixed = 0;
            HDRP_Sun_Color_Mixed = m_interiorLightColour;
            HDRP_Sun_Temperature_Mixed = m_interiorLightTemperature;
            HDRP_Sun_Intensity_Mixed = HDRP_Sun_Intensity_Midday;
            HDRP_Moon_Intensity_Mixed = 0;
            HDRP_FogAttenuationDistance_Mixed = 16000;
            sunAngle = 90;
            lightDirectionEulers = m_interiorLightDirection;
        }

        // Darkening
        var darkeningMultiplier = 1 - GetMaxDarkening();
        HDRP_Sun_Intensity_Mixed *= darkeningMultiplier;
        HDRP_Moon_Intensity_Mixed *= darkeningMultiplier;
        HDRP_Sky_Gradient_Color_Top_Mixed *= darkeningMultiplier;
        HDRP_Sky_Gradient_Color_Middle_Mixed *= darkeningMultiplier;
        HDRP_Sky_Gradient_Color_Bottom_Mixed *= darkeningMultiplier;

        //Move Sun through sky
        //-----------------------------------------------------------------------------------------------------------------
        if (sunRotater)
        {
            sunRotater.transform.localRotation = Quaternion.Euler(lightDirectionEulers);
        }
        
        //Set Sun/Moon + lighting + Fog colours
        //-----------------------------------------------------------------------------------------------------------------
        if (sunDirectionLight && moonDirectionLight)
        {
            if (GameSettings.SRPOptions.IsURP)
            {
                sunDirectionLight.color = sun_Color_Mixed;
                sunDirectionLight.colorTemperature = sun_Temperature_Mixed;

                moonDirectionLight.color = moon_Color_Mixed;
                moonDirectionLight.colorTemperature = moon_Temperature_Mixed;
            }
            else
            {
                var sunHDAL = sunDirectionLight.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
                var moonHDAL = moonDirectionLight.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();

                sunHDAL.SetColor(HDRP_Sun_Color_Mixed, HDRP_Sun_Temperature_Mixed);
                moonHDAL.SetColor(HDRP_Moon_Color_Mixed, HDRP_Moon_Temperature_Mixed);
            }      

            //Fade Out Sun Shadows when sun is nearing Horizon
            float shadow_Strength = 0;
            var activeLight = moonDirectionLight;

            if (sunAngle > 0 && sunAngle < 90)
            {
                shadow_Strength = sunAngle / 90;
                activeLight = sunDirectionLight;
            }

            if (sunAngle >= 90 && sunAngle < 180)
            {
                shadow_Strength = 1 - ((sunAngle - 90) / 90);
                activeLight = sunDirectionLight;
            }

            //Fade Out Moon Shadows when sun is nearing Horizon
            if (sunAngle > 180 && sunAngle < 270)
            {
                shadow_Strength = ((sunAngle - 180) / 90);
                activeLight = moonDirectionLight;
            }

            if (sunAngle >= 270 && sunAngle < 360)
            {
                shadow_Strength = 1 - ((sunAngle - 270) / 90);
                activeLight = moonDirectionLight;
            }

            //Dont know what this does, I think it was something Gary did to try and fix dark crossever peroid, don't think is needed now.
 /*     
            float sunMoonBlendTarget = (activeLight == sunDirectionLight) ? 1 : 0;
            m_sunMoonBlend = Mathf.Lerp(m_sunMoonBlend, sunMoonBlendTarget, .1f);
            HDRP_Sun_Intensity_Mixed *= m_sunMoonBlend;
            HDRP_Moon_Intensity_Mixed *= (1.0f - m_sunMoonBlend);
 */
            //Shadow Fade in/out Speed
            shadow_Strength *= 2.5f;

            //Max Shadow Strength
            if (shadow_Strength > ShadowStrengthMax)
            {
                shadow_Strength = ShadowStrengthMax;
            }
            
            //Set Shadow Strength URP
            if (GameSettings.SRPOptions.IsURP)
            {
                if (sunAngle >= 0 && sunAngle < 180)
                {
                    sunDirectionLight.shadowStrength = shadow_Strength;
                }

                if (sunAngle >= 180 && sunAngle <= 360)
                {
                    moonDirectionLight.shadowStrength = shadow_Strength;
                }
            }
            else
            {
                //Set Shadow Strength HDRP
                var sunHDAL = sunDirectionLight.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
                var moonHDAL = moonDirectionLight.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();

                if (sunAngle >= 0 && sunAngle < 180)
                {
                    sunHDAL.shadowDimmer = shadow_Strength;
                }

                if (sunAngle >= 180 && sunAngle <= 360)
                {
                    moonHDAL.shadowDimmer = shadow_Strength;
                }
            }


            //Set Intensity of Sun and Moon URP
            if (GameSettings.SRPOptions.IsURP)
            {
                sunDirectionLight.intensity = sun_Intensity_Mixed;
                moonDirectionLight.intensity = moon_Intensity_Mixed;
            }
            else
            {
                //Set Intensity of Sun and Moon HDRP
                var sunHDAL = sunDirectionLight.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
                var moonHDAL = moonDirectionLight.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
                sunHDAL.intensity = HDRP_Sun_Intensity_Mixed;
                moonHDAL.intensity = HDRP_Moon_Intensity_Mixed;
            }

            // Unity getter/setters can be expensive and may have side-effects so only set if a change has occurred
            var inactiveLight = activeLight == sunDirectionLight ? moonDirectionLight : sunDirectionLight;
            if (activeLight.shadows != LightShadows.Soft)
                activeLight.shadows = LightShadows.Soft;
            if (inactiveLight.shadows != LightShadows.None)
                inactiveLight.shadows = LightShadows.None;
            if (RenderSettings.sun != activeLight)
                RenderSettings.sun = activeLight;
        }
        
        //Modify Colour of Sun & Moon Objects (Only do this while game is running as Unity complains otherwise)
        if (sunObject && moonObject && Application.isPlaying)
        {
            if (GameSettings.SRPOptions.IsURP)
            {
                //URP
                sunObject.GetComponent<Renderer>().material.SetColor("_Color", sun_Color_Mixed + (sun_Color_Mixed * sun_Intensity_Mixed * 0.05f));
                moonObject.GetComponent<Renderer>().material.SetColor("_Color", moon_Color_Mixed + (moon_Color_Mixed * moon_Intensity_Mixed * 0.05f));
            }
            else
            {
                //HDRP
                sunObject.GetComponent<Renderer>().material.SetColor("_Color", HDRP_Sun_Color_Mixed);
                moonObject.GetComponent<Renderer>().material.SetColor("_Color", HDRP_Moon_Color_Mixed);
            }

            //Rotate Sun for Fun
            if (animateDayNightCycle)
            {
                sunObject.transform.Rotate(new Vector3(0, 0, 10 * Time.deltaTime));
            }
        }

        //Set Ambient Lighting
        RenderSettings.ambientMode = AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = ambientSkyColorMixed;
        RenderSettings.ambientEquatorColor = ambientEquatorColorMixed;
        RenderSettings.ambientGroundColor = ambientGroundColorMixed;

        //Set Fog URP
        RenderSettings.fog = renderFog;
        RenderSettings.fogColor = fogColorMixed;
        RenderSettings.fogDensity = maxFogDensity * fogColorMixed.a;

        //SkyBox Shader URP -  Using Marcos Blended Skybox Shader
        if (GameSettings.SRPOptions.IsURP && SkyBoxMaterial)
        {
            if (RenderSettings.skybox == null)
            {
                RenderSettings.skybox = SkyBoxMaterial;
            }

            if (RenderSettings.skybox.HasProperty("_Blend2"))
            {
                RenderSettings.skybox.SetFloat("_Blend", SunSetMix);
                RenderSettings.skybox.SetFloat("_Blend2", NightMix);
                RenderSettings.skybox.SetFloat("_Rotation", 360 - sunAngle);
            }
            
        }

        //SkyBox Shader HDRP -  Uses Unitys Gradient Sky
        if (!GameSettings.SRPOptions.IsURP && globalVolume)
        {
            GradientSky sky;
            globalVolume.profile.TryGet(out sky);
            if (sky)
            {
                sky.top.value = HDRP_Sky_Gradient_Color_Top_Mixed;
                sky.middle.value = HDRP_Sky_Gradient_Color_Middle_Mixed;
                sky.bottom.value = HDRP_Sky_Gradient_Color_Bottom_Mixed;
            }

            CloudLayer clouds;
            globalVolume.profile.TryGet(out clouds);
            if (clouds)
            {
                clouds.layerA.tint.value = HDRP_Cloud_Colour_Mixed;

                //Animate clouds
                clouds.layerA.rotation.value += 0.002f; 
                if (clouds.layerA.rotation.value>360)
                {
                    clouds.layerA.rotation.value -= 360;
                }

            }
        }

        Shader.SetGlobalVector("_OwnLandBaseColor", GameSettings.SRPOptions.IsURP ? DistrictColourMixed : HDRPDistrictColourMixed);

        //Post Processing URP
        if (GameSettings.SRPOptions.IsURP && globalVolume)
        {
            UnityEngine.Rendering.Universal.ColorAdjustments colorAdjustments; 
            if (globalVolume.profile.TryGet<UnityEngine.Rendering.Universal.ColorAdjustments>(out colorAdjustments))
            {
                colorAdjustments.postExposure.Override(post_CA_exposure_Mixed);
                colorAdjustments.contrast.Override(post_CA_contrast_Mixed);
                colorAdjustments.saturation.Override(post_CA_saturation_Mixed);
            }

            UnityEngine.Rendering.Universal.Bloom bloom;
            if (globalVolume.profile.TryGet<UnityEngine.Rendering.Universal.Bloom>(out bloom))
            {
                bloom.threshold.Override(post_BLM_threshold_Mixed);
                bloom.intensity.Override(post_BLM_intensity_Mixed);
            }
        }

        //Post Processing HDRP
        if (!GameSettings.SRPOptions.IsURP && globalVolume)
        {
            UnityEngine.Rendering.HighDefinition.ColorAdjustments colorAdjustments;
            if (globalVolume.profile.TryGet<UnityEngine.Rendering.HighDefinition.ColorAdjustments>(out colorAdjustments))
            {
                float adjustExposure;
                /*
                //Hack to make everyhting a bit brighter at dark zone during sunrise
                float adjustExposureMultiplier = 0.1f;

                if (dayNightCycle > 0.20 && dayNightCycle < 0.30)
                {
                    if (dayNightCycle > 0.20f && dayNightCycle <= 0.25f)
                    {
                        adjustExposure = Mathf.Abs (0.20f - dayNightCycle);
                    }
                    else
                    {
                        adjustExposure = 0.30f - dayNightCycle;
                    }

                    HDRP_Post_CA_exposure_Mixed += adjustExposure * adjustExposureMultiplier;
                }

                //Hack to make everyhting a bit brighter at dark zone during  senset
                if (dayNightCycle > 0.70 && dayNightCycle < 0.80)
                {
                    if (dayNightCycle > 0.70f && dayNightCycle <= 0.75f)
                    {
                        adjustExposure = Mathf.Abs(0.70f - dayNightCycle);
                    }
                    else
                    {
                        adjustExposure = 0.80f - dayNightCycle;
                    }

                    HDRP_Post_CA_exposure_Mixed += adjustExposure * adjustExposureMultiplier;
                }
                */
                colorAdjustments.postExposure.Override(HDRP_Post_CA_exposure_Mixed);
                colorAdjustments.contrast.Override(HDRP_Post_CA_contrast_Mixed);
                colorAdjustments.saturation.Override(HDRP_Post_CA_saturation_Mixed);
            }

            UnityEngine.Rendering.HighDefinition.Bloom bloom;
            if (globalVolume.profile.TryGet<UnityEngine.Rendering.HighDefinition.Bloom>(out bloom))
            {
                bloom.threshold.Override(HDRP_Post_BLM_threshold_Mixed);
                bloom.intensity.Override(HDRP_Post_BLM_intensity_Mixed);
            }

            UnityEngine.Rendering.HighDefinition.Fog fog;
            if (globalVolume.profile.TryGet<UnityEngine.Rendering.HighDefinition.Fog>(out fog))
            {
                if (isPossessing)
                {
                    fogModifier = Mathf.Lerp(fogModifier, 0.35f,0.1f);
                }
                else
                {
                    fogModifier = Mathf.Lerp(fogModifier, 1.0f, 0.1f);
                }

                fog.meanFreePath.Override(HDRP_FogAttenuationDistance_Mixed*fogModifier); //Stupid Unity API name grrrr!
            }
        }

        //----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
        //Hack to make crossover from Sun LIght to Moonlight less dark!
        //This is issue when time of day is 0.25 or 0.75, as both sun light and moon light directions are horizontal
        //I alter actual direction of lights around 0.25 and 0.75 to poiunt down a bit more, which kind of emuilates fact sun and moon are big discs and not a single point in middle.


        //Check if time to apply hack
        float moonLightAngleAdjustAmount = 0;
        float sunLightAngleAdjustAmount = 0;

        if (dayNightCycle > 0.20 && dayNightCycle < 0.30)
        {
            if (dayNightCycle > 0.20 && dayNightCycle <= 0.25)
            {
                moonLightAngleAdjustAmount = (dayNightCycle - 0.20f) * 256;
                sunLightAngleAdjustAmount = (0.20f - dayNightCycle) * 256;
            }
            else
            {
                moonLightAngleAdjustAmount = (0.30f - dayNightCycle) * 256;
                sunLightAngleAdjustAmount = (dayNightCycle - 0.30f) * 256;
            }
        }
        else if (dayNightCycle > 0.70 && dayNightCycle < 0.80)
        {
            if (dayNightCycle > 0.70 && dayNightCycle <= 0.75)
            {
                moonLightAngleAdjustAmount = -(dayNightCycle - 0.70f) * 256;
                sunLightAngleAdjustAmount = -(0.70f - dayNightCycle) * 256;
            }
            else
            {
                moonLightAngleAdjustAmount = -(0.80f - dayNightCycle) * 256;
                sunLightAngleAdjustAmount = -(dayNightCycle - 0.80f) * 256;
            }
        }

        //Set moon and sun light to angle based on time of day (i.e no hack being used/as it was before)
        moonDirectionLight.transform.localEulerAngles = new Vector3(180 - moonLightAngleAdjustAmount, 0, 0);
        sunDirectionLight.transform.localEulerAngles = new Vector3(0 - sunLightAngleAdjustAmount, 0, 0);


        //----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    }

    public void SetShadowResolution(int _res)
    {
        sunDirectionLight.SetShadowResolution(_res);
        moonDirectionLight.SetShadowResolution(_res);
    }
}
