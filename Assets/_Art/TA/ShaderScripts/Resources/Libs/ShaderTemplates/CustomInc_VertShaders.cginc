#ifndef CUSTOMINC_VERT_SHADERS
#define CUSTOMINC_VERT_SHADERS

#include "../CustomInc_SubMove.cginc"

half _WindDisplacementAmount;

#if TERRAIN_SHADER

	struct v2f_Terrain
	{
		_BASE_VARS(0)
		_WORLDPOS_VARS(5)
		SHADOW_COORDS(6) // put shadows data into TEXCOORD6
		_TRIPLANAR_VARS(7)
		_NTB_VARS(8, 9, 10)
		_LIGHTING_VARS(1, 12)
		_DISTANCE_VARS(13)
		_CONTROL_VARS(14)
		#if !WRITE_DEPTH_NORMALS
			_SCREENPOS_VAR(15)
		#endif
		float4 color : COLOR;
		_WORLDUV_VARS(16)
	};

	v2f_Terrain vert_Terrain(appdata_instancing v)
	{
		v2f_Terrain o;
		_VERT_INITIALIZE(o, v, v2f_Terrain);

		_CALC_WORLDPOS(o, v);
		_CALC_WORLDUVS(o);
		float4 patchUVs = worldUVs;
		o.worldUVs = worldUVs.xy;

		#if HEIGHTMAP_DISPLACE_ON
			v.vertex.y = 0;

			o.uv = patchUVs.xy;

			float4 worldPosXZ = float4(o.worldPos.x, v.vertex.y, o.worldPos.z, v.vertex.w);

			float offset = _GrassYOffset(_HeightMap, patchUVs);
			o.worldPos = _OffsetVertex(worldPosXZ, float3(0, offset, 0));

			half bumpAmount = 2048; 
			half3 normalFromGrayscale = _NormalFromGrayscale_VTX(patchUVs, _HeightMap, _HeightMap_TexelSize, bumpAmount);
			v.normal = normalFromGrayscale;






			float3 normal = float3(0, 0, 1);

			float heightSampleCenter = dot(tex2Dlod(_HeightMap, patchUVs), _HeightmapDot);
			float heightSampleRight = dot(tex2Dlod(_HeightMap, patchUVs + float4(_HeightMap_TexelSize.x, 0, 0, 0)), _HeightmapDot);
			float heightSampleUp = dot(tex2Dlod(_HeightMap, patchUVs + float4(0, _HeightMap_TexelSize.y, 0, 0)), _HeightmapDot);
			bumpAmount /= 2048.0;
		
			float sampleDeltaRight = heightSampleRight - heightSampleCenter;
			float sampleDeltaUp = heightSampleUp - heightSampleCenter;

			normal = cross(
				float3(1, 0, sampleDeltaRight * bumpAmount),
				float3(0, 1, sampleDeltaUp * bumpAmount));


			normal = normalize(normal).xzy;
		#else
			_CALC_WORLDPOS(o, v);
		#endif

	
		o.pos = UnityObjectToClipPos(mul(UNITY_MATRIX_I_M, o.worldPos));

		_CalculateDistanceFog(v.vertex, o.distanceFog);
		_WORLD_NORMAL_VERT(v);
		_CALC_NTB(o, v);
		_CALC_CONTROL_PARAMS(o, v);
		_TERRAIN_LIGHTING(o);

		#if !WRITE_DEPTH_NORMALS
			_CALC_SCREENPOS(o);
			o.color = v.color;
		#endif // SCREEN_NORMALS_LIGHTING

		return o;
	}

#endif

#if STANDARD_SHADER
	Base_Defs_VF;

	struct v2f_Standard
	{
		_BASE_VARS(0)
		_VIEWDIR_VARS(1)
		_WORLDPOS_VARS(2)
		_DISTANCE_VARS(3)
		_NTB_VARS(4, 5, 6)
		_LIGHTING_VARS(0, 7)
		float4 color : COLOR1;
		// SHADOW_COORDS(8)
		_TINT_WINDOW_INPUT_VARS(9, 10, 11, 12)
		#if _USE_DITHER_OPACITY
			_SCREENPOS_VAR(13)
		#endif
		_PREMIUM_VARS(14)
		#if LOW_SPEC
			_WORLDNORMAL_VARS(15)
		#endif
		_AGING_UV_VARS(16)
		
		#if _AO_ON_UV_2
		half2 uv2 : TEXCOORD17;
		#endif
		
		UNITY_VERTEX_INPUT_INSTANCE_ID
	};

	v2f_Standard vert_Standard (appdata_instancing v)
	{
		v2f_Standard o;
		UNITY_SETUP_INSTANCE_ID(v);
		o = (v2f_Standard)0;
		 _VERT_INITIALIZE(o, v, v2f_Standard);
		_FRAC_TIME_Y;

		ApplyAllSubMoves(v.vertex);
		
		TintWindow_Vertex_VF;
		_WORLD_NORMAL_VERT(v);

		#if LOW_SPEC
			o.worldNormal = worldNormal;
		#endif
		_CALC_WORLDPOS(o, v);
		_CALC_NTB(o, v);
		o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos.xyz);
		float nl = 1;

		_CACHED_SHADOW_VERT(o);
		_CalculateDistanceFog(v.vertex, o.distanceFog);

		#if _WORLD_UVS
		o.uv = (v.vertex.xz + o.worldPos.xz) * .05;
		#endif
		o.uv *= _MainTex_ST.xy;
		o.uv += _MainTex_ST.zw;
		o.uv += half2(_UVScrolling.x * _Time.y, _UVScrolling.y * _Time.y);

		float3 vertDisp = _WindTreeDisplacement(v.vertex, o.worldPos) * _WindDisplacementAmount;
		v.vertex.xyz += vertDisp.xyz;

		o.pos = TransformObjectToHClip(v.vertex);
		#if _USE_DITHER_OPACITY
			_CALC_SCREENPOS(o)
		#endif
		o.agingUV = v.uv * 5;

		#if _AO_ON_UV_2
		o.uv2 = v.uv_2;
		#endif
		
		TRANSFER_SHADOW(o)
		return o;
	}
#endif 

#if BASIC_SCENERY

struct v2f_BasicScenery
{
	_BASE_VARS(0)
	_VIEWDIR_VARS(1)
	_WORLDPOS_VARS(2)
	_DISTANCE_VARS(3)
	_LIGHTING_VARS(0, 7)
	SHADOW_COORDS(8)
	_WORLDNORMAL_VARS(9)
	_WORLDAXIS_VARS(10)
	UNITY_VERTEX_INPUT_INSTANCE_ID
};

sampler2D _MainTex; 
float4 _MainTex_ST;

v2f_BasicScenery vert_BasicScenery (appdata_instancing v)
{
	v2f_BasicScenery o;
	UNITY_SETUP_INSTANCE_ID(v);
	_VERT_INITIALIZE(o, v, v2f_BasicScenery);
	_CALC_WORLDPOSAXIS(o, v);
	_WORLD_NORMAL_VERT(v);
	o.worldNormal = worldNormal;
	_CALC_WORLDPOS(o, v);
	o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos.xyz);
	float nl = 1;
	_CACHED_SHADOW_VERT(o);
	_CalculateDistanceFog(v.vertex, o.distanceFog);
	o.uv *= _MainTex_ST.xy;
	o.uv += _MainTex_ST.zw;
	o.pos = UnityObjectToClipPos(v.vertex);
	TRANSFER_SHADOW(o)
	return o;
}

#endif

#if CSGCUBOID_SHADER
	Base_Defs_VF;

	struct v2f_CSGCuboid
	{
		_BASE_VARS(0)
		_VIEWDIR_VARS(1)
		_WORLDPOS_VARS(2)
		_DISTANCE_VARS(3)
		_NTB_VARS(4, 5, 6)
		_LIGHTING_VARS(0, 7)
		float4 color : COLOR1;
		SHADOW_COORDS(8)
		_TINT_WINDOW_INPUT_VARS(9, 10, 11, 12)
		_SCREENPOS_VAR(13)
		_PREMIUM_VARS(14)
		UVClip_Input(15)
		#if LOW_SPEC
			_WORLDNORMAL_VARS(16)
			half vertShadow : TEXCOORD17;
		#endif
		UNITY_VERTEX_INPUT_INSTANCE_ID
	};

	v2f_CSGCuboid vert_CSGCuboid(appdata_instancing v)
	{
		v2f_CSGCuboid o;
		UNITY_SETUP_INSTANCE_ID(v);
		_VERT_INITIALIZE(o, v, v2f_CSGCuboid);
		_FRAC_TIME_Y;
		TintWindow_Vertex_VF;
		_WORLD_NORMAL_VERT(v);
		#if LOW_SPEC
			o.worldNormal = worldNormal;
		#endif
		_CALC_WORLDPOS(o, v);
		_CALC_NTB(o, v);
		o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos.xyz);
		float nl = 1;
		_CACHED_SHADOW_VERT(o);
		_CalculateDistanceFog(v.vertex, o.distanceFog);
		o.uv *= _MainTex_ST.xy;
		o.uv += _MainTex_ST.zw;
		o.uv += half2(_UVScrolling.x * _Time.y, _UVScrolling.y * _Time.y);
		o.pos = UnityObjectToClipPos(v.vertex);
		_CALC_SCREENPOS(o)
		UVClip_Vertex;
		TRANSFER_SHADOW(o)
		#if LOW_SPEC
			float cachedShadowClouds;
			_CloudsShadows_Vertex(cachedShadowClouds, v.vertex);
			o.vertShadow = cachedShadowClouds;
		#endif
		return o;
	}
#endif 

#if BOXCLIP_SHADER
	Base_Defs_VF;

	struct v2f_BoxClip
	{
		_BASE_VARS(0)
		_WORLDPOS_VARS(1)
		float4 color : COLOR1;
		_TINT_WINDOW_INPUT_VARS(2, 3, 4, 5)
		_PREMIUM_VARS(6)
		_NTB_VARS(7, 8, 9)
		_LIGHTING_VARS(0, 10)
		_DISTANCE_VARS(11)
		_VIEWDIR_VARS(12)
	};


	v2f_BoxClip vert_BoxClip(appdata v)
	{
		v2f_BoxClip o;
		_VERT_INITIALIZE(o, v, v2f_BoxClip);
		_FRAC_TIME_Y;
		TintWindow_Vertex_VF;
		_WORLD_NORMAL_VERT(v);
		#if LOW_SPEC
			o.worldNormal = worldNormal;
		#endif
		_CALC_WORLDPOS(o, v);
		_CALC_NTB(o, v);
		o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos.xyz);
		float nl = 1;
		_CACHED_SHADOW_VERT(o);
		_CalculateDistanceFog(v.vertex, o.distanceFog);
		o.uv *= _MainTex_ST.xy;
		o.uv += _MainTex_ST.zw;
		o.uv += half2(_UVScrolling.x * _Time.y, _UVScrolling.y * _Time.y);
		o.pos = UnityObjectToClipPos(v.vertex);
		return o;
	}
#endif

#if WORLD_UI
	#if PROGRESS_BAR
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_UI.cginc"


		float4 _BackgroundCol;
		float _CornerRoundness;


		#if HAS_SECOND_PROGRESS
			float4 _SecondProgressBarCol;
			UNITY_INSTANCING_BUFFER_START(Props)
				UNITY_DEFINE_INSTANCED_PROP(float4, _ProgressBarCol)
				UNITY_DEFINE_INSTANCED_PROP(float, _Progress)
				UNITY_DEFINE_INSTANCED_PROP(float, _SecondProgressBar)
			UNITY_INSTANCING_BUFFER_END(Props)
		#else
			UNITY_INSTANCING_BUFFER_START(Props)
				UNITY_DEFINE_INSTANCED_PROP(float4, _ProgressBarCol)
				UNITY_DEFINE_INSTANCED_PROP(float, _Progress)
			UNITY_INSTANCING_BUFFER_END(Props)
		#endif

		struct v2f_ProgressBar
		{
			_BASE_VARS(0)
			SHADOW_COORDS(3) 
			_LIGHTING_VARS(0, 12)
			_WORLDPOS_VARS(6)
			_DISTANCE_VARS(7)
			_NTB_VARS(8,9,10)
			UNITY_VERTEX_INPUT_INSTANCE_ID
		};


		v2f_ProgressBar vert_ProgressBar (appdata_instancing v)
		{
			v2f_ProgressBar o;
			_VERT_INITIALIZE(o, v, v2f_ProgressBar);

			UNITY_SETUP_INSTANCE_ID(v);
			UNITY_TRANSFER_INSTANCE_ID(v, o);

			_WORLD_NORMAL_VERT(v); 
			_CALC_WORLDPOS(o, v);
				
			_CALC_NTB(o, v);
			o.uv = v.uv;

				
			half nl = 1;  // cancelling out vert_ProgressBar shading in favour of per pixel shading

			_CALC_WORLDPOS(o, v);
			_CACHED_SHADOW_VERT(o);
			_CalculateDistanceFog(v.vertex, o.distanceFog);
			o.pos = UnityObjectToClipPos(v.vertex);
			TRANSFER_SHADOW(o)
			return o;
		}
	#endif

	#if COUNTER
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
        #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_UI.cginc"

           

        struct v2f_Counter
        {
            float2 uv : TEXCOORD0;
            float4 pos : SV_POSITION;

            half2 uv_Units : TEXCOORD1;
            half4 clip_Units : TEXCOORD2;

            half2 uv_Tens : TEXCOORD3;
            half4 clip_Tens : TEXCOORD4;

            half2 uv_Hundreds : TEXCOORD5;
            half4 clip_Hundreds : TEXCOORD6;
                
            half2 uv_Thousands : TEXCOORD7;
            half4 clip_Thousands : TEXCOORD8;

            half4 valExists : TEXCOORD9;
                
            #if USE_ICON & !_MODE_NONE
                half2 uv_Icon : TEXCOORD10;
                half4 clip_Icon : TEXCOORD11;
            #endif
                
            #if (USE_ICON | USE_DECIMAL_POINT | _MODE_MONEYEARNED) & !_MODE_NONE
                half2 uv_Decimal : TEXCOORD12;
                half4 clip_Decimal : TEXCOORD13;
            #endif

			_WORLDPOS_VARS(14)
			
			UNITY_VERTEX_INPUT_INSTANCE_ID
        };

        sampler2D _NumbersTex;
        float4 _NumbersTex_ST;
        half _NumberSpacing;
        half _Mode;

        #if USE_ICON
            sampler2D _IconsTex;
            float4 _IconsTex_ST;
        #endif

        #if _MODE_MONEYEARNED | USE_DECIMAL_POINT
            sampler2D _DecimalTex;
            float4 _DecimalTex_ST;
        #endif

        UNITY_INSTANCING_BUFFER_START(Props)
            UNITY_DEFINE_INSTANCED_PROP(float, _Value)
			UNITY_DEFINE_INSTANCED_PROP(float4, _NumbersCol)
        UNITY_INSTANCING_BUFFER_END(Props)


            
            


		v2f_Counter vert_Counter (appdata_instancing v)
        {
			v2f_Counter o;
            _VERT_INITIALIZE(o, v, v2f_Counter);
			
			UNITY_SETUP_INSTANCE_ID(v);
			UNITY_TRANSFER_INSTANCE_ID(v, o);

            o.pos = UnityObjectToClipPos(v.vertex);
        	o.worldPos = mul(UNITY_MATRIX_M, v.vertex);

            half value = max(0, UNITY_ACCESS_INSTANCED_PROP(Props, _Value));

            #if _MODE_MONEYEARNED & !_MODE_NONE
                value = floor(value * 0.01) ;
            #else
                value = floor(value);
            #endif
                
            half unitsExist = step(0.9, value);
            half tensExist = step(0.9, value - 9);
            half hundredsExist = step(0.9, value - 99);
            half thousandsExist = step(0.9, value - 999);

            o.valExists = float4(unitsExist, tensExist, hundredsExist, thousandsExist);

            half unitsOffset = (unitsExist);
            half tensOffset = (tensExist);
            half hundredsOffset = (hundredsExist);
            half thousandsOffset = (thousandsExist);

                
            //void _Counter_UV_Quads(out half2 uv_units, out half2 uv_tens, out half2 uv_hundreds, out half2 uv_thousands, out half4 clipValsUnits, out half4 clipValsTens, out half4 clipValsHundreds, out half4 clipValsThousands, half value, half2 uv_in, half numberSpacing, float4 tex_ST)
            _Counter_UV_Quads(o.uv_Units, o.uv_Tens, o.uv_Hundreds, o.uv_Thousands, o.clip_Units, o.clip_Tens, o.clip_Hundreds, o.clip_Thousands, value, 1 - v.uv, _NumberSpacing, _NumbersTex_ST);
            o.uv_Units.x += (tensOffset  * 1.5 - hundredsOffset * 0.5 - thousandsOffset * 0.5) * _NumberSpacing;
            o.uv_Tens.x +=(1.75* unitsOffset - hundredsOffset * 0.75 - thousandsOffset * 0.5)  * _NumberSpacing;
            o.uv_Hundreds.x += (0.1 * thousandsOffset - thousandsOffset * 0.5) * _NumberSpacing;
            o.uv_Thousands.x += (thousandsOffset - 0.4) * _NumberSpacing;


            #if USE_ICON & !_MODE_NONE
                float4 iconST = float4(_NumbersTex_ST.xy * _IconsTex_ST.xy, _NumbersTex_ST.zw + _IconsTex_ST.zw);
                _Counter_UV_QuadDisplay_Icon(o.uv_Icon, o.clip_Icon, value, _Mode, 1 - v.uv, _NumberSpacing, iconST, 12);
                o.uv_Icon.x -= (tensOffset * 0.5 + hundredsOffset * 0.5 + thousandsOffset * 0.5) * _NumberSpacing;
                //o.uv_Thousands.x += (thousandsOffset - 0.4) * _NumberSpacing;
            #endif


            #if (_MODE_MONEYEARNED | USE_DECIMAL_POINT) & !_MODE_NONE
                float4 decimalST = float4(_NumbersTex_ST.xy * _DecimalTex_ST.xy, _NumbersTex_ST.zw + _DecimalTex_ST.zw);
                _Counter_UV_QuadDisplay_Icon(o.uv_Decimal, o.clip_Decimal, 0, _Mode, 1 - v.uv, _NumberSpacing, decimalST, 1);

                half tenExist = step(0.9, value - 9);
                half hundredkExist = step(0.9, value - 99);
                half kkExist = step(0.9, value - 999);

                o.uv_Decimal.x -= (tenExist * 1.4 + hundredsOffset * 1.4 + kkExist * 1.4) * _NumberSpacing * decimalST.x;
            #endif

            o.uv = v.uv;



            return o;
        }
	#endif

	#if LOCALIZED_TEXT
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
        #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_UI.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
           

        struct v2f_Localized_Text
        {
            float2 uv : TEXCOORD0;
			float4 uv_clip : TEXCOORD1;
            float4 pos : SV_POSITION;
			_LIGHTING_VARS(0, 2)
			_WORLDPOS_VARS(3)
			_NTB_VARS(4, 5, 6)
			_DISTANCE_VARS(7)
			UNITY_VERTEX_INPUT_INSTANCE_ID
        };

        sampler2D _TextTex;
		float4 _TextTex_ST;
		half _Language;
		float4 _TextColour;

        UNITY_INSTANCING_BUFFER_START(Props)
            UNITY_DEFINE_INSTANCED_PROP(float, _Row)
			UNITY_DEFINE_INSTANCED_PROP(float4, _BackgroundColour)
        UNITY_INSTANCING_BUFFER_END(Props)
			       

		v2f_Localized_Text vert_Localized_Text(appdata_instancing v)
        {
			v2f_Localized_Text o;
            _VERT_INITIALIZE(o, v, v2f_Localized_Text);
            UNITY_SETUP_INSTANCE_ID(v);
			UNITY_TRANSFER_INSTANCE_ID(v, o);

			half row = 10 - floor(max(0, UNITY_ACCESS_INSTANCED_PROP(Props, _Row)));
			half column = _Language;

            o.pos = UnityObjectToClipPos(v.vertex);
			_CalculateDistanceFog(v.vertex, o.distanceFog);

			_CALC_NTB(o, v);
			_CALC_WORLDPOS(o, v);
			_WORLD_NORMAL_VERT(v);
			_DOT_LIGHT_VERT;
			_CACHED_SHADOW_VERT(o);

			// position in top left corner
			half cellSize = 0.1;

			half2 outputUVs = v.uv;
			outputUVs -= 0.5;
			outputUVs *= half2(_TextTex_ST.xy) * cellSize;
			outputUVs.x += cellSize * 0.5;
			outputUVs.y -= cellSize * 0.5;


			outputUVs += _TextTex_ST.zw * cellSize;
			outputUVs.x += (column * cellSize) ;
			outputUVs.y += (row * cellSize);

			half rowIndex = row * cellSize;
			half columnIndex = column * cellSize;

			o.uv_clip.xyzw = half4(columnIndex, columnIndex + cellSize, rowIndex, rowIndex - cellSize);
			o.uv = outputUVs;

            return o;
        }
	#endif

	#if TIME_COUNTER
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_UI.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
		

		struct v2f_TimeCounter
		{
			float2 uv : TEXCOORD0;
			float4 pos : SV_POSITION;
			half4 uv_Minutes : TEXCOORD1;
			half4 clip_MinutesOnes : TEXCOORD2;
			half4 clip_MinutesTens : TEXCOORD3;

			half4 uv_Hours : TEXCOORD4;
			half4 clip_HoursOnes : TEXCOORD5;
			half4 clip_HoursTens : TEXCOORD6;

			half4 uv_Days : TEXCOORD7;
			half4 clip_DaysOnes : TEXCOORD8;
			half4 clip_DaysTens : TEXCOORD9;

			half3 valExists : TEXCOORD10;

			half2 uv_Minutes_Letter : TEXCOORD11;
			half2 clip_Minutes_Letter : TEXCOORD12;
			half2 uv_Hours_Letter : TEXCOORD13;
			half2 clip_Hours_Letter : TEXCOORD14;
			half2 uv_Days_Letter : TEXCOORD15;
			half2 clip_Days_Letter : TEXCOORD16;

			float4 color : COLOR0;
		};

		sampler2D _MainTex;
		sampler2D _NumbersTex;
		sampler2D _LettersTex;
		float4 _NumbersTex_ST;
		half _NumberSpacing;
		half _SectionSpacing;
		half _OffsetLetters;
		half _Alpha;

		UNITY_INSTANCING_BUFFER_START(Props)
			UNITY_DEFINE_INSTANCED_PROP(float, _Short)
			UNITY_DEFINE_INSTANCED_PROP(float, _Medium)
			UNITY_DEFINE_INSTANCED_PROP(float, _Long)
		UNITY_INSTANCING_BUFFER_END(Props)

		float4 _ShortCol;
		float4 _MediumCol;
		float4 _LongCol;

		v2f_TimeCounter vert_TimeCounter(appdata_instancing v)
		{
			v2f_TimeCounter o;
			_VERT_INITIALIZE(o, v, v2f_TimeCounter);
			UNITY_SETUP_INSTANCE_ID(v);

			o.pos = UnityObjectToClipPos(v.vertex);

			half minutes = floor(UNITY_ACCESS_INSTANCED_PROP(Props, _Short));
			half hours = floor(UNITY_ACCESS_INSTANCED_PROP(Props, _Medium));
			half days = floor(UNITY_ACCESS_INSTANCED_PROP(Props, _Long));

			half minutesExist = step(0.9, minutes);
			half hoursExist = step(0.9, hours);
			half daysExist = step(0.9, days);

			o.valExists = float3(minutesExist, hoursExist, daysExist);

			half minutesOffset = -(hoursExist + daysExist) * _SectionSpacing;

			half minutes_and_daysExist = max(minutesExist, daysExist);
			half hoursOffset = (minutesExist * _SectionSpacing) - (daysExist * _SectionSpacing);

			half daysOffset = (minutesExist + hoursExist) * _SectionSpacing;

			_Counter_UV_Doubles(o.uv_Minutes.xy, o.uv_Minutes.zw, o.clip_MinutesOnes, o.clip_MinutesTens, minutes, 1 - v.uv, _NumberSpacing, _NumbersTex_ST);
			o.uv_Minutes.xz += minutesOffset;


			_Counter_UV_Doubles(o.uv_Hours.xy, o.uv_Hours.zw, o.clip_HoursOnes, o.clip_HoursTens, hours, 1 - v.uv, _NumberSpacing, _NumbersTex_ST);
			o.uv_Hours.xz += hoursOffset;

			_Counter_UV_Doubles(o.uv_Days.xy, o.uv_Days.zw, o.clip_DaysOnes, o.clip_DaysTens, days, 1 - v.uv, _NumberSpacing, _NumbersTex_ST);
			o.uv_Days.xz += daysOffset;

			half2 uv_minutes, uv_hours, uv_days, clipValMinutes, clipValHours, clipValDays;
			_NumbersTex_ST.zw -= half2(_OffsetLetters, -0.03);
			_Counter_UV_Letters(uv_minutes, uv_hours, uv_days, clipValMinutes, clipValHours, clipValDays, minutes, hours, days, 1 - v.uv, _NumberSpacing, _NumbersTex_ST);
			o.uv_Minutes_Letter = uv_minutes;
			o.uv_Minutes_Letter.x += (minutesOffset * 3.33);
			o.clip_Minutes_Letter = clipValMinutes;

			o.uv_Hours_Letter = uv_hours;
			o.uv_Hours_Letter.x += (hoursOffset * 3.33);
			o.clip_Hours_Letter = clipValHours;

			o.uv_Days_Letter = uv_days;
			o.uv_Days_Letter.x += (daysOffset * 3.33);
			o.clip_Days_Letter = clipValDays;



			_WORLD_NORMAL_VERT(v);
			_DOT_LIGHT_VERT;
			o.color = nl;

			o.uv = v.uv;



			return o;
		}

	#endif

#endif



#endif // CUSTOMINC_VERT_SHADERS