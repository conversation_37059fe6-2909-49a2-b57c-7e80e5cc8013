#ifndef CUSTOMINC_COLOURADJUSTMENTS
#define CUSTOMINC_COLOURADJUSTMENTS

sampler2D _CurvesTex;

half3 _AdjustContrast(half3 color, half contrast)
{
	return saturate(lerp(half3(0.5, 0.5, 0.5), color, contrast));
}

half3 _RGB_To_Grayscale(half3 color, half amount)
{
	return saturate(lerp(color, dot(color, float3(0.3, 0.59, 0.11)), amount));
}

half3 _OverlayColour(half3 color, half3 overlay)
{
	float luminance = dot(color, float3(0.2126, 0.7152, 0.0722));

	float3 lowLuminance = color * 2 * overlay;
	float3 highLuminance = 1 - 2 * (1 - color)*(1 - overlay);

	//return step(0.15, luminance);

	return lerp(lowLuminance, highLuminance, step(0.25, luminance));
}

inline float3 _RGB_To_Sepia(float3 original)
{
	float3 output = original;

	output = _RGB_To_Grayscale(output, 0.9);
	//output = _AdjustContrast(output,1.025);

	float3 overlayCol = float3(0.6015625, 0.32421875, 0);
	output = _OverlayColour(output, overlayCol);
	//output += 0.1;

	return output;
}

inline float3 _RGB_To_GradientRemap(float3 original, sampler2D gradientRemap)
{
	float3 output = original;

	half luminance = _RGB_To_Grayscale(output, 1).x;

	output = tex2D(gradientRemap, luminance);

	return output;
}

float3 _FloatRemapCurve(half value)
{
	float3 output = tex2D(_CurvesTex, half2(0, 0.5));
	return output;
}

#endif // CUSTOMINC_COLOURADJUSTMENTS