#if false
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class ShaderReplacer : EditorWindow
{
    [MenuItem("Art Tools/TA/Shader Replacer")]
    public static void ShaderReplacerWindow()
    {
        GetWindow<ShaderReplacer>("Shader Replacer");
    }

    Shader m_oldShader;
    Shader m_newShader;
	bool m_editRenderQueue;
	bool m_replaceShader;
	public int m_renderQueue = 2000;
	bool m_justMakeDirty;
	bool m_editInstancingProp;
	bool m_enableInstancing;


	private void OnGUI()
    {
        GUILayout.Space(10);
        GUILayout.BeginVertical(GUILayout.Width(320));
        EditorDisplayUtilities.DrawTitle("Edit Material With Shader", 24);
        GUILayout.EndVertical();
		m_replaceShader = (bool)EditorGUILayout.Toggle("Replace Shader", m_replaceShader);

		m_oldShader = (Shader)EditorGUILayout.ObjectField("Shader To Find: ", m_oldShader, typeof(Shader), false);
		GUILayout.Space(10);
		if(m_replaceShader && !m_justMakeDirty)
		{
			m_newShader = (Shader)EditorGUILayout.ObjectField("New Shader : ", m_newShader, typeof(Shader), false);
		}

		m_editRenderQueue = (bool)EditorGUILayout.Toggle("Edit Render Queue", m_editRenderQueue);
		if (m_editRenderQueue)
		{
			m_renderQueue = EditorGUILayout.IntSlider(m_renderQueue, 1, 5000);
		}

		GUILayout.Space(10);
		m_justMakeDirty = (bool)EditorGUILayout.Toggle("Just Make Dirty", m_justMakeDirty);

		m_editInstancingProp = (bool)EditorGUILayout.Toggle("Edit Instancing", m_editInstancingProp);
		if(m_editInstancingProp)
		{
			m_enableInstancing = (bool)EditorGUILayout.Toggle("Instancing Enabled", m_enableInstancing);
		}

		if (GUILayout.Button("Start"))
        {
			ReplaceMats();
        }
    }

    void ReplaceMats()
    {
        Debug.Log("Finding Old Shaders instances - This will take a while!");
        //Fill string[] with all materials in project
        string[] guids = AssetDatabase.FindAssets("t:material", null);
		int shadersChanged = 0;
		int instancingApplied = 0;
		int renderQueueChanged = 0;
        //Check if material shader = old shader and if so replace with new shader
        foreach (string g in guids)
        {
            Material mat = AssetDatabase.LoadAssetAtPath<Material>(AssetDatabase.GUIDToAssetPath(g));
			if (m_oldShader != null && mat.shader == m_oldShader)
			{
				if (m_editInstancingProp)
				{
					if ((m_enableInstancing && mat.enableInstancing == false))
					{
						mat.enableInstancing = true;
						instancingApplied++;
					}
					else
					{
						mat.enableInstancing = false;
						instancingApplied++;
					}
				}

				if(m_editRenderQueue)
				{
					if(mat.renderQueue != m_renderQueue)
					{
						mat.renderQueue = m_renderQueue;
						renderQueueChanged++;
					}
				}

			
			
				if (m_justMakeDirty)
				{
					EditorUtility.SetDirty(mat);
					shadersChanged++;
				}
				else if(m_replaceShader)
				{
					mat.shader = m_newShader;
					shadersChanged++;
				}
			}
        }
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        if (EditorUtility.DisplayDialog(	"Done.\n", 
											shadersChanged.ToString() + " shaders touched.\n" + 
											"Instancing (" + m_enableInstancing.ToString() + ") Applied to " + instancingApplied.ToString() + " materials.\n" +
											"Render Queue changed in (" + renderQueueChanged.ToString() + " materials.\n"
											, "Ok"
											)){}
    }
}
#endif
