Shader "_Scenery/S_Road"
{
    Properties
    {
		// General Props
        [NoScaleOffset]_DetailTex ("Detail Texture", 2D) = "white" {}
        [NoScaleOffset]_NormTex ("Normal", 2D) = "bump" {}
		_WaveFrequency("Wave Frequency",Range(0.0,0.3)) = 0
		_WaveStrength("Wave Strength",Range(0.0,1.0)) = 0
		_ShadowPull("Shadow Pull", Range(1, 1.05)) = 1
		_TexTiling("Detail Scale",Range(0.01,0.25)) = 1

		// Concrete
		_RoadCol ("Road Color (R)", Color) = (1,1,1,1)
		_PaveInCol ("Pavement Inner Color (G)", Color) = (0.5,0.5,0.5,1)
		_PaveOutCol ("Pavement Outer Color (B)", Color) = (0.7,0.7,0.7,1)
        [NoScaleOffset]_Mask ("Mask", 2D) = "white" {}

		// Dirt
		_GrassTex("Grass Tex", 2D) = "white" {}
		_GrassTexBrightness("Grass Tex Brightness", Range(0, 1)) = 1
		_DirtTex("Dirt Tex", 2D) = "white" {}
		_DirtTexBrightness("Dirt Tex Brightness", Range(0, 1)) = 0.5
		[NoScaleOffset]_HeightTex("Height Texture", 2D) = "white" {}
		_DeltaEpsilon("Delta Epsilon", Range(0.0001, 0.1)) = 0.01
		_HeightmapBlending("Heightmap Blending", Range(0.01,0.5)) = 1
		_NoiseTexTiling("Noise Texture Tiling", Range(0.01,1.5)) = 1

		[HideInInspector] _Mode("__mode", Float) = 0.0
		[HideInInspector] _Projection("__proj", Float) = 0.0
		[HideInInspector]_Clip1("Clip1", Vector) = (0,1,0,0)
		[HideInInspector]_Clip2("Clip2", Vector) = (0,1,0,0)
		[HideInInspector]_Clip3("Clip3", Vector) = (0,1,0,0)
		[HideInInspector]_Clip4("Clip4", Vector) = (0,1,0,0)
		[HideInInspector]_CupedClip("CupedClip", Float) = 0
		[HideInInspector]_Clip5("Clip5", Vector) = (0,0,1,0)
		[HideInInspector]_ClipDirection("Clip Direction", Float) = 1
		[Toggle(IS_CONCRETE)]_IsConcrete("Is Concrete", Float) = 0
		[Toggle(WORLD_UV)]_WorldUV("World UVs", Float) = 0


		_B_C_Pinch("B / C Surface Pinch", Range(0.1, 5)) = 1.7

    			
        // GBuffer
        [HideInInspector] _StencilRefGBuffer("_StencilRefGBuffer", Int) = 2 // StencilUsage.RequiresDeferredLighting
        [HideInInspector] _StencilWriteMaskGBuffer("_StencilWriteMaskGBuffer", Int) = 3 // StencilUsage.RequiresDeferredLighting | StencilUsage.SubsurfaceScattering
    }
    SubShader
    {
    	Tags { "RenderPipeline" = "UniversalPipeline" "UniversalMaterialType" = "Lit" "RenderType" = "Opaque" }
		LOD 300
        Pass
        {
        	Tags { "LightMode"="UniversalForward" }
            Stencil
	        {
	            WriteMask [_StencilWriteMaskGBuffer]
	            Ref [_StencilRefGBuffer]
	            Comp Always
	            Pass Replace
	        }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			//#pragma multi_compile __ _BOXCLIP
			#pragma multi_compile __ IS_CONCRETE
			#pragma multi_compile __ WORLD_UV
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE
			#pragma multi_compile_fragment _ _SHADOWS_SOFT

			#define _IS_ROAD 1

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

			#pragma multi_compile_fwdbase
			//#pragma multi_compile_instancing
			#pragma target 3.5
			#include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
            


            struct v2f
            {
				_BASE_VARS(0)
                float2 uvWorld : TEXCOORD1;
				_WORLDPOS_VARS(2)
				SHADOW_COORDS(3)
				_LIGHTING_VARS(0, 4)
				_DISTANCE_VARS(5)
				_NTB_VARS(6, 7, 8)

				// dirt 
				#if !IS_CONCRETE
					fixed4 color : TEXCOORD9;
					half4 control : TEXCOORD10;
					half3 dot_XY_XZ_YZ :TEXCOORD11;
					_SCREENPOS_VAR(12)
				#endif
				_WORLDUV_VARS(13)
				//UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            v2f vert (appdata v)
            {
                v2f o;
				//UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_OUTPUT(v2f, o);
				o.uv = v.uv;
				_CALC_WORLDPOS(o, v);
				_CALC_WORLDUVS(o);
				o.worldUVs = worldUVs;

				half xOff = sin(o.worldPos.z*_WaveFrequency)*_WaveStrength;
				half zOff = cos(o.worldPos.x*_WaveFrequency)*_WaveStrength;

				o.worldPos.xz += half2(xOff,zOff);
				o.pos = mul(UNITY_MATRIX_VP, o.worldPos);
				_WORLD_NORMAL_VERT(v);
				o.uvWorld = mul(UNITY_MATRIX_M, v.vertex).xz;
				_TERRAIN_LIGHTING(o);
				//Normals
				_CALC_NTB(o, v)
				_CalculateDistanceFog(v.vertex, o.distanceFog);


				// dirt 
				#if !IS_CONCRETE
					_CALC_SCREENPOS(o);
					o.color = v.color;
					o.control = half4(o.color.x, 1-o.color.x, 0,0);
					o.dot_XY_XZ_YZ = o.control;
				#endif
                return o;
            }

            float4 frag(v2f i) : SV_Target
            {
				#ifdef _BOXCLIP
					_BoxClip(i.worldPos.xyz);
				#endif

				//return i.color;

				float4 baseCol = 0;
				#if IS_CONCRETE
					//Normals and light
					#if WORLD_UV
						half3 worldNormal = UnpackNormal(tex2D(_NormTex, i.uvWorld*_TexTiling));
					#else
						half3 worldNormal = UnpackNormal(tex2D(_NormTex, i.uv));
					#endif
					_WORLD_NORMAL_FRAG(i);
					_CACHED_SHADOW_MASKED_FRAG(i);
					_LIGHT_FROM_NORMAL_WORLD(i);
					_APPLY_SHADOW_FRAG(i);

					//return fixed4(worldNormal, 1);

					//Color
					fixed4 maskText = tex2D(_Mask,i.uv);
					fixed4 pavCol = lerp(_PaveInCol,_PaveOutCol,maskText.g);
					baseCol = lerp(pavCol,_RoadCol,maskText.r);

					//return baseCol;

					fixed4 detail = tex2D(_DetailTex, i.uvWorld*_TexTiling);
					fixed4 detailY = tex2D(_DetailTex, i.uv*_TexTiling *10);

					fixed4 DetailFinal = lerp(detail,detailY,floor(i.uv.x));
					fixed4 col = fixed4(baseCol.xyz*DetailFinal.xyz,1);
					col = _OwnLand(col, i.worldPos);
					//col.xyz = _LitAlbedo(col, shadow);
					//_ApplyDistanceFog(i.distanceFog, col.rgb);
					baseCol = col;
				#else
					// Terrain blending
					float textureDepth;
					float4 depthNormalsVar = _CameraNormalsTextureToWorld(i.screenPos, textureDepth);
					textureDepth = depthNormalsVar.w;




					half deltaEpsilon = _DeltaEpsilon;
					_TERRAIN_FRAG_DEFINES(i);
					controlTex = i.control;
					half2 noiseTex = tex2D(_WindNoise, i.worldPos.xz * _NoiseTexTiling).xy;
					controlTex.xy += noiseTex;

					//worldNormal = _PlanarNormal(i.worldPos, _NormTex, _TexTiling);

					_TERRAIN_DETAIL_HEIGHT_HQ(i);
					
					_WORLD_NORMAL_ROAD_HQ(i);
					//return col_Norm[0];
					_WORLD_NORMAL_FRAG(i);

            		depthNormalsVar = float4(worldNormal, 0); // TODO - HDRP - fix depthNormalsVar
					worldNormal = lerp(worldNormal, depthNormalsVar.xyz, i.color.y);
					//return fixed4(worldNormal, 1);

					_CACHED_SHADOW_MASKED_FRAG(i);
					_LIGHT_FROM_NORMAL_WORLD(i);
					_APPLY_SHADOW_FRAG(i);


					fixed4 combineAlbedo = _Heightblend_float4_x2(col_Norm[0], maxHeight, col_Norm[2], secondHeight);
					//return combineAlbedo;
					//Distance to Camera
					_CAMERA_DISTANCE_TINT(i)
						// Direction tint



					// used 1.7, which happens to be the same as B/C pinch value on M_terrain
					half triblend = pow(_Triblend(worldNormal.xyz).yyy, _B_C_Pinch);
					controlTex.x = triblend * i.color.x;

					//return controlTex.x;

					//HDRP _CAMERA_DIRECTION_TINT(i)

					col = _OwnLand(col, i.worldPos);
					//col.xyz = _LitAlbedo(col, shadow);
					//_ApplyDistanceFog(i.distanceFog, col.rgb);

					col.a = 1;
					baseCol = col;
				#endif

				return ApplyLighting(baseCol.rgb, worldNormal, i.worldPos, .1, .1);
            }
            ENDHLSL
        }
    	
		/*Pass
        {
        	Tags { "LightMode"="Forward" }
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			//#pragma multi_compile __ _BOXCLIP
			#pragma multi_compile __ IS_CONCRETE
			#pragma multi_compile __ WORLD_UV
			#define _IS_ROAD 1

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

			#pragma multi_compile_fwdbase
			//#pragma multi_compile_instancing
			#pragma target 3.5
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"


			


            struct v2f
            {
				_BASE_VARS(0)
                float2 uvWorld : TEXCOORD1;
				_WORLDPOS_VARS(2)
				SHADOW_COORDS(3)
				_LIGHTING_VARS(0, 4)
				_DISTANCE_VARS(5)
				_NTB_VARS(6, 7, 8)

				// dirt 
				#if !IS_CONCRETE
					fixed4 color : TEXCOORD9;
					half4 control : TEXCOORD10;
					half3 dot_XY_XZ_YZ :TEXCOORD11;
					_SCREENPOS_VAR(12)
				#endif
				_WORLDUV_VARS(13)
				//UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            v2f vert (appdata v)
            {
                v2f o;
				//UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_OUTPUT(v2f, o);
				o.uv = v.uv;
				_CALC_WORLDPOS(o, v);
				_CALC_WORLDUVS(o);
				o.worldUVs = worldUVs;

				half xOff = sin(o.worldPos.z*_WaveFrequency)*_WaveStrength;
				half zOff = cos(o.worldPos.x*_WaveFrequency)*_WaveStrength;

				o.worldPos.xz += half2(xOff,zOff);
				o.pos = mul(UNITY_MATRIX_VP, o.worldPos);
				_WORLD_NORMAL_VERT(v);
				o.uvWorld = mul(UNITY_MATRIX_M, v.vertex).xz;
				_TERRAIN_LIGHTING(o);
				//Normals
				_CALC_NTB(o, v)
				_CalculateDistanceFog(v.vertex, o.distanceFog);


				// dirt 
				#if !IS_CONCRETE
					_CALC_SCREENPOS(o);
					o.color = v.color;
					o.control = half4(o.color.x, 1-o.color.x, 0,0);
					o.dot_XY_XZ_YZ = o.control;
				#endif
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				#ifdef _BOXCLIP
					_BoxClip(i.worldPos.xyz);
				#endif

				//return i.color;

				#if IS_CONCRETE
					//Normals and light
					#if WORLD_UV
						half3 worldNormal = UnpackNormal(tex2D(_NormTex, i.uvWorld*_TexTiling));
					#else
						half3 worldNormal = UnpackNormal(tex2D(_NormTex, i.uv));
					#endif
					_WORLD_NORMAL_FRAG(i);
					_CACHED_SHADOW_MASKED_FRAG(i);
					_LIGHT_FROM_NORMAL_WORLD(i);
					_APPLY_SHADOW_FRAG(i);

					//return fixed4(worldNormal, 1);

					//Color
					fixed4 maskText = tex2D(_Mask,i.uv);
					fixed4 pavCol = lerp(_PaveInCol,_PaveOutCol,maskText.g);
					fixed4 baseCol = lerp(pavCol,_RoadCol,maskText.r);

					//return baseCol;

					fixed4 detail = tex2D(_DetailTex, i.uvWorld*_TexTiling);
					fixed4 detailY = tex2D(_DetailTex, i.uv*_TexTiling *10);

					fixed4 DetailFinal = lerp(detail,detailY,floor(i.uv.x));
					fixed4 col = fixed4(baseCol.xyz*DetailFinal.xyz,1);
					col = _OwnLand(col, i.worldPos);
					col.xyz = _LitAlbedo(col, shadow);
					_ApplyDistanceFog(i.distanceFog, col.rgb);
					
					return col;
				#else
					// Terrain blending
					float textureDepth;
					float4 depthNormalsVar = _CameraNormalsTextureToWorld(i.screenPos, textureDepth);
					textureDepth = depthNormalsVar.w;




					half deltaEpsilon = _DeltaEpsilon;
					_TERRAIN_FRAG_DEFINES(i);
					controlTex = i.control;
					half2 noiseTex = tex2D(_WindNoise, i.worldPos.xz * _NoiseTexTiling).xy;
					controlTex.xy += noiseTex;

					//worldNormal = _PlanarNormal(i.worldPos, _NormTex, _TexTiling);

					_TERRAIN_DETAIL_HEIGHT_HQ(i);
					
					_WORLD_NORMAL_ROAD_HQ(i);
					//return col_Norm[0];
					_WORLD_NORMAL_FRAG(i);

					worldNormal = lerp(worldNormal, depthNormalsVar.xyz, i.color.y);
					//return fixed4(worldNormal, 1);

					_CACHED_SHADOW_MASKED_FRAG(i);
					_LIGHT_FROM_NORMAL_WORLD(i);
					_APPLY_SHADOW_FRAG(i);


					fixed4 combineAlbedo = _Heightblend_float4_x2(col_Norm[0], maxHeight, col_Norm[2], secondHeight);
					//return combineAlbedo;
					//Distance to Camera
					_CAMERA_DISTANCE_TINT(i)
						// Direction tint



					// used 1.7, which happens to be the same as B/C pinch value on M_terrain
					half triblend = pow(_Triblend(worldNormal.xyz).yyy, _B_C_Pinch);
					controlTex.x = triblend * i.color.x;

					//return controlTex.x;

					_CAMERA_DIRECTION_TINT(i)

					col = _OwnLand(col, i.worldPos);
					col.xyz = _LitAlbedo(col, shadow);
					_ApplyDistanceFog(i.distanceFog, col.rgb);

					col.a = 1;
					return col;
				#endif

				return 0;

            }
            ENDCG
        }
    	
		UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_Roads"
    }

	SubShader
    {
        Tags {"LightMode" = "ForwardBase" }
		LOD 100
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			//#pragma multi_compile __ _BOXCLIP
			#pragma multi_compile __ IS_CONCRETE
			//#pragma multi_compile_instancing
			//#pragma shader_feature_local WORDLSPACE_NOLRMALS
			#define _IS_ROAD 1
			#pragma target 3.5

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
			

            struct v2f
            {
				float4 pos : POSITION;
				//_BASE_VARS(0)
				half2 uv : TEXCOORD0;
                float2 uvWorld : TEXCOORD1;
				_WORLDPOS_VARS(2)
				SHADOW_COORDS(3)
				_LIGHTING_VARS(0, 4)
				_DISTANCE_VARS(5)
				_NTB_VARS(6, 7, 8)
				// dirt 
				fixed4 color : TEXCOORD9;
				half4 control : TEXCOORD10;
				half3 dot_XY_XZ_YZ :TEXCOORD11;
            };

            v2f vert (appdata v)
            {
                v2f o;
				//UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_OUTPUT(v2f, o);
				o.uv = v.uv;
				o.color = v.color;
				o.control = half4(o.color.x, 1 - o.color.x, 0, 0);
				o.dot_XY_XZ_YZ = o.control;
				_CALC_WORLDPOS(o, v)

				half xOff = sin(o.worldPos.z*_WaveFrequency)*_WaveStrength;
				half zOff = cos(o.worldPos.x*_WaveFrequency)*_WaveStrength;

				o.worldPos.xz += half2(xOff,zOff);
				o.pos = mul(UNITY_MATRIX_VP, o.worldPos);
				TRANSFER_SHADOW(o);
                
				o.uvWorld = mul(UNITY_MATRIX_M, v.vertex).xz;
				half nl = 1;
				_CACHED_SHADOW_VERT(o);
				//Normals
				_CALC_NTB(o, v)
				_CalculateDistanceFog(v.vertex, o.distanceFog);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				#ifdef _BOXCLIP
					_BoxClip(i.worldPos.xyz);
				#endif

				#if IS_CONCRETE
					//Normals and light
					half3 worldNormal;
					_WORLD_NORMAL_FRAG_LQ(i);
					_CACHED_SHADOW_MASKED_FRAG(i);
					_LIGHT_FROM_NORMAL_WORLD(i);
					_APPLY_SHADOW_FRAG(i);

					//Color
					fixed4 maskText = tex2D(_Mask,i.uv);
					fixed4 pavCol = lerp(_PaveInCol,_PaveOutCol,maskText.g);
					fixed4 baseCol = lerp(pavCol,_RoadCol,maskText.r);

					fixed4 detail = tex2D(_DetailTex, i.uvWorld*_TexTiling);
					fixed4 detailY = tex2D(_DetailTex, i.uv*_TexTiling *10);

					fixed4 DetailFinal = lerp(detail,detailY,floor(i.uv.x));

					fixed4 col = fixed4(baseCol.xyz*DetailFinal.xyz,1);
					col = _OwnLand(col, i.worldPos);
					col.xyz = _LitAlbedo(col, shadow);
					_ApplyDistanceFog(i.distanceFog, col.rgb);
					return col;
				#else
					half deltaEpsilon = _DeltaEpsilon;
					_TERRAIN_FRAG_DEFINES(i);
					controlTex = i.control;
					half2 noiseTex = tex2D(_WindNoise, i.worldPos.xz * _NoiseTexTiling).xy;
					controlTex.xy += noiseTex;

					//worldNormal = _PlanarNormal(i.worldPos, _NormTex, _TexTiling);

					_TERRAIN_DETAIL_HEIGHT_LQ(i);

					_WORLD_NORMAL_ROAD_LQ(i);
					_WORLD_NORMAL_FRAG_LQ(i);

					//return col_Norm[0];

					_CACHED_SHADOW_MASKED_FRAG(i);
					_LIGHT_FROM_NORMAL_WORLD(i);
					_APPLY_SHADOW_FRAG(i);

					fixed4 combineAlbedo = col_Norm[0];
					combineAlbedo = _OwnLand(combineAlbedo, i.worldPos);
					//return combineAlbedo;
					//Distance to Camera
					//_CAMERA_DISTANCE_TINT(i)
					col = combineAlbedo;

					// Direction tint
					//_CAMERA_DIRECTION_TINT(i)
					col.xyz = _LitAlbedo(col, shadow);
					_ApplyDistanceFog(i.distanceFog, col.rgb);

					col.a = 1;
					return col;

				#endif

            }
            ENDCG
        }*/
		//UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_Roads"
    }
	CustomEditor "GUI_Roads"
}
