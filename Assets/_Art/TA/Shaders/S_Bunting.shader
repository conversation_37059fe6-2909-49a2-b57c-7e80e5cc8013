Shader "_Scenery/S_Bunting"
{
    Properties
    {
        _NoiseTex ("Noise", 2D) = "white" {}
		_WindPower("Wind Power",Range(0.0,1.0)) = 0
		_WindSpeed("Wind Speed",Range(0.0,1.0)) = 0
		_WindSize("Wind Size",Range(0.0,1.0)) = 0
		_Color1("Color 1", Color) = (1,1,1,1)
		_Color2("Color 2",Color) = (1,0,0,1)
		_Color3("Color 3",Color) = (0,1,0,1)
		_Color4("Color 4",Color) = (0,0,1,1)

        [HideInInspector]_NormTex ("Normal", 2D) = "bump" {}
    }
    SubShader
    {
        Tags {"LightMode" = "ForwardBase" }
        LOD 100

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			#pragma target 3.5

            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"

			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"

            sampler2D _NoiseTex;

			fixed _WindPower;
			fixed _WindSpeed;
			fixed _WindSize;

			fixed4 _Color1;
			fixed4 _Color2;
			fixed4 _Color3;
			fixed4 _Color4;

			sampler2D _NormTex;

            struct v2f
            {
                float4 vertex : SV_POSITION;
				fixed4 color : TEXCOORD0;

				//Shadows & clouds
				SHADOW_COORDS(1)
				float4 shadowUVs : TEXCOORD2;
				fixed clouds : COLOR0;
				_DISTANCE_VARS(3)
				_NTB_VARS(4, 5, 6)
				float4 worldPos: TEXCOORD7;
				float2 uv : TEXCOORD8;
            };

            v2f vert (appdata v)
            {
				_FRAC_TIME_Y;
                v2f o;
				UNITY_INITIALIZE_OUTPUT(v2f, o);
				o.uv = v.uv;
				o.worldPos = mul(UNITY_MATRIX_M, v.vertex);
				_CalculateDistanceFog(v.vertex, o.distanceFog);
				o.clouds = _ScrollingCloudUVs(o.worldPos).x;

				half windSpeed = _WindSpeed*_Time.y;
				half windSize = _WindSize*0.01;
				
				half4 offsetUV = half4((o.worldPos.xz*windSize)+windSpeed,0,0);
				half2 noise =  tex2Dlod(_NoiseTex,offsetUV);

				half offset = lerp(0,lerp(noise,0,1-_WindPower),v.color.r);

				o.worldPos.xy += offset;

                o.vertex = UnityObjectToClipPos(mul(UNITY_MATRIX_I_M,o.worldPos));

				o.shadowUVs = _ShadowUV(mul(UNITY_MATRIX_I_M,o.worldPos));
				TRANSFER_SHADOW(o);


				_CALC_NTB(o, v)

				half xMask = floor(v.uv.x*2);
				half yMask = floor(v.uv.y*2);

				fixed4 topCol = lerp(_Color1,_Color2,xMask);
				fixed4 botCol = lerp(_Color3,_Color4,xMask);
				o.color = lerp(botCol,topCol,yMask);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				fixed3 viewDirection = normalize(float3(float4(_WorldSpaceCameraPos.xyz,0) - i.worldPos.xyz));
				fixed3 lightDirection = normalize(_WorldSpaceLightPos0.xyz);
				half atten = 1;

				//Normals
					fixed3 normals = UnpackNormal(tex2D(_NormTex, i.uv));
					fixed3 normalDir = _WorldNormal(i.tspace0, i.tspace1, i.tspace2, normals.xyz);

				//lighting
				half lightFromNormal = _LightFromNormal(normalDir, _WorldSpaceLightPos0);

				//Shadows
				half perPixelShadow = _ShadowPixel_Blurred(i.shadowUVs);
				perPixelShadow = saturate(_Smoothstep(perPixelShadow, _ShadowsThreshold, _ShadowsThreshold_Blur));
				fixed shadow = saturate(SHADOW_ATTENUATION(i) * lightFromNormal) * i.clouds * perPixelShadow;

				fixed4 col = i.color;
				col.xyz = _LitAlbedo(col, shadow);
				_ApplyDistanceFog(i.distanceFog, col.rgb);

                return col;
            }
            ENDCG
        }
		UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_Roads"
    }
}
