
Shader "Custom/ActiveAttachPoint"
{

	Properties
	{
		_MainTex ("Texture", 2D) = "white" {}
		_MainColor ("Color", Color) = (1.0, 1.0, 1.0, 1.0)
	}
	SubShader
	{
		Tags { "RenderType" = "Transparent" "Queue" = "Transparent+100"}
        Cull Off
        Lighting Off
        ZWrite Off
        ZTest Always
        Fog { Mode Off }
        Blend SrcAlpha OneMinusSrcAlpha
        
        
		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			// make fog work
			#pragma multi_compile_fog
			
			#include "UnityCG.cginc"

			struct v2f
			{
				half4 pos : SV_POSITION;
            	half2 uv : TEXCOORD0;
           	 	fixed4 vertexColor : COLOR;
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;
			fixed4 _MainColor;
			
			v2f vert(appdata_full v) {
            v2f o;
           
            o.pos = UnityObjectToClipPos (v.vertex);  
            o.uv.xy = TRANSFORM_TEX(v.texcoord, _MainTex);
            o.vertexColor = v.color * _MainColor;
            return o;
        }
       
        fixed4 frag( v2f i ) : COLOR { 
            return tex2D (_MainTex, i.uv.xy) * i.vertexColor;
        }
			ENDCG
		}
	}
}
