#if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (https://www.swig.org).
// Version 4.3.0
//
// Do not make changes to this file unless you know what you are doing - modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public enum AkMIDICcTypes {
  BANK_SELECT_COARSE = 0,
  MOD_WHEEL_COARSE = 1,
  BREATH_CTRL_COARSE = 2,
  CTRL_3_COARSE = 3,
  FOOT_PEDAL_COARSE = 4,
  PORTAMENTO_COARSE = 5,
  DATA_ENTRY_COARSE = 6,
  VOLUME_COARSE = 7,
  BALANCE_COARSE = 8,
  CTRL_9_COARSE = 9,
  PAN_POSITION_COARSE = 10,
  EXPRESSION_COARSE = 11,
  EFFECT_CTRL_1_COARSE = 12,
  EFFECT_CTRL_2_COARSE = 13,
  CTRL_14_COARSE = 14,
  CTRL_15_COARSE = 15,
  GEN_SLIDER_1 = 16,
  GEN_SLIDER_2 = 17,
  GEN_SLIDER_3 = 18,
  GEN_SLIDER_4 = 19,
  CTRL_20_COARSE = 20,
  CTRL_21_COARSE = 21,
  CTRL_22_COARSE = 22,
  CTRL_23_COARSE = 23,
  CTRL_24_COARSE = 24,
  CTRL_25_COARSE = 25,
  CTRL_26_COARSE = 26,
  CTRL_27_COARSE = 27,
  CTRL_28_COARSE = 28,
  CTRL_29_COARSE = 29,
  CTRL_30_COARSE = 30,
  CTRL_31_COARSE = 31,
  BANK_SELECT_FINE = 32,
  MOD_WHEEL_FINE = 33,
  BREATH_CTRL_FINE = 34,
  CTRL_3_FINE = 35,
  FOOT_PEDAL_FINE = 36,
  PORTAMENTO_FINE = 37,
  DATA_ENTRY_FINE = 38,
  VOLUME_FINE = 39,
  BALANCE_FINE = 40,
  CTRL_9_FINE = 41,
  PAN_POSITION_FINE = 42,
  EXPRESSION_FINE = 43,
  EFFECT_CTRL_1_FINE = 44,
  EFFECT_CTRL_2_FINE = 45,
  CTRL_14_FINE = 46,
  CTRL_15_FINE = 47,
  CTRL_20_FINE = 52,
  CTRL_21_FINE = 53,
  CTRL_22_FINE = 54,
  CTRL_23_FINE = 55,
  CTRL_24_FINE = 56,
  CTRL_25_FINE = 57,
  CTRL_26_FINE = 58,
  CTRL_27_FINE = 59,
  CTRL_28_FINE = 60,
  CTRL_29_FINE = 61,
  CTRL_30_FINE = 62,
  CTRL_31_FINE = 63,
  HOLD_PEDAL = 64,
  PORTAMENTO_ON_OFF = 65,
  SUSTENUTO_PEDAL = 66,
  SOFT_PEDAL = 67,
  LEGATO_PEDAL = 68,
  HOLD_PEDAL_2 = 69,
  SOUND_VARIATION = 70,
  SOUND_TIMBRE = 71,
  SOUND_RELEASE_TIME = 72,
  SOUND_ATTACK_TIME = 73,
  SOUND_BRIGHTNESS = 74,
  SOUND_CTRL_6 = 75,
  SOUND_CTRL_7 = 76,
  SOUND_CTRL_8 = 77,
  SOUND_CTRL_9 = 78,
  SOUND_CTRL_10 = 79,
  GENERAL_BUTTON_1 = 80,
  GENERAL_BUTTON_2 = 81,
  GENERAL_BUTTON_3 = 82,
  GENERAL_BUTTON_4 = 83,
  REVERB_LEVEL = 91,
  TREMOLO_LEVEL = 92,
  CHORUS_LEVEL = 93,
  CELESTE_LEVEL = 94,
  PHASER_LEVEL = 95,
  DATA_BUTTON_P1 = 96,
  DATA_BUTTON_M1 = 97,
  NON_REGISTER_COARSE = 98,
  NON_REGISTER_FINE = 99,
  ALL_SOUND_OFF = 120,
  ALL_CONTROLLERS_OFF = 121,
  LOCAL_KEYBOARD = 122,
  ALL_NOTES_OFF = 123,
  OMNI_MODE_OFF = 124,
  OMNI_MODE_ON = 125,
  OMNI_MONOPHONIC_ON = 126,
  OMNI_POLYPHONIC_ON = 127
}
#endif // #if ! (UNITY_DASHBOARD_WIDGET || UNITY_WEBPLAYER || UNITY_WII || UNITY_WIIU || UNITY_NACL || UNITY_FLASH || UNITY_BLACKBERRY) // Disable under unsupported platforms.