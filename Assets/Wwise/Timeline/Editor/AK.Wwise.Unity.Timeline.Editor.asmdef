{"name": "AK.Wwise.Unity.Timeline.Editor", "references": ["AK.Wwise.Unity.API", "AK.Wwise.Unity.Timeline", "Unity.Timeline", "AK.Wwise.Unity.API.Editor", "AK.Wwise.Unity.API.WwiseTypes", "AK.Wwise.Unity.ProjectDatabase"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["!UNITY_SERVER"], "versionDefines": [{"name": "com.unity.timeline", "expression": "1.1.0", "define": "AK_ENABLE_TIMELINE"}], "noEngineReferences": false}