{"name": "AK.Wwise.Unity.AddressableInstaller", "rootNamespace": "", "references": ["AK.Wwise.Unity.MonoBehaviour.Editor", "Unity.Addressables.Editor", "Unity.Addressables", "Unity.ScriptableBuildPipeline.Editor", "Unity.ResourceManager", "Unity.ScriptableBuildPipeline", "AK.Wwise.Unity.API", "AK.Wwise.Unity.Addressables", "AK.Wwise.Unity.API.WwiseTypes", "AK.Wwise.Unity.ProjectDatabase", "AK.Wwise.Unity.Addressables.Editor", "AK.Wwise.Unity.MonoBehaviour"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["!UNITY_SERVER"], "versionDefines": [{"name": "com.unity.addressables", "expression": "1.8", "define": "UNITY_ADDRESSABLES"}, {"name": "com.audiokinetic.wwise.addressables", "expression": "1.0.0", "define": "AK_WWISE_ADDRESSABLES"}], "noEngineReferences": false}