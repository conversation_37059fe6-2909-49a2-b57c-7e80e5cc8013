#if false
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public enum EHotspotSubtype
{
	HotspotCloth,
	HotspotLumber,
	HotspotMetal,
	HotspotMineral,
	HotspotProduce,
	HotspotClothBig,
	HotspotLumberBig,
	HotspotMetalBig,
	HotspotMineralBig,
	HotspotProduceBig,
}

public class HotspotInfo
{
	public EHotspotSubtype m_subtype;
	public float m_radius;
	public int m_resourceCapacity;
	public int m_refreshTimeout;
	public int m_harvesterCap;
}

public class ResourceHotspotManager : MonoSingleton<ResourceHotspotManager>
{
	[SerializeField] Transform m_hotspotHolder;
	[SerializeField] Transform m_firstHotspotPos;
	[SerializeField] List<ResourceHotspot> m_hotspots = new List<ResourceHotspot>();
	[SerializeField] HarvesterLimitGUI m_harvesterLimitGUI;
	public List<ResourceHotspot> Hotspots => m_hotspots;

	[Header("Prefabs")]
	[SerializeField] ResourceHotspot m_clothHotspotPrefab;
	[SerializeField] ResourceHotspot m_metalHotspotPrefab;
	[SerializeField] ResourceHotspot m_woodHotspotPrefab;
	[SerializeField] ResourceHotspot m_produceHotspotPrefab;
	[SerializeField] ResourceHotspot m_mineralHotspotPrefab;

	bool m_hotspotsInitialised = false;
	bool m_restrictBuildingPlacement = false;
	NGCarriableResource.RAW_MATERIALS m_restrictedHotspotType;

	public int m_fabricToCrate { get; private set; }
	public int m_woodToCrate { get; private set; }
	public int m_metalToCrate { get; private set; }
	public int m_mineralToCrate { get; private set; }
	public int m_produceToCrate { get; private set; }
	public bool RestrictBuildingPlacement => m_restrictBuildingPlacement;
	Dictionary<EHotspotSubtype, HotspotInfo> m_hotspotInfos = new Dictionary<EHotspotSubtype, HotspotInfo>();

	public static event Action OnHotspotsLoaded;


	public void AddHotspotInfo(EHotspotSubtype _hotspotType, HotspotInfo _info)
	{
		m_hotspotInfos[_hotspotType] = _info;
	}

	public void Load()
	{
		// Register just in case. It'll get removed in InitialiseHotspots() anyway.
		//NGProductInfo.OnProductLineUnlocked += InitialiseHotspots;
		
		InitialiseHotspots();
	}

	void InitialiseHotspots()
	{	
		if(m_hotspotsInitialised)
			return;

		// If there's still no product line, wait until there is.
		//if( NGProductInfo.FirstProductLineData == null )
		//	return;

		//NGProductInfo.OnProductLineUnlocked -= InitialiseHotspots;
		
		/*var hotspot = Instantiate(GetPrefabForResource(NGProductInfo.FirstProductLineData.PrimaryMaterial));
		hotspot.transform.position = m_firstHotspotPos.position;
		hotspot.transform.SetParent(m_hotspotHolder, true);
		m_hotspots.Insert(0, hotspot);*/
		HotspotInfo info = null;

		for(int i = 0; i < m_hotspots.Count; i++)
		{
			if(m_hotspotInfos.TryGetValue(m_hotspots[i].Subtype, out info))
			{
				if(!GameManager.HasLoadedFromSeed && GameManager.Me.m_state.m_oldStyleData.m_hotspots.TryGetValue(i, out var hotspotData))
					m_hotspots[i].Initialise(info, hotspotData.m_resources, hotspotData.m_refreshProgress);
				else
					m_hotspots[i].Initialise(info, 0, 0f, true);
			}
		}
		m_hotspotsInitialised = true;

		OnHotspotsLoaded?.Invoke();
	}

	public void Save(ref SaveContainers.SaveCountryside _s)
	{
		_s.m_hotspots.Clear();
		for(int i = 0; i < m_hotspots.Count; i++)
		{
			_s.m_hotspots.Add(i, new SaveContainers.SaveHotspot(){	
					m_resources = m_hotspots[i].NumResources,
					m_refreshProgress = m_hotspots[i].RefreshProgress
				});	
		}
	}

	ResourceHotspot GetPrefabForResource(NGCarriableResource.RAW_MATERIALS _type)
	{
		ResourceHotspot prefab = null;
		switch(_type)
		{
			default:
			case NGCarriableResource.RAW_MATERIALS.CLOTH : prefab = m_clothHotspotPrefab;
				break;
			case NGCarriableResource.RAW_MATERIALS.WOOD : prefab = m_woodHotspotPrefab;
				break;
			case NGCarriableResource.RAW_MATERIALS.PRODUCE : prefab = m_produceHotspotPrefab;
				break;
			case NGCarriableResource.RAW_MATERIALS.METAL : prefab = m_metalHotspotPrefab;
				break;
			case NGCarriableResource.RAW_MATERIALS.MINERAL : prefab = m_mineralHotspotPrefab;
				break;
		}
		return prefab;	
	}

	/*public ResourceHotspot GetClosestHotspotToHarvester(NGCarriableResource.RAW_MATERIALS _type, Vector3 _pos)
	{
		float minSqrDistance = float.MaxValue;
		ResourceHotspot closestHotspot = null;
		foreach(var hotspot in m_hotspots)
		{
			if(hotspot.ResourceType != _type)
				continue;
			float sqrDist = (hotspot.transform.position - _pos).sqrMagnitude;
			if(sqrDist < minSqrDistance)
			{
				closestHotspot = hotspot;
				minSqrDistance = sqrDist;
			}
		}
		return closestHotspot;
	}*/
	// check for generic
	public ResourceHotspot GetClosestHotspotToHarvester(Vector3 _pos)
	{
		float minSqrDistance = float.MaxValue;
		ResourceHotspot closestHotspot = null;
		foreach(var hotspot in m_hotspots)
		{
			float sqrDist = (hotspot.transform.position - _pos).sqrMagnitude;
			if(sqrDist < minSqrDistance)
			{
				closestHotspot = hotspot;
				minSqrDistance = sqrDist;
			}
		}
		return closestHotspot;
	}

	public void DisplayHotspotRadii(bool _display)
	{
		foreach(var hotspot in m_hotspots)
		{
			hotspot.EnableRadius(_display);
		}
	}

	public bool CanHarvesterBePlacedHere(Vector3 point)
	{
		foreach(var hotspot in m_hotspots)
		{
			// Remove resource type check for generic harvester
			// if(hotspot.ResourceType != m_restrictedHotspotType)
			// 	continue;
				
			// Point must be within radius and there must be space for more harvesters
			if(CirclePointIntersect(point.GetXZVector2(), hotspot.transform.position.GetXZVector2(), hotspot.Radius))
				return hotspot.HasHarvesterSpace;
		}
		return false;
	}

	private bool CirclePointIntersect(Vector2 point, Vector2 _circleCentre, float _radius){
		Vector2 distance = new Vector2(_circleCentre.x - point.x, _circleCentre.y - point.y);
		return distance.sqrMagnitude <= _radius * _radius;
	}


	public void SetBuildingRestriction(bool _restrict, NGCarriableResource.RAW_MATERIALS _type = NGCarriableResource.RAW_MATERIALS.NONE)
	{
		m_restrictBuildingPlacement = _restrict;
		m_restrictedHotspotType = _type;
	}

	public List<Vector2[]> CalculateHotspotBounds()
	{
		var boundsList = new List<Vector2[]>();
		foreach(var hotspot in m_hotspots)
		{
			var bounds = hotspot.transform.GetComponent<BoxCollider>().bounds;
			Vector2[] minMax = { new Vector2(bounds.min.x, bounds.min.z), new Vector2(bounds.max.x, bounds.max.z) };
			boundsList.Add(minMax);
		}
		return boundsList;
	}

	public void Catchup(float _elapsedTime) {
		foreach(var hotspot in m_hotspots) {
			hotspot.Catchup(_elapsedTime);		
		}
	}

	public void ShowHarvesterLimitsForResource(NGCarriableResource.RAW_MATERIALS _resourceType)
	{
		HarvesterLimitGUI inst = null;
		foreach(var hotspot in m_hotspots)
		{
			// remove the resource type check - change to generic harvester
			// if(hotspot.ResourceType != _resourceType)
			// 	continue;
			inst = Instantiate(m_harvesterLimitGUI);
			inst.Setup(hotspot);
		}
	}

	public void RemoveHarvesterLimitsForResource(NGCarriableResource.RAW_MATERIALS _resourceType)
	{
		HarvesterLimitGUI inst = null;
		foreach(var hotspot in m_hotspots)
		{
			// remove the resource type check - change to generic harvester
			// if(hotspot.ResourceType != _resourceType)
			// 	continue;
			inst = hotspot.GetComponentInChildren<HarvesterLimitGUI>();
			if(inst != null)
				Destroy(inst.gameObject);
		}
	}
}
#endif