#if UNITY_EDITOR
using System.Linq;
using UnityEditor;
using UnityEngine;

public class AvatarCreator : MonoBehaviour
{
    /*[MenuItem("22Cans/Create Avatar From Processed Model")]
    public static void CreateAvatar()
    {
			 var selected = Selection.activeObject;

        if (selected == null)
        {
            Debug.LogError("No object selected.");
            return;
        }

        string path = AssetDatabase.GetAssetPath(selected);
        if (string.IsNullOrEmpty(path))
        {
            Debug.LogError("Selected object is not a valid asset.");
            return;
        }

        GameObject model = AssetDatabase.LoadAssetAtPath<GameObject>(path);
        var animator = model.GetComponent<Animator>();
        
        if (animator == null)
        {
            Debug.LogError("Model does not have an Animator component.");
            return;
        }

        Avatar avatar = AvatarBuilder.BuildGenericAvatar(model, "");//BuildHumanAvatar(model, new HumanDescription());
        if (avatar.isValid && avatar.isHuman)
        {
            AssetDatabase.CreateAsset(Object.Instantiate(avatar), "Assets/Avatars/ProcessedAvatar.asset");
            Debug.Log("Avatar created and saved.");
        }
        else
        {
            Debug.LogError("Avatar creation failed. Check bone hierarchy and mapping.");
        }
    }*/

		[MenuItem("22Cans/Create Humanoid Avatar From Selected Model")]
    public static void CreateHumanoidAvatarFromSelected()
    {
        Object selected = Selection.activeObject;

        if (selected == null)
        {
            Debug.LogError("No object selected.");
            return;
        }

        string path = AssetDatabase.GetAssetPath(selected);
        if (!path.EndsWith(".fbx", System.StringComparison.OrdinalIgnoreCase))
        {
            Debug.LogError("Selected asset is not an FBX model.");
            return;
        }

        // Load the imported model prefab
        GameObject modelPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
        if (modelPrefab == null)
        {
            Debug.LogError("Failed to load model prefab.");
            return;
        }

        // Find the avatar generated by Unity (already imported in the model)
        ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;
        if (importer == null || importer.animationType != ModelImporterAnimationType.Human)
        {
            Debug.LogError("Model is not set to use Humanoid animation type.");
            return;
        }

        GameObject instantiated = GameObject.Instantiate(modelPrefab);
        Animator animator = instantiated.GetComponent<Animator>();
        if (animator == null || animator.avatar == null || !animator.avatar.isHuman || !animator.avatar.isValid)
        {
            Debug.LogError("No valid humanoid Avatar found on the model.");
            GameObject.DestroyImmediate(instantiated);
            return;
        }

        // Duplicate the avatar
				// Remove ".fbx" from the end of the path.
        string avatarPath = path.Remove(path.Length-4) + "_HumanoidAvatar.asset";
        Avatar avatarCopy = Object.Instantiate(animator.avatar);

        AssetDatabase.CreateAsset(avatarCopy, avatarPath);
        Debug.Log("Humanoid Avatar duplicated at: " + avatarPath);

        GameObject.DestroyImmediate(instantiated);
    }

		/*[MenuItem("Tools/Assign Avatar To Selected Animations")]
    public static void AssignAvatarToSelected()
    {
        // Load your processed avatar
        var avatar = AssetDatabase.LoadAssetAtPath<Avatar>("Assets/_Art/Characters/SK_Marcos_Hero_Female._HumanoidAvatar.asset");

        if (avatar == null)
        {
            Debug.LogError("Avatar not found!");
            return;
        }

        foreach (var obj in Selection.objects)
        {
            string path = AssetDatabase.GetAssetPath(obj);
            var importer = AssetImporter.GetAtPath(path) as ModelImporter;
            if (importer != null)
            {
                importer.animationType = ModelImporterAnimationType.Human;
                importer.avatarSetup = ModelImporterAvatarSetup.CopyFromOther;
                importer.sourceAvatar = avatar;

                AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
                Debug.Log($"Assigned avatar to: {path}");
            }
        }
    }*/
}
#endif //UNITY_EDITOR