using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;
using System;
using Debug = UnityEngine.Debug;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditorInternal;
using System.Diagnostics;


[CustomEditor(typeof(AudioClipManager))]
public class AudioClipManagerEditor : MonoEditorDebug.MonoBehaviourEditor
{
	public System.Reflection.FieldInfo AudioDataGetter = typeof(AudioClipManager).GetField( "kAudioDataEditorOnly", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static );

	private AudioClipManager.AudioEventData _selectedData = null;

	private bool _filterPlaying = true;
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		EditorGUILayout.Space();

		AudioClipManager acm = ((AudioClipManager)target);

		if( !acm.gameObject.activeInHierarchy ) {
			EditorGUILayout.LabelField("Audio debugging not avaliable for prefab.", EditorStyles.boldLabel);
			return;
		}

		Dictionary<uint, AudioClipManager.AudioEventData> audioData = (Dictionary<uint, AudioClipManager.AudioEventData>)AudioDataGetter.GetValue(null);
		
		using( new EditorGUILayout.HorizontalScope()) {
			EditorGUILayout.LabelField("Playing ID | Event ID | Audio Name | Audio Source | Is Playing", EditorStyles.boldLabel);
			_filterPlaying = EditorGUILayout.Toggle( _filterPlaying, GUILayout.MaxWidth( 25f ) );
		}

		float width = EditorGUIUtility.currentViewWidth;

		using( new EditorGUILayout.VerticalScope(GUI.skin.box) )
		foreach( var kvp in audioData )
		{
			uint playingID = kvp.Key;
			var v = kvp.Value;

			if( _filterPlaying && !v.isPlaying ) {
				continue;
			}

			EditorGUI.BeginDisabledGroup(v != _selectedData);
			using( new EditorGUILayout.HorizontalScope( GUI.skin.box ) )
			{
				EditorGUILayout.LabelField(kvp.Key.ToString(), GUILayout.MaxWidth(25) );
				EditorGUILayout.LabelField(v.eventID.ToString(), GUILayout.MaxWidth(75) );
				EditorGUILayout.LabelField(v.name, GUILayout.MaxWidth(width*0.4f) );

				EditorGUI.BeginDisabledGroup(true);
				EditorGUILayout.ObjectField(v.owner, typeof(GameObject), true, GUILayout.MaxWidth(width*0.3f));
				EditorGUI.EndDisabledGroup();

				EditorGUILayout.Toggle(v.isPlaying, GUILayout.MaxWidth(20));

				if( v.isPlaying && GUILayout.Button( "Stop", GUILayout.MaxWidth(width*0.17f) ) ) {
					acm.StopSound( (int)playingID, v.owner );
				}
			}
			var rect = GUILayoutUtility.GetLastRect();
			EditorGUI.EndDisabledGroup();

			var e = Event.current;
			if( e.isMouse && e.button == 0 && rect.Contains( e.mousePosition ) ) {
				_selectedData = v;
			}
		}

		EditorGUILayout.Space();
		EditorGUILayout.LabelField( "Selected Audio StackTrace:", EditorStyles.boldLabel );
		string trace = "None";
		if( _selectedData != null )
		{
			trace = $"{_selectedData.name}\n";
			var stackTrace = _selectedData.stackTrace;
			int cap = Mathf.Min( 5, stackTrace.Length );
			// We don't care about TryPost
			for( int i = 2; i < cap; ++i )
			{
				trace += "\n";
				var frame = stackTrace[i];
				var filename = frame.GetFileName();
				if( filename == null ) continue;
				var split = filename.Split('\\');
				var method = frame.GetMethod();
				trace += $"{method.DeclaringType.Name}:{method.Name}() (at {split[split.Length-1]}:{frame.GetFileLineNumber()})";
			}

		}

		EditorGUILayout.TextArea( trace );

		EditorUtility.SetDirty( target );
	}
}

#endif

[System.Serializable]
public class AudioClipManager : MonoSingleton<AudioClipManager> {

	private class WeakReferenceCounter : WeakReference
	{
		public virtual int ReferenceCount { get; set; } = 1; 
		public WeakReferenceCounter(object target) : base(target) {}
	}

#if UNITY_EDITOR
	public class AudioEventData {
		public uint eventID;
		public string name;
		public GameObject owner;
		public bool isPlaying;
		public StackFrame[] stackTrace;
	}

	// Used for the inspector to show what audio is playing.
	private static Dictionary<uint, AudioEventData> kAudioDataEditorOnly = new Dictionary<uint, AudioEventData>();
#endif

	// =========================================================================
	[SerializeField] GameObject m_musicAudioBus;
	[SerializeField] GameObject m_VOAudioBus;
	uint m_packageID;
	string[] m_banks = new string[] {
		//"Init", // loaded automatically
		"MoatAudioMain"
	};
	string m_currentManagerSound = "";

	List<WeakReferenceCounter> m_trackingObjects = new List<WeakReferenceCounter>();
	Dictionary<uint, string> m_eventIDToName = new Dictionary<uint, string>();
	Dictionary<int, Action> m_callbacks = new Dictionary<int, Action>();
	int m_currentAmbientID = -1;
	static float s_playSounds;
	GameObject m_cam = null;
	GameObject GetListener => Camera.main?.gameObject;
	float m_musicVolume = 1f;
	float m_sfxVolume = 1f;
	float m_VOVolume = 1f;
	bool m_silenceAll = false;
	private float SFXVolume => m_sfxVolume;
	private float VOVolume => m_VOVolume;
	private float MusicVolume => m_musicVolume;

	const string c_prefsVOVolume = "MAVoiceVolume";
	const string c_prefsSFXVolume = "MASFXVolume";
	const string c_prefsMusicVolume = "MAMusicVolume";
	const string c_prefsSilenceAll = "MASilenceAll";

	// =========================================================================

	private bool m_readyToPlaySound = false;
	public bool ReadyToPlaySound { get => m_readyToPlaySound; set => m_readyToPlaySound = value; }
	private static bool s_haveLoadedBanks = false;
	private static bool s_disableAllSound = false;
	
	private bool NoSoundCalls => s_disableAllSound || m_readyToPlaySound == false;

	void LoadBanks()
	{
		if (!s_haveLoadedBanks)
		{
			s_haveLoadedBanks = true;
			
			uint bankId;
			try
			{
				foreach (string bank in m_banks)
				{
					AKRESULT result = AkSoundEngine.LoadBank(string.Format("{0}.bnk", bank), out bankId);
					if (result != AKRESULT.AK_Success)
					{
						UnityEngine.Debug.LogError(string.Format("Failed to load the {0} bank: {1}", bank, result));
						s_disableAllSound = true;
					}
				}
			}
			catch (Exception _e)
			{
				Debug.LogError($"Caught LoadBank exception {_e}\nDisabling all sound");
				s_disableAllSound = true;
			}
		}
	}
	void LoadVolumeData()
	{
		SetSilenceAll(GetSilenceAll());
		SetVOVolume(GetVOVolume());
		SetMusicVolume(GetMusicVolume());
		SetSFXVolume(GetSFXVolume());
		LoadAudioMastering();
	}

	private int m_currentAudioMasteringIndex = 0;

	private void LoadAudioMastering()
	{
		SetAudioMastering(MPlayerPrefs.GetInt("AudioMastering", 0));
	}
	
	public int GetAudioMastering() => m_currentAudioMasteringIndex;

	public void SetAudioMastering(int _index)
	{
		m_currentAudioMasteringIndex = _index;
		if (m_currentAudioMasteringIndex < 0 || m_currentAudioMasteringIndex >= s_audioMasteringTypes.Length)
			m_currentAudioMasteringIndex = 0;
		MPlayerPrefs.SetInt("AudioMastering", m_currentAudioMasteringIndex);
		PlaySound($"WwiseMastering_{s_audioMasteringTypes[m_currentAudioMasteringIndex].Replace(" ", "")}", GameManager.Me.gameObject, true);
	}

	public static string[] s_audioMasteringTypes = {"Desktop", "Headphones", "Night Mode"};

	override protected void _OnDestroy() {
		if (!NoSoundCalls)
		{
			AkSoundEngine.StopAll();
			AkSoundEngine.UnregisterGameObj(this.gameObject);
			AkSoundEngine.UnregisterAllGameObj();
		}
#if UNITY_EDITOR
		//AkSoundEngine.EditorIsSoundEngineLoaded = false; // GL - 250225 - AkSoundEngineController continues to attempt to run (LateUpdate) after shut-down, causing errors to spew continuously
#endif
		m_trackingObjects.Clear();
	}
	
	// -------------------------------------------------------------------------
	
	IEnumerator Start()
	{
		/*if (AkInitializer.Disable)
		{
			s_disableAllSound = true;
			yield break;
		}*/
		
		/*if (AkSoundEngine.IsInitialized() == false)
		{
			AkInitializer.GetAkInitializerGameObject().SetActive(false);
			yield return null;
			AkInitializer.GetAkInitializerGameObject().SetActive(true);
			yield return null;
			Debug.LogError($"Wwise ACM init fudge applied");
		}*/
		yield return null;
		if (!s_disableAllSound) AkSoundEngine.RegisterGameObj(this.gameObject);
		if (m_musicAudioBus == null)
		{
			m_musicAudioBus = new GameObject("MusicAudioBus");
			m_musicAudioBus.transform.parent = GameManager.Me.transform;
			m_musicAudioBus.transform.localPosition = Vector3.zero;
			m_musicAudioBus.transform.localRotation = Quaternion.identity;
		}
		if (m_VOAudioBus == null)
		{
			m_VOAudioBus = new GameObject("VOAudioBus");
			m_VOAudioBus.transform.parent = Camera.main.transform;
			m_VOAudioBus.transform.localPosition = Vector3.forward * 1;
			m_VOAudioBus.transform.localRotation = Quaternion.identity;
		}
		LoadBanks();
		m_readyToPlaySound = true;
		LoadVolumeData();
	}

	public IEnumerator FadeOut()
	{
		if (NoSoundCalls) yield break;
		if (GetSilenceAll()) yield break;
		var sfxVolume = GetSFXVolume();
		var voVolume = GetVOVolume();
		var musicVolume = GetMusicVolume();
		for (float t = 0; t <= 1; t += Time.deltaTime)
		{
			var fader = Mathf.Max(0, 1 - t);
			SetGameParameter("MasterVolume_SFX", sfxVolume * fader, null);
			SetGameParameter("MasterVolume_MUSIC", musicVolume * fader, null);
			SetGameParameter("MasterVolume_VO", voVolume * fader, null);
			yield return null;
		}
	}

	// -------------------------------------------------------------------------

	bool CanPlayAudio() {
	#if DEVELOPMENT_BUILD || UNITY_EDITOR
		return !s_playSounds.IsZero();
	#else
		return true;
	#endif
	}

	// -------------------------------------------------------------------------

	public bool HasAnySoundPlaying(GameObject go)
	{
		return NoSoundCalls ? false : AkSoundEngine.GetIsGameObjectActive(go);
	}

	public bool IsPlaying(int _uniqueID)
	{
		if (NoSoundCalls) return false;
		ulong gameObjectID = AkSoundEngine.GetGameObjectFromPlayingID((uint)_uniqueID);
		return (gameObjectID != AkSoundEngine.AK_INVALID_GAME_OBJECT);
	}
	
	private static float s_visualiseAudio = 0;
	private static DebugConsole.Command s_visualiseAudioCmd = new ("visualiseaudio", _s => s_visualiseAudio = string.IsNullOrEmpty(_s) ? (s_visualiseAudio < .0001f ? 1 : 0) : floatinv.Parse(_s));
	
	private static GameObject s_conePrimitive = null;

	public static GameObject CreateConePrimitive()
	{
		if (s_conePrimitive == null)
		{
			const int c_radials = 32;
			const float c_height = 1, c_radius = .5f;
			var verts = new Vector3[c_radials * 2 + 1];
			var nrms = new Vector3[c_radials * 2 + 1];
			var inds = new int[c_radials * 3 + (c_radials - 2) * 3];
			verts[0] = Vector3.zero;
			var anglePerRadial = Mathf.PI * 2 / c_radials;
			for (int i = 0; i < c_radials; ++i)
			{
				float sin = Mathf.Sin(anglePerRadial * i) * c_radius, cos = Mathf.Cos(anglePerRadial * i) * c_radius;
				verts[1 + i] = new Vector3(sin, cos, c_height);
				nrms[1 + i] = new Vector3(sin, cos, 0);
				verts[1 + c_radials + i] = new Vector3(sin, cos, c_height);
				nrms[1 + c_radials + i] = Vector3.forward;
				inds[i * 3 + 0] = 0; inds[i * 3 + 1] = 1 + i; inds[i * 3 + 2] = 1 + (i + 1) % c_radials;
			}
			for (int i = 0; i < c_radials - 2; ++i)
			{
				inds[c_radials * 3 + i * 3 + 0] = 1 + c_radials;
				inds[c_radials * 3 + i * 3 + 1] = 1 + c_radials + i + 2;
				inds[c_radials * 3 + i * 3 + 2] = 1 + c_radials + i + 1;
			}
			
			var go = new GameObject("ConePrimitive");
			go.transform.SetParent(Me.transform);
			var mf = go.AddComponent<MeshFilter>();
			var mr = go.AddComponent<MeshRenderer>();
			var mc = go.AddComponent<MeshCollider>();
			mr.material = GlobalData.Me.m_positiveSelectionMaterial;
			mf.mesh = new Mesh();
			mf.mesh.vertices = verts;
			mf.mesh.normals = nrms;
			mf.mesh.SetTriangles(inds, 0);
			mc.sharedMesh = mf.mesh;
			mf.mesh.UploadMeshData(true);
			go.SetActive(false);
			s_conePrimitive = go;
		}
		var inst = Instantiate(s_conePrimitive);
		inst.SetActive(true);
		return inst;
	}

	private static Dictionary<GameObject, GameObject> s_visualiseAudioObjects = new();

	private void PingVisualisation(GameObject _o)
	{
		if (s_visualiseAudio < .001f) return;
		if (s_visualiseAudioObjects.TryGetValue(_o, out var vis))
			vis.transform.localScale = Vector3.one * s_visualiseAudio;
	}

	private void RemoveVisualisation(GameObject _o)
	{
		if (s_visualiseAudio < .001f) return;
		if (s_visualiseAudioObjects.TryGetValue(_o, out var vis))
		{
			Destroy(vis, .5f);
			s_visualiseAudioObjects.Remove(_o);
		}
	}

	private void VisualiseAudio(GameObject _o, bool _isAlive)
	{
		if (s_visualiseAudio < .001f)
		{
			if (s_visualiseAudioObjects.Count > 0)
			{
				foreach (var kvp in s_visualiseAudioObjects)
					Destroy(kvp.Value);
				s_visualiseAudioObjects.Clear();
			}
			return;
		}
		if (_o != null && _isAlive)
		{
			if (s_visualiseAudioObjects.TryGetValue(_o, out var vis) == false)
			{
				vis = CreateConePrimitive();
				Destroy(vis.GetComponent<Collider>());
				vis.transform.SetParent(transform);
				s_visualiseAudioObjects[_o] = vis;
				vis.transform.localScale = Vector3.one * s_visualiseAudio;
			}
			if (m_overrideLookup.TryGetValue(_o, out var transformOverride) && transformOverride != null)
			{
				var (pos, fwd, up) = transformOverride.GetTransform();
				vis.transform.position = pos;
				vis.transform.LookAt(pos + fwd, up);
			}
			else
			{
				var ak = _o.GetComponent<AkGameObj>();
				var pos = _o.transform.position;
				if (ak != null && ak.m_positionOffsetData != null) pos += ak.m_positionOffsetData.positionOffset;
				vis.transform.position = pos;
				vis.transform.rotation = _o.transform.rotation;
			}
			vis.transform.localScale = Vector3.one * Mathf.Lerp(vis.transform.localScale.x, s_visualiseAudio * .5f, .05f);
		}
		else
		{
			if (s_visualiseAudioObjects.TryGetValue(_o, out var vis))
			{
				Destroy(vis, .5f);
				s_visualiseAudioObjects.Remove(_o);
			}
		}
	}

	GameObject m_listenerObject = null;
	void Update()
	{
		if (NoSoundCalls) return;

		if (m_listenerObject == null)
		{
			m_listenerObject = new GameObject("WwiseListener");
			AkSoundEngine.AddDefaultListener(m_listenerObject);
		}
		var listener = GetListener;
		if (listener != null)
		{
			m_listenerObject.transform.position = listener.transform.position;
			m_listenerObject.transform.rotation = listener.transform.rotation;
		}
		
		for(int i = m_trackingObjects.Count-1; i >= 0; i--) {
			GameObject gameObj = m_trackingObjects[i].Target as GameObject;
			VisualiseAudio(gameObj, m_trackingObjects[i].IsAlive);
			if(gameObj != null && m_trackingObjects[i].IsAlive) {
				SetAudioTransform(gameObj);
			} else {
				RemoveGameObjectAtIndex(i, true);
			}
		}
	}

	private System.Reflection.FieldInfo m_akGameObjIsStatic = typeof(AkGameObj).GetField("isStaticObject", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
	private Dictionary<GameObject, AudioTransformOverride> m_overrideLookup = new();
	public void RefreshAudioTransformCache(GameObject _o) => m_overrideLookup.Remove(_o);
	private void SetAudioTransform(GameObject _o, bool _forceAkPosition = false)
	{
		if (!m_overrideLookup.TryGetValue(_o, out var transformOverride))
		{
			var ak = _o.GetComponent<AkGameObj>();
			if (ak != null)
			{
				ak.isEnvironmentAware = false;
				if (_o.isStatic)
					m_akGameObjIsStatic.SetValue(ak, true);
			}
			transformOverride = _o.GetComponentInParent<AudioTransformOverride>();
			if (transformOverride != null)
			{
				if (ak != null)
					ak.m_positionOffsetData = new AkGameObjPositionOffsetData() {positionOffset = transformOverride.m_positionAdjust};
				if (ak != null && transformOverride.m_rotationAdjust.sqrMagnitude < .001f * .001f)
					transformOverride = null; // no rotation override, don't store since the position offset is done in the AkGameObj position offset data
			}
			m_overrideLookup[_o] = transformOverride;
		}
		if (transformOverride != null)
			transformOverride.ApplyAk(_o);
		else if (_forceAkPosition)
			AkSoundEngine.SetObjectPosition(_o, _o.transform.position, _o.transform.forward, _o.transform.up);
	}

	// -------------------------------------------------------------------------

	const int c_audioClipHandle = -999;

	public List<string> m_unityClipEventNames = new List<string>();
	public List<AudioClip> m_unityClips = new List<AudioClip>();
	Dictionary<string, AudioClip> m_unityClipLookup;

	public void RegisterClip(string _eventName, AudioClip _clip) {
		if (m_unityClipLookup == null) PopulateUnityClips();
		m_unityClipLookup[_eventName] = _clip;
	}
	void PopulateUnityClips() {
		int count = m_unityClipEventNames.Count;
		if (m_unityClipEventNames.Count != m_unityClips.Count) {
			UnityEngine.Debug.LogError($"AudioClipManager: uclip count disparity; uclips will be wrong! Names:{m_unityClipEventNames.Count} Clips:{m_unityClips.Count}");
			count = Mathf.Min(count, m_unityClips.Count);
		}
		m_unityClipLookup = new Dictionary<string, AudioClip>();
		for (int i = 0; i < count; i ++) {
			m_unityClipLookup[m_unityClipEventNames[i]] = m_unityClips[i];
		}
	}
	public AudioClip GetClip(string _eventName) {
		if (m_unityClipLookup == null) PopulateUnityClips();
		AudioClip clip;
		if (m_unityClipLookup.TryGetValue(_eventName, out clip)) return clip;
		return null;
	}

	AudioSource[] m_sources;
	int m_nextSource = 0;
	const int c_sourceBufferSize = 16;
	public int PlaySound(AudioClip _clip, Transform _at = null, float _volume = 1f) {
		//Debug.Log("PlaySound(Clip) " + _clip.name);
		var listener = GetListener;
		if (listener == null){
			return c_audioClipHandle;
        }
		if (listener.GetComponent<AudioListener>() == null)
			listener.AddComponent<AudioListener>();
		if (_at == null || _at.transform.position.sqrMagnitude < .001f) _at = listener.transform;
		
		//AudioSource.PlayClipAtPoint(_clip, _at.position, _volume);
		// support for pitch shift
		if (m_sources == null) {
			var prnt = new GameObject("USources");
			prnt.transform.SetParent(transform);
			m_sources = new AudioSource[c_sourceBufferSize];
			for (int i = 0; i < c_sourceBufferSize; ++i) {
				var go = new GameObject($"USource{1+i}");
				go.transform.SetParent(prnt.transform);
				m_sources[i] = go.AddComponent<AudioSource>();
			}
		}
		var src = m_sources[m_nextSource];
		m_nextSource = (m_nextSource + 1) % c_sourceBufferSize;
		src.clip = _clip; src.transform.position = _at.position; src.volume = _volume;
		src.Play();
		src.name = $"Clip@{DateTime.UtcNow.Ticks}";

		return c_audioClipHandle;
	}

	// -------------------------------------------------------------------------
	
	public void SetGameParameter( string _name, float _value, GameObject _go ) {
		if (NoSoundCalls) return;
		// This is a super basic version of SetGameParameter. Please pester Chance if you think you need to use this and it's not working.
		if (_go == null)
			AkSoundEngine.SetRTPCValue(_name, _value);
		else
			AkSoundEngine.SetRTPCValue( _name, _value, _go );
	}

	// -------------------------------------------------------------------------

	private struct WwiseCookie {
		public uint eventID { get; private set; }
		public GameObject gameObject { get; private set; }

		public WwiseCookie(uint eventID, GameObject go ) {
			this.eventID = eventID;
			this.gameObject = go;
		}
	}

	private static bool EventExists(uint _eventID)
	{
		var res = AkSoundEngine.PrepareEvent(AkPreparationType.Preparation_Load, new uint[] {_eventID}, 1);
		return res == AKRESULT.AK_Success;
	}

	// Used for an existing playingID.
	private int TryPost(int _uniqueIdentifier, GameObject _obj, string prefix) {
		if (NoSoundCalls) return (int)AkSoundEngine.AK_INVALID_PLAYING_ID;
		
		_obj = ValidateGO( _obj );

		uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_uniqueIdentifier);
		if(m_eventIDToName.ContainsKey(eventID)) {
            var name = m_eventIDToName[eventID].Replace("PlaySound_", "");
            eventID = AkSoundEngine.GetIDFromString($"{prefix}_{name}");
            if (EventExists(eventID))
				return (int)AkSoundEngine.PostEvent(eventID, _obj);
		}

		return (int)AkSoundEngine.AK_INVALID_PLAYING_ID;
	}
	
	private HashSet<string> m_eventErrorShown = new();
	private void ShowEventError(string _name)
	{
		if (m_eventErrorShown.Add(_name))
			Debug.LogError($"Wwise: unknown event <color=#a0a0ff>{_name}</color>");
	}

	private int m_logEvents = 0;
	private Dictionary<string, int> m_eventCount = new();
	private static DebugConsole.Command s_logaudioevents = new("logaudio", (_s) => Me.m_logEvents = string.IsNullOrEmpty(_s) ? 20 : int.Parse(_s));
	
	private static HashSet<string> s_inhibitSounds = new();
	private static DebugConsole.Command s_inhibitsoundcmd = new ("inhibitsound", _s => { if (s_inhibitSounds.Contains(_s)) s_inhibitSounds.Remove(_s); else s_inhibitSounds.Add(_s); });

	private static DebugConsole.Command s_playaudiocmd = new("playsound", _s => Me.PlaySound(_s, GameManager.Me.gameObject));

	// Used to create a playing ID.
	private int TryPost(string _name, GameObject _obj, string _prefix, bool _allowGetPlayPosition = false) {
		if (NoSoundCalls) return -1;
		
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (s_inhibitSounds.Contains(_name))
			return -1;
#endif
		
		if (m_logEvents > 0)
		{
			var newCount = m_eventCount.TryGetValue(_name, out var count) ? count + 1 : 1;
			m_eventCount[_name] = newCount;
			if (newCount < m_logEvents)
				Debug.LogError($"Wwise f:{Time.frameCount} play {_name} on {_obj?.name ?? "<none>"}", _obj);
		}

		_obj = ValidateGO( _obj );
		
		var clip = GetClip(_name);
		if (clip != null)
		{
			float volume;
			switch (_prefix)
			{
				case "VO": volume = VOVolume; break;
				case "Music": volume = MusicVolume; break;
				default: volume = SFXVolume; break;
			}
			return PlaySound(clip, _obj.transform, volume);
		}

		uint eventID = AkSoundEngine.GetIDFromString(_name);
		
		var ak = _obj.GetComponent<AkGameObj>();
		if (ak != null) SetAudioTransform(_obj, true);

		WwiseCookie cookie = new WwiseCookie(eventID, _obj);
		uint playingId = AkSoundEngine.AK_INVALID_PLAYING_ID;
		uint flags = (uint) AkCallbackType.AK_EndOfEvent;
		if (_allowGetPlayPosition)
			flags |= (uint) AkCallbackType.AK_EnableGetSourcePlayPosition;
		if (EventExists(eventID))
			playingId = AkSoundEngine.PostEvent(eventID, _obj, flags, EventCallback, cookie);
		if (playingId == AkSoundEngine.AK_INVALID_PLAYING_ID)
		{
			ShowEventError(_name);
			return -1;
		}
		
		TrackSound(playingId, _obj);

		m_eventIDToName[eventID] = _name;

#if UNITY_EDITOR
		AudioEventData audioData = new AudioEventData();
		audioData.eventID = eventID;
		audioData.name = $"{_name}";
		audioData.owner = _obj;
		audioData.isPlaying = true;
		audioData.stackTrace = new StackTrace(true).GetFrames();
		kAudioDataEditorOnly[playingId] = audioData;
#endif

		return (int)playingId;
	}
	
	private bool IsGlobalObject(GameObject _obj) => _obj == GameManager.Me.gameObject || _obj == m_musicAudioBus || _obj == m_VOAudioBus;
	Dictionary<GameObject, List<uint>> m_playingSounds = new();
	List<GameObject> m_playingSoundsToRemove = new();
	private void TrackSound(uint _playingId, GameObject _obj)
	{
		if (IsGlobalObject(_obj))
			return;
		Utility.DoNextFrame(() =>
		{
			if (_obj == null) return; // object was destroyed same frame
			int valueType = (int) AkQueryRTPCValue.RTPCValue_GameObject;
			if (AkSoundEngine.GetRTPCValue("UseObstruction", _obj, (uint) _playingId, out var useObstruction, ref valueType) != AKRESULT.AK_Success || useObstruction == 0)
				return;

			if (!m_playingSounds.TryGetValue(_obj, out var list))
			{
				list = new List<uint>();
				m_playingSounds[_obj] = list;
			}
			list.Add(_playingId);
			CheckOcclusion(_obj);
		});
	}
	public int RaycastSounds => m_playingSounds.Count;
	private void UpdateTrackedSounds()
	{
		m_playingSoundsToRemove.Clear();
		foreach (var kvp in m_playingSounds)
		{
			var list = kvp.Value;
			for (int i = list.Count - 1; i >= 0; i--)
			{
				if (IsPlaying((int)list[i]) == false)
					list.RemoveAt(i);
			}
			if (list.Count == 0)
			{
				m_playingSoundsToRemove.Add(kvp.Key);
				m_occlusionState.Remove(kvp.Key);
			}
			else
			{
				CheckOcclusion(kvp.Key);
			}
		}
		foreach (var r in m_playingSoundsToRemove)
			m_playingSounds.Remove(r);
	}
	RaycastHit[] m_occlusionCheck = new RaycastHit[64];
	Dictionary<GameObject, GameObject> m_occlusionState = new();
	private void CheckOcclusion(GameObject _obj)
	{
		if (_obj == null) return;
		var objTransform = _obj.transform;
		var camToObj = _obj.transform.position + Vector3.up * 3 - Camera.main.transform.position;
		var ray = new Ray(Camera.main.transform.position, camToObj);
		int count = Physics.RaycastNonAlloc(ray, m_occlusionCheck, camToObj.magnitude);
		GameObject occluder = null;
		var currentDrag = InputUtilities.GetCurrentDragObject();
		var ng = _obj.GetComponentInParent<NGCommanderBase>();
		var buildingHolder = GlobalData.Me.m_buildingHolder;
		for (int i = 0; i < count; ++i)
		{
			var hitTransform = m_occlusionCheck[i].collider.transform;
			if (hitTransform.IsChildOf(objTransform)) continue;
			if (objTransform.IsChildOf(hitTransform)) continue;
			if (hitTransform.parent == objTransform.parent) continue;
			//if (hitTransform == PlayerHandManager.Me.Hand) continue;
			//if (hitTransform.GetComponentInParent<NGMovingObject>()) continue;
			if (hitTransform.IsChildOf(buildingHolder) == false && m_occlusionCheck[i].collider is TerrainCollider == false) continue;
			if (ng != null && hitTransform.GetComponentInParent<NGCommanderBase>() == ng) continue;
			var db = hitTransform.GetComponentInParent<DTDragBlock>();
			if (db != null && db.IsDragging) continue;
			if (currentDrag != null && hitTransform.IsChildOf(currentDrag.transform)) continue;
			occluder = hitTransform.gameObject;
			break;
		}
		if (m_occlusionState.TryGetValue(_obj, out var lastOccluder) == false || lastOccluder != occluder)
		{
			//Debug.LogError($"Object {_obj.name} changed occlusion state to {occluded}");
			m_occlusionState[_obj] = occluder;
			AkSoundEngine.SetRTPCValue("ObstructionValue", occluder != null ? 1 : 0, _obj);
		}
	}

	public static string SanitiseName(string _name)
	{
		if (_name == null) return "<none>";
		int index = _name.IndexOf('>');
		if (index != -1) _name = _name.Substring(0, index);
		return _name;
	}

	public string DebugOccluders(string _filter)
	{
		_filter = _filter.ToLower();
		bool useFilter = _filter != "*";
		var sb = new System.Text.StringBuilder();
		var clr = "ffc0c0";
		foreach (var kvp in m_occlusionState)
		{
			if (kvp.Value == null) continue;
			if (useFilter && kvp.Key.name.ToLower().Contains(_filter) == false) continue;
			sb.AppendLine($"<color=#{clr}>{SanitiseName(kvp.Key.name)} occluded by {SanitiseName(kvp.Value?.name)}</color>");
			clr = clr == "ffc0c0" ? "80ff80" : "ffc0c0";
		}
		return sb.ToString();
	}

	void LateUpdate()
	{
		UpdateTrackedSounds();
	}

	public void SetSoundSwitchVO(string state, string stateValue)
	{
		SetSoundSwitch(state, stateValue, m_VOAudioBus);
	}

	public void SetSoundSwitchMusic(string state, string stateValue)
	{
		SetSoundSwitch(state, stateValue, m_musicAudioBus);
	}

	// function to hold switch calls that are not ready yet
	public void SetSoundSwitch_Future(string state, string stateValue, GameObject go) {}

	public void SetSoundSwitch(string state, string stateValue, GameObject go)
	{
		if (NoSoundCalls) return;
		string v = stateValue.Replace(" ", string.Empty);
		//Debug.Log("SetSoundSwitch " + state + " to >" + v + "< (length " + v.Length + ")");
		AKRESULT result = AkSoundEngine.SetSwitch(state, v, go);
		if (result != AKRESULT.AK_Success)
		{
			UnityEngine.Debug.LogError(string.Format("Failed to set switch {0}:{1}", stateValue, result));
        }
	}

	public void SetHumanoidType(NGMovingObject _obj, string _type)
	{
		SetSoundSwitch("EntityType", $"Entity_{_type}", _obj.gameObject);
	}

	public void SetHumanoidType(NGMovingObject _obj, string _type, int _level)
	{
		SetSoundSwitch("EntityType", $"Entity_{_type}_LEVEL_0{_level}", _obj.gameObject);
	}

	public void SetMusicSwitch(string _state, string _value)
	{
		SetSoundSwitch(_state, _value, m_musicAudioBus);
	}

	public void SetSoundState(string state, string stateValue)
	{
		if (NoSoundCalls) return;
		string v = stateValue.Replace(" ", string.Empty);
		AKRESULT result = AkSoundEngine.SetState(state, v);
		if (result != AKRESULT.AK_Success)
		{
			UnityEngine.Debug.LogError(string.Format("Failed to set state {0}:{1}", stateValue, result));
		}
	}

	public void SetBoolSwitch(GameObject _o, string _switch, bool _on)
	{
		SetSoundSwitch(_switch, $"{_switch}_{(_on ? "TRUE" : "FALSE")}", gameObject);
	}

	public void SetPossessedSwitch(GameObject _o, bool _possessed) => SetBoolSwitch(_o, "PossessMode", _possessed);

	public void SetSurfaceType(string _type, GameObject _go, bool _isDesigning)
	{
		SetSoundSwitch(_isDesigning ? "MaterialType" : "GroundSurfaceType", _type, _go);
	}

	public void SetImpactAudio(GameObject _impacter, GameObject _impactedAgainst)
	{
		var type = "GROUND";
		if (_impactedAgainst.GetComponentInParent<MABuilding>() != null && _impactedAgainst.GetComponentInParent<BaseBlock>() == null) type = "BUILDING";
		else if (_impactedAgainst.GetComponentInParent<MAWildBlock>() != null) type = "BUILDING";
		else if (_impactedAgainst.GetComponentInParent<RoadSetLink>() != null) type = "FENCE";
		else if (_impactedAgainst.GetComponentInParent<TreeHolder>() != null) type = "TREE";
		else if (GlobalData.Me.IsWaterAt(_impacter.transform.position)) type = "WATER";
		if (m_logEvents > 0) Debug.LogError($"Impact on {_impacter.name} against {_impactedAgainst.name} type ImpactTarget_{type}", _impacter);
		SetSoundSwitch("PhysicsImpactTarget", $"ImpactTarget_{type}", _impacter);
	}

	public void SetSoundset(GameObject _o, int _index)
	{
		SetSoundSwitch("UniqueSoundsetID", $"SoundsetID_{_index + 1:00}", _o);
	}
	

	public void SetSoundState(string stateValue)
	{
		if (NoSoundCalls) return;
		return;
		AKRESULT result = AkSoundEngine.SetState("Scenes", stateValue);
		if (result != AKRESULT.AK_Success)
		{
			UnityEngine.Debug.LogError(string.Format("Failed to set state {0}:{1}", stateValue, result));
		}
	}

	static uint[] s_playingIds = new uint[50];
	public bool IsPlaying(string _name, GameObject _obj)
	{
		if (NoSoundCalls) return false;
		
		var clip = GetClip(_name);
		if (clip != null)
		{
			for (int i = 0; i < m_sources.Length; ++i)
			{
				if (m_sources[i].clip == clip)
				{
					// with sound disabled or Unity sound disabled the AudioClip hack fails to report playing status
					// instead record the time the clip started and use that and the clip length to determine if it -should- be playing
					if (long.TryParse(m_sources[i].name.Substring(5), out var startTime))
					{
						var playTime = DateTime.UtcNow.Ticks - startTime;
						var ms = playTime / 10000;
						var clipMs = (long) (clip.length * 1000);
						return ms > 0 && ms < clipMs;
					}
					//return m_sources[i].isPlaying;
				}
			}
			return false;
		}
		
		uint testEventId = AkSoundEngine.GetIDFromString(_name);
		uint count = (uint)s_playingIds.Length;
		AKRESULT result = AkSoundEngine.GetPlayingIDsFromGameObject(_obj, ref count, s_playingIds);
		for (int i = 0; i < count; i++)
		{
			uint playingId = s_playingIds[i];
			uint eventId = AkSoundEngine.GetEventIDFromPlayingID(playingId);
			if (eventId == testEventId)
				return true;
		}
		return false;
	}

	public bool IsPlayingVO(string _name)
	{
		return IsPlaying(_name, m_VOAudioBus);
	}	
	public bool IsPlaying(GameObject _obj) // KW: HasAnySoundPlaying/AkSoundEngine.GetIsGameObjectActive doesn't seem to work as expected
	{
		if (NoSoundCalls) return false;
		uint count = (uint)s_playingIds.Length;
		AKRESULT result = AkSoundEngine.GetPlayingIDsFromGameObject(_obj, ref count, s_playingIds);
		return count > 0;
	}

	public void StopPlaying(string _name, GameObject _obj)
	{
		if (NoSoundCalls) return;
		uint testEventId = AkSoundEngine.GetIDFromString(_name);
		uint count = (uint)s_playingIds.Length;
		AKRESULT result = AkSoundEngine.GetPlayingIDsFromGameObject(_obj, ref count, s_playingIds);
		for (int i = 0; i < count; i++)
		{
			uint playingId = s_playingIds[i];
			uint eventId = AkSoundEngine.GetEventIDFromPlayingID(playingId);
			if (eventId == testEventId)
			{
				StopSound((int)playingId, _obj);
				return;
			}
		}
	}

	// -------------------------------------------------------------------------

	public int PlaySoundOld(string _name, Transform _obj)
	{
		return PlaySoundOld(_name, _obj.gameObject);
	}
	public int PlaySoundOld(string _name, GameObject _obj, float _pitch = 1)
	{
		return -1;
	}

	public int PlayUISound(string _name)
	{
		return PlaySound(_name, GameManager.Me.gameObject, true);
	}

	public int PlaySound(string _name, GameObject _obj, bool _allowPreLoad = false, bool _allowGetSoundPosition = false) {
		if (_obj == null) return -1;
		if (NoSoundCalls) return -1;
		if (_allowPreLoad == false && GameManager.Me.LoadComplete == false) return -1;
		if (string.IsNullOrEmpty(_name)) return -1;
		int playingId = TryPost(_name, _obj, "PlaySound", _allowGetSoundPosition);
		if (playingId != -1)
			CacheGameObject(_obj);
		return (int)playingId;
	}

	// -------------------------------------------------------------------------

	public int PlaySound(string _name, Transform _transform) {
		return PlaySound(_name, _transform.gameObject);
	}

    public void PlayTutorialSound(string _name, Transform _transform)
    {
        int eventID = PlaySound(_name, _transform.gameObject);
        RegisterCallback(eventID, OnTutorialSoundComplete);
    }

    public void OnTutorialSoundComplete()
    {

    }
	// -------------------------------------------------------------------------
	// for placeholders
	public int PlayMusic_Future(string _name, float _volume = 1.0f, bool _overridePlayerPref = false) => -1;
	public int PlayMusic_Old(string _name, float _volume = 1.0f, bool _overridePlayerPref = false) => -1;
	
	int m_musicID = -1;
	[EditorDebug]
	public int PlayMusic(string _name, float _volume = 1.0f, bool _overridePlayerPref = false) {
		//Debug.Log("PlayMusic " + _name);
		if (m_musicID != -1)
			;//StopMusic(m_musicID, 1000);
		return m_musicID = TryPost(_name, m_musicAudioBus, "Music");
	}

	public int PlayVO(string _name, float _volume = 1.0f, bool _overridePlayerPref = false, int _direction = 0)
	{
		m_VOAudioBus.transform.localPosition = Vector3.forward * 1.0f + Vector3.right * (_direction * 1.0f);
		return TryPost(_name, m_VOAudioBus, "VO");
	}

	// -------------------------------------------------------------------------
	
	private void EventCallback(object _cookie, AkCallbackType _type, AkCallbackInfo _info) {
		if((uint)(_type & AkCallbackType.AK_EndOfEvent) == (uint)AkCallbackType.AK_EndOfEvent) {
			AkEventCallbackInfo evInfo = (AkEventCallbackInfo)_info;
			WwiseCookie cookie = (WwiseCookie)(_cookie);
			int key = (int)evInfo.playingID;
			if(m_callbacks.TryGetValue(key, out var callback)) {
				callback();
				m_callbacks.Remove(key);
			}
			if( cookie.gameObject != null ) {
				RemoveGameObject(cookie.gameObject);
			}

#if UNITY_EDITOR
			if( kAudioDataEditorOnly.TryGetValue( evInfo.playingID, out var audioData ) ) {
				audioData.isPlaying = false;
			}
#endif
		}
	}

	// -------------------------------------------------------------------------

	private void RemoveGameObject(GameObject _obj) {
		RemoveVisualisation(_obj);
		int index = LookupGameObjectIndex(_obj);
		RemoveGameObjectAtIndex(index);
	}
	
	private void RemoveGameObjectAtIndex( int index, bool force = false ) {
		if( index == -1 ) {
			return;
		}

		var count = --m_trackingObjects[index].ReferenceCount;
		if( force || count <= 0 ) {
			m_trackingObjects.RemoveAt(index);
		}
	}

	// -------------------------------------------------------------------------

	private bool ShouldCacheGameObject(GameObject _obj)
	{
		return _obj.isStatic == false;
	}

	private void CacheGameObject( GameObject _obj ) {
		_obj = ValidateGO( _obj );

		if (ShouldCacheGameObject(_obj))
		{
			PingVisualisation(_obj);
			int index = LookupGameObjectIndex( _obj );
			if( index == -1 ) {
				WeakReferenceCounter reference = new WeakReferenceCounter(_obj);
				m_trackingObjects.Add(reference);
			} else {
				m_trackingObjects[index].ReferenceCount++;
			}
		}

		SetAudioTransform(_obj);
	}

	private int LookupGameObjectIndex(GameObject _obj) {
		int result = -1;
		for(int i = 0; i < m_trackingObjects.Count; i++) {
			if(m_trackingObjects[i].IsAlive) {
				GameObject reference = m_trackingObjects[i].Target as GameObject;
				if(reference == _obj) {
					result = i;
					break;
				}
			}
		}
		return result;
	}
	
	// -------------------------------------------------------------------------
	public void StopMusic(int _uniqueIdentifier, int _fadeDuration = 4000){
		if (NoSoundCalls) return;
		Debug.Log("StopMusic " + _uniqueIdentifier);
		m_musicID = -1;
		StopSoundWithFade(_uniqueIdentifier, _fadeDuration);
#if UNITY_EDITOR

		uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_uniqueIdentifier);
		if( kAudioDataEditorOnly.TryGetValue( (uint)_uniqueIdentifier, out var audioData ) ) {
			audioData.isPlaying = false;
		}
#endif
	}

	public void StopVO(int _uniqueIdentifier)
	{
		StopSound(_uniqueIdentifier, m_VOAudioBus);
	}

	public void StopId(ref int _id)
	{
		if (_id > 0)
		{
			StopSound(_id);
			_id = 0;
		}
	}


	[EditorDebug]
	public void StopSound(int _uniqueIdentifier, GameObject _obj = null)
    {
	    if (NoSoundCalls) return;
		//Debug.Log("StopSound " + _uniqueIdentifier); commented out because spam
		if (TryPost(_uniqueIdentifier, _obj, "StopSound" ) == AkSoundEngine.AK_INVALID_PLAYING_ID) {
			ForceStopSound(_uniqueIdentifier);
		}
#if UNITY_EDITOR
		// Else if because ForceStopSound will also attempt this.
		else {
			uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_uniqueIdentifier);
			//Debug.Log("StopSound ID:" + eventID); commented out because spam
			if( kAudioDataEditorOnly.TryGetValue( (uint)_uniqueIdentifier, out var audioData ) ) {
				audioData.isPlaying = false;
			}
		}
#endif
	}

	// public bool IsPlaying(int _uniqueIdentifier, GameObject _obj = null){
	// 	AudioEventData audioData;
	// 	uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_uniqueIdentifier);
	// 	if( GetEventIDFromPlayingID.TryGetValue( (uint)_uniqueIdentifier, out audioData ) ) {
	// 			audioData.isPlaying = false;
	// 		}
	// 	return audioData.isPlaying;
	// }

	// -------------------------------------------------------------------------

	public void ForceStopSound(int _uniqueIdentifier) {
		if (NoSoundCalls) return;
		AkSoundEngine.StopPlayingID((uint)_uniqueIdentifier);

#if UNITY_EDITOR
		uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_uniqueIdentifier);
		if( kAudioDataEditorOnly.TryGetValue( (uint)_uniqueIdentifier, out var audioData ) ) {
			audioData.isPlaying = false;
		}
#endif
	}
	// -------------------------------------------------------------------------

	public void StopSoundWithFade(int _uniqueIdentifier, int _fadeDuration = 1000, AkCurveInterpolation _fadeInterpolation = AkCurveInterpolation.AkCurveInterpolation_Linear ) {
		if (NoSoundCalls) return;
		AkSoundEngine.StopPlayingID((uint)_uniqueIdentifier, _fadeDuration, _fadeInterpolation );
#if UNITY_EDITOR
		uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_uniqueIdentifier);
		if( kAudioDataEditorOnly.TryGetValue( (uint)_uniqueIdentifier, out var audioData ) ) {
			audioData.isPlaying = false;
		}
#endif
	}

	// -------------------------------------------------------------------------

	public void Seek(int _handle, int _position, GameObject _obj)
	{
		uint eventID = AkSoundEngine.GetEventIDFromPlayingID((uint)_handle);
		AkUnitySoundEngine.SeekOnEvent(eventID, _obj, _position, false, (uint)_handle);
	}

	public int GetPlayPosition(int _handle)
	{
		AkUnitySoundEngine.GetSourcePlayPosition((uint) _handle, out int position);
		return position;
	}

	// -------------------------------------------------------------------------
	
	private void OnApplicationQuit() {
		Utility.IsQuitting = true;
		
		if (s_disableAllSound) return;
		
		m_readyToPlaySound = false;
		
		AkSoundEngine.StopAll();
		AkSoundEngine.UnregisterAllGameObj();

		/*foreach(string bank in m_banks) {
			AkSoundEngine.UnloadBank(bank, System.IntPtr.Zero);
		}

		AkSoundEngine.UnloadAllFilePackages();*/
	}
	
	// -------------------------------------------------------------------------

	public void RegisterCallback(int _eventID, Action _callback) {
        if (m_callbacks.ContainsKey(_eventID))
            return;
		m_callbacks.Add(_eventID, _callback);
	}

	// -------------------------------------------------------------------------

	public void UnregisterCallback(int _eventID) {
		m_callbacks.Remove(_eventID);
	}

	// -------------------------------------------------------------------------

	public void SetMusicVolume(float _value)
	{
		m_musicVolume = _value;
		MPlayerPrefs.SetFloat(c_prefsMusicVolume, _value);
        SetGameParameter("MasterVolume_MUSIC", MusicVolume, null);
    }

	public float GetMusicVolume()
	{
		return MPlayerPrefs.GetFloat(c_prefsMusicVolume, 1f);
	}

	public void SetVOVolume(float _value)
	{
		m_VOVolume = _value;
		MPlayerPrefs.SetFloat(c_prefsVOVolume, _value);
		SetGameParameter("MasterVolume_VO", VOVolume, null);
	}

	public float GetVOVolume()
	{
		return MPlayerPrefs.GetFloat(c_prefsVOVolume, .6f);
	}
	
	public void SetSFXVolume(float _value)
	{
		m_sfxVolume = _value;
		MPlayerPrefs.SetFloat(c_prefsSFXVolume, _value);
		SetGameParameter("MasterVolume_SFX", SFXVolume, null);
	}

	public float GetSFXVolume()
	{
		return MPlayerPrefs.GetFloat(c_prefsSFXVolume, 1f);
	}

	public bool GetSilenceAll()
	{
		return MPlayerPrefs.GetInt(c_prefsSilenceAll, 0) != 0;
	}

	public void SetSilenceAll(bool _silence)
	{
		m_silenceAll = _silence;
		MPlayerPrefs.SetInt(c_prefsSilenceAll, _silence ? 1 : 0);
		var type = _silence ? "MUTE" : "UNMUTE";
		PlaySound($"WwiseMusic_{type}", m_musicAudioBus, true);
		PlaySound($"WwiseVO_{type}", m_VOAudioBus, true);
		PlaySound($"WwiseSFX_{type}", GameManager.Me.gameObject, true);
	}

	public float GetOverallVOVolume()
	{
		if (GetSilenceAll()) return 0;
		return GetVOVolume();
	}

	public void SetBezierDistance(float d)
	{
		if (NoSoundCalls) return;
		AkSoundEngine.SetRTPCValue("Bezier_Distance", d, gameObject);
	}

	public void SetWorkerHeight(float _height, GameObject _gameObject)
	{
		if (NoSoundCalls) return;
		AkSoundEngine.SetRTPCValue("Worker_Height", _height, _gameObject);
	}

	// -------------------------------------------------------------------------
	
	private GameObject ValidateGO( GameObject _obj ) {
		return ( _obj != null ) ? _obj : this.gameObject;
	}
}
