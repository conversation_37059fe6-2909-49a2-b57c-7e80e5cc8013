
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class NGPowerLevel
{
    public static List<NGPowerLevel> s_powerLevels = new List<NGPowerLevel>();
    public static List<NGPowerLevel> GetList => s_powerLevels;
    public string DebugDisplayName => m_power;
    
    public string id;
    public bool m_debugChanged;
    public string m_power;
    public string m_ssao;
    public string m_antiAliasing;
    public string m_textureQuality;
    public string m_shadowQuality;
    public string m_grass;
    public string m_wildlife;

    public static List<NGPowerLevel> LoadInfoStage(System.Func<NGPowerLevel, bool> _callbackOnEachElement)
    { 
        s_powerLevels = NGKnack.ImportKnackInto<NGPowerLevel>(_callbackOnEachElement); 
        return s_powerLevels;
    }
    public static bool PostLoad(NGPowerLevel _what)
    {
        return true;
    }
    public static List<NGPowerLevel> LoadInfo()
    { 
        s_powerLevels = NGKnack.ImportKnackInto<NGPowerLevel>(PostLoad); 
        return s_powerLevels;
    }

    public static NGPowerLevel PowerLevel
    {
        get
        {
            if(s_powerLevels.Count > 1)
            {
                int iPowerLevel = s_powerLevels.FindIndex(x => x.m_power == NGDeviceID.DeviceId.m_powerLevel);
                NGPowerLevel powerLevel = s_powerLevels[iPowerLevel];
                s_powerLevels.Clear();
                s_powerLevels.Add(powerLevel);
            }

            return s_powerLevels.Count > 0 ? s_powerLevels[0] : null;
        }
    }
}
