using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(RoadSet))]
public class RoadSetEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		if (Application.isPlaying == false) return;
		RoadSet.s_autoRefresh = GUILayout.Toggle(RoadSet.s_autoRefresh, "Auto Refresh");
		if (RoadSet.s_autoRefresh == false)
		{
			if (GUILayout.Button("Refresh Path Visuals"))
			{
				if (RoadManager.Me != null)
					RoadManager.Me.GeneratePathVisuals();
			}
		}
	}
}
#endif

public class RoadSet : MonoBehaviour {
	public static bool s_autoRefresh = false;
	
	[System.Flags]
	public enum BuildingsAccepted {
		House = 1,
		Building = 2,
		RailwayStation = 4,
		Dock = 8,
	};
	[Header("Main Meshes")]
	public GameObject m_prefabEnd;
	public GameObject m_prefabStraight;
	public GameObject[] m_prefabStraights;
	public GameObject m_prefabCorner;
	public GameObject m_prefabJunction;
	public GameObject m_prefabCross;
	public GameObject m_prefabBlend;
	public GameObject m_prefabCap;
	public GameObject m_prefabCapEnd;
	public GameObject m_prefabCapIntersection;
	public GameObject m_prefabCapMinor;
	public GameObject m_prefabCapFurniture;
	public GameObject m_prefabConstruction;
	public float m_heightAdjust = 0.25f;
	public bool m_onlyWholeSections = false;
	public bool m_meshCastsShadows = true;
	public bool m_noMeshColliders = false;
	public SurfaceMaterial.ESurfaceType m_surfaceType = SurfaceMaterial.ESurfaceType.Mud;
	[Header("Bridges")]
	public GameObject m_prefabBridgeMid;
	public GameObject m_prefabBridgeAlt;
	public GameObject m_prefabBridgeEnd;
	public GameObject m_prefabBridgeColliderMid;
	public GameObject m_prefabBridgeColliderAlt;
	public GameObject m_prefabBridgeColliderEnd;
	public float m_bridgeOverlap = 2;
	public SurfaceMaterial.ESurfaceType m_bridgeSurfaceType = SurfaceMaterial.ESurfaceType.Wood;
	[Header("Palisade Options")]
	public GameObject m_prefabGate;
	public float m_insetGateDistance = 2.5f;
	public GameObject m_prefabDestructionEffect;
	public GameObject m_prefabExplode;
	public GameObject m_prefabBreakLeft;
	public GameObject m_prefabBreakRight;
	public GameObject m_prefabStraightCollision;
	public GameObject m_prefabCapCollider;
	public GameObject m_prefabCapIntersectionCollider;
	public string m_breakRepairMaterial = NGCarriableResource.c_timber;
	public bool m_isPerimeterType = false;
	[Header("River Path Options")]
	public Material m_createSurfaceMaterial = null;
	public GameObject[] m_createSurfaceDecorationsTop;
	public GameObject[] m_createSurfaceDecorationsBottom;
	public float m_createSurfaceLevel = 1;
	public bool m_createSurfaceAlwaysFlat = false;
	public float m_createSurfaceAverageLengthBetweenDrops = 5;
	public float m_createSurfaceMaxDropHeight = 2;
	public float m_createSurfaceHeightSmoothRadiusMultiplier = 2;
	public float m_createSurfaceMaxSlope = .05f;
	public bool m_fillWaterPresence = false;
	public float m_islandWidth = 0, m_islandHeightMin = 0, m_islandHeightMax = 0, m_islandRandom = 0, m_islandSteepness = 0;
	public float m_islandStartEnd = 0;
	[Header("Ravine Options")]
	public bool m_isRavine = false;
	public float m_ravineStartFraction;
	public float m_ravineEndFraction;
	public float m_ravineEdgeSmooth = 0;
	[Header("Splat Path Options")]
	[FormerlySerializedAs("m_splatOuter")]
	public int m_splatOuterChannel = -1;
	[FormerlySerializedAs("m_splatInner")]
	public int m_splatInnerChannel = -1;
	[FormerlySerializedAs("m_splatRadius")]
	public float m_splatRadiusOuter = 4;
	public float m_splatInnerFrac = 0.4f;
	public float SplatRadiusInner => m_splatRadiusOuter * m_splatInnerFrac;
	public float m_splatOuterRandomness = 0;
	public float m_splatInnerRandomness = 0.4f;
	public float m_splatInnerRandomRate = 0.2f;
	public bool m_splatOnly = false;
	[Header("Terrain Path Options")]
	public float m_terrainAdjust = -0.3f;
	public float m_pathToBuildingDistance = 0;
	public float m_heightWidthInner = 4;
	public float m_heightWidthOuter = 6;
	public float m_detailWidth = 4;
	public float m_treeRemoveWidth = 0;
	public bool m_removesGrass = true;
	[Header("General Path Options")]
	public bool m_isPavement = false;
	public bool m_blocksNavigation = false;
	public bool m_pushThroughable = false;
	public bool m_creatureBreakable;
	public bool m_isNonCutter = false;
	public bool m_isCorruptable = false;
	public bool m_corruptOthers = false;
	public bool m_noNavBlockerInterrupt;
	public bool m_ignoreWater = false;
	public bool IsPublic => m_isPublic;
	public bool m_isPublic = true;
	public bool m_isConveyor;
	public float m_pathWidth = 4;
	public float m_pavementWidth = 1;
	public float m_navPathWidth = 4;
	public float m_navPavementWidth = 1;
	public float m_extrudeInset = 0;
	public float m_buildingYAdjust = 0;
	public float m_handleRaise = 0;
	public Sprite m_buttonIcon;
	public int m_priority = 100;
	public float m_stepSize = 8;
	public AkEventHolder m_pathDragAudio;
	public AkEventHolder m_pathConstructAudio;
	public BuildingsAccepted m_buildingsAccepted = BuildingsAccepted.House | BuildingsAccepted.Building | BuildingsAccepted.RailwayStation | BuildingsAccepted.Dock;
	GameObject[] m_prefabs; public GameObject[] Prefabs => m_prefabs;
	void Awake() {
		m_prefabs = new GameObject[] { m_prefabEnd, m_prefabStraight, m_prefabCorner, m_prefabJunction, m_prefabCross, m_prefabBlend, m_prefabCap, m_prefabCapMinor, m_prefabBridgeMid, m_prefabBridgeAlt, m_prefabBridgeEnd };
	}

	private float m_cachedEndCapLength = -1;
	public float EndCapLength()
	{
		if (m_cachedEndCapLength < 0)
			m_cachedEndCapLength = ManagedBlock.GetTotalVisualBounds(m_prefabCap, null, false, true).size.x;
		return m_cachedEndCapLength;
	}

#if UNITY_EDITOR
	float m_refreshTime = 0;
	void Update()
	{
		if (s_autoRefresh && m_refreshTime > 0 && Time.unscaledTime > m_refreshTime)
		{
			m_refreshTime = 0;
			if (RoadManager.Me != null)
				RoadManager.Me.GeneratePathVisuals();
		}
	}

	void OnValidate()
	{
		if (s_autoRefresh && Application.isPlaying && Time.unscaledTime > 10)
			m_refreshTime = Time.unscaledTime + .5f;
	}
#endif
}
