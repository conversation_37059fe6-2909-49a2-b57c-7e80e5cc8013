#define NEW_SCHEME
//#define DEBUG_MAP
using System.Collections;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;

#if NEW_SCHEME
using BuildingPlacement = BuildingPlacementManager.BuildingPos;
#else
using BuildingPlacement = GameObject;
#endif

public class BuildingDrag : DragBase
{
	private GameObject m_lastHit;
	override public bool ShouldHoldDrag => BuildingPlacementManager.Me.HasPlots == false;

	public override void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag)
	{
		BuildingPlacementManager.Me.ShouldShowConfirm = false;
		var ray = Cam.RayAtScreenPosition(InputPosition);
		var hit = BuildingPlacementManager.Me.BuildingRaycast(ray);
		BuildingPlacementManager.Me.UpdatePlaceholderPosition(hit, 5000);
	}

#if NEW_SCHEME
	public static Vector3 PlotPosition(BuildingPlacement _plot)
	{
		return _plot?.m_isValid ?? false ? _plot.m_pos : default;
	}
#else
	public static Vector3 PlotPosition(GameObject _plot)
	{
		return _plot.transform.position;
	}
#endif
	
	public override void OnDragEnd(bool _undo)
	{
		switch(MAGameInterface.CheckSpecialBuildPlacement(PlotPosition(BuildingPlacementManager.Me.m_lastHitPlot)))
		{
			case MAFeedbackCondition.Result.Allowed:
				BuildingPlacementManager.Me.PlaceCardWithoutConfirm();
				break;
				
			case MAFeedbackCondition.Result.Denied:
				BuildMode.CancelExternally();
				break;
				
			case MAFeedbackCondition.Result.None:
				OnDragUpdate(default, default);
				BuildingPlacementManager.Me.ShouldShowConfirm = true;
				BuildingPlacementManager.Me.ShowConfirmationDialogNow();
				break;
		}
	}
}

public class BuildingPlacementManager : MonoSingleton<BuildingPlacementManager> {
	public Transform m_buildingHolder;
	public GameObject m_confirmationDialog;
	public GameObject m_costConfirmationDialog;
	public Ray? m_overideRay;

	bool m_plotsSettled = false;
	bool m_moveMode = false;
	bool m_isDrag = false;
	GameObject m_line = null;
	System.Action m_onPlaceCb = null;
	private bool m_confirmed = false;
	Transform m_previousBestPlot = null;
	
	public bool HasPlots => m_plotsSettled;

	private System.Func<bool, bool> m_createCallback;
	private System.Action m_waitingForConfirmCallback;
	public static bool Consuming => Me == null ? false : (Me.gameObject.activeSelf || Me.m_switchedOffFrame == Time.frameCount);
	protected override void _Awake() {
		gameObject.SetActive(false);
	}
	private Vector3 m_restoreAreaMin, m_restoreAreaMax;
	protected override void _OnEnable() {
		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.BuildingPlacement);
		if (NGCommanderBase.s_heldBuilding != null)
		{
			RestoreTreelikes(NGCommanderBase.s_heldBuilding);
			(m_restoreAreaMin, m_restoreAreaMax) = NGCommanderBase.s_heldBuilding.GetComponent<BuildingNav>().RemovePreviousNavigationData();
		}
		else
			(m_restoreAreaMin, m_restoreAreaMax) = (Vector3.one, Vector3.zero);
		if (m_currentDesign != null)
		{
			var design = m_currentDesign.m_design;
			if (design.StartsWith("1|#")) // empty plot indicator
			{
				var bits = design.Split('|');
				bits = bits[1][1..].Split(':');
				if (true)
				{
					m_currentDesign = new GameState_Design($"1|MABase{bits[0]}x{bits[1]}|0|");
				}
				else
				{
					m_emptyPlotWidth = int.Parse(bits[0]);
					m_emptyPlotHeight = int.Parse(bits[1]);
					m_currentDesign = null;
				}
			}
		}
		GeneratePlotVisuals();
   		m_isDrag = false;//GameManager.GetMouseButton(0);
		m_line = Instantiate(GlobalData.Me.m_pickupLinePrefab);
		m_line.layer = 0;
		MakePlaceholder(m_currentDesign);
		m_firstHitPlot = m_lastHitPlot = ToPlot(GetNearestPlot(m_placeholder.transform));
		ClearBlockerVisuals();
	}
	void OnDisable() {
		if (Utility.IsShuttingDown || RoadManager.Me == null) return; 
		if (m_restoreAreaMin.x < m_restoreAreaMax.x)
			RoadManager.Me.m_pathSet.UpdateNav(m_restoreAreaMin, m_restoreAreaMax);
		KeyboardShortcutManager.Me.PopShortcuts();
		CleanupBezier();
		m_overideRay = null;
		if (m_placeholder != null) {
			NGCommanderBase mgr = m_placeholder.GetComponent<NGCommanderBase>();
			if (mgr != null)
				mgr.DestroyMe();
			else
				Destroy(m_placeholder);
			m_placeholder = null;
		}
		if (IsConfirming) {
			Confirmation(false);
        }
        else 
        { 
    		if (NGCommanderBase.s_heldBuilding != null)
                NGCommanderBase.PlaceHeldBuilding(false);
        }
		DestroyPlotVisuals();
	}
	void CleanupBezier() {
		if (m_line != null) {
			Destroy(m_line);
			m_line = null;
		}
	}

	public const float c_mabaseSnapHeight = .9f;

#if UNITY_EDITOR
	[UnityEditor.MenuItem("22Cans/Art/Generate MABase Prefabs")]
	static void GenerateMABases()
	{
		var base1x1 = Resources.Load<GameObject>("_Prefabs/_Blocks/_CurrentlyUsed/_MA_Base/MABase1x1");
		for (int z = 1; z <= 5; ++z)
		{
			for (int x = 1; x <= 5; ++x)
			{
				if (x == 1 && z == 1) continue;
				var inst = Instantiate(base1x1);
				var baseBlock = inst.GetComponent<BaseBlock>();
				baseBlock.m_baseWidth = x;
				baseBlock.m_baseDepth = z;
				var blk = inst.GetComponent<Block>();
				blk.m_blockInfoID = $"MABase{x}x{z}";
				for (int zz = 0; zz < z; ++zz)
				{
					for (int xx = 0; xx < x; ++xx)
					{
						if (xx == 0 && zz == 0) continue;
						var localPos = new Vector3(xx * 4, blk.m_toVisuals.GetChild(0).transform.localPosition.y, zz * 4);
						var vis = Instantiate(blk.m_toVisuals.GetChild(0).gameObject, blk.m_toVisuals);
						vis.name = $"Base{xx}{zz}";
						vis.transform.localPosition = localPos;
						var hinge = Instantiate(blk.m_toHinges.GetChild(1).gameObject, blk.m_toHinges);
						hinge.name = $"Top{xx}{zz}";
						hinge.transform.localPosition = localPos + Vector3.up * blk.m_toHinges.GetChild(1).localPosition.y;
					}
				}
				UnityEditor.PrefabUtility.SaveAsPrefabAsset(inst, $"Assets/Resources/_Prefabs/_Blocks/_CurrentlyUsed/_MA_Base/MABase{x}x{z}.prefab");
				DestroyImmediate(inst);
			}
		}
		Debug.LogError($"Generate complete");
	}

	[UnityEditor.MenuItem("22Cans/Art/Set MABase Snap Heights")]
	static void SetBaseSnapHeights()
	{
		for (int z = 1; z <= 5; ++z)
		{
			for (int x = 1; x <= 5; ++x)
			{
				var basePad = Resources.Load<GameObject>($"_Prefabs/_Blocks/_CurrentlyUsed/_MA_Base/MABase{x}x{z}");
				var inst = Instantiate(basePad);
				var baseBlock = inst.GetComponent<BaseBlock>();
				var snaps = inst.GetComponentsInChildren<SnapHinge>();
				for (int i = 0; i < snaps.Length; ++i)
				{
					var snap = snaps[i];
					if (snap.GetHingeDirectionType() == SnapHinge.EType.Top)
						snap.transform.localPosition = snap.transform.localPosition.NewY(c_mabaseSnapHeight);
				}
				UnityEditor.PrefabUtility.SaveAsPrefabAsset(inst, $"Assets/Resources/_Prefabs/_Blocks/_CurrentlyUsed/_MA_Base/MABase{x}x{z}.prefab");
				DestroyImmediate(inst);
			}
		}
		Debug.LogError($"Fixup complete");
	}
#endif

	public void ToggleOff()
	{
		if(!IsActive) return;

		gameObject.SetActive(false);
	}

	private UnityEngine.EventSystems.PointerEventData m_fromDrag = null; // the drag (if any) that started this placement
	
	private int m_switchedOffFrame = 0;
	
	public void Toggle(bool _switchOn, GameState_Design _design, System.Action _onPlaceCb = null) {
		if (IsActive == _switchOn) return;
		if (_switchOn == false) m_switchedOffFrame = Time.frameCount;
		m_currentDesign = _design;
		m_createCallback = null;
		m_waitingForConfirmCallback = null;
		m_onPlaceCb = _onPlaceCb;
		m_constructionFraction = 0;
		m_moveMode = _switchOn;
		m_fromDrag = null;
		gameObject.SetActive(_switchOn);
	}
	
	public void ToggleBuild(bool _switchOn, GameState_Design _design, float _constructionFraction, System.Func<bool, bool> _createCallback = null, System.Action _onWaitingForConfirm = null, UnityEngine.EventSystems.PointerEventData _fromDrag = null) {
		if (IsActive == _switchOn) return;
		m_currentDesign = _design;
		m_createCallback = _createCallback;
		m_waitingForConfirmCallback = _onWaitingForConfirm;
		m_onPlaceCb = null;
        m_constructionFraction = _constructionFraction;
        m_moveMode = _switchOn;
        m_fromDrag = _fromDrag;
        gameObject.SetActive(_switchOn);
	}

	public bool IsActive => gameObject.activeSelf;
	int m_plotWidth = 2, m_plotHeight = 1;

#if NEW_SCHEME
#else
	List<Vector4> m_plotCenters = new List<Vector4>();
	void AddPlot(Vector2 _origin, int _x, int _z, int _direction) {
		var v = new Vector4(_origin.x + _x * c_buildingTileSize, 0, _origin.y + _z * c_buildingTileSize, (float)_direction);
		m_plotCenters.Add(v);
	}

	const int c_blockSize = 8;
	public const byte c_empty = 0;
	public const byte c_self = 1;
	public const byte c_plot = 10;
	public const byte c_plotFront = 11;
	public const byte c_plotFill = 12;
	public const byte c_building = 20;
	public const byte c_road = 30;
	public const byte c_roadConstruction = 31;
	public const byte c_roadWrongType = 32;
	public const byte c_undefined = 255;

	public static bool IsRoadType(byte _type) {
		return _type == BuildingPlacementManager.c_road || _type == BuildingPlacementManager.c_roadConstruction || _type == BuildingPlacementManager.c_roadWrongType;
	}
	public static bool IsBuildingType(byte _type) {
		return _type == BuildingPlacementManager.c_building;
	}
	
	public Vector3 m_debugPlot;
	public bool m_debugPlotOnly = false;
	void TryFillSpace(Vector2 _min, RoadBlock _blockData, int _ix, int _iz, int _xc, int _zc, int _dir) {
		bool haveSpace = true;
		int dfx = -(int)Mathf.Sin(_dir * Mathf.Deg2Rad), dfz = -(int)Mathf.Cos(_dir * Mathf.Deg2Rad);
		int dsx = dfz, dsz = -dfx;
		byte match = c_road, match2 = c_road;
		for (int t = -1; t < m_plotHeight; ++t) {
			for (int s = 0; s < m_plotWidth; ++s) {
				int stx = _ix + dsx * s + dfx * t;
				int stz = _iz + dsz * s + dfz * t;
				var cell = _blockData[stx, stz]; 
				if (cell != match && cell != match2) {
					haveSpace = false;
					s = t = 1000;
				}
			}
			match = c_empty;
			match2 = c_plot;
		}
		if (haveSpace) {
			if (NGManager.Me.m_granularBuildingPlacement) {
				_blockData[_ix, _iz] = c_plot;
			} else {
				for (int t = 0; t < m_plotHeight; ++t) {
					for (int s = 0; s < m_plotWidth; ++s) {
						int stx = _ix + dsx * s + dfx * t;
						int stz = _iz + dsz * s + dfz * t;
						_blockData[stx, stz] = t == 0 ? (s == 0 ? c_plot : c_plotFront) : c_plotFill;
					}
				}
			}
			float x = _min.x + (_xc + _ix) * c_buildingTileSize, z = _min.y + (_zc + _iz) * c_buildingTileSize;
			float w = m_plotWidth * c_buildingTileSize, h = m_plotHeight * c_buildingTileSize;
			bool debug = x > m_debugPlot.x - m_debugPlot.z && x < m_debugPlot.x + m_debugPlot.z && z > m_debugPlot.y - m_debugPlot.z && z < m_debugPlot.y + m_debugPlot.z;
			if (!debug && m_debugPlotOnly) return;
			AdjustForOrientation(ref x, ref z, ref w, ref h, _dir + 180);
			if (!GlobalData.Me.CheckTerrainBlocker(x, z, x + w, z + h, debug)) {
				if (IsPlotUnlocked(x, z, w, h))
					AddPlot(_min, _xc + _ix, _zc + _iz, _dir);
			}
		}
	}

	bool IsPlotUnlocked(float _x, float _z, float _w, float _h)
	{
		var v = new Vector3(_x, 0, _z);
		v.x -= _w * .5f - .5f;
		v.z -= _h * .5f - .5f;
		if (!DistrictManager.Me.IsWithinDistrictBounds(v)) return false;
		v.x += _w + 1;
		if (!DistrictManager.Me.IsWithinDistrictBounds(v)) return false;
		v.z += _h + 1;
		if (!DistrictManager.Me.IsWithinDistrictBounds(v)) return false;
		v.x -= _w + 1;
		if (!DistrictManager.Me.IsWithinDistrictBounds(v)) return false;
		return true;
	}
	
	public static void AdjustForOrientation(ref float _x, ref float _z, ref float _w, ref float _h, int _dir) {
		_w -= c_buildingBlockSize; _h -= c_buildingBlockSize;
		_dir = ((_dir + 3600) / 90) & 3;
		switch (_dir) {
			case 0: break;
			case 1: (_w, _h) = (_h, _w); _z -= _h; break;
			case 2: _x -= _w; _z -= _h; break; 
			case 3: (_w, _h) = (_h, _w); _x -= _w; break;
		}
		_x -= c_buildingBlockSize * .5f; _z -= c_buildingBlockSize * .5f;
		_w += c_buildingBlockSize; _h += c_buildingBlockSize;
	}
	void FillHouseData(RoadBlock _blockData, float _x, float _z, int _w, int _h, float _dir, byte _fill = c_building) {
		int dfx = -(int)Mathf.Sin(_dir * Mathf.Deg2Rad), dfz = -(int)Mathf.Cos(_dir * Mathf.Deg2Rad);
		int dsx = dfz, dsz = -dfx;
		if (_blockData.IsInRange((int)_x, (int)_z)) {
			for (int t = 0; t < _h; ++t) {
				for (int s = 0; s < _w; ++s) {
					int stx = (int)(_x - dsx * s - dfx * t);
					int stz = (int)(_z - dsz * s - dfz * t);
					_blockData[stx, stz] = _fill;
				}
			}
		}
	}
	public static void GetBuildingFootprint(GameState_Building _data, out int _x, out int _z, out int _w, out int _h) {
		float fx = _data.m_x, fz = _data.m_z;
		float fw = _data.m_cachedWidth * c_buildingBlockSize, fh = _data.m_cachedHeight * c_buildingBlockSize;
		AdjustForOrientation(ref fx, ref fz, ref fw, ref fh, (int)_data.m_direction);
		_x = (int)fx; _z = (int)fz;
		_w = (int)fw; _h = (int)fh;
	}
#endif


	int m_nextBuildingId = 1;
	void CheckBuildingIds(int _existingId) {
		m_nextBuildingId = Mathf.Max(m_nextBuildingId, _existingId+1);
	}
	public int GenerateBuildingId() {
		return m_nextBuildingId ++;
	}

	private NGCommanderBase m_lastBuilt = null;

	public int LastBuildingId => m_nextBuildingId - 1;
	public NGCommanderBase LastBuilding => m_lastBuilt;

#if NEW_SCHEME
	BuildingPlacement m_firstHitPlot = null;
	public BuildingPlacement m_lastHitPlot;
#else
	GameObject m_firstHitPlot = null;
	public GameObject m_lastHitPlot;
#endif
	const string c_plotName = "__Plot__";
	private bool HasMoved => m_firstHitPlot != m_lastHitPlot;

#if NEW_SCHEME
	public void UpdatePlaceholderPosition(BuildingPlacement _cursorPos, float _maxDistance = 5)
	{
		if (_cursorPos.m_isValid == false) return;
		m_moveMode = true;
		if (m_confirmationDialog.activeSelf)
			m_confirmationDialog.SetActive(false);
		if (m_lastHitPlot == null) m_lastHitPlot = new() { m_isValid = true };
		m_lastHitPlot.m_pos = _cursorPos.m_pos;
		m_lastHitPlot.m_rot = _cursorPos.m_rot;
		MovePlaceHolder(m_lastHitPlot, true);
	}

	public BuildingPlacement BuildingRaycast(Ray _ray)
	{
		if (Physics.Raycast(_ray, out var hit, 10000, GameManager.c_layerTerrainBit))
			return GetNearestPlot(hit.point);
		return Empty;
	}

	private BuildingPlacement FindNearestPlotToMouse()
	{
		var ray = GameManager.Me.m_camera.RayAtScreenPosition(Utility.InputPos);
		return BuildingRaycast(ray);
	}

	private (Vector3, float) SnapToRoad(Vector3 _to)
	{
		const float c_maxDistanceToSnap = 6;
		RoadManager.Me.m_pathSet.GetClosestPathToPoint(null, false, _to, c_maxDistanceToSnap, out var path, out var pathT, (_p) => (_p.Set.m_buildingsAccepted & m_accepted) != 0);
		if (path != null)
		{
			var (pos, fwd, side) = path.GetPosFwdSide(pathT);
			if (math.dot((float3) _to - pos, side) < 0)
			{
				side = -side;
				fwd = -fwd;
			}
			var roadWidth = path.Set.m_navPathWidth;
			const float c_distanceFromRoadEdge = 2;
			_to = pos + side * (m_plotHeight * c_buildingTileSize + roadWidth + c_distanceFromRoadEdge);
			return (_to.GroundPosition(), Mathf.Atan2(fwd.x, fwd.z) * Mathf.Rad2Deg);
		}
		return (_to, m_lastHitPlot?.m_rot ?? 0);
	}

	private BuildingPlacement GetNearestPlot(Vector3 _to, float _maxDistance = 5)
	{
		var (pos, rot) = SnapToRoad(_to);
		return new BuildingPlacement(pos, rot);
	}

	private BuildingPlacement GetNearestPlot(Transform _to, float _maxDistance = 5) => new (_to);

	private void DoBezier(BuildingPlacement hitObj)
	{
		if (m_line != null)
		{
			m_line.SetActive(false);
		}
		else if (hitObj != null)
		{
			m_line.SetActive(true);
			var held = NGCommanderBase.s_heldBuilding;
			var from = GameManager.Me.m_camera.RayAtMouse().GetPoint(15f);

			if (NGCommanderBase.s_heldBuilding != null)
				from = NGCommanderBase.s_heldBuilding.transform.position;

			var to = hitObj.m_pos + Vector3.up * .02f;
			m_line.GetComponent<BezierLine>().SetControlPoints(from, to, BezierLine.ControlGeneration.SmoothStart);

			DragBase.UpdateBezier(m_line.GetComponent<LineRenderer>(), GlobalData.Me.m_buildingPlaceBezierColour);
		}
	}
#else
	public void UpdatePlaceholderPosition(Vector3 _cursorPos, float _maxDistance = 5)
	{
		m_moveMode = true;
		if (m_confirmationDialog.activeSelf)
			m_confirmationDialog.SetActive(false);
		var hit = GetNearestPlot(_cursorPos, _maxDistance);
		if (hit != null)
		{
			MovePlaceHolder(hit.gameObject, true);
			m_lastHitPlot = hit.gameObject;
		}
	}
	private Transform GetNearestPlot(Transform _to, float _maxDistance) => GetNearestPlot(_to.position, _maxDistance); 
	private Transform GetNearestPlot(Vector3 _to, float _maxDistance = 5)
	{
		float bestD2 = _maxDistance * _maxDistance;
		Transform bestPlot = null;
		if (m_plotsSettled == false) return bestPlot;
		foreach (Transform c in m_buildingHolder) {
			if (!c.gameObject.activeSelf) continue;
			var origin = c.GetChild(0);
			if (origin.childCount == 0) continue;
			var vis = origin?.GetChild(0);
			if (vis.childCount == 0) continue;
			var plot = vis?.GetChild(0);
			if (plot != null && plot.name == c_plotName)
			{
				var pos = vis.position + plot.forward * (m_plotHeight - 1) * c_buildingTileSize * .5f;
				var d2 = (pos - _to).xzSqrMagnitude();
				if (d2 < bestD2)
				{
					bestD2 = d2;
					bestPlot = plot;
				}
			}
		}
		return bestPlot;
	}

    private GameObject FindNearestPlotToMouse()
    {
        var ray = m_overideRay == null ? GameManager.Me.m_camera.RayAtMouse() : m_overideRay.Value;
        RaycastHit hit;
		if (Physics.Raycast(ray, out hit, 1000.0f, GameManager.c_layerTerrainBit)) {
			// find nearest plot to mouse
			var bestPlot = GetNearestPlot(hit.point);
			if (bestPlot != null) 
			{
				if(bestPlot != m_previousBestPlot)
                {
					m_previousBestPlot = bestPlot;
					if (GameManager.Me.IsOKToPlayUISound())
						AudioClipManager.Me.PlaySoundOld("PlaySound_Move_Building_Scroll", GameManager.Me.transform);
				}
				return bestPlot.gameObject;
			}
		}

		m_previousBestPlot = null;
        return null;
    }
	
    private void DoBezier(GameObject hitObj) 
    {
        if (m_line != null) 
        {
			m_line.SetActive(false);
		} 
        else if (hitObj != null) 
        {
			m_line.SetActive(true);
			var held = NGCommanderBase.s_heldBuilding;
			var from = GameManager.Me.m_camera.RayAtMouse().GetPoint(15f);

			if (NGCommanderBase.s_heldBuilding != null)
				from = NGCommanderBase.s_heldBuilding.transform.position;

			var to = hitObj.transform.position + Vector3.up*.02f;
			m_line.GetComponent<BezierLine>().SetControlPoints(from, to, BezierLine.ControlGeneration.SmoothStart);

			DragBase.UpdateBezier(m_line.GetComponent<LineRenderer>(), GlobalData.Me.m_buildingPlaceBezierColour);
		}
    }
#endif

	private bool IsSufficientlyFlat(Vector3 _center, Vector3 _fwd, Vector3 _right, float _width, float _depth)
	{
		var corner = _center - _fwd * (_depth * .5f) - _right * (_width * .5f);
		const float c_step = .5f;
		float minY = 1e23f, maxY = -1e23f;
		float avgY = 0, avgCount = 0;
		List<float> heights = new();
		for (float z = 0; z < _depth; z += c_step)
		{
			for (float x = 0; x < _width; x += c_step)
			{
				var pos = corner + _fwd * z + _right * x;
				var y = pos.GroundPosition().y;
				avgY += y; avgCount += 1;
				minY = Mathf.Min(minY, y); maxY = Mathf.Max(maxY, y);
				heights.Add(y);
			}
		}
		avgY /= avgCount;
		float rms = 0;
		for (int i = 0; i < heights.Count; ++i)
		{
			var dy = heights[i] - avgY;
			rms += dy * dy;
		}
		rms /= avgCount;
		rms = Mathf.Sqrt(rms);
		//Debug.LogError($"RMS: {rms} avg {avgY} min {minY} max {maxY} points {heights.Count}");
		const float c_maxRMS = 1.3f;
		return rms < c_maxRMS;
	}
	
	private static bool s_debugAllowAnywhere = false;
	private static DebugConsole.Command s_debugAllowAnywhereCmd = new ("allowbuildanywhere", _s => Utility.SetOrToggle(ref s_debugAllowAnywhere, _s), "Allow building anywhere", "<bool>");
	
#if NEW_SCHEME
	public BuildingPlacementBlockerVisual m_blockerPrefab;
	
	private List<BuildingPlacementBlockerVisual> m_blockerVisual = new ();
	void ShowBlockers(long[] _blockers)
	{
		while (m_blockerVisual.Count < _blockers.Length)
			m_blockerVisual.Add(Instantiate(m_blockerPrefab, transform));
		for (int i = 0; i < _blockers.Length; ++i)
		{
			var index = (int)_blockers[i];
			var type = (int)(_blockers[i] >> 32);
			var pos = GlobalData.Me.I2V(index).GroundPosition(0);
			m_blockerVisual[i].transform.position = pos;
			m_blockerVisual[i].SetType((BuildingPlacementBlockerVisual.EType)type);
		}
		for (int i = _blockers.Length; i < m_blockerVisual.Count; ++i)
			m_blockerVisual[i].SetType(BuildingPlacementBlockerVisual.EType.None);
	}
	void ClearBlockerVisuals()
	{
		for (int i = 0; i < m_blockerVisual.Count; ++i)
			m_blockerVisual[i].SetType(BuildingPlacementBlockerVisual.EType.None);
		m_placeholderIsPositive = true;
	}

	bool m_placeholderIsPositive = true;
	private void MovePlaceHolder(BuildingPlacement _pos, bool _dragging)
	{
		if (m_placeholder != null && _pos != null)
		{
			if (_dragging)
			{
				m_isDrag = true;
				var newPos = _pos.m_pos;
				var newRot = Quaternion.Euler(0, _pos.m_rot + 180, 0);
				if (m_placeholder.transform.position.AlmostEquals(newPos) == false || m_placeholder.transform.rotation.AlmostEquals(newRot) == false)
				{
					m_placeholder.transform.rotation = newRot;
					m_placeholder.transform.position = newPos;
					var right = m_placeholder.transform.right;
					var fwd = m_placeholder.transform.forward;
					var plotCenter = newPos + right * (((m_plotWidth - 1) * .5f) * c_buildingTileSize) + fwd * (((m_plotHeight - 1) * .5f) * c_buildingTileSize);
					float plotW = m_plotWidth * c_buildingTileSize + PathManager.c_buildingMargin * 2, plotH = m_plotHeight * c_buildingTileSize + PathManager.c_buildingMargin * 2; 
					var blockers = PathManager.Path.GetPlotBlockers(plotCenter, fwd, right, (int)(plotW * .75f), (int)(plotH * .75f));
					var isValidPos = blockers.Length == 0;
					ShowBlockers(blockers);
					if (IsSufficientlyFlat(plotCenter, fwd, right, plotW, plotH) == false)
						isValidPos = false;
					if (s_debugAllowAnywhere) isValidPos = true;
					if (m_placeholderIsPositive != isValidPos)
					{
						m_placeholder.ReplaceAllMaterials(isValidPos ? GlobalData.Me.m_positiveSelectionMaterial : GlobalData.Me.m_negativeSelectionMaterial);
						m_placeholderIsPositive = isValidPos;
					}
					//GameManager.Me.ClearGizmos("BP");
					//GameManager.Me.AddGizmoOCube("BP", plotCenter.GroundPosition(1), right * (plotW * .5f), fwd * (plotH * .5f), Vector3.up * 2, isValidPos ? new Color(.3f, 1f, .3f, .5f) : new Color(1f, .3f, .3f, .5f));
				}
			}
		}
	}
#else
    private void MovePlaceHolder(GameObject hitObj, bool dragging)
    {
	    if (m_placeholder != null) 
        {
	        //m_placeholder.SetActive(hitObj != null);
	        
			if (NGCommanderBase.s_heldBuilding != null)
			{
				if (dragging)
				{
					m_isDrag = true;
					if (hitObj != null)
					{
						m_placeholder.transform.rotation = hitObj.transform.rotation * Quaternion.Euler(0, 180, 0);
						m_placeholder.transform.position = OffsetPosition(hitObj.GetComponent<Plot>().FinalPos(), m_placeholder.transform.rotation, m_plotWidth, m_plotHeight);
					}
				}
			} else
            {
                if (dragging)
                {
                    m_isDrag = true;
                    if (hitObj != null)
                    {
                        m_placeholder.transform.rotation = hitObj.transform.rotation * Quaternion.Euler(0, 180, 0);
                        m_placeholder.transform.position = OffsetPosition(hitObj.GetComponent<Plot>().FinalPos(), m_placeholder.transform.rotation, m_plotWidth, m_plotHeight);
                    }
                }
			}
		}
    }
	
    static Transform PlotToHolder(Transform _t) => _t.parent.parent.parent;
    static Transform PlotToOrigin(Transform _t) => _t.parent.parent;
#endif
    
    public void PlaceCardWithoutConfirm()
    {
		var hitObj = ToPlot(m_lastHitPlot);
	    if (hitObj != null)
	    {
			AudioClipManager.Me.PlayUISound("PlaySound_HolderCard_Release");
			
			if (NGCommanderBase.s_heldBuilding == null)
			{
				CreateBuildingOnPlot(hitObj);
		    }
		}
	    gameObject.SetActive(false); // leave build mode
    }
    
	public void ConfirmPlace(BuildingPlacement hitObj, bool confirm)
	{
		var canAfford = hitObj != Empty && m_placeholderIsPositive;
        if (confirm && hitObj != null)
		{
			if (GameManager.Me.IsOKToPlayUISound())
				AudioClipManager.Me.PlaySoundOld("PlaySound_Move_Building_Place", GameManager.Me.transform);
			if (m_waitingForConfirmCallback != null)
				m_waitingForConfirmCallback();
			CleanupBezier();
			var topCenter = ManagedBlock.GetTopCenter(m_placeholder);
			ShowConfirmationDialog(topCenter, (_b) =>
			{
				if (GameManager.Me.AreMouseInteractionsBlocked) return;
				if (_b)
					AudioClipManager.Me.PlayUISound("PlaySound_HolderCard_Release");
				else
					AudioClipManager.Me.PlayUISound("PlaySound_PlaceCardCancel");
				
				if (NGCommanderBase.s_heldBuilding == null)
				{
					if (_b)
						CreateBuildingOnPlot(hitObj);
				}
				else
				{
					if (_b)
					{
#if NEW_SCHEME
						var (pos, rot) = hitObj.FinalPositionAndRotation();
#else
						var. rot = hitObj.transform.rotation;
						var pos = PlotToOrigin(hitObj.transform).position; 
#endif
						int dir = (int)(rot.eulerAngles.y + 180);
						
						var held = NGCommanderBase.s_heldBuilding;

						var (prevMin, prevMax) = held.GetExtents();

						held.m_stateData.m_x = pos.x;
						held.m_stateData.m_z = pos.z;
						held.m_stateData.m_direction = dir;
						var rot180 = rot * Quaternion.Euler(0, 180, 0);

						NGCommanderBase.MoveHeldBuilding(OffsetPosition(pos, rot180, m_plotWidth, m_plotHeight), rot180);
						SetBuildingPosition(held.gameObject, held.Visuals.gameObject, held.m_stateData);

						var nav = held.GetComponent<BuildingNav>();
						nav.RefreshNavigationDetails(m_plotWidth, m_plotHeight);
						held.DoorPosInner = nav.DoorInterior;
						held.DoorPosOuter = nav.DoorExterior;

						var (newMin, newMax) = held.GetExtents();

						//GameManager.Me.ClearGizmos("MoveBuilding");
						//GameManager.Me.AddGizmoCubeMinMax("MoveBuilding", prevMin, prevMax, new Color(1, 0, 0, .5f), true);
						//GameManager.Me.AddGizmoCubeMinMax("MoveBuilding", newMin, newMax, new Color(0, 1, 0, .5f), true);
						
						RoadManager.Me.m_pathSet.CreateVisuals(prevMin, prevMax, null);
						RoadManager.Me.m_pathSet.CreateVisuals(newMin, newMax, null);
						SetFactoryPositionAndScale(held.gameObject, held.Visuals.gameObject, held.m_stateData, true);
					}
					else
					{
						RemoveTreelikes(NGCommanderBase.s_heldBuilding);
						NGCommanderBase.PlaceHeldBuilding(false);
					}
				}
				gameObject.SetActive(false); // leave build mode
			}, _canAfford: canAfford);
		}
        else if (confirm)
        {
			gameObject.SetActive(false); // leave build mode
		}
    }

    public bool ShouldShowConfirm { get; set; }

    private Vector3 m_clickDownPos = Vector3.zero;
    void UpdateMoveMode()
    {
	    ShowConfirmationDialogNow();

	    if (m_firstHitPlot == null)
			m_firstHitPlot = m_lastHitPlot = ToPlot(GetNearestPlot(m_placeholder.transform));
	    if (GameManager.GetMouseButtonDown(0) && Utility.IsMouseOverUI() == false && m_placeholder != null)
			m_placeholder.GetComponent<DragBase>().StartDrag(true, 0);
    }
    
#if NEW_SCHEME
	private BuildingPlacement Empty = new BuildingPlacement();
#else
	private GameObject Empty => gameObject;
#endif
	
    private int m_dragInProgress = 0; 
    public void ShowConfirmationDialogNow()
    {
	    // if we're scrolling switch off the confirm dlg
	    if (GameManager.GetDragContinued()) ++m_dragInProgress;
	    else m_dragInProgress = 0;
	    bool showConfirm = ShouldShowConfirm && m_dragInProgress < 15;
	    if (showConfirm && m_placeholder != null)
	    {
		    //GameManager.Me.FocusCamera(m_lastHitPlot.transform, .2f);
		    var topCenter = ManagedBlock.GetTopCenter(m_placeholder);
		    MoveConfirmationDialog(m_confirmationDialog, topCenter);
	    }
	    if (m_confirmationDialog.activeSelf != showConfirm)
	    {
		    if (showConfirm) ConfirmPlace(m_lastHitPlot ?? m_firstHitPlot, true);
		    else m_confirmationDialog.SetActive(false);
	    }
    }

	BuildingPlacement m_currentHighlightPlot = null;
	void Update() {
		//if (m_pattern == null) return;

#if !NEW_SCHEME
		CheckPlotVisualRadius();
#endif
		
		if (EKeyboardFunction.Cancel.IsDown()) Toggle(false, null, null);
		
#if NEW_SCHEME
		var rotate = (Input.GetKey(KeyCode.LeftArrow) ? -1 : 0) + (Input.GetKey(KeyCode.RightArrow) ? 1 : 0);
		if (m_lastHitPlot != null && rotate * rotate > 0)
		{
			const float c_rotateSpeed = 90;
			m_lastHitPlot.m_rot += rotate * c_rotateSpeed * Time.deltaTime;
			if (m_lastHitPlot.m_rot < -180) m_lastHitPlot.m_rot += 360;
			else if (m_lastHitPlot.m_rot > 180) m_lastHitPlot.m_rot -= 360;
		}
#endif

		if (m_moveMode)
		{
			UpdateMoveMode();
			MovePlaceHolder(m_lastHitPlot, true);
		}
		else
		{
			if (IsConfirming) return;
			
			var hitObj = FindNearestPlotToMouse();
			DoBezier(hitObj);

			bool dragging = GameManager.GetMouseButton(0);
			MovePlaceHolder(hitObj, dragging);

			bool confirm = (m_isDrag == true && !dragging);
			if (NGCommanderBase.s_heldBuilding == null)
				confirm = m_isDrag ? !GameManager.GetMouseButton(0) : GameManager.GetMouseButtonDown(0);

			ConfirmPlace(hitObj, confirm);
			
			m_lastHitPlot = hitObj;
		}
		if (m_currentHighlightPlot != m_lastHitPlot)
		{
			SetPlotMaterial(m_currentHighlightPlot, false);
			SetPlotMaterial(m_lastHitPlot, true);
			m_currentHighlightPlot = m_lastHitPlot;
		}
#if DEBUG_MAP
		if (Input.GetKeyDown(KeyCode.Comma)) {
			AdjustSolo(-1);
		} else if (Input.GetKeyDown(KeyCode.Period)) {
			AdjustSolo(1);
		}
#endif
	}
	
	const float c_pad = 0;//.05f;
	static Vector3 OffsetPosition(Vector3 _basePos, Quaternion _rot, int _w, int _h) {
		float wScale = 1 - (c_pad * 2 / _w);
		float hScale = 1 - (c_pad * 2 / _h);
		float sideOffset = c_buildingTileSize * (1 - wScale) * 0.5f * (_w - 1) - (_w - 1) * c_buildingTileSize * .5f;
		float fwdOffset = -c_buildingTileSize * (1 - hScale) * 0.5f * _h + .5f - (_h - 1) * c_buildingTileSize * .5f;
		return _basePos + _rot * new Vector3(sideOffset, 0, fwdOffset);
	}

	
	public bool IsConfirming => m_confirmationCallback != null;
	System.Action<bool> m_confirmationCallback = null;
	public void ShowConfirmationDialog(Vector3 _pos, System.Action<bool> _cb, int _cost = 0, string _overrideTitle = null, bool _canAfford = true, float _raiseByFraction = 0.15f) {
		var dlg = (_cost == 0 && _overrideTitle == null) ? m_confirmationDialog : m_costConfirmationDialog;
		if (_cost != 0 || _overrideTitle != null) {
			var costLabel = dlg.transform.FindChildRecursiveByName("Cost");
			var acceptButton = dlg.transform.FindChildRecursiveByName("Accept (1)");
			var costTxt = costLabel?.GetComponent<TMPro.TextMeshProUGUI>();
			if (costTxt != null) costTxt.text = _overrideTitle ?? $"Cost: {GlobalData.CurrencySymbol}{_cost}";
			if (acceptButton != null) acceptButton.GetComponent<UnityEngine.UI.Button>().interactable = _canAfford;
		}
		else
		{
			var acceptButton = dlg.transform.FindChildRecursiveByName("Accept")?.GetComponent<UnityEngine.UI.Button>();
			if (acceptButton != null) acceptButton.interactable = _canAfford;
		}
		MoveConfirmationDialog(dlg, _pos, _raiseByFraction);
		dlg.SetActive(true);
		m_confirmationCallback = _cb;
	}

	public void MoveConfirmationDialog(GameObject _dlg, Vector3 _pos, float _raiseByFraction = 0.15f)
	{
		Vector3 pos = RectTransformUtility.WorldToScreenPoint(GameManager.Me.m_camera, _pos) + new Vector2(0f, Screen.height * _raiseByFraction);
		var posMargin = Screen.height * .2f; // 175 out of 800 pixels
		pos.x = Mathf.Clamp(pos.x, posMargin, Screen.width - posMargin);
		pos.y = Mathf.Clamp(pos.y, posMargin, Screen.height - posMargin);
		_dlg.transform.position = pos;
	}

	public void Confirmation(bool _confirm)
	{
		// var special = MAGameInterface.CheckSpecialBuildPlacement(m_lastHitPlot);
		// if (_confirm && special)
		// {
		// 	_confirm = false;
		// }
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		var cb = m_confirmationCallback;
		m_confirmationCallback = null;
		m_confirmed = _confirm;
		m_confirmationDialog.SetActive(false);
		m_costConfirmationDialog.SetActive(false);
		cb(_confirm);
		if (m_createCallback != null) m_createCallback(_confirm);
	}

	public class BuildingPos
	{
		public Vector3 m_pos;
		public float m_rot;
		public bool m_isValid;

		public BuildingPos()
		{
		}

		public BuildingPos(Vector3 _pos, float _rot = 0)
		{
			m_pos = _pos;
			m_rot = _rot;
			m_isValid = true;
		}

		public BuildingPos(Transform _from)
		{
			m_pos = _from.position;
			m_rot = _from.eulerAngles.y - 180;
			m_isValid = true;
		}

		public (Vector3, Quaternion) FinalPositionAndRotation()
		{
			var rot = Quaternion.Euler(0, m_rot, 0);
			var pos = m_pos;
			pos.x += c_buildingTileSize * .5f;
			pos.z += c_buildingTileSize * .5f;
			pos += rot * new Vector3(0, 0, .5f);
			return (pos, rot);
		}

		public static bool operator==(BuildingPos _p1, BuildingPos _p2)
		{
			if (ReferenceEquals(_p1, _p2)) return true;
			if (ReferenceEquals(_p1, null) || ReferenceEquals(_p2, null)) return false;
			return _p1.m_pos.AlmostEquals(_p2.m_pos) && _p1.m_rot.Nearly(_p2.m_rot) && _p1.m_isValid == _p2.m_isValid;
		}
		public static bool operator!=(BuildingPos _p1, BuildingPos _p2) => !(_p1 == _p2);
	}
	
	void SetPlotMaterial(BuildingPlacement _obj, bool _isSelected)
	{
#if !NEW_SCHEME
		if (_obj == null) return;
		_obj.GetComponent<Plot>().SetState(false && _isSelected, m_plotWidth, m_plotHeight);
#endif
	}

#if NEW_SCHEME
	void CreateBuildingOnPlot(BuildingPlacement _plot)
	{
		if (_plot == null) return;
		var dir = _plot.m_rot + 180;
		var x = _plot.m_pos.x;
		var z = _plot.m_pos.z;
#else
	void CreateBuildingOnPlot(GameObject _plot)
	{
		var dir = _plot.transform.rotation.eulerAngles.y + 180;
		var x = PlotToOrigin(_plot.transform).position.x;
		var z = PlotToOrigin(_plot.transform).position.z;
#endif
		var data = AddBuildingToState(x, z, m_plotWidth, m_plotHeight, dir, m_currentDesign, 0, m_constructionFraction);
		var go = CreateBuilding(data, true, null);
		CheckRoadRemoves(x, z, dir);
		if (m_onPlaceCb != null) m_onPlaceCb();
	}

	void CheckRoadRemoves(float _x, float _z, float _dir) {
#if !NEW_SCHEME
		float fx = _x, fz = _z;
		float fw = m_plotWidth * c_buildingBlockSize, fh = m_plotHeight * c_buildingBlockSize;
		AdjustForOrientation(ref fx, ref fz, ref fw, ref fh, (int)_dir);
		if (RoadManager.Me.RemoveRoads(fx,fz,fw, fh))
			TerrainManager.Me.RegenerateRoads(new Vector3(fx - c_buildingBlockSize, 0, fz - c_buildingBlockSize), new Vector3(fx+fw+c_buildingBlockSize*2, 0, fz+fh+c_buildingBlockSize*2));
        else
			TerrainManager.Me.RegenerateDegrass(new Vector3(fx - BuildingPlacementManager.c_buildingBlockSize, 0, fz - BuildingPlacementManager.c_buildingBlockSize), new Vector3(fx+fw+BuildingPlacementManager.c_buildingBlockSize*2, 0, fz+fh+BuildingPlacementManager.c_buildingBlockSize*2));
#endif
	}

	GameState_Building AddBuildingToState(float _x, float _z, int _w, int _h, float _dir, GameState_Design _design, int _id, float _constructionFraction) {
		var data = new GameState_Building(_x, _z, _dir, _design, _w, _h, _id, _constructionFraction);
		GameManager.Me.m_state.m_buildings.Add(data);
		return data;
	}
	
	void SetPlaceholderValues(GameObject _holder) {
		// foreach (var i in _holder.GetComponentsInChildren<c_objectGUI>())
		// 	i.DestroyMe();
		foreach (var ps in _holder.GetComponentsInChildren<ParticleSystem>())
			Destroy(ps);
		_holder.SetLayerRecursively(GameManager.c_layerIgnoreRaycast); // Ignore Raycast
		_holder.ReplaceAllMaterials(GlobalData.Me.m_positiveSelectionMaterial);
		_holder.DeactivateCanvasUI();
		foreach (var cll in _holder.GetComponentsInChildren<Collider>())
			cll.enabled = false;
	}
	void MakePlaceholder(GameState_Design _design) {
		GameObject holder;

        if (NGCommanderBase.s_heldBuilding != null)
        {
	        var copyBase = NGCommanderBase.s_heldBuilding;  
	        var toCopy = copyBase.transform.Find("Visuals").gameObject;
	        copyBase.gameObject.SetActive(false);
	        toCopy.gameObject.SetActive(false);
	        holder = Instantiate(toCopy);
	        toCopy.gameObject.SetActive(true);
	        foreach (var st in holder.GetComponentsInChildren<SoundTrigger>(true)) st.enabled = false;
	        foreach (var bnb in holder.GetComponentsInChildren<BuildingNavBlocker>(true)) { bnb.enabled = false; Destroy(bnb); }
	        foreach (var pb in holder.GetComponentsInChildren<PathBlock>(true)) { pb.enabled = false; Destroy(pb); }
	        foreach (var pi in holder.GetComponentsInChildren<PlantInstance>()) pi.gameObject.SetActive(false);
	        holder.SetActive(true); // only activate after we've destroyed the various components we don't want
		    holder.transform.rotation = copyBase.transform.rotation;
	        holder.transform.position = copyBase.transform.position;
            holder.name = "BuildingPlaceholder";
            holder.transform.SetParent(transform, true);
            SetPlaceholderValues(holder);
        }
        else if (_design == null)
        {
            // empty plot
            holder = new GameObject("BuildingPlaceholderEmpty");
            var visuals = Instantiate(GlobalData.Me.m_roadSegmentEndpointEditPrefab, holder.transform);
            holder.transform.SetParent(transform, true);
            holder.ReplaceAllMaterials(GlobalData.Me.m_positiveSelectionMaterial);
            visuals.transform.localPosition = new Vector3(m_plotWidth  * 4 * .5f - 2, .2f, m_plotHeight * 4 * .5f - 2.4f);
            visuals.transform.localScale = new Vector3(m_plotWidth * 4, .1f, m_plotHeight * 4);
            var grid = DesignTableManager.Me.CreateDesignInPlacePlots(0, 0, m_plotWidth, m_plotHeight, 0);
            grid.transform.SetParent(visuals.transform, false);
            grid.transform.localPosition = Vector3.up * .01f;
            grid.transform.localScale = new Vector3(1 / visuals.transform.localScale.x, 1 / visuals.transform.localScale.y, 1 / visuals.transform.localScale.z);
            Utility.DoNextFrame(() =>
            {
	            if (GameManager.Me.RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .5f, 0), out var hit, GameManager.c_layerTerrainBit))
	            {
		            m_firstHitPlot = m_currentHighlightPlot = null;
		            m_lastHitPlot = ToPlot(GetNearestPlot(hit.point, 1000));
		            MovePlaceHolder(m_lastHitPlot, true);
		            m_isDrag = false;
	            }
            });
        }
        else
        {
            holder = new GameObject("BuildingPlaceholder");
            holder.transform.SetParent(transform);
            AnalyseBuildingDesign(_design, null, (_go, _w, _h) =>
            {
                float buildingScale = TerrainBlock.GlobalScale;
                float wScale = 1 - (c_pad * 2 / _w);
                float hScale = 1 - (c_pad * 2 / _h);
                float uScale = 1 - c_pad;
                _go.transform.localScale = new Vector3(wScale, uScale, hScale) * buildingScale;
                SetPlaceholderValues(holder);
            }, holder);
        }
        var pickup = holder.AddComponent<BuildingDrag>();
        if (m_fromDrag == null)
	        ShouldShowConfirm = true;
        else
	        pickup.StartDrag(true, (int)m_fromDrag.button);
        holder.layer = GameManager.c_layerIgnoreRaycast; // Ignore Raycast
        m_placeholder = holder;
	}
	GameObject m_placeholder;
	
#if NEW_SCHEME
	public static BuildingPlacement ToPlot(BuildingPlacement _pos) => _pos;
#else
	public static GameObject ToPlot(Transform _pos) => _pos?.gameObject;
#endif

	public static GameObject CreateSetPiece(GameState_Building _data)
	{
		var prefab = Resources.Load<GameObject>(_data.m_setPiecePrefab);
		if(prefab == null)
			return null;
			
		var go = Instantiate(prefab, GlobalData.Me.m_buildingHolder);
		go.transform.position = (new Vector3(_data.m_x, 0, _data.m_z)).GroundPosition();
		go.transform.rotation = Quaternion.Euler(0, _data.m_direction, 0);
		var ngc = go.GetComponent<NGCommanderBase>();
		ngc.m_stateData = _data;
		ManageBuildingId(ngc, _data.m_id);
		return go;
	}

	public static void ManageBuildingId(NGCommanderBase _ngc, int _id = 0)
	{
		if (_id == 0) _id = Me.GenerateBuildingId();
		_ngc.m_linkUID = _id;
		Me.CheckBuildingIds(_id);
		GameManager.Me.LinkBuildingId(_id, _ngc);
		_ngc.InitialiseBuildingData(_ngc.m_stateData, _ngc.gameObject, _ngc.transform.Find("Visuals")?.gameObject ?? _ngc.gameObject); 
	}

	public static GameObject CreateBuilding(GameState_Building _data, bool _replaceId, System.Action<GameObject> _cb, bool _checkEmptyDesigns = false, bool _noNavUpdate = false) 
	{
		if (_data.m_buildingDesign == null)
		{
			Debug.LogError($"#!# CreateBuilding failed - design {_data.m_buildingDesign} out of range");
			return null;
		}
		else if(_data.m_buildingDesign.HasDesign == false)
		{
			Debug.LogError($"#!# CreateBuilding failed - no blocks in design");
			return null;
		}

		if (_replaceId) _data.m_id = Me.GenerateBuildingId();

		GameObject holder = MABuilding.Create(GlobalData.Me.m_buildingHolder).gameObject;
		
		var ngf = holder.GetComponent<NGCommanderBase>();
		if (ngf != null) {
			ngf.m_linkUID = _data.m_id;
			ngf.m_stateData = _data;
			if (_replaceId) GameManager.Me.LinkBuildingId(ngf.m_linkUID, ngf);
		}
		Me.CheckBuildingIds(_data.m_id);
		Me.m_lastBuilt = ngf;

		var visuals = holder.transform.Find("Visuals");
		if (visuals == null) {
			visuals = (new GameObject("Visuals")).transform;
			visuals.parent = holder.transform;
		}
		if (_data.m_buildingDesign == null) // design-in-place system
		{
			SetFactoryPositionAndScale(ngf.gameObject, visuals.gameObject, _data, false, _noNavUpdate);
			ngf.InitialiseBuildingData(_data, ngf.gameObject, visuals.gameObject, false);
			if(Me.m_createCallback != null) Me.m_createCallback(true);
			Me.m_createCallback = null;
			
			DesignTableManager.Me.StartDesignGlobally(ngf, null);
			foreach (Transform child in visuals)
				CheckRoadsAndDegrass(child);
		}
		else
		{
			bool straightToDesign = _replaceId;
			System.Func<bool, bool> backupCb = null;
			if (straightToDesign)
			{
				backupCb = Me.m_createCallback;
				Me.m_createCallback = null;
			}
			if(Me.m_createCallback != null) Me.m_createCallback(true);
			Me.m_createCallback = null;
			BuildBuildingDesign(_data, ngf, visuals, false, _o => {
				ngf.RefreshDesignDetails();
				//FlattenBuildingPlot(_data, ngf.gameObject, ngf, visuals);
				ngf.InitialiseBuildingData(_data, ngf.gameObject, visuals.gameObject, false);
				if (_cb != null) _cb(_o);
				if (straightToDesign)
				{
					if(backupCb != null) backupCb(true);
					DesignTableManager.Me.StartDesignGlobally(ngf, null, true);
				}
			}, _noNavUpdate);
		}
		
		return holder;
	}

    public static void CheckRoadsAndDegrass(Transform _visuals)
    {
#if !NEW_SCHEME
        int dir = (int)(_visuals.rotation.eulerAngles.y + 180);
		float fx = _visuals.position.x, fz = _visuals.position.z;
		float fw = 2 * BuildingPlacementManager.c_buildingBlockSize, fh = BuildingPlacementManager.c_buildingBlockSize;
		BuildingPlacementManager.AdjustForOrientation(ref fx, ref fz, ref fw, ref fh, dir);
		if (RoadManager.Me.RemoveRoads(fx,fz,fw, fh))
			TerrainManager.Me.RegenerateRoads(new Vector3(fx - BuildingPlacementManager.c_buildingBlockSize, 0, fz - BuildingPlacementManager.c_buildingBlockSize), new Vector3(fx+fw+BuildingPlacementManager.c_buildingBlockSize*2, 0, fz+fh+BuildingPlacementManager.c_buildingBlockSize*2));
        else
			TerrainManager.Me.RegenerateDegrass(new Vector3(fx - BuildingPlacementManager.c_buildingBlockSize, 0, fz - BuildingPlacementManager.c_buildingBlockSize), new Vector3(fx+fw+BuildingPlacementManager.c_buildingBlockSize*2, 0, fz+fh+BuildingPlacementManager.c_buildingBlockSize*2));
#endif
    }

    public static void RecreateBuilding(GameState_Building _data, NGCommanderBase _ngf, bool _needsRebuild, Dictionary<string, int> _blocksBefore, System.Action<NGCommanderBase> _onComplete = null) {
		var visuals = _ngf.Visuals;
		var ngs = visuals.GetComponentsInChildren<NGCommanderBase>();
		for (int i = 0; i < ngs.Length; ++i) ngs[i].DestroyMe();
		var goTemp = new GameObject("ToDestroy");
		while (visuals.childCount > 0)
		{
			var block = visuals.GetChild(0).transform;
			foreach (var cmp in block.GetComponentsInChildren<BCBase>(true))
				Destroy(cmp); // remove the component early to avoid conflicting with the blocks we're about to add
			block.SetParent(goTemp.transform, true);
		}
		_data.m_designedBy = NGPlayer.Me.PlayerName;
		visuals.parent.rotation = Quaternion.identity;
		BuildBuildingDesign(_data, _ngf, visuals, true, _o => {
			if (_blocksBefore != null)
			{
				var blocksAfter = _o.GetComponentsInChildren<Block>();
				List<Block> newBlocks = new();
				foreach (var b in blocksAfter)
				{
					var id = b.BlockIDPlusPos;
					if (_blocksBefore.TryGetValue(id, out int oldIndex))
					{
						_blocksBefore.Remove(id);
					}
					else
						newBlocks.Add(b);
				}
				HashSet<string> blocksBeforeRemaining = new();
				foreach (var b in _blocksBefore) blocksBeforeRemaining.Add(b.Key.Split('@')[0]);
				foreach (var b in newBlocks)
				{
					if (blocksBeforeRemaining.Contains(b.BlockID))
						blocksBeforeRemaining.Remove(b.BlockID);
				}
			}
			Destroy(goTemp);
			_ngf.RefreshDesignDetails();
			_ngf.InitialiseBuildingData(_data, _ngf.gameObject, _ngf.Visuals.gameObject, _needsRebuild);

			if (DesignTableManager.Me.m_isInDesignGlobally) DesignTableManager.CreateAllDrags(_ngf);
			
			_onComplete?.Invoke(_ngf);
		});
	}
	
    const float c_flattenInnerRadius = 4;
    const float c_flattenOuterRadius = 6;
	static void FlattenBuildingPlot(GameState_Building _data, GameObject holder, NGCommanderBase _ngf, Transform _visuals, bool _noNavUpdate = false)
	{
		var nav = holder.GetComponent<BuildingNav>();
		float desiredHeight = nav.GetDesiredBuildingHeight();

		if (_ngf != null && _visuals != null && _ngf.FlattensLand && _visuals.childCount > 0)
		{
			var first = _visuals.GetChild(0).GetComponent<Block>();
			if (first != null && first.name.StartsWith("MABase"))
			{
				GlobalData.Me.BeginBatchTerrainOperations();
				foreach (Transform plot in first.m_toHinges)
					if (plot.gameObject.activeSelf && plot.transform.forward.y > 0)
						GlobalData.Me.SetHeightsRadial(plot.position, c_flattenInnerRadius, c_flattenOuterRadius, desiredHeight, Vector3.one * -10000, Vector3.one * 10000, _fillWithOriginal: true);
				foreach (Transform plot in first.m_toHinges)
					if (plot.gameObject.activeSelf && plot.transform.forward.y > 0)
						GlobalData.Me.SetHeightsRadial(plot.position, c_flattenInnerRadius, c_flattenOuterRadius, desiredHeight, Vector3.one * -10000, Vector3.one * 10000);
				var dirtyRegion = GlobalData.Me.GetTerrainDirtyRegion();
				GlobalData.Me.EndBatchTerrainOperations();

				if (_noNavUpdate == false)
				{
					var min = GlobalData.FromTerrain(dirtyRegion.Item1);
					var max = GlobalData.FromTerrain(dirtyRegion.Item2);
					float cellOffset = (float) GlobalData.c_maxSlopeEffectDistance / GlobalData.c_terrainXZScale;
					min.x -= cellOffset;
					min.z -= cellOffset;
					max.x += cellOffset;
					max.z += cellOffset;
					RoadManager.Me.m_pathSet.UpdateNav(min, max);
				}
			}
		}
		RefreshBuildingRaise(_visuals, _ngf, holder, false);
	}
	
	public static (Vector3, Vector3) GetFlattenBounds(NGCommanderBase _building, float _groundExtent = 0)
	{
		Vector3 min = Vector3.one * 1e23f, max = Vector3.one * -1e23f;
		var visuals = _building.Visuals;
		if (_building != null && visuals != null && _building.FlattensLand && visuals.childCount > 0)
		{
			var first = visuals.GetChild(0).GetComponent<Block>();
			if (first != null && first.name.StartsWith("MABase"))
			{
				foreach (Transform plot in first.m_toHinges)
				{
					if (plot.gameObject.activeSelf && plot.transform.forward.y > 0)
					{
						var thisMin = plot.position - Vector3.one * c_flattenOuterRadius;
						var thisMax = plot.position + Vector3.one * c_flattenOuterRadius;
						min = Vector3.Min(min, thisMin);
						max = Vector3.Max(max, thisMax);
					}
				}
			}
			if (_groundExtent > 0)
			{
				var c = (min + max) * .5f;
				c = c.GroundPosition();
				min.y = c.y - _groundExtent;
				max.y = c.y + _groundExtent;
			}
		}
		return (min, max);
	}

	public static void RefreshBuildingHeight(NGCommanderBase _ngc)
	{
		var nav = _ngc.GetComponent<BuildingNav>();
		nav.UpdateNavigationData(0, 0);
		FlattenBuildingPlot(_ngc.m_stateData, _ngc.gameObject, _ngc, _ngc.Visuals.transform);
		nav.MoveBuilding();
	}
	
	static void RefreshBuildingRaise(Transform _visuals, NGCommanderBase _ngf, GameObject holder, bool _ignoreDisabled)
	{
		var nav = holder.GetComponent<BuildingNav>();
		float desiredHeight = nav.GetDesiredBuildingHeight();
		float buildingRaise = 0;
		if (_ngf != null && _visuals != null && _ngf.FlattensLand && _visuals.childCount > 0)
		{
			for (int i = 0; i < _visuals.childCount; ++i)
			{
				var block = _visuals.GetChild(i).GetComponent<Block>();
				if (block != null && block.name.StartsWith("MABase") == false && (block.gameObject.activeSelf || _ignoreDisabled == false))
				{
					var info = NGBlockInfo.GetInfo(block.m_blockInfoID);
					if (info.m_buildingRaise > buildingRaise)
						buildingRaise = info.m_buildingRaise;
				}
			}
			for (int i = 0; i < _visuals.childCount; ++i)
			{
				var block = _visuals.GetChild(i).GetComponent<Block>();
				if (block.m_ignoreBuildingRaise)
				{
					var delta = buildingRaise - block.m_raiseApplied;
					block.m_raiseApplied = buildingRaise;
					_visuals.GetChild(i).position -= Vector3.up * delta;
					block.m_toHinges.position += Vector3.up * delta;
				}
			}
		}
		holder.transform.localPosition += Vector3.up * (desiredHeight + c_buildingHeightOffset + buildingRaise - holder.transform.position.y);
		_ngf.m_buildingRaise = buildingRaise;
	}
	public const float c_buildingHeightOffset = -.5f; 

	public int m_debugLinkUID = -1;
	public List<Vector3> m_debugPoints = null;
	public float m_debugHighest, m_debugLowest;

	public static void ReapplyFlatten(NGCommanderBase _ngc, bool _noNavUpdate = false)
	{
		SetFactoryPositionAndScale(_ngc.gameObject, _ngc.Visuals.gameObject, _ngc.m_stateData, true, _noNavUpdate);
	}

	static void SetBuildingPosition(GameObject _root, GameObject _visuals, GameState_Building _data)
	{
		int w = _data.m_cachedWidth, h = _data.m_cachedHeight;
		float buildingScale = TerrainBlock.GlobalScale;
		float wScale = 1; // - (c_pad * 2 / w);
		float hScale = 1; // - (c_pad * 2 / h);
		float uScale = 1; // - c_pad;
		float sideOffset = c_buildingTileSize * (1 - wScale) * 0.5f * (w - 1);
		float fwdOffset = -c_buildingTileSize * (1 - hScale) * 0.5f * h + .5f;
		var pos = new Vector3((float) _data.m_x - c_buildingTileSize * .5f, 0, (float) _data.m_z - c_buildingTileSize * .5f);

		_root.transform.SetParent(GlobalData.Me.m_buildingHolder);
		_root.transform.localRotation = Quaternion.Euler(0, _data.m_direction, 0);
		_root.transform.localPosition = pos + _visuals.transform.right * sideOffset + _visuals.transform.forward * fwdOffset;
		_visuals.transform.localScale = new Vector3(wScale, uScale, hScale) * buildingScale;
	}

	static void SetFactoryPositionAndScale(GameObject _root, GameObject _visuals, GameState_Building _data, bool _isRedesign, bool _noNavUpdate = false)
	{
		if (_data.IsSetPiece) return;

		int w = _data.m_cachedWidth, h = _data.m_cachedHeight;
		
		SetBuildingPosition(_root, _visuals, _data);
		
		BuildingNav nav;
		if (!_isRedesign)
		{
			nav = _root.AddComponent<BuildingNav>();
			_root.GetComponent<NGCommanderBase>().AddPlantController();
			_root.AddComponent<WwiseAnimationEvent>();
		}
		else
			nav = _root.GetComponent<BuildingNav>();
		nav.UpdateNavigationData(w, h);
		FlattenBuildingPlot(_data, _root, _root.GetComponent<NGCommanderBase>(), _visuals.transform, _noNavUpdate);
		nav.MoveBuilding();
		
		var ngf = _root.GetComponent<NGCommanderBase>();
		if (ngf != null) {
			ngf.RefreshTopmost();
			if (!_isRedesign) {
				GameManager.Me.PostProcessFactory(ngf);
			}
		}

		BuildingPickupBehaviour building;
		if (!_isRedesign) {
			building = _root.AddComponent<BuildingPickupBehaviour>();
			if(_root.GetComponent<Pickup>() == null)
				_root.AddComponent<Pickup>();
			if(_root.GetComponent<NGStandardClick>() == null)
				_root.AddComponent<NGStandardClick>();
			_root.SetActive(true);
		} else {
			building = _root.GetComponent<BuildingPickupBehaviour>();
			if (building != null)
				building.MoveBuilding(); // recalculate door position
		}
		
		DisableTreesUnderPlot(_visuals.transform.position, w, h, true);
		
		foreach (Transform child in _visuals.transform)
			CheckRoadsAndDegrass(child);

		MaterialSetColour.RandomiseAllMaterials(_visuals.gameObject);
	}

	static void RestoreTreelikes(NGCommanderBase _building)
	{
		if (_building == null) return;
		var visuals = _building.Visuals;
		var nav = _building.GetComponent<BuildingNav>();
		var (w, h) = nav.GetPreviousNavigationSize();
		DisableTreesUnderPlot(visuals.position, w, h, false);
	}

	static void RemoveTreelikes(NGCommanderBase _building)
	{
		if (_building == null) return;
		var visuals = _building.Visuals;
		var nav = _building.GetComponent<BuildingNav>();
		var (w, h) = nav.GetPreviousNavigationSize();
		DisableTreesUnderPlot(visuals.position, w, h, true);
	}

	static void DisableTreesUnderPlot(Vector3 _pos, int _w, int _h, bool _disable = true)
	{
		const float c_buildingCullMargin = 5.0f;
		Vector3 offset = new Vector3(_w * c_buildingTileSize * .5f + c_buildingCullMargin, 0, _h * c_buildingTileSize * .5f + c_buildingCullMargin);
		var min = _pos - offset;
		var max = _pos + offset;
		if (_disable)
			TerrainPopulation.Me.DisableInstancesInRange(min, max, _save: false);
		else
			TerrainPopulation.Me.EnableInstancesInRange(min, max);
	}
	
	static void BuildBuildingDesign(GameState_Building _data, NGCommanderBase _ngf, Transform _visuals, bool _isRedesign, System.Action<GameObject> _cb, bool _noNavUpdate = false) {
		_ngf.gameObject.SetActive(false);
		AnalyseBuildingDesign(_data.m_buildingDesign, null, (_go, _w, _h) => {
			if (_data.m_cachedWidth != _w || _data.m_cachedHeight != _h) {
				Debug.Log($"Error in cached size for building - cached:{_data.m_cachedWidth}x{_data.m_cachedHeight} actual:{_w}x{_h}");
				_data.m_cachedWidth = _w; _data.m_cachedHeight = _h;
			}
			var holder = _visuals.parent.gameObject;
			_visuals.localPosition = Vector3.zero;
			SetFactoryPositionAndScale(holder, _visuals.gameObject, _data, _isRedesign, _noNavUpdate);
			_go.SetStaticRecursively(true);
			_ngf.gameObject.SetActive(true);
			if (_cb != null) _cb(_visuals.gameObject);
		}, _visuals.gameObject);
	}

#if !NEW_SCHEME
	public class RoadBlock {
		byte[] m_cells;
		int m_size, m_padding, m_stride; public int Stride => m_stride;
		public RoadBlock(int _size, int _padding) {
			m_size = _size; m_padding = _padding; m_stride = _size + _padding * 2;
			m_cells = new byte[m_stride*m_stride];
		}
		public byte this[int _x, int _z] { get { return Get(_x, _z); } set { Set(_x, _z, value); } }
		public byte Get(int _x, int _z) {
			_x += m_padding; _z += m_padding; 
			if (_x < 0 || _z < 0 || _x >= m_stride || _z >= m_stride) return c_undefined;
			return m_cells[_x + _z * m_stride];
		}
		public void Set(int _x, int _z, byte _value) {
			_x += m_padding; _z += m_padding; 
			if (_x < 0 || _z < 0 || _x >= m_stride || _z >= m_stride) return;
			m_cells[_x + _z * m_stride] = _value;
		}
		public void Clear() {
			for (int i = 0; i < m_stride*m_stride; ++i) m_cells[i] = 0;
		}
		public byte[] Cells { get { return m_cells; } set { m_cells = value; } }
		public bool IsInRange(int _x, int _z) { return IsInRange(_x) && IsInRange(_z); }
		public bool IsInRange(int _v) {
			_v += m_padding;
			return _v >= 0 && _v < m_stride;
		}
		public bool Overlaps(int _sx, int _sz, int _ex, int _ez) {
			return _ex >= -m_padding && _sx < m_size+m_padding && _ez >= -m_padding && _sz < m_size+m_padding;
		}
		public void ClampRange(ref int _start, ref int _end) {
			if (_start < -m_padding) _start = -m_padding; if (_end > m_size+m_padding-1) _end = m_size+m_padding-1;
		}
		public int MinIndex => -m_padding;
		public int MaxIndex => m_size + m_padding;
		
		public void Rotate(int _direction) {
			_direction = ((_direction + 3600) / 90) & 3;
			if (_direction == 0) return;
			int stride = Stride;
			var oldData = Cells;
			var newData = new byte[stride * stride];
			for (int y = 0; y < stride; ++y) {
				for (int x = 0; x < stride; ++x) {
					int ox, oy;
					switch (_direction) {
						default: case 1: ox = y; oy = stride - 1 - x + 1; break;
						case 2: ox = stride - 1 - x + 1; oy = stride - 1 - y + 2; break;
						case 3: ox = stride - 1 - y + 2; oy = x; break;
					}
					if (ox >= 0 && ox < stride && oy >= 0 && oy < stride)
						newData[x + y * stride] = oldData[ox + oy * stride];
					else
						newData[x + y * stride] = 0;
				}
			}
			Cells = newData;
		}
	}
#endif
	
	public const string c_extentTypeNormal = "Normal";
	public const string c_extentTypeNone = "None";
	public const string c_extentTypeBox = "Box";
		
	GameObject m_pattern = null;
	GameState_Design m_currentDesign;
	float m_constructionFraction = 0f;
	public const float c_buildingBlockSize = 4.0f;
	public const float c_buildingTileSize = c_buildingBlockSize * TerrainBlock.GlobalScale;
	static void AnalyseBuildingDesign(GameState_Design _design, Transform _parent, System.Action<GameObject, int, int> _onComplete, GameObject _holder = null) {
		if (_design != null) {
			var state = GameManager.Me.m_state;
			var design = _design;
			var go = _holder;
			if (go == null) {
				go = new GameObject("Building");
				go.transform.SetParent(_parent);
			}
			DesignTableManager.Me.RestoreDesign(DesignTableManager.RestoreType.Building, design.m_design, go.transform, (_o,componentsChanged) => {
				go.transform.position = Vector3.zero;
				var blocks = go.GetComponentsInChildren<Block>();
				Vector3 min = Vector3.one * 1e23f, max = Vector3.one * -1e23f;
				var baseBlock = go.GetComponentInChildren<BaseBlock>(true);
				int width, height;
				Vector3 rebase = Vector3.zero;
				if (baseBlock != null)
				{
					width = (int)baseBlock.m_baseWidth;
					height = (int)baseBlock.m_baseDepth;
				}
				else
				{
					foreach (var b in blocks)
					{
						var extentType = NGBlockInfo.GetInfo(b.m_blockInfoID)?.m_extentType;
						Bounds bounds;
						if (extentType == c_extentTypeNone)
							continue;
						if (extentType == c_extentTypeBox)
						{
							var boxes = b.gameObject.GetComponentsInChildren<BoxCollider>();
							if (boxes.Length == 0)
							{
								Debug.LogError($"Block {b.m_blockInfoID} with extent type Box has no box collider");
								continue;
							}
							if (boxes.Length > 1)
								Debug.LogWarning($"Block {b.m_blockInfoID} with extent type Box has multiple box colliders, picking one arbitrarily");
							bounds = boxes[0].bounds;
						}
						else
							bounds = ManagedBlock.GetTotalVisualBounds(b.gameObject, null, b.name.StartsWith("MABase") == false);
						min = Vector3.Min(min, bounds.center - bounds.extents);
						max = Vector3.Max(max, bounds.center + bounds.extents);
					}
					int minX = Mathf.FloorToInt(min.x / c_buildingBlockSize + 512) - 512 + 1, minZ = Mathf.FloorToInt(min.z / c_buildingBlockSize + 512) - 512 + 1;
					rebase = new Vector3(minX * c_buildingBlockSize, 0, minZ * c_buildingBlockSize);
					width = Mathf.Max(1, Mathf.RoundToInt((max.x - min.x) / c_buildingBlockSize));
					height = Mathf.Max(1, Mathf.RoundToInt((max.z - min.z) / c_buildingBlockSize));
				}
				foreach (var b in blocks) {
					if (b.transform.parent.GetComponentInParent<Block>(true) != null) continue; // hierarchical blocks
					b.transform.position -= rebase;
				}
				_onComplete(go, width, height);
			}, false, false, design.m_componentIds);
		}
	}

#if DEBUG_MAP
	Vector2 DEBUG_MapOrigin;
	int DEBUG_MapBlocksX, DEBUG_MapBlocksZ;
	byte[] DEBUG_MapData;
#endif
	void GeneratePlotVisuals() {
#if !NEW_SCHEME
		m_plotCenters.Clear();
#endif
		m_lastHitPlot = null;
		m_plotsSettled = false;

		if (m_emptyPlotWidth > 0)
		{
			GenerateEmptyPlotVisuals(m_emptyPlotWidth, m_emptyPlotHeight);
			return;
		}

		if (m_currentDesign != null) {
			m_accepted = RoadSet.BuildingsAccepted.Building;

			AnalyseBuildingDesign(m_currentDesign, m_buildingHolder, (_go, _w, _h) => {
				m_pattern = _go;
				m_plotWidth = _w; m_plotHeight = _h;
				m_pattern.SetActive(false);
#if NEW_SCHEME
				m_plotsSettled = true;
#else
				if (m_accepted == RoadSet.BuildingsAccepted.Dock)
				{
					CreateDockBlocks();
				}
				GeneratePlotVisualsComplete();
#endif
			});
		}
	}
	
	private static DebugConsole.Command s_empty = new ("desbld", _s =>
	{
		var bits = _s.Split(",");
		Me.StartEmptyPlotPlacement(bits[0], int.Parse(bits[1]), int.Parse(bits[2]));
	});
	
	int m_emptyPlotWidth = 0, m_emptyPlotHeight = 0;
	public void StartEmptyPlotPlacement(string _type, int _width, int _height)
	{
		m_emptyPlotWidth = _width;
		m_emptyPlotHeight = _height;
		gameObject.SetActive(true);
	}
	
	void GenerateEmptyPlotVisuals(int _blocksW, int _blocksH)
	{
		m_plotWidth = _blocksW; m_plotHeight = _blocksH;
		m_accepted = RoadSet.BuildingsAccepted.Building;
#if !NEW_SCHEME
		GeneratePlotVisualsComplete();
#endif
	}

#if !NEW_SCHEME
	void CreateDockBlocks()
	{
		foreach (var t in NGManager.Me.m_dockSpawnPoints)
		{
			Vector3 pos = new Vector3(t.m_position.x, t.m_position.y, t.m_position.z);
			if(DistrictManager.Me.IsWithinDistrictBounds(pos))
			{
				Vector4 v4 = new Vector4(pos.x, pos.y, pos.z, (int)t.m_direction * 90);
				m_plotCenters.Add(v4);
			}
		}
	}
	
	GameObject[] m_plotObjects;

	Vector3 m_plotAcceptedZoneCurrentCenter, m_lastGenerateCenter;
	const float c_plotAcceptedZoneRadius = 40;
	const float c_plotZoneRadius = 75;

	void GeneratePlotVisualsComplete()
	{
		var plotZoneCenter = GameManager.Me.GetTerrainPointAtCenterOfScreen();
		m_lastGenerateCenter = plotZoneCenter;
		if (NGCommanderBase.s_heldBuilding != null) NGCommanderBase.s_heldBuilding.GetComponent<BuildingNav>().RemovePreviousNavigationData();
		m_plotCenters = RoadManager.Me.GeneratePlots(m_plotWidth, m_plotHeight, m_accepted, plotZoneCenter, c_plotZoneRadius);
		if (NGCommanderBase.s_heldBuilding != null) NGCommanderBase.s_heldBuilding.GetComponent<BuildingNav>().UpdateNavigationData(m_plotWidth, m_plotHeight);
		m_plotObjects = new GameObject[m_plotCenters.Count];
		m_plotsSettled = true;
		CheckPlotVisualRadius();
	}
	
	void CheckPlotVisualRadius()
	{
		if (m_plotsSettled == false) return;
		if (GameManager.Me.RaycastAtPoint(new Vector3(Screen.width * .5f, Screen.height * .5f, 0), out var hit, GameManager.c_layerTerrainBit))
			m_plotAcceptedZoneCurrentCenter = hit.point;

		if ((m_plotAcceptedZoneCurrentCenter - m_lastGenerateCenter).xzSqrMagnitude() > c_plotZoneRadius * c_plotZoneRadius)
		{
			DestroyPlotVisuals();
			GeneratePlotVisuals();
			return;
		}
		// generate plot meshes for any plots in range
		for (int i = 0; i < m_plotCenters.Count; ++i) {
			var v = m_plotCenters[i];
			var v3 = new Vector3(v.x, v.y, v.z);

			bool isVisible = (m_plotAcceptedZoneCurrentCenter - v3).xzSqrMagnitude() < c_plotAcceptedZoneRadius * c_plotAcceptedZoneRadius;
			if (!isVisible && m_plotObjects[i] != null)
			{
				Destroy(m_plotObjects[i]);
				m_plotObjects[i] = null;
			}
			else if (isVisible && m_plotObjects[i] == null)
			{
				int dir = (int) v.w;
				float dirRad = dir * Mathf.Deg2Rad;
				var side = new Vector3(Mathf.Sin(dirRad - Mathf.PI * .5f), 0, Mathf.Cos(dirRad - Mathf.PI * .5f)) * c_buildingTileSize; //c_directions[(dir-1)&3] * c_buildingTileSize;
				var back = new Vector3(Mathf.Sin(dirRad - Mathf.PI), 0, Mathf.Cos(dirRad - Mathf.PI)) * c_buildingTileSize; //c_directions[(dir-2)&3] * c_buildingTileSize;
				float wScale = 1 - (.05f * 2 / m_plotWidth);
				float hScale = 1 - (.05f * 2 / m_plotHeight);
				var sideToCenter = side * ((float) (m_plotWidth - 1) * 0.5f);
				var frontToCenter = back * ((float) (m_plotHeight - 1) * 0.5f);
				var centerOffset = sideToCenter + frontToCenter;
				// vp is the position of the plot visual, based on the center of the plot
				var vp = v3 + frontToCenter - new Vector3(c_buildingTileSize * .5f, 0, c_buildingTileSize * .5f);
				// vc is the building data (state) position, based on the front corner of the plot with the out-by-half-block offset
				var vc = v3 - sideToCenter;
				var hPos = v3 - back * .5f;
				if (PlotExcluder.IsExcluded(hPos)) continue;
				vc.y = GlobalData.Me.GetRealHeight(hPos) + .5f; // height at door pos outer-ish
				v3.y = vp.y = vc.y;

				var holder = new GameObject($"Plot {v.x:n0},{v.z:n0} {m_plotWidth * c_buildingTileSize}x{m_plotHeight * c_buildingTileSize}");
				holder.transform.SetParent(m_buildingHolder);
				var origin = new GameObject("Origin");
				origin.transform.SetParent(holder.transform);
				var vis = new GameObject("Visuals");
				vis.transform.SetParent(origin.transform);

				//var go = Instantiate(GlobalData.Me.m_plotPrefab);
				var go = new GameObject("PlotHolder");
				go.transform.SetParent(vis.transform);

				holder.transform.position = v3;
				origin.transform.position = vc;
				vis.transform.position = vp;

				var plot = TerrainSectionMesh.Create("plotMesh", vp, new Vector3(m_plotWidth * 0 + .5f, 0, m_plotHeight * 0 + .5f) * c_buildingTileSize, .15f, GlobalData.Me.m_plotUnselectedMaterial);
				plot.m_uvRotate = dir;
				var goPlot = plot.gameObject;
				goPlot.transform.SetParent(go.transform, true);

				go.name = c_plotName;
				go.transform.localRotation = Quaternion.Euler(0, (float) dir, 0);
				go.AddComponent<Plot>();
				SetPlotMaterial(go, false);

				if (v.w > 360 * 9)
				{
					Utility.DoNextFrame(() =>
					{
						var mr = go.GetComponentInChildren<MeshRenderer>();
						mr.material.color = Color.red;
					});
				}

				goPlot.transform.localPosition = Vector3.zero;
				goPlot.transform.rotation = Quaternion.identity;
				goPlot.transform.localScale = new Vector3(1 / go.transform.lossyScale.x, 1 / go.transform.lossyScale.y, 1 / go.transform.lossyScale.z);
				
				m_plotObjects[i] = go;
			}
		}
	}
#endif
	
	// Functions to handle divide-towards-zero on negative co-ordinates
	public static float WorldToRoadTileWorld(float _f) {
		return Mathf.Floor((_f + 512) / RoadManager.c_roadTileScale) * RoadManager.c_roadTileScale - 512; 
	}
	public static int WorldToBuildingTile(int _n) {
		return (_n + 512) / (int)c_buildingTileSize - (512 / (int)c_buildingTileSize);
	}
	public static float WorldToBuildingTile(float _f) {
		return ((_f + 512) / c_buildingTileSize) - (512 / (int)c_buildingTileSize);
	}
	public static Vector2 WorldToBuildingTile(Vector2 _v) {
		return new Vector2((float)WorldToBuildingTile(_v.x), (float)WorldToBuildingTile(_v.y));
	}
	public static Vector2 WorldToBuildingTileWorld(Vector2 _v) {
		return new Vector2((float)WorldToBuildingTile(_v.x), (float)WorldToBuildingTile(_v.y)) * c_buildingTileSize;
	}
	static int s_soloRoad = -1;
	void AdjustSolo(int _dir) {
		s_soloRoad += _dir;
		DestroyPlotVisuals();
		GeneratePlotVisuals();
	}
	RoadSet.BuildingsAccepted m_accepted;

	void DestroyPlotVisuals() {
#if !NEW_SCHEME
		m_plotCenters.Clear();
#endif
		m_buildingHolder.DestroyChildren();
		Destroy(m_pattern);
		m_pattern = null;
	}

#if DEBUG_MAP
	List<Vector3> m_gizmoData = new List<Vector3>(); Vector3 m_gizmoOrigin;
	void OnDrawGizmos() {
		Gizmos.color = new Color(1,.9f,.9f,.5f);
		for (int i = 0; i < m_gizmoData.Count; i += 2) {
			Gizmos.DrawCube((m_gizmoData[i]+m_gizmoData[i+1])*.5f, (m_gizmoData[i+1]-m_gizmoData[i])*.5f); 
		}
		for (int iz = 0; iz < DEBUG_MapBlocksZ; ++iz) {
			for (int ix = 0; ix < DEBUG_MapBlocksX; ++ix) {
				int v = ((int)DEBUG_MapData[ix+iz*DEBUG_MapBlocksX]) / 10;
				var pos = new Vector3(DEBUG_MapOrigin.x + (ix - .5f) * c_buildingTileSize, 0, DEBUG_MapOrigin.y + (iz - .5f) * c_buildingTileSize);
				pos.y = GlobalData.Me.GetRealHeight(pos);
				Gizmos.color = new Color(((v >> 0) & 1) * 255, ((v >> 1) & 1) * 255, ((v >> 2) & 1) * 255);
				Gizmos.DrawCube(pos, Vector3.one * 1f);
			}
		}
		UnityEditor.Handles.Label(DEBUG_MapOrigin, $"{DEBUG_MapOrigin}");
	}
#else
	void OnDrawGizmos()
	{
		if (m_debugPoints != null)
		{
			foreach (var p in m_debugPoints)
			{
				Color c = Color.white;
				if (p.y.Nearly(m_debugLowest, .01f)) c = Color.red;
				else if (p.y.Nearly(m_debugHighest, .01f)) c = Color.green;
				Gizmos.color = c;
				Gizmos.DrawCube(p, Vector3.one * .2f);
			}
		}
	}
#endif

	/*void OnDrawGizmos() {
		var extent = new Vector3(c_buildingTileSize * .8f, .1f, c_buildingTileSize * .8f);
		var doorExtent = Vector3.one * .3f;
		foreach (var v in m_plotCenters) {
			int dir = (int)v.w;
			var side = c_directions[(dir-1)&3] * c_buildingTileSize;
			var back = c_directions[(dir-2)&3] * c_buildingTileSize;
			var v3 = new Vector3(v.x, v.y, v.z);
			var centerOffset = side * ((float)(m_plotWidth-1) * 0.5f) + back * ((float)(m_plotHeight-1) * 0.5f);
			centerOffset.y = .02f;
			var vc = v3 + centerOffset;
			var totalExtent = side * m_plotWidth + back * m_plotHeight;
			totalExtent.x = Mathf.Abs(totalExtent.x); totalExtent.z = Mathf.Abs(totalExtent.z);
			totalExtent.x *= 1 - (.05f * 2 / m_plotWidth);
			totalExtent.z *= 1 - (.05f * 2 / m_plotHeight);
			totalExtent.y = .25f;
			Gizmos.color = Color.black;
			Gizmos.DrawCube(vc, totalExtent);
			for (int t = 0; t < m_plotHeight; ++t) {
				for (int s = 0; s < m_plotWidth; ++s) {
					Gizmos.color = (s == 0 && t == 0) ? Color.white : Color.grey;
					Gizmos.DrawCube(v3 + side * (float)s + back * (float)t, extent);
				}
			}
			Gizmos.color = Color.green;
			Gizmos.DrawCube(v3+c_directions[dir]*(Mathf.Abs(Vector3.Dot(c_directions[dir], extent))*.5f), doorExtent);
		}
	}*/
}

public class BuildModeInfo {
	public enum EBuildCategory
	{
		None,
		Civic,
		Business,
		Decoration,
		Roads
	}
	public static bool IsBuildingCategory(EBuildCategory _cat) {
		return _cat == EBuildCategory.Civic || _cat == EBuildCategory.Business;
	}
}

public static class BuildMode
{
	public static bool BuildToolActive => BuildingPlacementManager.Me.IsActive;
	public static void CancelExternally() {
		BuildingPlacementManager.Me.Toggle(false, null, null);
	}
	public static void MoveBuilding(NGCommanderBase _building)
	{
		BuildingPlacementManager.Me.Toggle(true, _building.Design, () => {});
	}
}
