using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class DTDragCategory : DragBase {
	public int m_index = 0;
	public GameObject m_contents;
	public float m_angleMin = 0, m_angleMax = 0;
	public bool m_autoReturn = false;
	
	float PositionOnSlider(Ray _ray) { return DragBase.PositionOnSlider(_ray, m_positionAtStart, Vector3.up); }

	override public Camera Cam => DesignTableManager.Me.m_designCamera;
	float m_slidePosition = 0, m_slidePositionSmoothed = 0;
	float m_returnAngle;
	float m_posPrevious;
	Vector3 m_positionAtStart;
	Vector3 m_rotationAtStart;
	bool m_havePositionAtStart = false;
	bool m_isDragging = false;
	bool m_slidePositionAuthoratative = false;

	public void SetPosition(float _f)
	{
		m_slidePosition = m_slidePositionSmoothed = _f;
		m_slidePositionAuthoratative = true;
	}

	public float GetPosition()
	{
		return m_slidePosition;
	}
	
	override public void OnDragStart() {
		// if (NGTutorialManager.Me.IsDesignTableFullyUnlocked() == false)
		// 	return;

		if (!m_havePositionAtStart) {
			m_positionAtStart = transform.position;
			m_rotationAtStart = transform.localEulerAngles;
			m_havePositionAtStart = true;
		}
		m_isDragging = true;
		m_posPrevious = PositionOnSlider(Cam.RayAtMouse());
		m_returnAngle = transform.localEulerAngles.x;
		if (!m_slidePositionAuthoratative)
			m_slidePosition = m_slidePositionSmoothed = m_returnAngle / m_turnSpeed;
	}

	public float m_turnSpeed = 100;
	public float m_turnRatchetAngle = 1f;
	override public void OnDragEnd(bool _undo) {
		// if (NGTutorialManager.Me.IsDesignTableFullyUnlocked() == false)
		// 	return;

		if (m_autoReturn) {
			float dRequired = m_angleMin + m_angleMax - m_returnAngle - m_slidePosition * m_turnSpeed;
			if (dRequired * dRequired < .01f * .01f) {
				// pulled to min or max, trigger it
				GetComponentInParent<IDTDragTrigger>()?.DragTriggered(this);
			}
			m_slidePosition = m_returnAngle / m_turnSpeed;
		}
		m_isDragging = false;
	}
	override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		// if (NGTutorialManager.Me.IsDesignTableFullyUnlocked() == false)
		// 	return;

		var cam = DesignTableManager.Me.m_designCamera;
		var dragNow = PositionOnSlider(cam.RayAtMouse());
		var delta = dragNow - m_posPrevious;
		m_slidePosition += delta;
		m_posPrevious = dragNow;
	}
	void Update() {
		if (m_angleMax - m_angleMin < .001f) {
			if (m_slidePosition > 180f) m_slidePosition -= 360f;
			else if (m_slidePosition < -180f) m_slidePosition += 360f;
		} else {
			m_slidePosition = Mathf.Clamp(m_slidePosition, m_angleMin / m_turnSpeed, m_angleMax / m_turnSpeed);
		}
		m_slidePositionSmoothed = Mathf.Lerp(m_slidePositionSmoothed, m_slidePosition, .25f);
		if (m_isDragging && m_havePositionAtStart) {
			transform.localEulerAngles = new Vector3(Mathf.Round(m_slidePositionSmoothed * m_turnSpeed / m_turnRatchetAngle) * m_turnRatchetAngle, m_rotationAtStart.y, m_rotationAtStart.z);
		}
	}
}
