using System;
using System.Collections;
using System.Collections.Generic;
using Cans.Analytics;
using UnityEngine;
using ClipperLib;

#if UNITY_EDITOR
using UnityEditor;
#endif

using Polygon = System.Collections.Generic.List<ClipperLib.IntPoint>;
using PolygonCollection = System.Collections.Generic.List<System.Collections.Generic.List<ClipperLib.IntPoint>>;
using UnityEngine.Rendering;
using UnityEngine.Playables;
//using Sirenix.OdinInspector;
using DG.Tweening;
using Object = UnityEngine.Object;

public class DistrictManager : MonoSingleton<DistrictManager>
{
	public DistrictData m_data = new();

	[SerializeField] private Camera _districtRenderer = null;
	[SerializeField] private Material districtBlit;
	
	public Camera RenderCamera => _districtRenderer;
	public Material RenderMaterial => districtBlit;
	public Material m_mapEdgeMaterial;
	public Material m_mapFillLockedMaterial;
	public Material m_mapFillUnlockedMaterial;
	public Material m_mapTopHighlightMaterial;
	
	public Texture2D m_texture;
	public Unity.Collections.NativeArray<byte> m_textureData;
	
	public AkEventHolder m_unlockAudio;
	
	public GameObject[] m_mapIconPrefabs;
	
	private static DebugConsole.Command s_districteditcmd = new ("districtedit", _s => Me.EditDistricts(_s));

	public const int c_textureSize = 512;

	void Start()
	{
		m_texture = new Texture2D(c_textureSize, c_textureSize, TextureFormat.RG16, false, true);
		m_texture.wrapMode = TextureWrapMode.Clamp;
		m_data.Load();
	}
	
	public void SetAnimationSpeed(float _speed)
	{
		m_data.m_unlockAnimationSpeed = _speed;
	}

	private bool m_districtEdit = false; public bool IsEditing => m_districtEdit;

	public bool LastUnlockFlybyComplete => m_data.LastUnlockFlybyComplete;
	
	private void EditDistricts(string _s)
	{
		Utility.SetOrToggle(ref m_districtEdit, _s);
		EditDistrictsModeChanged();
	}

	public void CloseEditDistricts()
	{
		m_districtEdit = false;
		EditDistrictsModeChanged();
	}
	
	private void EditDistrictsModeChanged()
	{
		if (m_districtEdit)
		{
			m_data.GenerateEditVisuals();
			KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.DistrictEdit);
			//var sw = System.Diagnostics.Stopwatch.StartNew();
			//Vector3 v = Vector3.one * 99999; for (int i = 0; i < 1000*1000; ++i) foreach (var d in m_data.m_districtPolygons) d.PointInside(v);
			//sw.Stop(); Debug.LogError($"1m tests in {sw.ElapsedMilliseconds}ms");
		}
		else
		{
			m_data.ClearEditVisuals();
			KeyboardShortcutManager.Me.PopShortcuts();
		}
		m_data.ForceUI();
		m_data.Render();
		UIManager.Me.m_districtEditUI.gameObject.SetActive(m_districtEdit);
	}

	private static DebugConsole.Command s_districtAnimateCmd = new("districtanim", _s => Me.GetDistrictByID(_s).SetBlendLevel(0));

	GameObject m_nearestPointDebugObj;
	private void UpdateNearestPointDebug()
	{
		if (m_nearestPointDebugObj == null)
		{
			m_nearestPointDebugObj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
			Destroy(m_nearestPointDebugObj.GetComponent<Collider>());
		}
		if (Physics.Raycast(Camera.main.ScreenPointToRay(Input.mousePosition), out var hit, 1000, GameManager.c_layerTerrainBit))
			m_nearestPointDebugObj.transform.position = NearestPointInOwnedDistrict(hit.point).GroundPosition(.5f);
	}
	
#if UNITY_EDITOR
	void Update()
	{ 
		if (m_districtEdit)
		{
			m_data.UpdateEdit();
		}
		//UpdateNearestPointDebug();
	}

	void OnDrawGizmos()
	{
		foreach (var poly in m_data.m_districtPolygons) poly.DrawGizmos();
	}
#endif

	public void SetDistrictMapIntensity(float _intensity, bool _showingMap) => m_data.SetDistrictMapIntensity(_intensity, _showingMap);
	
	void LateUpdate()
	{
		m_data.UpdateAnimation();
	}

	public Vector3 NearestPointInOwnedDistrict(Vector3 _pos, float _distanceInside = .5f)
	{
		var res = m_data.NearestPointInOwnedDistrict(_pos, _distanceInside);
		return res;
	}
	
	public DistrictPolygon GetDistrictByID(string _id)
	{
		return m_data.DistrictFromID(_id);
	}

	public string GetDistrictName(Vector3 point)
	{
		var (name, _) = GetDistrictAtPoint(point);
		return name;
	}

	private Dictionary<long, DistrictPolygon> m_districtLookup = new();
	public bool IsWithinDistrictBoundsCached(Vector3 _point)
	{
		var key = (long)(_point.x * 16) + ((long)(_point.z * 16) << 32);
		if (m_districtLookup.TryGetValue(key, out var district) == false)
		{
			district = m_data.DistrictAtPoint(_point);
			m_districtLookup[key] = district;
		}
		return (district != null && district.IsUnlocked) || m_data.FindLocalUnlockAtPoint(_point);
	}

	public bool IsWithinDistrictBounds(Vector3 point, bool _useCache = false)
	{
		if (_useCache) return IsWithinDistrictBoundsCached(point);
		foreach (var d in m_data.m_districtPolygons)
		{
			if (d.IsUnlocked == false) continue;
			if (d.PointInside(point))
				return true;
		}
		if (m_data.FindLocalUnlockAtPoint(point)) return true;
		return false;
	}

	public void Load(SaveContainers.SaveCountryside _value)
	{
		m_data.LoadUnlocks(_value.m_districtData);
		m_data.Render();
	}

	public void Save(ref SaveContainers.SaveCountryside _value)
	{
		_value.m_districtData = m_data.SaveUnlocks();
	}

	public bool IsDistrictUnlocked(string _name)
	{
		var res = m_data.DistrictFromID(_name);
		return res?.IsUnlocked ?? false;
	}
	
	public void AddLocalUnlock(Vector3 _pos, float _radius)
	{
		m_data.AddLocalUnlock(_pos, _radius);
		this.DoPostLoad(NGManager.Me.UpdateOutOfRangeCommanders);
	}
	public void RemoveLocalUnlock(Vector3 _pos) => m_data.RemoveLocalUnlock(_pos);
	
	public (string name, bool isUnlocked) GetDistrictAtPoint(Vector3 _position, bool _unlockIgnoresLocals = false)
	{
		var res = m_data.DistrictAtPoint(_position);
		if (res == null) return ("", false);
		var unlocked = res.IsUnlocked;
		if (unlocked == false && _unlockIgnoresLocals == false)
			unlocked = m_data.FindLocalUnlockAtPoint(_position);
		return (res.m_name, unlocked);
	}

	public static void CleanUp()
	{
	}

	public void UnlockDistrictByID(string _id)
	{
		_id = m_data.SanitiseID(_id);
		m_data.Unlock(_id, true);
		NGManager.Me.UpdateOutOfRangeCommanders();
	}

	public void LockDistrictByID(string _id)
	{
		_id = m_data.SanitiseID(_id);
		m_data.Unlock(_id, false);
		NGManager.Me.UpdateOutOfRangeCommanders();
	}

	public List<string> GetUnlockedDistrictIDs() => m_data.UnlockedIDs;

	public void ToggleMask(bool enable)
	{
		var tex = enable ? m_texture : Texture2D.whiteTexture;
		Shader.SetGlobalTexture("_OwnLandMask", tex);
		Shader.SetGlobalTexture("_OwnLandMaskVS", tex);
	}

	public void DoDoubleClick() => m_data.DoDoubleClick();
	
	static DebugConsole.Command s_unlockdistrict = new("unlockdistrict", _s => Me.UnlockDistrictByID(_s));
	static DebugConsole.Command s_lockdistrict = new("lockdistrict", _s => Me.LockDistrictByID(_s));
}
