using UnityEngine;

public class PickupAndThrowBehaviour : MonoBehaviour, IPickupBehaviour
{
	private bool m_isHeld = false;
	public bool Held => m_isHeld;
	public bool AllowPutDown => false;
	public float PutDownReleaseHeight => 0;

	private static bool s_fullThrow = true, s_fullThrowAutoTarget = true;
	private static DebugConsole.Command s_fullThrowCmd = new ("throwbarrels", _s => Utility.SetOrToggle(ref s_fullThrow, _s));
	private static DebugConsole.Command s_fullThrowAutoTargetCmd = new("autotarget", _s => Utility.SetOrToggle(ref s_fullThrowAutoTarget, _s));
	
	const float c_reticuleRaiseFraction = .15f;
	public void OnPickup()
	{
		m_isHeld = true;
		if (s_fullThrow == false)
			UIManager.Me.ShowReticule(true, c_reticuleRaiseFraction);
	}

	static float c_arcTimeMultiplier => NGManager.Me.m_throwArcTimeMultiplier;
	static float c_screenSpaceAngle => NGManager.Me.m_throwScreenSpaceAngle;
	static float c_pullBackFraction => NGManager.Me.m_throwPullBackFraction;
	
	public bool GetCurrentTargetVelocity(ref Vector3 _vel, bool _includeVelocityExtrapolation)
	{
		bool res = false;
		Transform chrToHit = null;
		_vel.y = _vel.xzMagnitude() * .5f;
		if (s_fullThrowAutoTarget && _vel.sqrMagnitude > .01f * .01f)
		{
			float arcTime = _vel.y * -2 / Physics.gravity.y; // the time when the Y value is the same as at start, the total arc time on a flat plane
			var speed = _vel.xzMagnitude();
			var requiredDistance = speed * arcTime;
			var indesc = PlayerHandManager.Indescriminate;
			var dir = _vel.normalized;
			var maxDistance = requiredDistance * c_arcTimeMultiplier;
			const string c_debugLabel = null;
			var chrs = PlayerHandManager.GetNearestObjects(indesc, true, indesc, indesc, indesc, indesc, false, 1, maxDistance * (1 + c_pullBackFraction), transform.position - dir * (maxDistance * c_pullBackFraction), null, dir, false, _coneAngleScreenSpace: c_screenSpaceAngle, _onlyInFrustum:true, _debugLabel: c_debugLabel);
			if (chrs.Count > 0)
			{
				var bestDiffSqrd = 1e23f;
				for (int i = 0; i < chrs.Count; ++i)
				{
					var chr = chrs[i];
					var distanceSqrd = (chr.transform.position - transform.position).xzSqrMagnitude();
					var diffSqrd = distanceSqrd - requiredDistance * requiredDistance;
					if (diffSqrd < bestDiffSqrd)
					{
						bestDiffSqrd = diffSqrd;
						chrToHit = chr;
					}
				}
			}
		}
		//Debug.LogError($"Throw with velocity {_vel} targets {chrToHit?.name}");
		if (chrToHit != null)
		{
			var chrBody = chrToHit.GetComponentInChildren<Rigidbody>();
			if (chrBody != null)
			{
				float timeToHit;
				(_vel, timeToHit) = transform.GetVelocityToMoveWithXZSpeed(chrToHit.position, _vel.xzMagnitude());
				if (_includeVelocityExtrapolation)
				{
					var extrapolationPos = chrToHit.position + chrBody.linearVelocity.GetXZ() * timeToHit;
					(_vel, _) = transform.GetVelocityToMoveWithXZSpeed(extrapolationPos, _vel.xzMagnitude());
					//Debug.LogError($"  Final velocity {_vel} time to hit {timeToHit} - extrap pos {extrapolationPos}");
				}
				res = true;
			}
		}
		return res;
	}

	public void OnDrop(Vector3 _pos, GameObject _target, GameObject _source, Vector3 _smoothedMouseDrag, bool _undo, SpecialHandlingAction _action)
	{
		m_isHeld = false;
		var body = gameObject.GetComponent<Rigidbody>();
		if (body == null) body = gameObject.AddComponent<Rigidbody>();
		body.position = transform.position;
		body.isKinematic = false;
		if (s_fullThrow)
		{
			var camXform = Camera.main.transform;
			_smoothedMouseDrag *= NGManager.Me.m_decorationThrowVelocityScaler;
			var vel = camXform.forward * _smoothedMouseDrag.y + camXform.right * _smoothedMouseDrag.x;
			GetCurrentTargetVelocity(ref vel, true);
			body.linearVelocity = vel;
			body.position += vel * Time.deltaTime; // move immediately to avoid looking like it stops for a frame
		}
		else
		{
			GameManager.Me.RaycastAtPoint(Utility.mousePosition + Vector3.up * (Screen.height * c_reticuleRaiseFraction), gameObject, PlayerHandManager.Me.Hand.gameObject, out var hit, -1); //GameManager.c_layerTerrainBit);
			const float c_throwSpeed = 30;
			body.SetVelocityToMoveWithSpeed(hit.point, c_throwSpeed, (_r) => { });
			UIManager.Me.ShowReticule(false);
		}
		AfterTouch.AddAfterTouch(body.gameObject, 3);
	}

	public GameObject GetBestTarget(int _inputId, GameObject _obj, Vector3 _pos, out SpecialHandlingAction _action)
	{
		_action = null;
		return null;
	}

	public GameObject GetSource()
	{
		return gameObject;
	}

	public Color BezierColour(GameObject _s, GameObject _t)
	{
		return Color.red;
	}

	public bool DropNoBezier()
	{
		return false;
	}
}

