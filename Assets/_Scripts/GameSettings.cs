using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering.HighDefinition;
using UnityEngine.Rendering.Universal;
using ShadowQuality = UnityEngine.ShadowQuality;

public static class GameSettings
{
	//========================================================
	private const string c_gammaPref = "Gamma";
	private const string c_powerPref = "Power";
	private const string c_aaPref = "AA";
	private const string c_texResPref = "TexRes";
	private const string c_vsyncPref = "VSync";
	private const string c_anisoPref = "Aniso";
	private const string c_ssaoPref = "SSAO";
	private const string c_shadowResPref = "ShadowResolution";
	private const string c_envPref = "Environment";
	private const string c_qualityPref = "Quality";
	private const string c_physicsPref = "Physics";
	private const string c_treeQualityPref = "TreeQuality";
	private const string c_dofLevelPref = "DOF";
	private const string c_HUDScalePref = "HUDScale";
	private const string c_GUIScalePref = "GUIScale";

	public const int DoNotSet = -1;
	public const int ReadFromStorage = 999;
	//========================================================
	static private float s_gammaLevel = 1;
	static private int s_powerLevel = 100;
	static private int s_antiAliasing = 3;
	static private int s_texResolution = 3;
	static private int s_vsync = 1;
	static private int s_aniso = 1;
	static private int s_ssao = 1;
	static private int s_shadowResolution = 3;
	static private int s_environment = 2;
	static private int s_quality = 1;
	static private int s_physics = 1;
	static private int s_treeQuality = 3;
	static private int s_depthOfFieldLevel = 3;
	static private float s_HUDScale = 1;
	static private float s_GUIScale = 1;
    //========================================================

    public static int PowerLevel => s_powerLevel;
    
    static public float CurrentGUIScale => s_GUIScale;
    
    static public ScriptableRendererFeature m_SSAOFeature;
    
    static private UnityEngine.Rendering.Universal.UniversalAdditionalLightData GetLightingData()
	{
		return GameManager.Me.MainLight.GetComponent<UnityEngine.Rendering.Universal.UniversalAdditionalLightData>();
	}

	static private UnityEngine.Rendering.Universal.UniversalAdditionalCameraData GetCameraData()
	{
		return GameManager.Me.m_camera.GetComponent<UnityEngine.Rendering.Universal.UniversalAdditionalCameraData>();
	}

	static private UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData GetHDCameraData()
	{
		return GameManager.Me.m_camera.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData>();
	}

	static private float s_baseShadowDistance = 700;
	static private int s_lastSetAntiAliasingQuality = -1;
	static private void SetAntiAliasingQuality(int _level)
	{
		if (s_lastSetAntiAliasingQuality == _level) return;
		s_lastSetAntiAliasingQuality = _level;
		
		if (SRPOptions.IsURP == false)
		{
			var hd = GetHDCameraData();
			if (_level == 0)
			{
				hd.antialiasing = HDAdditionalCameraData.AntialiasingMode.None;
			}
			else
			{
				hd.antialiasing = HDAdditionalCameraData.AntialiasingMode.SubpixelMorphologicalAntiAliasing;
				hd.SMAAQuality = (HDAdditionalCameraData.SMAAQualityLevel) (_level - 1);
			}
			return;
		}
		
		var data = GetCameraData();
		
#if true
		data.antialiasing = AntialiasingMode.None;
		_level = Mathf.Clamp(_level, 0, 3);
		GameManager.Me.m_camera.allowMSAA = _level > 0;
		var asset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
		if (asset != null)
			asset.msaaSampleCount = 1 << _level;
#else
		data.antialiasing = _level == 0 ? AntialiasingMode.None : AntialiasingMode.SubpixelMorphologicalAntiAliasing;
		if (_level > 0)
			data.antialiasingQuality = (AntialiasingQuality)(_level - 1);
#endif
	}
	static private bool s_lastSetSSAO = true;
	static private void SetSSAO(bool _enable)
	{
		if (m_SSAOFeature == null) return;
		
		if (s_lastSetSSAO == _enable) return;
		s_lastSetSSAO = _enable;

		if (SRPOptions.IsURP == false)
		{
			SRPOptions.HDSSAO.active = _enable;
			return;
		}
		m_SSAOFeature.SetActive(_enable);
		if (_enable) m_SSAOFeature.Create();
		else m_SSAOFeature.Dispose();
	}


	static private UnityEngine.Rendering.Universal.LiftGammaGain GetGamma()
	{
		var vols = UnityEngine.Rendering.VolumeManager.instance.GetVolumes(1);
		UnityEngine.Rendering.Universal.LiftGammaGain lgg = null;
		for (int i = 0; i < vols.Length; ++i)
		{
			if (vols[i].isGlobal) //was getting looking at local volumes missing the gamma component and returning null
				vols[i].profile?.TryGet(out lgg);
		}
		return lgg;
	}

	static private UnityEngine.Rendering.Universal.ScriptableRendererFeature GetAmbientOcclusion()
	{
		var asset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
		//var rnd = asset.GetRenderer(0);
		// TODOHDRP
		//foreach (var f in asset.LoadBuiltinRendererData().rendererFeatures)
		//{
		//	if (f.name == "SSAO") return f;
		//}
		return null;
	}

	public static class SRPOptions
	{
		private static bool s_isURP;
		private static UnityEngine.Rendering.RenderPipelineAsset s_currentRenderPipeline;
		private static System.Type s_universalRenderPipelineAssetType;
		private static System.Reflection.FieldInfo s_mainLightShadowmapResolutionFieldInfo;
		private static System.Reflection.FieldInfo s_mainLightCastShadowsFieldInfo;

		static SRPOptions()
		{
			Init();
		}
		static void Init()
		{
			var pipeline = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline;
			if (s_currentRenderPipeline == pipeline) return;
			s_isURP = pipeline is UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset;
			s_currentRenderPipeline = pipeline;
			if (s_isURP)
			{
				s_universalRenderPipelineAssetType = (pipeline as UniversalRenderPipelineAsset).GetType();
				s_mainLightShadowmapResolutionFieldInfo = s_universalRenderPipelineAssetType.GetField("m_MainLightShadowmapResolution", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
				s_mainLightCastShadowsFieldInfo = s_universalRenderPipelineAssetType.GetField("m_MainLightShadowsSupported", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
			}
		}
		
		private static UnityEngine.Rendering.Volume s_volume = null;

		private static void InitHDRPVolume()
		{
			if (s_volume == null)
			{
				s_volume = CameraRenderSettings.Me.m_mainVolume;
			}
		}
		
		public static UniversalRenderPipelineAsset URPAsset 
		{
			get
			{
				Init();
				if (!s_isURP) return null;
				return UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
			}
		}

		public static UnityEngine.Rendering.HighDefinition.HDShadowSettings HDShadows => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.HDShadowSettings>();
		public static UnityEngine.Rendering.HighDefinition.DepthOfField HDDepthOfField => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.DepthOfField>();
		public static UnityEngine.Rendering.HighDefinition.ScreenSpaceAmbientOcclusion HDSSAO => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.ScreenSpaceAmbientOcclusion>();
		public static UnityEngine.Rendering.HighDefinition.LiftGammaGain HDLiftGammaGain => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.LiftGammaGain>();
		public static UnityEngine.Rendering.HighDefinition.VisualEnvironment HDVisualEnvironment => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.VisualEnvironment>();
		public static UnityEngine.Rendering.HighDefinition.GradientSky HDGradientSky => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.GradientSky>();
		public static UnityEngine.Rendering.HighDefinition.ColorAdjustments HDColorAdjustments => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.ColorAdjustments>();
		public static UnityEngine.Rendering.HighDefinition.Exposure HDExposure => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.Exposure>();
		public static UnityEngine.Rendering.HighDefinition.CloudLayer HDCloudLayer => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.CloudLayer>();
		public static UnityEngine.Rendering.HighDefinition.Fog HDFog => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.Fog>();
		public static UnityEngine.Rendering.HighDefinition.Bloom HDBloom => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.Bloom>();
		public static UnityEngine.Rendering.HighDefinition.ShadowsMidtonesHighlights HDShadowsMidtonesHighlights => HDVolumeComponent<UnityEngine.Rendering.HighDefinition.ShadowsMidtonesHighlights>();

		public static T HDVolumeComponent<T>() where T : UnityEngine.Rendering.VolumeComponent
		{
			Init();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			if (s_isURP) return null;
			InitHDRPVolume();
			if (!s_volume.profile.TryGet<T>(out var fx))
				fx = s_volume.profile.Add<T>(true);
			return fx;
#else
			return null;
#endif
		}
		
		public static bool IsURP
		{
			get
			{
				Init();
				return s_isURP;
			}
		}
   
		public static UnityEngine.Rendering.Universal.ShadowResolution MainLightShadowResolution
		{
			get
			{
				Init();
				if (s_isURP) return (UnityEngine.Rendering.Universal.ShadowResolution) s_mainLightShadowmapResolutionFieldInfo.GetValue(UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline);
				return UnityEngine.Rendering.Universal.ShadowResolution._1024;
			}
			set
			{
				Init();
				if (s_isURP) s_mainLightShadowmapResolutionFieldInfo.SetValue(UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline, value);
			}
		}
		public static bool MainLightCastShadows
		{
			get
			{
				Init();
				if (s_isURP) return (bool) s_mainLightCastShadowsFieldInfo.GetValue(UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline);
				return true;
			}
			set
			{
				Init();
				if (s_isURP) s_mainLightCastShadowsFieldInfo.SetValue(UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline, value);
			}
		}
	}
	static private void SetShadowQuality(int _res)
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		bool castShadows = _res >= 256;
		if (castShadows)
		{
			int cascades = Mathf.Min(4, _res / 256); // 1, 2 or 4 cascades
			float distanceReduce = Mathf.Min(1, _res * (1.0f / 1024.0f));
			if (_res == 256) _res = 512; // 256 is too small, instead we further reduce the cast distance
			if (SRPOptions.IsURP)
				SRPOptions.MainLightShadowResolution = (UnityEngine.Rendering.Universal.ShadowResolution)_res;
			else
			{
				SRPOptions.HDShadows.cascadeShadowSplitCount.Override(cascades);
				Marcos_Procedural_Sky.Me.SetShadowResolution(_res);
			}
			CameraRenderSettings.Me.m_shadowCastDistanceMax = 700 * distanceReduce;
		}
		SRPOptions.MainLightCastShadows = castShadows;

		float pointShadowScale = _res * (1.0f / 2048.0f);
		pointShadowScale *= pointShadowScale; // shrink fast for low levels
		DisableLightWithDistance.SetPointLightShadowDistanceMultiplier(pointShadowScale);
#endif	
	}

	static DebugConsole.Command s_qualityLevel = new ("quality", _s => SetQualityLevel(int.Parse(_s))); 
	static private void SetQualityLevel(int _level)
	{
		if (QualitySettings.GetQualityLevel() == _level) return;
		QualitySettings.SetQualityLevel(_level);
	}

	static private void SetEnvironment(int _env)
	{
		bool grass = _env >= 2;
		bool wildlife = _env >= 1;
		TerrainPopulation.ShowGrass = grass;
		ParticlesMeshVelocity.GlobalDisable = !wildlife;
	}

	static private void SetPhysicsLevel(int _level)
	{
		if (_level == 0) _level = 1; // GL - 1/25 breaks things
		Time.fixedDeltaTime = 1.0f / (25 << _level);
	}

	static private void SetDepthOfFieldLevel(int _level)
	{
		DepthOfFieldController.Level = _level;
	}

	static private void SetHUDScale(float _scale)
	{
		if (NGManager.Me == null) return;
		
		foreach (var t in NGManager.Me.m_HUDScaleTransforms)
		{
			if(t != null)
				t.Scale(_scale);
		}
	}
	static private void SetGUIScale(float _scale)
	{
		if (NGManager.Me == null) return;
		
		foreach (var t in NGManager.Me.m_GUIScaleTransforms)
		{
			if(t != null)
				t.SetScale(_scale);
//			t.m_rectTransform.anchoredPosition = t.m_startAnchoredPosition * _scale;
//			t.m_rectTransform.localScale = new Vector3(_scale, _scale, _scale);
		}
		var activeInfos =GameObject.FindObjectsOfType<GUIInfoBase>();
		foreach (var info in activeInfos)
		{
			info.SetScale(_scale);
		}

		
	}


	//========================================================
	static public void LoadSettings()
	{
		GameManager.Me.StartCoroutine(Co_LoadSettings());
	}
	private static IEnumerator Co_LoadSettings()
	{
		// TODOHDRP
		yield return null;
		//while (GetAmbientOcclusion() == null) yield return null;
		
		Gamma(ReadFromStorage);
		Power(ReadFromStorage);
		AntiAliasing(ReadFromStorage);
		TextureResolution(ReadFromStorage);
		VSync(ReadFromStorage);
		SSAO(ReadFromStorage);
		ShadowResolution(ReadFromStorage);
		Environment(ReadFromStorage);
		Quality(ReadFromStorage);
		NGManager.Me?.SetupHUD_GUIScale();
		HUDScale(ReadFromStorage);
		GUIScale(ReadFromStorage);
		Physics(ReadFromStorage);
		DOFLevel(ReadFromStorage);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		TreeQuality(ReadFromStorage);
#endif //#!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	}

	private static DebugConsole.Command s_minsettings = new DebugConsole.Command("minsettings", _s => SetMinimums());
	public static void SetMinimums()
	{
		SettingsUIController.s_saveEnabled = false;
		AntiAliasing(0);
		TextureResolution(0);
		SSAO(0);
		ShadowResolution(0);
		Environment(0);
		Quality(0);
		AnisotropicFiltering(0);
		TerrainPopulation.ShowTrees = false;
		Physics(0);
		DOFLevel(0);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		TreeQuality(0);
#endif //#!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	}
	
	//========================================================
	private static DebugConsole.Command s_setpower = new DebugConsole.Command("setpower", _s => Power(int.Parse(_s)));
	private static DebugConsole.Command s_setgamma = new DebugConsole.Command("setgamma", _s => Gamma(float.Parse(_s)));
	private static DebugConsole.Command s_setaa = new DebugConsole.Command("setaa", _s => AntiAliasing(int.Parse(_s)));
	private static DebugConsole.Command s_settexres = new DebugConsole.Command("settexres", _s => TextureResolution(int.Parse(_s)));
	private static DebugConsole.Command s_setvysnc = new DebugConsole.Command("setvsync", _s => VSync(int.Parse(_s)));
	
	//========================================================
	static public float Gamma(float _set = 1e23f)
	{
		if (_set < 1e22f)
		{
			if (_set >= ReadFromStorage) _set = MPlayerPrefs.GetFloat(c_gammaPref, 1);
			_set = Mathf.Clamp(_set, .5f, 2f);
			if (SRPOptions.IsURP)
			{
				var g = GetGamma();
				if (g != null)
				{
					g.gamma.overrideState = true;
					g.gamma.value = new Vector4(1, 1, 1, _set - 1.25f);
				}
			}
			else
			{
				var lgg = SRPOptions.HDLiftGammaGain;
				if (lgg != null)
				{
					lgg.active = true;
					lgg.gamma.overrideState = true;
					lgg.gamma.value = new Vector4(1, 1, 1, _set - 1);
				}
			}
			MPlayerPrefs.SetFloat(c_gammaPref, _set);
			s_gammaLevel = _set;
		}
		return s_gammaLevel;
	}
	static public int Power(int _set = -1)
	{
		if (_set != -1)
		{
			if (_set >= ReadFromStorage) _set = MPlayerPrefs.GetInt(c_powerPref, 100);
			_set = Mathf.Clamp(_set, 0, 100);
			MPlayerPrefs.SetInt(c_powerPref, _set);
			s_powerLevel = _set;
		}
		return s_powerLevel;
	}	
	static public int AntiAliasing(int _set = DoNotSet)
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		return Update(_set, 0, 3, 3, ref s_antiAliasing, c_aaPref, _v => SetAntiAliasingQuality(_v));
#else
		return 0;
#endif
	}
	static public int TextureResolution(int _set = DoNotSet)
	{
		return Update(_set, 0, 3, 3, ref s_texResolution, c_texResPref, _v => Utility.GlobalTextureLimit = 3 - _v);
	}
	static private DebugConsole.Command s_vsynccmd = new ("vsync", _s =>
	{
		QualitySettings.vSyncCount = int.Parse(_s);
	});
	static public int VSync(int _set = DoNotSet)
	{
		return Update(_set, 0, 4, 1, ref s_vsync, c_vsyncPref, _v => QualitySettings.vSyncCount = _v);
	}
	static public int AnisotropicFiltering(int _set = DoNotSet)
	{
		return Update(_set, 0, 1, 1, ref s_aniso, c_anisoPref, _v => QualitySettings.anisotropicFiltering = _v == 0 ? UnityEngine.AnisotropicFiltering.Disable : UnityEngine.AnisotropicFiltering.ForceEnable);
	}
	static public int SSAO(int _set = DoNotSet)
	{
		return Update(_set, 0, 1, 1, ref s_ssao, c_ssaoPref, _v => SetSSAO(_v != 0));
	}
	static public int ShadowResolution(int _set = DoNotSet)
	{
		return Update(_set, 0, 3, 3, ref s_shadowResolution, c_shadowResPref, _v => SetShadowQuality(2048 / (1 << (3 - _v))));
	}
	static public int Environment(int _set = DoNotSet)
	{
		return Update(_set, 0, 2, 2, ref s_environment, c_envPref, _v => SetEnvironment(_v));
	}

	static public int Quality(int _set = DoNotSet)
	{
		return Update(_set, 0, 1, 1, ref s_quality, c_qualityPref, _v => SetQualityLevel(_v));
	}

	static public int Physics(int _set = DoNotSet)
	{
		return Update(_set, 0, 2, 1, ref s_physics, c_physicsPref, _v => SetPhysicsLevel(_v));
	}

	static public int TreeQuality(int _set = DoNotSet)
	{
		return Update(_set, 0, 4, 3, ref s_treeQuality, c_treeQualityPref, _v => TerrainPopulation.Me.AdjustTreeQualityFromSetting(_v));
	}

	static public int DOFLevel(int _set = DoNotSet)
	{
		return Update(_set, 0, 4, 3, ref s_depthOfFieldLevel, c_dofLevelPref, _v => SetDepthOfFieldLevel(_v));
	}

	static public float HUDScale(float _set = DoNotSet)
	{
		var defaultValue = 0.75f;
#if UNITY_IOS || UNITY_ANDROID
		if (Utility.IsTablet == false) defaultValue = 1.25f; 
#endif
		return Update(_set, .25f, 2f, defaultValue, ref s_HUDScale, c_HUDScalePref, _v => SetHUDScale(_v));
	}
	
	static public float GUIScale(float _set = DoNotSet)
	{
		var defaultValue = 0.75f;
#if UNITY_IOS || UNITY_ANDROID
		if (Utility.IsTablet == false) defaultValue = 1.25f; 
#endif
		return Update(_set, .25f, 2f, defaultValue, ref s_GUIScale, c_GUIScalePref, _v => SetGUIScale(_v));
	}

	private static DebugConsole.Command s_delallkeys = new DebugConsole.Command("deleteallkeys", _s =>
	{
		if (_s == "DoIt") MPlayerPrefs.DeleteAll();
	});

	static private int Update(int _set, int _min, int _max, int _default, ref int _backing, string _pref, System.Action<int> _cb)
	{
		if (_set >= 0)
		{
			if (_set >= ReadFromStorage) _set = MPlayerPrefs.GetInt(_pref, _default);
			_set = Mathf.Clamp(_set, _min, _max);
			_cb(_set);
			MPlayerPrefs.SetInt(_pref, _set);
			_backing = _set;
		}
		return _backing;
	}
	static private float Update(float _set, float _min, float _max, float _default, ref float _backing, string _pref, System.Action<float> _cb)
	{
		if (_set >= 0)
		{
			if (_set >= ReadFromStorage) _set = MPlayerPrefs.GetFloat(_pref, _default);
			_set = Mathf.Clamp(_set, _min, _max);
			_cb(_set);
			MPlayerPrefs.SetFloat(_pref, _set);
			_backing = _set;
		}
		return _backing;
	}
}



// From this thread: https: //forum.unity.com/threads/turn-urp-ssao-on-and-off-at-runtime.1066961/
public static class URPUtility
{
    static Dictionary<System.Type, Dictionary<string, FieldInfo>> s_FieldLUT = new Dictionary<System.Type, Dictionary<string, FieldInfo>>();
    static Dictionary<System.Type, string> s_TypeNameLUT = new Dictionary<System.Type, string>();
 
    public static ScriptableRendererFeature ssao
    {
        get
        {
            const string kTypeName = "ScreenSpaceAmbientOcclusion";
            return GetRendererFeature(kTypeName);
        }
    }
 
    public static bool ssaoEnabled
    {
        get
        {
            var v = ssao;
            if (v != null)
                return v.isActive;
            return false;
        }
        set
        {
            var v = ssao;
            if (v != null)
                v.SetActive(value);
        }
    }
 
    public static float ssaoDirectLightingStrength
    {
        get => GetSettingsFloat(ssao, "m_Settings", "DirectLightingStrength");
        set => SetSettingsFloat(ssao, "m_Settings", "DirectLightingStrength", value);
    }
 
    public static float ssaoRadius
    {
        get => GetSettingsFloat(ssao, "m_Settings", "Radius");
        set => SetSettingsFloat(ssao, "m_Settings", "Radius", value);
    }
 
    public static float ssaoIntensity
    {
        get => GetSettingsFloat(ssao, "m_Settings", "Intensity");
        set => SetSettingsFloat(ssao, "m_Settings", "Intensity", value);
    }
 
    public static ScriptableRendererFeature decals
    {
        get
        {
            const string kTypeName = "DecalRendererFeature";
            return GetRendererFeature(kTypeName);
        }
    }
 
    public static float decalsMaxDrawDistance
    {
        get => GetSettingsFloat(decals, "m_Settings", "maxDrawDistance");
        set => SetSettingsFloat(decals, "m_Settings", "maxDrawDistance", value);
    }
 
    static float GetSettingsFloat(ScriptableRendererFeature feature, string settingsName, string memberName)
    {
        if (feature == null)
            return 0;
 
        var settingsMember = GetField(feature.GetType(), settingsName);
        var settings = settingsMember.GetValue(feature);
 
        var directLightingStrengthMember = GetField(settings.GetType(), memberName);
        return (float)directLightingStrengthMember.GetValue(settings);
    }
 
    static void SetSettingsFloat(ScriptableRendererFeature feature, string settingsName, string memberName, float value)
    {
        if (feature == null)
            return;
 
        var settingsMember = GetField(feature.GetType(), settingsName);
        var settings = settingsMember.GetValue(feature);
 
        var directLightingStrengthMember = GetField(settings.GetType(), memberName);
        directLightingStrengthMember.SetValue(settings, value);
    }
 
    static FieldInfo GetField(System.Type type, string fieldName)
    {
        if (!s_FieldLUT.TryGetValue(type, out var fields))
            s_FieldLUT[type] = fields = new Dictionary<string, FieldInfo>();
 
        if (!fields.TryGetValue(fieldName, out var field))
            fields[fieldName] = field = type.GetField(fieldName, BindingFlags.NonPublic | BindingFlags.Instance);
 
        return field;
    }
 
    public static ScriptableRendererFeature GetRendererFeature(string typeName)
    {
        var asset = UniversalRenderPipeline.asset;
        var type = asset.GetType();
        var fieldInfo = GetField(type, "m_RendererDataList");
        var renderDatas = (ScriptableRendererData[])fieldInfo.GetValue(asset);
        if (renderDatas == null)
            return null;
 
        foreach (var renderData in renderDatas)
        {
            foreach (var rendererFeature in renderData.rendererFeatures)
            {
                if (rendererFeature == null)
                    continue;
 
                var featureType = rendererFeature.GetType();
                if (!s_TypeNameLUT.TryGetValue(featureType, out var name))
                    s_TypeNameLUT[featureType] = name = featureType.Name;
 
                if (name == typeName)
                    return rendererFeature;
            }
        }
 
        return null;
    }
 
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterAssembliesLoaded)]
    static void RuntimeInitializeOnLoadMethod()
    {
        s_FieldLUT = new Dictionary<System.Type, Dictionary<string, FieldInfo>>();
        s_TypeNameLUT = new Dictionary<System.Type, string>();
    }
}
