using System;
using System.Collections.Generic;
using UnityEngine;

public class ToolTipHolder : MonoBehaviour
{
    public static Dictionary<ToolTipCategory, List<ToolTipHolder>> s_toolTips = new();
    
    public enum ToolTipCategory
    {
        None,
        DesignTable,
    }
    
    public enum ToolTipType 
    {
        None,
        InfoButton,
        DesignGauge,
        InfoPanel,
        UndoButton,
        RedoButton,
        DesignDetails,
        DesignParts,
        CardOnTable,
        PriceLabel,
    }
    
    public enum Direction
    {
        left,
        right,
        up,
        down,
    }
    
    public Dictionary<ToolTipType, string> s_typeToMessage = new()
    {
        { ToolTipType.InfoButton, "<LMB> Show Part\nDetails" },
        { ToolTipType.DesignGauge, "Quality Score" },
        { ToolTipType.InfoPanel, "<LMB> To Expand" },
        { ToolTipType.UndoButton, "<LMB> Undo" },
        { ToolTipType.RedoButton, "<LMB> Redo" },
        { ToolTipType.DesignDetails, "<LMB> To Expand" },
        { ToolTipType.DesignParts, "<RMB> For Info" },
        { ToolTipType.CardOnTable, "<LMB> For Order\nDetails" },
        { ToolTipType.PriceLabel, "Sales Price" },
    };
    
    public ToolTipCategory m_category;
    public ToolTipType m_toolTipType;
    public Direction m_direction;
    
    public string Text 
    {
        get 
        {
            s_typeToMessage.TryGetValue(m_toolTipType, out var result);
            return result;
        }
    }
    
    public void Awake()
    {
        if(m_category == ToolTipCategory.None)
            return;
        
        if(s_toolTips.TryGetValue(m_category, out var tips))
            tips.Add(this);
        else
            s_toolTips[m_category] = new List<ToolTipHolder>{ this };
    }

    public void OnDestroy()
    {
        s_toolTips.TryGetValue(m_category, out var tips);
        if(tips == null) return;
        tips.Remove(this);
    }

    public static void DisplayAll(ToolTipCategory _type)
    {
        s_toolTips.TryGetValue(_type, out var list);
        if(list == null)
            return;
        foreach(var item in list)
        {
            MAHelper.Create(item.m_toolTipType.ToString(), item.gameObject, "DesignTable", item.Text, 0, -1, 0, item.m_direction.ToString());
        }
    }
    
    public static void DestroyAll(ToolTipCategory _type)
    {
        s_toolTips.TryGetValue(_type, out var list);
        if(list == null)
            return;
        foreach(var item in list)
        {
            item.transform.DestroyChildren();
        }
    }
    
}
