using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public interface IPickupBehaviour {
	void OnPickup();
	void OnDrop(Vector3 _pos, GameObject _target, GameObject _source, Vector3 _smoothedMouseDrag, bool _undo, SpecialHandlingAction _action);
	GameObject GetBestTarget(int _inputId, GameObject _obj, Vector3 _pos, out SpecialHandlingAction _action);
	GameObject GetSource();
	Color BezierColour(GameObject _s, GameObject _t);
	bool DropNoBezier();
	bool AllowPutDown { get; }
	float PutDownReleaseHeight { get; }
	bool Held { get; }
}

public interface IOnClick {
	void OnClick(int _inputId, bool _longClick);
}

public class Pickup : DragBase {
	private GameObject m_bestTarget = null;
	public IPickupBehaviour Behaviour => m_behaviour;
	
	public AkEventHolder m_pickupAudio;
	
	public bool m_isGrabType = false;
	public bool m_lockedInPlace = false;
	public System.Action<Pickup> m_onDragDetected; //can be used as a simple external indicator that a drag attempt was made -> invoked even when item is locked in place.
	public virtual string m_handAnimationEvent => m_isGrabType ? "HoldGrab" : "Hold";
	
    public static bool AnyDragging => DragHolder.s_activeDrags.Count > 0 && !GameManager.Me.IsDesignTable;
	public static bool IsLongHold { get { return s_isLongHold; } }
	private static bool s_isLongHold = false;
	IPickupBehaviour m_behaviour;
	GameObject m_source; public GameObject Source => m_source;
	Vector3 m_positionAtDragStart;
	float m_lerpToFinalDragOrigin = 0;
	bool m_lerpToFinalDragOriginHeld = false;
	bool m_flick = false;
	float m_dragStartTime;

	override public bool SupportsLongPress => GetComponent<NGCommanderBase>() != null;
	
	override public bool IsLongPress => s_isLongHold;
	
	override public bool AcceptsClicks => true;
	override public bool UpdatesDuringClick => true;
	override public bool HasBezier => true;
	override public Color BezierColour => m_behaviour?.BezierColour(m_source, m_bestTarget) ?? Color.clear;
	override public BezierLine.ControlGeneration BezierShape => BezierLine.ControlGeneration.SmoothBoth;

	public static void SetMaterialAlphaBlend(Material mat, UnityEngine.Rendering.CompareFunction _fn)
	{
		mat.SetFloat("_SurfaceType", 1); // transparent
		mat.SetFloat("_BlendMode", 0); // alpha blending mode
		mat.SetFloat("_AlphaCutoffEnable", 0);
		mat.SetFloat("_ZWrite", 0);
		mat.SetFloat("_ZTest", (int)_fn);
		mat.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent + 10;
	}

	private static int s_throwIndicatorType = 2;
	private static DebugConsole.Command s_throwIndicatorTypeCmd = new ("throwindicator", _s => s_throwIndicatorType = int.Parse(_s));

	private LineRenderer m_throwIndicator;
	private LineRenderer m_throwIndicatorTargeted;
	private GameObject m_throwIndicatorTarget;
	private float m_throwIndicatorTime;
	private void StartThrowIndicator()
	{
		if (s_throwIndicatorType == 0) return;

		m_throwIndicatorTime = 0;
		
		if (s_throwIndicatorType == 1 || s_throwIndicatorType == 2)
		{
			m_throwIndicatorTarget = GameObject.CreatePrimitive(PrimitiveType.Quad);//Sphere);
			var titXform = m_throwIndicatorTarget.transform; 
			titXform.SetParent(transform);
			titXform.localScale = new Vector3(2 / transform.lossyScale.x, 2 / transform.lossyScale.y, 2 / transform.lossyScale.z);//new Vector3(1 / transform.lossyScale.x, 2 / transform.lossyScale.y, 2 / transform.lossyScale.z);
			var titRnd = m_throwIndicatorTarget.GetComponent<Renderer>(); 
			titRnd.material = new Material(GlobalData.Me.m_throwIndicatorTargetMaterial);
			titRnd.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
			var cll = m_throwIndicatorTarget.GetComponent<MeshCollider>();
			cll.convex = true; // stop error message about concave mesh colliders before we delete this
			cll.enabled = false;
			Destroy(cll);
			return;
		}

		var go = new GameObject("ThrowIndicator");
		go.transform.SetParent(transform);
		go.transform.localPosition = Vector3.zero;
		go.transform.localScale = Vector3.one;
		go.transform.localEulerAngles = Vector3.right * -90;
		m_throwIndicator = go.AddComponent<LineRenderer>();
		var mat = new Material(GlobalData.Me.m_throwIndicatorMaterial);
		m_throwIndicator.material = mat;
		m_throwIndicator.startWidth = 1f;
		m_throwIndicator.endWidth = 1f;
		m_throwIndicator.alignment = LineAlignment.TransformZ;
		m_throwIndicator.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;

		var goT = new GameObject("ThrowIndicator");
		goT.transform.SetParent(transform);
		goT.transform.localPosition = Vector3.zero;
		goT.transform.localScale = Vector3.one;
		goT.transform.localRotation = Quaternion.identity;
		m_throwIndicatorTargeted = goT.AddComponent<LineRenderer>();
		var mat2 = new Material(GlobalData.Me.m_throwIndicatorMaterial);
		mat2.SetColor("_Color", new Color(1, .3f, .1f, .6f));
		m_throwIndicatorTargeted.material = mat2;
		m_throwIndicatorTargeted.startWidth = 0f;
		m_throwIndicatorTargeted.endWidth = 0f;
	}

	void LateUpdate() => UpdateThrowIndicator();

	private void UpdateThrowIndicatorTarget(Vector3 _vel, Vector3 _velTargeted, bool _haveVelTargeted)
	{
		if (m_throwIndicatorTarget == null) return;
		

		const float c_blendInTime = .5f;
		var blendIn = Mathf.Clamp01(m_throwIndicatorTime / c_blendInTime);
		var colour = _haveVelTargeted ? new Color(1, .5f, .5f) : Color.white;
		colour *= blendIn * 1.4f;
		m_throwIndicatorTarget.GetComponent<MeshRenderer>().material.SetColor("_UnlitColor", colour);
		if (s_throwIndicatorType == 2 && _haveVelTargeted) _vel = _velTargeted;
		
		const float dt = .05f;
		var pos = transform.position;
		var acc = Physics.gravity;
		for (float t = 0; t < 3; t += dt)
		{
			var posAtT = pos + _vel * t + acc * (t * t * .5f);
			var posAtTGround = posAtT.GroundPosition();
			if (posAtT.y < posAtTGround.y)
			{
				var blendFactor = 1 - blendIn * .5f;
				var blendPos = Vector3.Lerp(m_throwIndicatorTarget.transform.position, posAtTGround, blendFactor);
				m_throwIndicatorTarget.transform.position = blendPos.GroundPosition(.5f);
				m_throwIndicatorTarget.transform.eulerAngles = Vector3.right * 90;//Vector3.forward * 90.0f;
				break;
			}
		}
	}

	private void UpdateThrowIndicator()
	{
		if (m_throwIndicator == null && m_throwIndicatorTarget == null) return;

		m_throwIndicatorTime += Time.deltaTime;
		
		var delta = SmoothedDragDelta;
		delta *= NGManager.Me.m_decorationThrowVelocityScaler;
		var camXform = Camera.main.transform;
		var vel = camXform.forward * delta.y + camXform.right * delta.x;
		var velTargeted = vel;
		bool haveVelTargeted = false;

		var beh = m_source?.GetComponent<PickupAndThrowBehaviour>();
		if (beh != null && beh.GetCurrentTargetVelocity(ref velTargeted, false))
			haveVelTargeted = true;

		UpdateThrowIndicatorTarget(vel, velTargeted, haveVelTargeted);

		if (m_throwIndicator == null) return;

		if (s_throwIndicatorType == -1)
		{
			m_throwIndicator.alignment = LineAlignment.View;
			m_throwIndicator.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.On;

			void FillThrowIndicator(Vector3 _vel, LineRenderer _line)
			{
				const float c_t = 2, c_dt = .02f;
				const int c_steps = (int) (c_t / c_dt);
				var positions = new Vector3[c_steps];
				var rootPos = transform.position;
				for (int i = 0; i < c_steps; ++i)
				{
					var t = i * c_dt;
					positions[i] = rootPos + _vel * t + Physics.gravity * (t * t * .5f);
				}
				_line.positionCount = c_steps;
				_line.SetPositions(positions);
			}

			FillThrowIndicator(vel, m_throwIndicator);

			if (haveVelTargeted)
			{
				FillThrowIndicator(velTargeted, m_throwIndicatorTargeted);
				m_throwIndicatorTargeted.startWidth = m_throwIndicatorTargeted.endWidth = 1.5f;
			}
			else
				m_throwIndicatorTargeted.startWidth = m_throwIndicatorTargeted.endWidth = m_throwIndicatorTargeted.startWidth * .9f;
		}
		else if (s_throwIndicatorType == 3)
		{
			vel = velTargeted;
			vel.y = 0;
			const float c_maxThrowIndicatorSpeed = 30;
			float colourLerp = 0;
			if (vel.sqrMagnitude > c_maxThrowIndicatorSpeed * c_maxThrowIndicatorSpeed)
			{
				var mag = vel.magnitude;
				colourLerp = Mathf.Clamp01((mag - c_maxThrowIndicatorSpeed) / 10);
				vel *= c_maxThrowIndicatorSpeed / mag;
			}
			m_throwIndicator.material.SetColor("_Color", Color.Lerp(GlobalData.Me.m_throwIndicatorMaterial.GetColor("_Color"), Color.red, colourLerp * .5f));
			m_throwIndicator.positionCount = 2;
			m_throwIndicator.SetPosition(0, transform.position);
			m_throwIndicator.SetPosition(1, transform.position + vel * .5f);
			m_throwIndicator.startWidth = m_throwIndicator.endWidth = vel.magnitude * .25f;
			if (m_throwIndicatorTargeted.positionCount > 0)
			{
				m_throwIndicatorTargeted.startWidth = m_throwIndicatorTargeted.endWidth = 0;
				m_throwIndicatorTargeted.positionCount = 0;
			}
		}
		else if (s_throwIndicatorType == 2 || s_throwIndicatorType == 1)
		{
		}
	}
	public void EndThrowIndicator()
	{
		if (m_throwIndicatorTarget != null)
		{
			Destroy(m_throwIndicatorTarget);
			m_throwIndicatorTarget = null;
		}
		if (m_throwIndicator == null) return;
		Destroy(m_throwIndicator.gameObject);
		Destroy(m_throwIndicatorTargeted.gameObject);
		m_throwIndicator = null;
		m_throwIndicatorTargeted = null;
	}

	override public Vector3 DragPlaneNormal => Vector3.Slerp(-GameManager.Me.m_camera.transform.forward, Vector3.up, NGManager.Me.m_dragPlaneUpness);
	public const float c_dragPlaneForwardPosition = 15f;
	private Vector3 m_dragPlaneHorizontalRaiseOrigin;
	private float m_dragPlaneInitialCameraLevel;
	
	private float m_dragRaise = 0;
	protected virtual float DragRaise => m_dragRaise;

	private static string s_pickupPlaneDebug = "";
	private static bool s_pickupPlaneDebugActive = false;
	private static DebugConsole.Command s_pickupPlaneDebugCmd = new ("pickupplanedebug", _s => Utility.SetOrToggle(ref s_pickupPlaneDebugActive, _s));
	public static bool PickupPlaneDebugActive => s_pickupPlaneDebugActive;
	public static string PickupPlaneDebug => s_pickupPlaneDebug;
	
	private void SetDragPlaneHorizontalRaiseOrigin(bool _isDragStart)
	{
		if (_isDragStart)
			m_dragPlaneInitialCameraLevel = Cam.transform.position.y;
		var heightAdjust = 0;//Mathf.Max(0, Cam.transform.position.y - m_dragPlaneInitialCameraLevel);
		var ray = Cam.RayAtMouse();
		
		// first raycast non-terrain directly
		float hitDistance = 1e23f;
		if (GameManager.Me.RaycastWithExclude(ray, PlayerHandManager.Me.Hand.gameObject, gameObject, out var hitObjects, ~(GameManager.c_layerIgnoreCameraBit | GameManager.c_layerTerrainBit), true))
		{
			hitDistance = hitObjects.distance;
			m_dragPlaneHorizontalRaiseOrigin = hitObjects.point;
			if (s_pickupPlaneDebugActive)
				s_pickupPlaneDebug = $"Hit {hitObjects.transform.name} {hitObjects.collider.GetType().Name} {hitObjects.collider.isTrigger} at {hitObjects.point}";
		}
		
		// now raycast terrain with raise
		var offset = Vector3.up * (NGManager.Me.m_dragPlaneRaiseHorizontalAmount + DragRaise);
		ray.origin -= offset;
		if (Physics.Raycast(ray, out var hit, 1e23f, GameManager.c_layerTerrainBit, QueryTriggerInteraction.Ignore))
		{
			const float c_terrainBias = .9f;
			var terrainPoint = hit.point + offset + Vector3.up * heightAdjust;
			if (hit.distance < hitDistance * c_terrainBias || terrainPoint.y > m_dragPlaneHorizontalRaiseOrigin.y)
			{
				m_dragPlaneHorizontalRaiseOrigin = terrainPoint;
				if (s_pickupPlaneDebugActive)
					s_pickupPlaneDebug = $"Hit {hit.transform.name} {hit.collider.GetType().Name} {hit.collider.isTrigger} at {hit.point} [dist {hit.distance} - was {hitDistance}]";
			}
		}
	}

	override public Vector3 DragPlaneOrigin
	{
		get {
			var originVertical = GameManager.Me.m_camera.transform.position + GameManager.Me.m_camera.transform.forward * c_dragPlaneForwardPosition;
			
			var originHorizontal = NGManager.Me.m_dragPlaneRaiseHorizontal ? 
				m_dragPlaneHorizontalRaiseOrigin : 
				GameManager.Me.m_camera.transform.position + GameManager.Me.m_camera.transform.forward * NGManager.Me.m_dragPlaneForwardHorizontal;
			var originHV = Vector3.Lerp(originVertical, originHorizontal, NGManager.Me.m_dragPlaneUpness);
			var originFinal = Vector3.Lerp(m_positionAtDragStart, originHV, m_lerpToFinalDragOrigin);
			return originFinal;
			///return Vector3.Lerp(m_positionAtDragStart, GameManager.Me.m_camera.transform.position + GameManager.Me.m_camera.transform.forward * Mathf.Lerp(c_dragPlaneForwardPosition, NGManager.Me.m_dragPlaneForwardHorizontal, NGManager.Me.m_dragPlaneUpness), m_lerpToFinalDragOrigin);
		}
	}

	void PrepareForUndrag() {
		//bool colour = true;
		//NGCommanderBase cb = gameObject.GetComponentInChildren<NGCommanderBase>();
		//if (cb != null)
		//	colour = cb.IsWithinOpenDistrict();
		//Utility.SetGreyable(gameObject, colour);
	}

	//========
	bool m_longPressInProgress = false, m_longPressChecked = false;
	NGCommanderBase m_longPressCommander;
	PointerEventData m_longPressEventData;
	bool LongPressCheck() {
		if (!m_longPressChecked) {
			m_longPressChecked = true;
			var ng = GetComponent<NGCommanderBase>();
			if (ng != null && ng.IsLongPressReceiver && ng.InOwnedDistrictState == NGCommanderBase.EInOwnedDistrictState.InOwnedDistrict) {
				m_longPressInProgress = true;
				m_haveConvertedToDrag = false;
				m_longPressCommander = ng;
				m_longPressEventData = new PointerEventData(EventSystem.current) {
					pointerCurrentRaycast = new RaycastResult() { gameObject = gameObject },
					position = InputPosition,
				};
				m_longPressCommander.OnBeginLongPress(m_longPressEventData);
				ConfirmDrag();
			}
		}
		return m_longPressInProgress;
	}

	bool CheckMouseRay()
    {
		var ray = GameManager.Me.m_camera.RayAtMouse();
		RaycastHit hit;
		if (Physics.Raycast(ray, out hit, 1000.0f))
		{
			var cb = hit.transform.GetComponentInParent<NGCommanderBase>();
			if (cb == m_longPressCommander)
				return true;
		}
		return false;
    }

	bool LongPressUpdate() {
		if (m_longPressInProgress) {
			s_isLongHold = true;
			if (DragThreshholdExceeded && m_longPressCommander.CanDragMeInLongPress())
				if (ConvertLongPressToDrag())
					return false;
			m_longPressEventData.position = InputPosition;
			m_longPressEventData.button = InputId;
			m_longPressCommander.OnUpdateLongPress(m_longPressEventData, TimeSinceClick - LongHoldTime);
			return true;
		}
		return false;
	}
	bool LongPressFinish() {
		if (m_longPressInProgress) {
			s_isLongHold = false;
			m_longPressCommander.OnEndLongPress(m_longPressEventData);
			m_longPressInProgress = false;
			m_longPressChecked = false;
			return true;
		}
		return false;
	}

	private int m_longPressFixCounter = 0;
	void Update()
	{
		// catch and fix-up a drag issue
		if (m_longPressInProgress)
		{
			if (!GameManager.GetMouseButton(0) && !GameManager.GetMouseButton(1))
			{
				++m_longPressFixCounter;
				if (m_longPressFixCounter > 5)
				{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
					Debug.LogError($"LongPress error caught and fixed");
#endif
					LongPressFinish();
				}
			}
		}
		else
		{
			m_longPressFixCounter = 0;
		}
	}
	
	//========

	public static bool IsMouseOverBuilding(NGCommanderBase _ngc, float _distance = 0)
	{
		if (_ngc == null) return false;
		var ngTransform = _ngc.transform;
		var ray = GameManager.Me.m_camera.RayAtMouse();
		RaycastHit[] hits;
		if (_distance * _distance > .001f * .001f)
			hits = Physics.SphereCastAll(ray, _distance, 1000);
		else
			hits = Physics.RaycastAll(ray, 1000);
		foreach (var hit in hits)
			if (hit.transform.IsChildOf(ngTransform))
				return true;
		return false;
	}

	private bool m_haveConvertedToDrag = false, m_convertToDragLatch = false;
	public bool ConvertLongPressToDrag() {
		if (m_haveConvertedToDrag) return false;
		var ng = GetComponent<MABuilding>();
		if (ng != null)
		{
			// only convert to a drag if we've dragged off the building by a certain amount
			if (IsMouseOverBuilding(ng, NGManager.Me.m_buildingDragDistanceToPickup))
			{
				m_convertToDragLatch = false;
				return false;
			}
			// now check that there's somewhere for the dragged content to go
			if(ng.GetBestStockDestination().Empty)
				return false;
		}
		if (m_convertToDragLatch) return false;
		m_haveConvertedToDrag = true;
		ForceDrag();
		return false; // GL - we now continue the long hold while we drag the item
        LongPressFinish();
        return true;
    }

    override public void OnDragStart()
	{
		BeginDragging();
	}


	private void DebugDragPlane()
	{
#if false//UNITY_EDITOR // note: to enable this in future the two => values in NGManager will need to be changed back to =
		var change = (Utility.GetKey(KeyCode.LeftBracket) ? -1.0f : 0.0f) + (Utility.GetKey(KeyCode.RightBracket) ? 1.0f : 0.0f);
		var changeForward = (Utility.GetKey(KeyCode.Comma) ? -1.0f : 0.0f) + (Utility.GetKey(KeyCode.Period) ? 1.0f : 0.0f);
		if (Utility.GetKeyDown(KeyCode.Slash)) NGManager.Me.m_dragPlaneRaiseHorizontal = !NGManager.Me.m_dragPlaneRaiseHorizontal; 
		if (change * change > .01f * .01f || changeForward * changeForward > .01f * .01f || Utility.GetKeyDown(KeyCode.Slash))
		{
			NGManager.Me.m_dragPlaneUpness = Mathf.Clamp01(NGManager.Me.m_dragPlaneUpness + change * Time.deltaTime);
			if (NGManager.Me.m_dragPlaneRaiseHorizontal)
				NGManager.Me.m_dragPlaneRaiseHorizontalAmount = Mathf.Clamp(NGManager.Me.m_dragPlaneRaiseHorizontalAmount + changeForward * 5 * Time.deltaTime, 1, 15);
			else
				NGManager.Me.m_dragPlaneForwardHorizontal = Mathf.Clamp(NGManager.Me.m_dragPlaneForwardHorizontal + changeForward * 10 * Time.deltaTime, 15, 45);
			if (NGManager.Me.m_dragPlaneRaiseHorizontal)
				Debug.LogError($"DEBUG: changed drag plane parameters to Upness:{NGManager.Me.m_dragPlaneUpness} Raise:{NGManager.Me.m_dragPlaneRaiseHorizontalAmount}");
			else
				Debug.LogError($"DEBUG: changed drag plane parameters to Upness:{NGManager.Me.m_dragPlaneUpness} Forward:{NGManager.Me.m_dragPlaneForwardHorizontal}");
		}
#endif
	}

	private static BuildingHighlight s_highlight;
	public static void UpdateBuildingHighlight(float _delayTimer = float.MaxValue)
	{
		s_highlight?.DestroyMe();
		s_highlight = null; 
	}
	public GameObject LastTarget => m_lastTarget;
	private GameObject m_lastTarget;
	private SpecialHandlingAction m_lastAction;
	private float m_heldOverTarget = 0;
	public bool m_dragHandledByPhysics = false;
	Vector3 m_lastMousePos;
	private Vector3 m_startMousePos;
	override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		m_smoothedDragFade = Mathf.Lerp(m_smoothedDragFade, 1, .05f);
		SetDragPlaneHorizontalRaiseOrigin(false);
		DebugDragPlane();
		if (!LongPressUpdate() && CouldBeClick && TimeSinceClick > LongHoldTime && IsDragging) {
			if (!LongPressCheck()) {
				EndDrag();
			}
		}
		if (m_dragActive)
		{
			const float c_maxFlickTime = .2f;
			if (Time.time > m_dragStartTime + c_maxFlickTime)
				m_flick = false;
			m_lastTarget = null;
			m_lastAction = null;
		}
		if (!CouldBeClick && m_source != null) 
		{
			var pos = RaycastMouse;
			if (m_lerpToFinalDragOriginHeld == false)
				m_lerpToFinalDragOrigin = Mathf.Lerp(m_lerpToFinalDragOrigin, 1, .1f);
			var pickup = m_source.GetComponent<Pickup>() ?? this;
			if (true)//pickup.m_dragHandledByPhysics)
				PlayerHandManager.Me.SetFingertipPosition(pos);
			else
				m_source.transform.position = pos.AboveGround(1);
			
			var mo = m_source.GetComponent<NGMovingObject>();
			if(mo != null && mo.CanReturn == false)
			{
				var fromOrigin = (Input.mousePosition - m_startMousePos);    
				var heading = (Input.mousePosition - m_lastMousePos);
				var dir = Vector3.Dot(fromOrigin.normalized, heading.normalized);
				if (dir < 0 && fromOrigin.magnitude > Screen.dpi * 0.5f)
				{
					mo.CanReturn = true;
				}
				m_lastMousePos = Input.mousePosition;
			}
			
			var target = m_dragActive ? m_behaviour?.GetBestTarget((int) InputId, m_source, m_source.transform.position, out m_lastAction) : m_lastTarget;
			m_lastTarget = target;
			TrackTarget(target);
			NGCommanderBase ngc = null;
			if (target == null) {
				DisableBezier();
			} else {
                if (target != m_bestTarget)
                {
                    HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.BezierChange);
                    m_bestTarget = target;
                }
                var targetPos = GetBestBezierPosition((int)InputId, m_source, target);
				var bezierDest = m_source.GetComponentInChildren<BezierAnchor>();
				var bezierPos = bezierDest == null ? pos : bezierDest.transform.position;

				EnableBezier(bezierPos, targetPos+Vector3.up*1);

				var ownerCommander = GetComponent<NGCommanderBase>();
				var targetNGC = target.GetComponent<NGCommanderBase>();
				bool isConvertedDragItem = ownerCommander != null && m_haveConvertedToDrag;
				bool autoDrop = isConvertedDragItem || (NGManager.Me.m_autoDropOnNormalDrags && (ownerCommander == null || ownerCommander != targetNGC));
				if (autoDrop)
				{
					if (IsMouseOverBuilding(targetNGC))
					{
						m_heldOverTarget += Time.deltaTime;
						if (m_heldOverTarget >= NGManager.Me.m_autoDropOnBuildingAfter)
						{
							DoDrop();
							DestroyBezier();
							m_source = null;
							m_convertToDragLatch = true;
							m_haveConvertedToDrag = false;
						}
					}
				}
				else
					m_heldOverTarget = Mathf.Max(0, m_heldOverTarget - Time.deltaTime); 
			}
			if (NGUnlocks.MultiPickup && NGManager.Me.m_enableSmartMultiPickup)
				m_source.GetComponent<ReactPickup>().AdjustQuantities(ngc);
		}
	}

	public static Vector3 GetBestBezierPosition(int _inputId, GameObject _source, GameObject _target)
	{
		var targetPos = _target.transform.position;
		
		var anchor = _target.GetComponentInChildren<BezierAnchor>();
		if (anchor != null)
		{
			_target = anchor.gameObject;
			targetPos = anchor.gameObject.transform.position;
		}
		
		var ngc = _target.GetComponent<NGCommanderBase>();
		if (ngc != null) targetPos = ngc.GetBestDropPosition(_source?.GetComponent<NGMovingObject>());
		return targetPos;
	}

	override public bool ShouldHoldDrag {
		get {
			// called when we tried to let go
			// if we're throwing the object, let it go immediately
			m_dragActive = false;
			if (!m_lerpToFinalDragOriginHeld && RagdollHelper.WillThrowObject(SmoothedDragDelta)) return false;
			if (m_behaviour == null || m_behaviour.AllowPutDown == false) return false;
			if (m_flick) return false;
			if (NGManager.Me.m_dragPlaneUpness > .9f) return false;
			// if we're putting the object down, smooth the hand to the final position
			if (m_source == null && gameObject == null) return false; // something destroyed us
			GameManager.Me.RaycastAtPoint(InputPosition, m_source ?? gameObject, PlayerHandManager.Me.Hand.gameObject, out var hit, -1);//GameManager.c_layerTerrainBit);
			m_positionAtDragStart = hit.point;
			m_lerpToFinalDragOriginHeld = true;
			m_lerpToFinalDragOrigin = Mathf.Max(0, m_lerpToFinalDragOrigin - Time.deltaTime * NGManager.Me.m_putDownSpeed);
			LockInputPosition(hit.point + Vector3.up * m_behaviour.PutDownReleaseHeight);

			const float c_letGoWhenHandIsThisLow = 6;
			return m_lerpToFinalDragOrigin > .01f && PlayerHandManager.Me.Fingertip.position.HeightAboveGround() > c_letGoWhenHandIsThisLow;
		}
	}

	override public void OnDragEnd(bool _undo) {
		//NGKeyboardHelperGUI.DestroyActive();

		var body = GetComponent<Rigidbody>();
		if (body != null) body.isKinematic = false;

		UpdateBuildingHighlight();
		EndThrowIndicator();
			
		if (LongPressFinish())
			if (m_haveConvertedToDrag == false)
				return;
		PrepareForUndrag();
		
		if (m_source != null)
			DoDrop(_undo);
		m_lerpToFinalDragOrigin = 0; // make sure this is reset for next drag
		m_lerpToFinalDragOriginHeld = false;
		m_source = null;
	}

	void DoDrop(bool _undo = false)
	{
		if (_undo)
		{
			var obj = m_source.GetComponent<NGMovingObject>();
			if (obj != null && obj.UndoDrag())
				return;
		}

		if (m_behaviour != null)
		{
			var target = m_lastTarget; //m_behaviour.GetBestTarget((int)InputId, m_source, m_source.transform.position);
			var action = m_lastAction;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
			if (Input.GetKey(KeyCode.L)) target = null;
#endif
			/*if (target == null)
			{
				var pickup = m_source.GetComponent<ReactPickup>();
				if (pickup != null && (pickup.m_contents.Name.Equals(NGCarriableResource.c_rawMaterialAny) || pickup.m_contents.Name.Equals(NGCarriableResource.c_rawResourceAny)))
				{
					Destroy(m_source.gameObject);
					m_source = null;
					return;
				}
			}*/
			if (_undo)
			{
				target = null;
				action = null;
			}
			m_behaviour.OnDrop(m_source.transform.position, target, m_source, m_lerpToFinalDragOriginHeld ? Vector3.zero : SmoothedDragDelta, _undo, action);
			if (target == null)
				AudioClipManager.Me.PlaySoundOld("PlaySound_DropInterrupt", transform);
		}
		else
		{
			m_source.GetComponent<NGMovingObject>().SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
		}
	}

	[ShowInInspector]private float LongClickTime => .25f;//(NGManager.Me != null) ? NGManager.Me.m_longClickTime : .75f; // GL - setting this high since we no longer use long-click in this way, only through the LongHold system
	private float LongHoldTime => (NGManager.Me != null) ? NGManager.Me.m_longClickTime : .75f;
	override public void OnClick() {
		if (IsInDistrict == false) return;
		m_longPressChecked = false;
		var onClick = GetComponent<IOnClick>();
		Utility.LastClickLength = TimeSinceClick;
		//Debug.Log($"Click Hold Time {TimeSinceClick}");
		//if (!((onClick as NGStandardClick)?.GetComponent<NGFactory>()?.IsLongPressReceiver ?? false)) // GL - check - do we need to inhibit clicks at the end of long holds?
		onClick?.OnClick((int)InputId, TimeSinceClick > LongClickTime);
	}

	private bool CanPickup()
	{
		var ngcommander = PointerDownObject == null ? null : PointerDownObject.GetComponentInParent<NGCommanderBase>();

		if(ngcommander == null) return true;

		// foreach(var t in NGTutorialManager.Me.RestrictedPickupBuildings)
		// {
		// 	if(ngcommander.GetType() == t)
		// 	{
		// 		return false;
		// 	}
		// }
		// NGTutorialManager.Me.FireDragTrigger();
		return true;
	}

	private bool m_dragActive = false; // drag active and not in let-go phase
	public bool IsDragActive => m_dragActive;
	
	protected bool m_overrideIsInDistrict = false;
	public void SetOverrideDistrictFilter(bool _value) => m_overrideIsInDistrict = _value;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool IsInDistrict => m_overrideIsInDistrict || DistrictManager.Me.IsWithinDistrictBounds(transform.position, true) || GameManager.IsEditMode;
#else
	public bool IsInDistrict => true;
#endif
	
	bool c_canPickUpHeroOutOfDistrict => NGManager.Me.m_canPickUpHeroOutOfDistrict;
	bool c_canPickUpOtherCharacterOutOfDistrict => NGManager.Me.m_canPickUpOtherCharacterOutOfDistrict;
	bool c_canPickUpNonCharacterOutOfDistrict => NGManager.Me.m_canPickUpNonCharacterOutOfDistrict;
	public bool LegalToPickUp()
	{
		var ng = GetComponent<NGMovingObject>();
		bool isHero = ng is MAHeroBase;
		bool isNonCharacter = ng is not MACharacterBase;
		bool isOtherCharacter = isHero == false && isNonCharacter == false;
		bool checkDistricts = 
			(isHero && c_canPickUpHeroOutOfDistrict == false) ||
			(isOtherCharacter && c_canPickUpOtherCharacterOutOfDistrict == false) ||
			(isNonCharacter && c_canPickUpNonCharacterOutOfDistrict == false);
		if (checkDistricts && IsInDistrict == false)
		{
			MAHelper.s_triedToPickupObjectInGrey = GetComponent<NGLegacyBase>();
			return false;
		}
		return true;
	}

	public void BeginDragging(GameObject _override = null, Pickup _overrideFrom = null, bool _playAudio = true)
	{
		if (GameManager.Me.IsPossessing || GameManager.Me.IsDesignTable || PlayerHandManager.Me.CanPowerPickUp == false || LegalToPickUp() == false)
		{
			EndDrag();
			return;
        }

		var ch = GetComponent<MACharacterBase>();
		if ((ch != null) && (ch.IsDisappearing || ch.InState(CharacterStates.WaitingToTeleport)))
		{
			EndDrag();
			return;
		}

		m_onDragDetected?.Invoke(this);
		
		if (m_lockedInPlace)
		{
			EndDrag();
			return;
		}
		
		var body = GetComponent<Rigidbody>();
		if (body != null) body.isKinematic = true;

		m_smoothedDragFade = 0;
		StartThrowIndicator();
		SetDragPlaneHorizontalRaiseOrigin(true);
		m_flick = true;
		m_dragStartTime = Time.time;
		m_dragActive = true;
		m_positionAtDragStart = transform.position;
		m_lerpToFinalDragOrigin = PlayerHandManager.Me.IsOver2D ? 1 : 0;
		m_lerpToFinalDragOriginHeld = false;
		m_startMousePos = Input.mousePosition;
		
		m_behaviour = GetComponent<IPickupBehaviour>();
		var ng = GetComponent<NGCommanderBase>();
		if (ng != null)
		{
			// if (PISSManager.UsePISS && PISSManager.PISSActive)
			// {
			// 	EndDrag(); 
			// 	return;
			// }
		}
		else
		{
			var source = m_behaviour?.GetSource();
			var movingObject = source?.GetComponent<NGMovingObject>();
			if (movingObject != null) m_dragRaise = movingObject.m_dragRaise;
			// if (movingObject != null && ((PISSManager.UsePISS && PISSManager.PISSActive == false) || MAUnlocks.CanPickup(movingObject) == false))
			// {
			// 	EndDrag();
			// 	return;
			// }
		}
		
		if (_override != null) CopyParameters(_overrideFrom);
		if (CanPickup())
		{
			if (_override != null) {
				_override.GetComponent<Pickup>().EndDrag();
				StartDrag(true);
				m_source = _override;
			} else {
				m_source = m_behaviour?.GetSource();
			}
		}

		if (m_source != null) {
			if (_playAudio)
			{
				var character = m_source.GetComponent<MACharacterBase>();
				if (character == null || character.IsAlive)
					AudioClipManager.Me.PlaySound(m_pickupAudio?.Event("PlaySound_HandPickupItem") ?? "PlaySound_HandPickupItem", gameObject);
			}
			var mo = m_source.GetComponent<NGMovingObject>();
			
			if(mo)
			{
				TrackObject(mo);
				mo.CanReturn = false;
				mo.DraggedFrom = ng;
				
				if (mo is ReactPickupPersistent pickup)
				{
					pickup.IsBeingDragged = true;
					
					var nextInChain = pickup.m_nextInChain;
					while(nextInChain != null)
					{
						nextInChain.DraggedFrom = ng;
						nextInChain = nextInChain.m_nextInChain;
					}
				}
			}
			m_behaviour?.OnPickup();
		} else {
			EndDrag();
		}
	}
	
	private static DebugConsole.Command s_spawnpickup = new ("spawnpickup", _s =>
	{
		NGCarriableResource resource = NGCarriableResource.GetInfo(_s);
		if (resource == null)
		{
			_s = _s.ToLower().Trim();
			foreach (KeyValuePair<string, NGCarriableResource> ngCarriableResource in NGCarriableResource
				         .s_carriableResources)
			{
				if (ngCarriableResource.Key.ToLower().Trim() == _s)
				{
					resource = ngCarriableResource.Value;
					break;
				}
			}
		}

		if (resource == null || resource.IsNone)
		{
			Debug.LogError($"Failed to create pickup: {_s}");
			return;
		}
        
		var pickupSetup = new PickupSetup();
		pickupSetup.m_quantity = 1;
		pickupSetup.m_killPickup = false;
		pickupSetup.m_holder = GlobalData.Me.m_pickupsHolder;
		pickupSetup.m_onComplete = null;
		ReactPickupPersistent pickup = null;
		if (resource.IsProduct)
		{
			pickup = NGReactPickupAny.Create(null, resource, pickupSetup);
		}
		else
		{
			pickup = ReactPickupPersistent.Create(null, resource, 1, GlobalData.Me.m_pickupsHolder, false, null);
		}

		if (pickup == null)
		{
			Debug.LogError($"Failed to create pickup: {_s}");
			return;
		}

		if (pickup.GetComponent<Pickup>() == null)
			pickup.gameObject.AddComponent<Pickup>();
			
		
		Ray heldCharacterRay = Camera.main.ScreenPointToRay(Input.mousePosition);
		if (Physics.Raycast(heldCharacterRay, out RaycastHit hitInfo))
		{
			pickup.transform.position = hitInfo.point + Vector3.up * 30f;
			return;
		}
		var b = MABuilding.FindBuilding("Tavern");
		if (b != null)
		{
			pickup.transform.position = hitInfo.point + Vector3.up * 30f;
			return;
		}

		pickup.transform.position = Camera.main.ScreenToWorldPoint(Input.mousePosition);
	});

}
