using UnityEngine;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using Unity.Burst;
using Unity.Collections.LowLevel.Unsafe;

public partial class GlobalData
{
	[BurstCompile]
	public struct CheckLineJob : IJob
	{
		[ReadOnly] float3 m_start;
		[ReadOnly] float3 m_end;
		[ReadOnly] int m_navShift;
		[ReadOnly] int m_tolerance;
		[ReadOnly] NativeArray<byte> m_navGrid;
		[ReadOnly] NativeArray<float> m_costs;
		NativeArray<bool> m_output;

		public CheckLineJob(float3 _start, float3 _end, int _navShift, int _tolerance, NativeArray<byte> _navGrid, NativeArray<float> _costs)
		{
			m_start = _start;
			m_end = _end;
			m_navShift = _navShift;
			m_tolerance = _tolerance;
			m_navGrid = _navGrid;
			m_costs = _costs;
			m_output = new NativeArray<bool>(1, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		}

		public void Execute()
		{
			m_output[0] = CheckLine(m_start, m_end, m_navShift, m_tolerance, m_costs, m_navGrid);
		}

		public static bool CheckLine(float3 _start, float3 _end, int _navShift, int _tolerance, NativeArray<float> _costs, NativeArray<byte> _navGrid)
		{
			int ind1 = V2I(ref _start, _navShift), ind2 = V2I(ref _end, _navShift);

			int col1 = ind1 >> _navShift, col2 = ind2 >> _navShift;
			int row1 = ind1 - (col1 << _navShift), row2 = ind2 - (col2 << _navShift);

			bool revGrad;
			int colStart, rowStart;
			if (col1 < col2)
			{
				colStart = col1;
				rowStart = row1;
				revGrad = row1 > row2;
			}
			else
			{
				colStart = col2;
				rowStart = row2;
				revGrad = row1 <= row2;
			}
			int colSpan = col1 + col2 - 2 * colStart;
			int rowSpan = revGrad ? (2 * rowStart - row1 - row2) : (row1 + row2 - 2 * rowStart);
			int cols = 0, rows = 0;
			for (int i = 0; i < colSpan + rowSpan; ++i)
			{
				if ((1 + cols) * rowSpan < (1 + (revGrad ? -rows : rows)) * colSpan)
					++cols;
				else if (revGrad)
					--rows;
				else
					++rows;

				int next = (rows + rowStart) + ((cols + colStart) << _navShift);

				var cost = _costs[_navGrid[next]];
				if (cost <= C_MaxCostCutCorner)
					continue;

				--_tolerance;
				if (_tolerance < 0)
					return true;
			}
			return false;
		}

		private static int V2I(ref float3 _v, int _navShift) => TerrainXr(_v.x) + (TerrainZr(_v.z) << _navShift);

		public bool GetOutput()
		{
			var output = m_output[0];
			m_output.Dispose();
			return output;
		}
	}

	public static float DistanceSqrdFromSegment(float _segLengthSqrd, float3 _segOverSegLengthSqrd, float3 _seg1, float3 _seg2, float3 _point)
	{
		if (_segLengthSqrd < 0.001f) return math.lengthsq((_point - _seg1).xz);
		var t = math.clamp(math.dot((_point - _seg1).xz, (_seg2 - _seg1).xz), 0, _segLengthSqrd);
		var projection = _seg1 + _segOverSegLengthSqrd * t;
		return math.lengthsq((_point - projection).xz);
	}

	public static float SignedDistanceFromLine(float _segLengthSqrd, float3 _segOverSegLengthSqrd, float3 _seg1, float3 _seg2, float3 _segSide, float3 _point)
	{
		if (_segLengthSqrd < 0.001f) return math.length((_point - _seg1).xz);
		var t = math.dot((_point - _seg1).xz, (_seg2 - _seg1).xz);
		var projection = _seg1 + _segOverSegLengthSqrd * t;
		return math.dot(_point - projection, _segSide);
	}

	private DebugConsole.Command s_sideBiasCmd = new ("navsidebias", _s => floatinv.TryParse(_s, out Me.m_sideBias), "Set the navigation side bias", "<float,-100,100>");
	private float m_sideBias = 0;
	
	[BurstCompile]
	public struct NavJob : IJob
	{
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeArray<byte> m_navGrid;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeArray<byte> m_navGridLowRes;
		[ReadOnly] int m_terrainOriginX, m_terrainOriginZ;
		[ReadOnly] int m_navShift;
		[ReadOnly] int m_navShiftLow;
		[ReadOnly] int m_navMask;
		[ReadOnly] int m_navW, m_navH;
		[ReadOnly] int m_gridReduceShift;
		[ReadOnly] bool m_skipPolish;
		[ReadOnly] int m_gridReduce;
		[ReadOnly] float3 m_from;
		[ReadOnly] float3 m_to;
		[ReadOnly] float3 m_fromOriginal;
		[ReadOnly] float3 m_toOriginal;
		[ReadOnly] NativeArray<float> m_costs;
		[ReadOnly] int m_maxElements;
		[ReadOnly] NativeArray<int> m_neighbours;
		[ReadOnly] NativeArray<float2> m_neighbourDirections;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeList<float4> m_pathBlockers;
		[ReadOnly] float m_corridorWidth; // 0 for no corridor
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeArray<int> m_navGenerationFrame; 
		[ReadOnly] int m_navGenerationFrameAtStart;
		[ReadOnly] int m_frameAtStart;
		[ReadOnly] int m_polishStepSize;
		[ReadOnly] int m_idSlot, m_idConfirm;
		[ReadOnly] float m_directNavBias;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeHashMap<int, int> s_navPointNeighbours;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeHashMap<int, int> s_navPointLowResNeighbours;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeListOfLists s_navPointNeighboursList;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeHashMap<int, int> s_navPointStrutToNavPos;
		[NativeDisableContainerSafetyRestriction] [ReadOnly] NativeHashMap<int, float3> s_navPointStrutToWorldPos;
		[ReadOnly] private float m_destinationRadius;
		[ReadOnly] private bool m_relaxPath;
		[ReadOnly] private bool m_navDisableMixedCells;
		[ReadOnly] private int m_gridQuantiseMask;
		[ReadOnly] private float m_sideBiasInvDistance, m_sideBiasPower;

		NativeList<float3> m_result;
		NativeArray<float3> m_finalEndPoint;
		NativeList<int> m_interim;
		NativeArray<int> m_stats;

		//NativeHashSet<int> m_openSet;
		NativeList<int> m_openSet;
		NativeParallelHashSet<int> m_closedSet;
		NativeParallelHashMap<int, int> m_cameFrom;
		NativeParallelHashMap<int, float> m_fScore;
		NativeParallelHashMap<int, float> m_gScore;

		float3 m_originalDestination;
		float3 m_segDistancePrep;
		float3 m_segSide;
		float m_segLengthSqrd;

		int m_previousCurrent;
		
		int m_restrictRegionMinX, m_restrictRegionMinZ, m_restrictRegionMaxX, m_restrictRegionMaxZ;
		
		bool m_checkDestination;
		bool m_isTopLevelNav; 
		bool m_isDebug;
		
		public enum EResult { FoundTarget, FoundNearest, FoundNothing, Cancelled };
		
		
		
		public NavJob(float3 _from, float3 _to, NativeArray<float> _costs, NativeArray<byte> _navGrid, NativeArray<byte> _navGridLowRes, int _w, int _h, int _shift, int _mask, NativeList<float4> _pathBlockers, int _idSlot, float _corridorWidth = 0, SubNavData _subNav = null, float _destinationRadius = 0, bool _isDebug = false)
		{
			m_terrainOriginX = _subNav == null ? c_terrainOriginX : (int) _subNav.m_origin.x;
			m_terrainOriginZ = _subNav == null ? c_terrainOriginZ : (int) _subNav.m_origin.z;
			s_navPointNeighbours = NavStrut.s_navPointNeighbours;
			s_navPointLowResNeighbours = NavStrut.s_navPointLowResNeighbours;
			s_navPointNeighboursList = NavStrut.s_navPointNeighboursList;
			s_navPointStrutToNavPos = NavStrut.s_navPointStrutToNavPos;
			s_navPointStrutToWorldPos = NavStrut.s_navPointStrutToWorldPos;
			m_polishStepSize = Me.m_navGenerationPolishStepSize;
			m_navGenerationFrame = Me.m_navGenerationFrameArray;
			m_navGenerationFrameAtStart = m_navGenerationFrame[0];
			m_frameAtStart = Time.frameCount;
			m_idSlot = _idSlot;
			m_idConfirm = m_navGenerationFrame[_idSlot];
			m_skipPolish = Me.m_navSkipPolish;
			m_directNavBias = Me.m_directNavBias;
			m_destinationRadius = _destinationRadius;
			m_isTopLevelNav = true;
			m_relaxPath = Me.m_relaxPath;
			m_navDisableMixedCells = Me.m_navDisableMixedCells;
			m_gridQuantiseMask = 0;
			if (_corridorWidth > 0 || _subNav != null)
			{
				m_gridReduceShift = 0;
				m_gridReduce = 1;
			}
			else
			{
				m_gridReduceShift = Me.m_navQuantiseShift;
				m_gridReduce = 1 << m_gridReduceShift;
				int quantiseX = ~(m_gridReduce - 1);
				int quantiseZ = (quantiseX << _shift) | _mask;
				m_gridQuantiseMask = quantiseX & quantiseZ;
			}
			m_navGrid = _navGrid;
			m_navGridLowRes = _navGridLowRes;
			m_navW = _w;
			m_navH = _h;
			m_navShift = _shift;
			m_navShiftLow = m_navShift - m_gridReduceShift;
			m_navMask = _mask;
			m_from = _from;
			m_to = _to;
			m_fromOriginal = _from;
			m_toOriginal = _to;
			m_costs = _costs;
			m_pathBlockers = _pathBlockers;
			m_corridorWidth = _corridorWidth;

			m_previousCurrent = 0;

			m_checkDestination = true;
			
			m_result = new NativeList<float3>(0, Allocator.Persistent);
			Allocator memoryPersistence = Allocator.Persistent;//TempJob;
			m_interim = new NativeList<int>(0, memoryPersistence);
			m_finalEndPoint = new NativeArray<float3>(1, memoryPersistence);
			m_finalEndPoint[0] = m_to;
			
			m_originalDestination = m_to;

			m_neighbours = new NativeArray<int>(8, memoryPersistence, NativeArrayOptions.UninitializedMemory);
			m_neighbours[0] = -1; m_neighbours[1] = 1; m_neighbours[2] = -m_navW; m_neighbours[3] = m_navW;
			m_neighbours[4] = -1 - m_navW; m_neighbours[5] = -1 + m_navW; m_neighbours[6] = 1 - m_navW; m_neighbours[7] = 1 + m_navW;

			m_neighbourDirections = new NativeArray<float2>(8, memoryPersistence, NativeArrayOptions.UninitializedMemory);
			const float c_invRoot2 = .7071f;
			m_neighbourDirections[0] = new float2(-1, 0); m_neighbourDirections[1] = new float2(1, 0);
			m_neighbourDirections[2] = new float2(0, -1); m_neighbourDirections[3] = new float2(0, 1);
			m_neighbourDirections[4] = new float2(-c_invRoot2, -c_invRoot2); m_neighbourDirections[5] = new float2(-c_invRoot2, c_invRoot2);
			m_neighbourDirections[6] = new float2(c_invRoot2, -c_invRoot2); m_neighbourDirections[7] = new float2(c_invRoot2, c_invRoot2);

			m_maxElements = 1024;
			//m_openSet = new NativeHashSet<int>(m_maxElements, memoryPersistence);
			m_openSet = new NativeList<int>(m_maxElements, memoryPersistence);
			m_closedSet = new NativeParallelHashSet<int>(m_maxElements, memoryPersistence);
			m_cameFrom = new NativeParallelHashMap<int, int>(m_maxElements, memoryPersistence);
			m_fScore = new NativeParallelHashMap<int, float>(m_maxElements, memoryPersistence);
			m_gScore = new NativeParallelHashMap<int, float>(m_maxElements, memoryPersistence);

			m_segDistancePrep = m_to - m_from; m_segDistancePrep.y = 0;
			m_segLengthSqrd = math.lengthsq(m_segDistancePrep);
			if (m_segLengthSqrd > .1f)
				m_segDistancePrep /= m_segLengthSqrd;
			m_segSide = math.normalize(new float3(-m_segDistancePrep.z, 0, m_segDistancePrep.x));

			m_stats = new NativeArray<int>(3, memoryPersistence, NativeArrayOptions.ClearMemory);

			m_restrictRegionMinX = 0;
			m_restrictRegionMaxX = m_navW;
			m_restrictRegionMinZ = 0;
			m_restrictRegionMaxZ = m_navH;

			var sideBiasAbs = math.abs(Me.m_sideBias);
			var falloff = 1 + sideBiasAbs;
			m_sideBiasInvDistance = math.sign(Me.m_sideBias) / falloff;
			m_sideBiasPower = falloff * .5f;//sideBiasAbs > .1f ? 2 : 1;

			m_isDebug = _isDebug;
		}

		public (int, int) GetStats() // must be called before GetOutput
		{
			return (m_stats[1], m_stats[2]);
		}

		public EResult GetResult()
		{
			return (EResult)m_stats[0];
		}

		public NativeList<float3> GetOutput()
		{
			m_interim.Dispose();
			m_openSet.Dispose();
			m_closedSet.Dispose();
			m_cameFrom.Dispose();
			m_fScore.Dispose();
			m_gScore.Dispose();
			m_neighbours.Dispose();
			m_neighbourDirections.Dispose();
			m_finalEndPoint.Dispose();
			m_stats.Dispose();


			return m_result;
		}

		public float TerrainXf(float _worldX) => (_worldX - m_terrainOriginX) * c_terrainXZScale;
		public float TerrainZf(float _worldZ) => (_worldZ - m_terrainOriginZ) * c_terrainXZScale;
		public int TerrainXr(float _worldX) => (int) (TerrainXf(_worldX) + .5f);
		public int TerrainZr(float _worldZ) => (int) (TerrainZf(_worldZ) + .5f);
	
		private int V2I(ref float3 _v) => TerrainXr(_v.x) + (TerrainZr(_v.z) << m_navShift);

		private float3 I2V(int _i)
		{
			if (_i < 0)
				return s_navPointStrutToWorldPos[_i];
			int x = _i & m_navMask, z = _i >> m_navShift;
			return new float3((float) x / c_terrainXZScale * TerrainBlock.GlobalScale / c_navScale + m_terrainOriginX, 0, (float) z / c_terrainXZScale * TerrainBlock.GlobalScale / c_navScale + m_terrainOriginZ);
		}

		private float DistanceHeuristic(int _from, int _to)
		{
			if (_from < 0) _from = s_navPointStrutToNavPos[_from];
			if (_to < 0) _to = s_navPointStrutToNavPos[_to];
			int x1 = _from & m_navMask, z1 = _from >> m_navShift, x2 = _to & m_navMask, z2 = _to >> m_navShift;
			int dx = x1 - x2, dz = z1 - z2;
			return CalculateDistanceHeuristic(dx, dz);
			//return math.sqrt(dx*dx+dz*dz); // euclidean distance, not a good fit since on a grid it will tend to under-estimate
			if (dx < 0) dx = -dx; if (dz < 0) dz = -dz;
			const float c_root2minus1 = .4142f;
			return dx + dz + math.min(dx, dz) * c_root2minus1; // good fit for a grid, matches the straight/diagonal nature of grid nav
			//return dx + dz; // manhatten distance, tends to over-estimate but fairly good fit
		}


		private void MakeResultArray(int _count)
		{
			m_result.Resize(m_result.Length + _count, NativeArrayOptions.UninitializedMemory);
		}

		private bool OutOfBounds(int _index)
		{
			var pos = I2V(_index);
			if (PathBlock.IsInside(pos, m_pathBlockers)) return true;
			int x = _index & m_navMask, z = _index >> m_navShift;
			if (m_corridorWidth > 0)
				if (DistanceSqrdFromSegment(m_segLengthSqrd, m_segDistancePrep, m_from, m_to, pos) > m_corridorWidth * m_corridorWidth)
					return true;
			return x <= m_restrictRegionMinX || x >= m_restrictRegionMaxX || z <= m_restrictRegionMinZ || z >= m_restrictRegionMaxZ; // don't allow 0 so we dont risk negative neighbours
		}

		private void InsertIntoOpenList(int _new)
		{
			float fScoreNew = m_fScore[_new];
			if (!m_openSet.Contains(_new))
			{
				// binary search insert pos
				var lower = 0;
				var upper = m_openSet.Length;
				while (upper > lower)
				{
					var mid = (lower + upper) / 2;
					var midItem = m_openSet[mid];
					var fScoreMid = m_fScore[midItem];
					var diff = fScoreMid - fScoreNew;
					if (diff == 0)
						lower = upper = mid;
					else if (diff > 0)
						lower = mid + 1;
					else
						upper = mid;
				}
				if (lower == m_openSet.Length)
					m_openSet.Add(_new);
				else
				{
					m_openSet.InsertRangeWithBeginEnd(lower, lower+1);
					m_openSet[lower] = _new;
				}
			}
		}

		public int NavGridIndex(int _index)
		{
			if (m_gridReduceShift == 0 || _index < 0)
				return _index;
			int x = _index & m_navMask;
			int z = _index >> m_navShift;
			x >>= m_gridReduceShift;
			z >>= m_gridReduceShift;
			return x + (z << m_navShiftLow);
		}

		public int NavGrid(int _index, bool _raw = false)
		{
			if(_index < 0)
            {
				return (int)NavCostTypes.OffRoad; // KW: use OffRoad for NavStruts
            }
			if (m_gridReduceShift == 0)
				return m_navGrid[_index];
			var value = m_navGridLowRes[NavGridIndex(_index)];
			if (m_navDisableMixedCells && value == (int) NavCostTypes.MixedLowResCell) value = (int)NavCostTypes.LowNoNav; 
			if (_raw == false && value == (int)NavCostTypes.MixedLowResCell)
				return m_navGrid[_index];
			return value;
		}

		public int Quantise(int _index)
		{
			if (_index < 0) return _index;
			if (m_gridReduceShift == 0)
				return _index;
			return _index & m_gridQuantiseMask;
			/*int x = _index & m_navMask;
			int z = _index >> m_navShift;
			//x += m_gridReduce >> 1;
			//z += m_gridReduce >> 1;
			x = (x >> m_gridReduceShift) << m_gridReduceShift;
			z = (z >> m_gridReduceShift) << m_gridReduceShift;
			return x + (z << m_navShift);*/
		}

		public int Unquantise(int _index) // shift to center of cell
		{
			if (_index < 0) return _index;
			return _index + (m_gridReduce >> 1) + ((m_gridReduce >> 1) << m_navShift);
		}

		private void CheckForDegenerateDestination()
		{
			if (m_checkDestination == false) return;
			m_checkDestination = true;
			if (CheckForDegenerateTarget(ref m_to, true))
				m_finalEndPoint[0] = m_to;
			CheckForDegenerateTarget(ref m_from, false);
		}
		private bool CheckForDegenerateTarget(ref float3 _pos, bool _isDestination = false)
		{
			if (_pos.y < 0) return false; // strut, ignore
			int iTo = V2I(ref _pos);
			var cutoff = _isDestination ? 995 : 1000; // allow start points inside low-no-nav since we can walk out of that, but don't allow walk into it
			if (m_costs[m_navGrid[iTo]] >= cutoff)
			{
				// navigating to no-nav; don't! it'll take forever, searching exhaustively
				// find closest nav grid entry that isn't cost 1000
				// don't allow building inner
				uint allowedBitmap = 0;
				for (int i = 0; i < m_costs.Length; ++i)
					if (i != (int)NavCostTypes.BuildingInner && m_costs[i] < 900)
						allowedBitmap |= 1u << i;
				int iReplace = -1, iReplaceDistSqrd = 20 * 20;
				for (int steps = 1; steps * steps < iReplaceDistSqrd; ++steps)
				{
					int iStepL = iTo - steps - (steps << m_navShift), iStepB = iTo - steps + (steps << m_navShift);
					int iStepR = iTo + steps + (steps << m_navShift), iStepT = iTo + steps - (steps << m_navShift);
					for (int step = 0, crossDist = steps; step < steps + steps; ++step, --crossDist)
					{
						int totalDistSqrd = steps * steps + crossDist * crossDist;
						if (totalDistSqrd < iReplaceDistSqrd)
						{
							if (((allowedBitmap >> m_navGrid[iStepL]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepL, totalDistSqrd);
							if (((allowedBitmap >> m_navGrid[iStepB]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepB, totalDistSqrd);
							if (((allowedBitmap >> m_navGrid[iStepR]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepR, totalDistSqrd);
							if (((allowedBitmap >> m_navGrid[iStepT]) & 1) != 0) (iReplace, iReplaceDistSqrd) = (iStepT, totalDistSqrd);
						}
						iStepL += m_navW; iStepB += 1; iStepR -= m_navW; iStepT -= 1;
					}
				}
				if (iReplace != -1)
				{
					_pos = I2V(iReplace);
					return true;
				}
			}
			return false;
		}

		private bool IsNear(int _index1, int _index2, int _maxDistance)
		{
			int x1 = _index1 & m_navMask;
			int z1 = _index1 >> m_navShift;
			int x2 = _index2 & m_navMask;
			int z2 = _index2 >> m_navShift;
			int dx = x1 - x2, dz = z1 - z2;
			return dx * dx + dz * dz < _maxDistance * _maxDistance;
		}

		private bool IsValidForClosestPoint(int _type)
		{
			const int c_allowedNearestPointTypesBm = (1 << (int)NavCostTypes.Road) | (1 << (int)NavCostTypes.Pavement) | (1 << (int)NavCostTypes.Rail) | (1 << (int)NavCostTypes.OffRoad) | (1 << (int)NavCostTypes.Gate);
			return ((c_allowedNearestPointTypesBm >> _type) & 1) == 1;
		}

		private bool IsAtEnd(int _current, int _destination)
		{
			if (Quantise(_current) == Quantise(_destination)) return true;
			if (m_destinationRadius < 1) return false;
			var currentPos = I2V(_current);
			var destPos = m_originalDestination;//I2V(_destination);
			if (math.lengthsq((currentPos - destPos).xz) <= m_destinationRadius * m_destinationRadius)
			{
				if (m_gridReduceShift == 0)
					m_finalEndPoint[0] = currentPos;
				return true;
			}
			return false;
		}

		private float AccumulateScores(int _from, int _seg)
		{
			float total = 0;
			int prev = 0;
			for (int i = 0; i <= _seg; ++i)
			{
				var p = m_result[_from + i];
				int index = V2I(ref p);
				if (i > 0)
				{
					int px = prev & m_navMask, pz = prev >> m_navShift;
					int nx = index & m_navMask, nz = index >> m_navShift;
					int dx = px - nx, dz = pz - nz;
					if (dx * dx > 1 || dz * dz > 1)
					{
						// not a direct neighbour, use the path version
						total += AccumulateDirectScores(i - 1, 1);
						index = -1; // flag as counted
					}
				}
				if (index >= 0)
					total += m_costs[NavGrid(index)];
				prev = index;
			}
			return total;
		}

		private float AccumulateDirectScores(int _from, int _seg)
		{
			var fromPos = m_result[_from];
			var toPos = m_result[_from + _seg];
			var fromIndex = V2I(ref fromPos);
			var toIndex = V2I(ref toPos);
			int x0 = fromIndex & m_navMask, y0 = fromIndex >> m_navShift;
			int x1 = toIndex & m_navMask, y1 = toIndex >> m_navShift;
			int dx = math.abs(x1 - x0), dy = math.abs(y1 - y0);
			int sx = x0 < x1 ? 1 : -1;
			int sy = y0 < y1 ? 1 : -1;
			int err = dx - dy;
			float total = 0;
			while (true)
			{
				var index = x0 + (y0 << m_navShift);
				var costMultiplier = DirectionBasedCostMultiplier(index);
				total += m_costs[NavGrid(index)] * costMultiplier;
				if (x0 == x1 && y0 == y1) break;
				int e2 = 2 * err;
				if (e2 > -dy)
				{
					err -= dy;
					x0 += sx;
				}
				if (e2 < dx)
				{
					err += dx;
					y0 += sy;
				}
			}
			return total;
		}

		private void ReplaceDirect(int _from, int _seg)
		{
			var fromPos = m_result[_from];
			var toPos = m_result[_from + _seg];
			var step = (toPos - fromPos) / _seg;
			for (int i = 1; i < _seg; ++i)
				m_result[_from + i] = fromPos + step * i;
		}

		private void RelaxPath()
		{
			for (int i = 0; i < m_result.Length - 2; )
			{
				if (m_result[i].y < 0) // don't relax from a strut
				{
					++i;
					continue;
				}
				int seg = 2;
				const int c_maxPull = 40;
				for (; i + seg < m_result.Length && seg < c_maxPull; ++seg)
				{
					if (m_result[i + seg].y < 0) // don't relax into a strut
						break;
					var existingTotal = AccumulateScores(i, seg);
					var newTotal = AccumulateDirectScores(i, seg);
					if (newTotal > existingTotal)
						break;
				}
				// seg failed (or too long), use seg - 1 if appropriate
				--seg;
				ReplaceDirect(i, seg);
				i += seg;
			}
		}
		
		private float DirectionBasedCostMultiplier(int _index)
		{
			return 1;
			var neighbourPos = I2V(_index);
			var signedDistanceFromStraight = SignedDistanceFromLine(m_segLengthSqrd, m_segDistancePrep, m_from, m_to, m_segSide, neighbourPos);
			var directionFactor = math.clamp(signedDistanceFromStraight * m_sideBiasInvDistance, 0, 1);
			return directionFactor * (1 - m_sideBiasPower) + m_sideBiasPower;
		}

		public void Execute()
		{
			CheckForDegenerateDestination();
			m_originalDestination = m_to;
			RunSearch();
			if (m_result.Length >= 2)
			{
				m_result[0] = m_fromOriginal;
				m_result[^1] = m_finalEndPoint[0];
			}
			if (m_relaxPath)
				RelaxPath();
		}
		private void RunSearch()
		{
			var additionalNeighbours = m_gridReduce > 1 ? s_navPointLowResNeighbours : s_navPointNeighbours;
			int iFrom = m_from.y < 0 ? (int)m_from.y : V2I(ref m_from), iTo = m_to.y < 0 ? (int)m_to.y : V2I(ref m_to);
			if (NavGrid(iFrom, true) != (int)NavCostTypes.MixedLowResCell)
				iFrom = Quantise(iFrom);
			iTo = Quantise(iTo);
			if (iFrom == iTo)
			{
				if (m_gridReduceShift > 0 && !m_skipPolish)
				{
					m_gridReduceShift = 0;
					m_gridReduce = 1;
					NextStep();
					return;
				}
				int first = m_result.Length;
				MakeResultArray(1);
				m_result[first] = m_to;
				return;
			}

			m_openSet.Add(iFrom);
			m_gScore[iFrom] = 0f;
			m_fScore[iFrom] = DistanceHeuristic(iFrom, iTo);

			var neighbours = m_neighbours;

			int current = -1;
			while (m_openSet.Length > 0)
			{
				if (m_navGenerationFrameAtStart != m_navGenerationFrame[0] || m_idConfirm != m_navGenerationFrame[m_idSlot])
				{
					// nav data changed during nav, cancel the job
					m_stats[0] = (int)EResult.Cancelled;
					return;
				}
				int bestIndex = m_openSet.Length - 1;
				current = m_openSet[bestIndex];
				if (IsAtEnd(current, iTo))
					 break;
				m_openSet.RemoveAt(bestIndex);
				m_closedSet.Add(current);
				const float c_strutCost = 3;
				float currentCost = current < 0 ? c_strutCost : m_costs[NavGrid(current)];
				var currentPos = I2V(current);
				int neighbourStart = 0, neighbourCount = 8, neighbourAddFirst = 0;
				if (additionalNeighbours.TryGetValue(NavGridIndex(current), out var strutNeighboursId))
				{
					neighbourCount += s_navPointNeighboursList.m_toc[strutNeighboursId + 1];
					neighbourAddFirst = s_navPointNeighboursList.m_toc[strutNeighboursId + 0];
				}
				bool isInMixedCell = current >= 0 && NavGrid(current, true) == (int)NavCostTypes.MixedLowResCell;
				if (current < 0) neighbourStart = 8;
				for (int n = neighbourStart; n < neighbourCount; n++)
				{
					int neighbour, neighbourCostMultiplier = 1 << m_gridReduceShift;
					if (n >= 8)
					{
						neighbour = Quantise(s_navPointNeighboursList.m_list[neighbourAddFirst + n - 8]);
					}
					else
					{
						var offs = neighbours[n];
						neighbour = current + (offs << m_gridReduceShift);
						if (OutOfBounds(neighbour)) continue;
						if (isInMixedCell)
						{
							neighbour = current + offs;
							neighbourCostMultiplier = 1;
						}
						else if (NavGrid(neighbour, true) == (int) NavCostTypes.MixedLowResCell)
						{
							var qCurrent = Quantise(current);
							neighbour = current;
							neighbourCostMultiplier = 0;
							do
							{
								neighbour += offs;
								++neighbourCostMultiplier;
							} while (Quantise(neighbour) == qCurrent);
						}
						if (NavGrid(neighbour, true) != (int) NavCostTypes.MixedLowResCell)
						{
							neighbour = Quantise(neighbour);
							if (isInMixedCell)
								neighbourCostMultiplier = 1 << (m_gridReduceShift - 1); // from edge of previous hi-res cell to center of this, half normal cell distance
						}
					}
					if (m_closedSet.Contains(neighbour)) continue;
					float cost, directionCostMultiplier = 1;
					if (n >= 8)
					{
						cost = c_strutCost;
					}
					else
					{
						cost = m_costs[NavGrid(neighbour)];
						if (neighbour == iTo && m_gridReduceShift > 0) cost = 1; // allow low-res nav to destination even if it's no-nav since hi-res won't be
						if (cost >= 990 && cost <= 999 && (currentCost < 990 || IsNear(current, iFrom, 16) == false)) continue; // don't traverse through 990 (low-no-nav) unless you're already in it 
						if (cost > 999 && (currentCost <= 999 || m_gridReduceShift > 0)) continue; // allow traverse through 1000 if you're already in 1000 (high-res only)
						cost *= neighbourCostMultiplier;
						cost *= 1 + (n >> 2) * .4142f;
						directionCostMultiplier = DirectionBasedCostMultiplier(neighbour);
					}
					//
					float tentativeG = m_gScore[current] + cost * m_gridReduce * directionCostMultiplier;
					float neighbourG;
					if (!m_gScore.TryGetValue(neighbour, out neighbourG) || tentativeG < neighbourG)
					{
						m_cameFrom[neighbour] = current;
						m_gScore[neighbour] = tentativeG;
						m_fScore[neighbour] = tentativeG + DistanceHeuristic(neighbour, iTo);
						InsertIntoOpenList(neighbour);
					}
				}
				current = -1;
			}
			m_stats[m_gridReduceShift > 0 ? 1 : 2] += m_closedSet.Count();
			if (m_isTopLevelNav) m_stats[0] = (int)EResult.FoundTarget;
			if (current == -1)
			{
				// failed to find route
				// find closest openSet entry to destination and unwind to there
				if (m_isTopLevelNav) m_stats[0] = (int)EResult.FoundNearest;
				float bestDSqrd = 1e23f;
				float3 bestPos = new float3(5,5,5);
				int bestIndex = -1;
				foreach (var visited in m_closedSet)
				{
					var navType = NavGrid(visited);
					if (IsValidForClosestPoint(navType) == false) continue;
					var visitedPos = I2V(visited);
					var dSqrd = math.lengthsq((visitedPos - m_to).xz);
					if (dSqrd < bestDSqrd)
					{
						dSqrd += DistanceSqrdFromSegment(m_segLengthSqrd, m_segDistancePrep, m_from, m_to, visitedPos) * 5;
						if (dSqrd < bestDSqrd)
						{
							bestDSqrd = dSqrd;
							bestPos = visitedPos;
							bestIndex = visited;
						}
					}
				}
				if (bestIndex == -1)
				{
					// couldn't find a close point, give up
					if (m_isTopLevelNav)
					{
						// high-level (low-res) nav, show we failed
						MakeResultArray(1); m_result[0] = m_from;
						m_finalEndPoint[0] = m_from;
						m_stats[0] = (int)EResult.FoundNothing;
					}
					return;
				}
				current = bestIndex;
				m_to = bestPos;
				m_finalEndPoint[0] = m_to;
			}
			if (m_gridReduceShift > 0 && !m_skipPolish)
			{
				// route possible, do a full-res nav
				m_gridReduceShift = 0;
				m_gridReduce = 1;
				m_isTopLevelNav = false;

				if (m_polishStepSize > 0)
				{
					CalculateInterim(current, iFrom);
					if (m_interim.Length >= m_polishStepSize)
					{
						// Polish takes multiple segments
						int first = 0, step = m_polishStepSize;
						while (first < m_interim.Length)
						{
							int last = math.min(first + step, m_interim.Length - 1);
							if (last > m_interim.Length - m_polishStepSize / 4) // allow the last segment to be up to 25% longer to avoid hooking around at the end
							{
								last = m_interim.Length - 1;
								step = last - first + 1;
							}
							m_from = I2V(m_interim[first]);
							if (first > 0 && first < m_interim.Length - 1) 
							{
								var prev = I2V(m_interim[first - 1]);
								var next = I2V(m_interim[first + 1]);
								if (prev.y >= 0 && next.y >= 0) // don't blend in nav struts
									m_from += ((prev - m_from) + (next - m_from)) * .2f;
							}
							m_to = I2V(m_interim[last]);
							if (last == m_interim.Length - 1)
								m_to = m_finalEndPoint[0];
							if (last > 0 && last < m_interim.Length - 1)
							{
								var prev = I2V(m_interim[last - 1]);
								var next = I2V(m_interim[last + 1]);
								if (prev.y >= 0 && next.y >= 0) // don't blend in nav struts
									m_to += ((prev - m_to) + (next - m_to)) * .2f;
							}
							SetRestrictAndNextStep(first, last);
							first += step;
						}
					}
					else
					{
						// Polish fits in one segment
						SetRestrictAndNextStep(0, m_interim.Length - 1);
					}
				}
				else
				{
					// Don't sub-divide polish, always fit in one segment
					SetRestrictAndNextStep(0, 0);
				}
				return;
			}
			AccumulateResult(current, iFrom);
		}

		void SetRestrictAndNextStep(int _start, int _end)
		{
			const int c_restrictMargin = 64;
			if (_end > 0)
			{
				m_restrictRegionMinX = 1000000; m_restrictRegionMinZ = 1000000; m_restrictRegionMaxX = -1000000; m_restrictRegionMaxZ = -1000000;
				for (int i = _start; i <= _end; ++i)
				{
					if (m_interim[i] < 0) continue;
					int x = m_interim[i] & m_navMask;
					int z = m_interim[i] >> m_navShift;
					if (x < m_restrictRegionMinX) m_restrictRegionMinX = x;
					if (x > m_restrictRegionMaxX) m_restrictRegionMaxX = x;
					if (z < m_restrictRegionMinZ) m_restrictRegionMinZ = z;
					if (z > m_restrictRegionMaxZ) m_restrictRegionMaxZ = z;
				}
				m_restrictRegionMinX = Mathf.Max(0, m_restrictRegionMinX - c_restrictMargin);
				m_restrictRegionMinZ = Mathf.Max(0, m_restrictRegionMinZ - c_restrictMargin);
				m_restrictRegionMaxX = Mathf.Min(4095, m_restrictRegionMaxX + c_restrictMargin);
				m_restrictRegionMaxZ = Mathf.Min(4095, m_restrictRegionMaxZ + c_restrictMargin);
			}
			else
			{
				m_restrictRegionMinX = 0; m_restrictRegionMinZ = 0; m_restrictRegionMaxX = 4096; m_restrictRegionMaxZ = 4096;
			}
			NextStep();
		}

		void NextStep()
		{
			m_openSet.Clear();
			m_gScore.Clear();
			m_fScore.Clear();
			m_closedSet.Clear();
			m_cameFrom.Clear();
			m_previousCurrent = 0;
			RunSearch();
		}

		void CalculateInterim(int current, int iFrom)
		{
			if (current == -1) return;
			int originalCurrent = current;
			int count = 1;
			if (current != iFrom)
			{
				while (true)
				{
					current = m_cameFrom[current];
					if (current == iFrom) break;
					++count;
				}
			}
			// reverse path into result
			if (count == 1)
			{
				m_interim.Resize(2, NativeArrayOptions.UninitializedMemory);
				m_interim[0] = iFrom;
				m_interim[1] = current;
			}
			else
			{
				m_interim.Resize(count, NativeArrayOptions.UninitializedMemory);
				current = originalCurrent;
				int next = count;
				while (true)
				{
					current = m_cameFrom[current];
					m_interim[--next] = current;
					if (current == iFrom)
						break;
				}
			}
		}

		void AccumulateResult(int current, int iFrom)
		{
			// count result steps
			if (current == -1) return;
			int originalCurrent = current;
			int count = 1;
			if (current != iFrom)
			{
				while (true)
				{
					current = m_cameFrom[current];
					if (current == iFrom) break;
					++count;
				}
			}
			// reverse path into result
			int firstOutput = m_result.Length;
			int toAdd = count;
			if (firstOutput == 0) ++toAdd;
			else --firstOutput;
			MakeResultArray(toAdd);
			
			if (count == 1)
			{
				m_result[firstOutput] = m_from;
				m_result[firstOutput + 1] = m_to;
			}
			else
			{
				current = originalCurrent;
				int next = count;
				m_result[firstOutput + next] = I2V(Unquantise(current));
				while (true)
				{
					current = m_cameFrom[current];
					int index = --next;
					m_result[firstOutput + index] = I2V(Unquantise(current));
					if (current == iFrom)
						break;
				}
			}
			if (firstOutput > 0)
			{
				for (int j = m_previousCurrent; j < firstOutput; ++j)
				{
					var check = m_result[j];
					for (int i = firstOutput; i < firstOutput + count; ++i)
					{
						var delta = check - m_result[i];
						if (delta.x * delta.x + delta.z * delta.z < .01f * .01f)
						{
							// found a loop, remove it
							int toRemove = i - j;
							for (int k = i; k < firstOutput + count; ++k)
								m_result[k - toRemove] = m_result[k];
							m_result.Resize(m_result.Length - toRemove, NativeArrayOptions.UninitializedMemory);
							count -= toRemove;
							j = firstOutput;
							break;
						}
					}
				}
			}
			m_previousCurrent = originalCurrent;
		}
	}
}
