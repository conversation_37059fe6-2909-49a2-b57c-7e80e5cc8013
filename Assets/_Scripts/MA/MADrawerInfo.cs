using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class MADrawerInfo
{
    public static List<MADrawerInfo> s_drawerInfos = new List<MADrawerInfo>();
    public static List<MADrawerInfo> GetList => s_drawerInfos;
    public string DebugDisplayName => m_drawerIndex;

    public string id;
    public bool m_debugChanged;
    public string m_drawerIndex;
    public int m_number;
    public string m_drawerTitle;
    public string m_displayTitle;
    public string m_drawerName;
    public string m_displayName;
    public string m_drawerType;
    public bool m_unlockAtStart;
    [ScanField] public string m_district;

    public bool IsUnlocked()
    {
        return m_unlockAtStart || GameManager.Me.m_state.m_designTableDetails.m_unlockedDrawers.ContainsKey(m_drawerIndex) || GameManager.s_debugUnlockDraws;
    }
    
    public static bool PostImport(MADrawerInfo _what)
    {
        return true;
    }
    public static List<MADrawerInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_drawerInfos = NGKnack.ImportKnackInto<MADrawerInfo>(PostImport);
        return s_drawerInfos;
    }

    public static List<(string, string)> GetDrawerNames(string _name)
    {
        var results = new List<(string, string)>();
        if (_name == null) return results;
        foreach (var s in _name.Split(';', ',', '\n', '|'))
        {
            var bits = s.Split(':');
            if (bits.Length == 2)
                results.Add((bits[0], bits[1]));
        }
        return results;
    }

    public static List<MADrawerInfo> GetInfosByDrawerName(string _name)
    {
        var found = s_drawerInfos.FindAll(o => o.m_drawerName.Equals(_name, StringComparison.OrdinalIgnoreCase));
        if (found == null || found.Count == 0)
        {
            found = s_drawerInfos.FindAll(o => o.m_drawerIndex.Equals(_name, StringComparison.OrdinalIgnoreCase));
        }
        return found;
    }

    public static List<MADrawerInfo> GetInfos(string _name)
    {
        var results = new List<MADrawerInfo>();
        if (_name == null) return results;
        foreach (var s in _name.Split(';', ',', '\n', '|'))
        {
            var i = GetInfo(s);
            if(i != null)
                results.Add(i);
        }

        return results;
    }

    public static MADrawerInfo GetInfo(string _name)
    {
        return s_drawerInfos.Find(o => o.m_drawerIndex.Equals(_name, StringComparison.OrdinalIgnoreCase));
    }
}
