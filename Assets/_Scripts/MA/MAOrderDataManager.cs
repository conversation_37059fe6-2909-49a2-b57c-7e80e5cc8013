using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MAOrderDataManager : MonoSingleton<MAOrderDataManager>
{
	public const string c_giftTypeName = "Order";
	
	public class GlobalOrderData
	{
		[Save] public Dictionary<string, OrderSequence> m_dispatchSequences = new();
	}
	
	// If order ID is null, will get last order with design
	public GameState_Product GetOrderWithDesign(string _orderId = null)
	{
		if(_orderId.IsNullOrWhiteSpace())
		{
			// Get last order with design
			int lastOrderId = GameManager.Me.m_state.m_highestOrderId;
			while(lastOrderId >= 0)
			{
				GameManager.Me.m_state.m_orders.TryGetValue(lastOrderId, out var order);
				
				if(order != null && order.GameProduct != null && order.GameProduct.HasDesign && order.GameProduct.Design.HasDesign)
				{
					return order.GameProduct;
				}
				--lastOrderId;
			}
		}
		else
		{
			// Get specific order design
			foreach(var order in GameManager.Me.m_state.m_orders.m_values)
			{
				if(order == null || order.OrderInfoIndexer != _orderId)
					continue;
					
				var product = order.GameProduct;
				if(product == null || product.HasDesign == false && product.Design.HasDesign == false)
					continue;
				
				return order.GameProduct;
			}
		}
		return null;
	}
		
	private OrderSequence m_emptySequence = new();
	
	public OrderSequence GetOrderSequence(string _type, bool _create = false)
	{
		if(_type.IsNullOrWhiteSpace())
		{
			Debug.LogError("Invalid sequence name");
			return m_emptySequence;
		}
		
		_type = _type.ToLower();
		
		if(m_globalOrderData.m_dispatchSequences.TryGetValue(_type, out var _sequence))
			return _sequence;
		if(_create)
		{
			var newSequence = new OrderSequence();
			m_globalOrderData.m_dispatchSequences[_type] = newSequence;
			return newSequence;
		}
		return m_emptySequence;
	}
	
	private GlobalOrderData m_globalOrderData = new();
	
	public MAOrderBoardUI m_maOrderBoardUIPrefab;
	public GameObject m_maOrderBoardWorldPrefab;
	public GameObject m_maOrderBoardUIRowPrefab;
    
    public MAOrder FindOrderByInfo(MAOrderInfo _info)
    {
	    foreach(var order in GameManager.Me.m_state.m_orders.m_values)
	    {
		    if(order.OrderInfo == _info)
		    {
			    return order;
		    }
	    }
	    return null;
    }
    
    public MAOrder FindOrderByID(int _id)
	{
		if(GameManager.Me.m_state.m_orders.TryGetValue(_id, out var value))
		{
			return value;
		}
		return MAOrder.EmptyOrder;
	}
	
	public void SaveAll()
	{
		GameManager.Me.m_state.m_globalOrderData = SSerializer.Serialize(m_globalOrderData);
	}
	
	public void Load()
	{
		if (GameManager.HasLoadedFromSeed == false && SSerializer.IsSSerialized(GameManager.Me.m_state.m_globalOrderData)) // ignore empty or degenerate data
		{
			SSerializer.DeserializeInPlace(m_globalOrderData, GameManager.Me.m_state.m_globalOrderData);
		}
	}
	
	public void PostLoad()
	{
		foreach(var order in GameManager.Me.m_state.m_orders.m_values)
		{
			order.PostLoad();
		}
	}

	private void Update()
	{
		if(GameManager.Me.LoadComplete == false) return;
		
		GameManager.Me.m_state.m_tavernSequence.Update();
	}

	public class OrderCount
	{
		public string m_subject;
		public int m_count;
	}
	
	public void LoadDispatchOrders(string _sequenceName, string _blockName, string _command)
	{
		var orders = MAOrderInfo.GetInfosByBlockName(_blockName);
		bool clear = _command.ToLower() == "clear";
		if(_sequenceName.IsNullOrWhiteSpace())
		{
			Debug.LogError("Invalid sequence name when loading dispatch orders");
			return;
		}
		
		var sequence = GetOrderSequence(_sequenceName, true);
		
		if(sequence == m_emptySequence) return;
		
		sequence.Insert(orders, clear);
	}
	
	public void LoadDispatchOrder(string _sequenceName, MAOrderInfo _newOrderInfo)
	{
		if(_newOrderInfo == null)
		{
			Debug.LogError("Could not find order to push to dispatch");
			return;
		}
		
		var sequence = GetOrderSequence(_sequenceName, true);
		
		if(sequence == m_emptySequence) return;
		
		sequence.Insert(_newOrderInfo, false, OrderSequence.AddAction.CreateExtraSlot);
	}
	
	public void ClearDispatchOrderSequence(string _sequenceName = "")
	{
		_sequenceName = _sequenceName.ToLower();
		if(_sequenceName.IsNullOrWhiteSpace())
		{
			foreach(var s in m_globalOrderData.m_dispatchSequences.Values)
			{
				s.m_sequence.Clear();
			}
		}
		else if(m_globalOrderData.m_dispatchSequences.TryGetValue(_sequenceName, out var sequence))
		{
			sequence.m_sequence.Clear();
		}
			
	}
	
	public bool LockOrder(MAOrder _orderToLock, bool _lock)
	{
		if(_orderToLock.IsValid && _orderToLock.IsComplete == false)
		{
			_orderToLock.m_locked = _lock;
		}
		return false;
	}

	[Serializable]
	public class QualityEntry
	{
		public float m_rangeMin;
		public Sprite m_sprite;
		public string m_name;
		public Color m_color;
		public string m_colorHex;
		
		public string GetColordString()
		{
			return $"<color={m_colorHex}>{m_name}</color>";
		}
	}

	public List<OrderCount> CountHistoricalOrders(Func<MAOrder, string> _getter)
	{
		Dictionary<string, int> orderProductCounts = new Dictionary<string, int>();
		foreach(var historicalOrder in GameManager.Me.m_state.m_orderDataHistory) //history of ALL completed orders, their data, such as assigned building id, is still set
		{
			string result = _getter(historicalOrder);
			if(string.IsNullOrWhiteSpace(result) == false)
			{
				if(orderProductCounts.ContainsKey(_getter(historicalOrder)) == false)
				{
					orderProductCounts.Add(result, 1);
				}
				else
				{
					orderProductCounts[_getter(historicalOrder)]++;
				}
			}
		}

		List<OrderCount> sortedProductCounts = new();
		foreach(var orderCount in orderProductCounts)
		{
			sortedProductCounts.Add(new OrderCount() { m_count = orderCount.Value, m_subject = orderCount.Key });
		}
		sortedProductCounts.Sort((a, b) => b.m_count.CompareTo(a.m_count));

		return sortedProductCounts;
	}

	public List<OrderCount> CountHistoricalOrdersByProduct()
	{
		var productLineList = CountHistoricalOrders(x => x.ProductLine);
		return productLineList;
	}

	public List<OrderCount> CountHistoricalOrdersByFaction()
	{
		var factionList = CountHistoricalOrders(x =>
		{
			MAOrderInfo orderInfo = x.OrderInfo;
			return orderInfo.Faction.m_name;
		});
		return factionList;
	}

	
	[Serializable] 
	public class CurrencyMetadata
	{
		public string m_id;
		public string m_name;
		public string m_spriteTag;
	}
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAOrderDataManager))]
public class MAOrderDataManagerEditor : Editor
{
	bool show;			
	bool show1;			
	bool show2;		
	bool show1b;
	private string text1 = "<AllDispatches or e.g. StartingDispatch>";
	private string text2 = "<BlockName>";
	private string text3 = "<clear or keep>";
	private string text4 = "<indexer>";
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		MAOrderDataManager maOrderDataManager = target as MAOrderDataManager;
		if(Application.isPlaying)
		{
			GUILayout.Label("------------------------------------------");
			GUILayout.Label("Order Sequence Tests");
			GUILayout.BeginHorizontal();
			GUILayout.Label("InsertOrderIntoDispatchSequence: ");
			text4 = GUILayout.TextField(text4);
			GUILayout.EndHorizontal();
			if(!string.IsNullOrWhiteSpace(text1) &&
			   !string.IsNullOrWhiteSpace(text4))
			{
				MAOrderInfo maOrderInfo = MAOrderInfo.GetInfo(text4);
				if(maOrderInfo != null && GUILayout.Button($"Push Order to Board. Dispatch(s): '{text1}' OrderIndexer: '{text4}'"))
				{
					//maOrderDataManager.InsertOrderIntoDispatchSequence(text1, maOrderInfo);
				}
			}
			
			GUILayout.Space(20);
			
			GUILayout.Label("Trigger Order Sequence: ");
			GUILayout.BeginHorizontal();
			text1 = GUILayout.TextField(text1);
			text2 = GUILayout.TextField(text2);
			text3 = GUILayout.TextField(text3);
			GUILayout.EndHorizontal();

			if(!string.IsNullOrWhiteSpace(text1) &&
			   !string.IsNullOrWhiteSpace(text2) &&
			   !string.IsNullOrWhiteSpace(text3))
			{
				if(GUILayout.Button($"TriggerDispatchOrderSequence. Dispatch(s): '{text1}' BlockName: '{text2}' keep/clear: '{text3}'"))
				{
					//maOrderDataManager.TriggerDispatchOrderSequence(text1, text2, text3);
				}
			}
			GUILayout.Label("------------------------------------------");
			
			GUILayout.Label($"Orders on History List: {GameManager.Me.m_state.m_orderDataHistory.Count}");

			show1b = EditorGUILayout.Foldout(show1b, "Orders in Sequence: ");
			if(show1b)
			{
				foreach(var o in GameManager.Me.m_state.m_orders.m_values)
				{
					EditorGUILayout.TextArea(o.DebugOutputString);
				}
			}
			
			show2 = EditorGUILayout.Foldout(show2, "All Historical Orders: ");
			if(show2)
			{
				foreach(var o in GameManager.Me.m_state.m_orderDataHistory)
				{
					EditorGUILayout.TextArea(o.DebugOutputString);
				}
			}
		}
	}
}
#endif