using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class TavernSequenceItem : IComparable<TavernSequenceItem>
{
    public string GenericType => m_genericType;
    public int Level => m_level;
    public bool IsIndividualType => m_selectRandomFromType == false;
    public bool IsRecurringItem => m_quantity < 0;
    public int Priority => m_priority;
    public string CurrentCharacterType => m_characterType;
    
    [SerializeField] private int m_quantity;
    [SerializeField] private int m_priority;
    [SerializeField] private string m_characterType;
    
    [SerializeField] private bool m_selectRandomFromType;
    [SerializeField] private string m_genericType;
    [SerializeField] private int m_level;
    
    public TavernSequenceItem() {}
    public TavernSequenceItem(string _name, int _quantity, int _priority)
    {
        m_characterType = _name;
        m_quantity = _quantity;
        m_priority = _priority;
    }
    
    public TavernSequenceItem(string _genericType, int _level, int _quantity, int _priority)
    {
        m_selectRandomFromType = true;
        m_genericType = _genericType;
        m_level = _level;
        m_quantity = _quantity;
        m_priority = _priority;
        TrySetNextItem();
    }
    
    public void AdjustQuantity(int _quantity)
    {
        if(IsRecurringItem) return;
        m_quantity += _quantity;
    }
        
    private void TrySetNextItem()
    {
        if(IsIndividualType)
            return;
            
        if(IsRecurringItem == false && m_quantity == 0)
            return;
        
        List<string> choices = new();
        foreach(var info in MACreatureInfo.s_creatureInfos)
        {
            if(info.m_initialLevel != m_level) continue;
            if(info.m_creatureType != m_genericType) continue;
            choices.Add(info.m_name);
        }
        foreach(var info in MAWorkerInfo.s_workerInfos)
        {
            if(info.m_level != m_level) continue;
            if(info.m_workerType != m_genericType) continue;
            choices.Add(info.m_name);
        }
        
        if(choices.Count == 0)
        {
            Debug.LogError($"Could not find any Workers or Creatures that match Type {m_genericType} and Level {m_level} to load into TavernSequenceItem.");
            return;
        }
        int index = UnityEngine.Random.Range(0, choices.Count);
        m_characterType = choices[index];
    }
    
    public bool OnUsedItem()
    {
        TrySetNextItem();
        
        // quantity of -1 is a recurring item
        if(IsRecurringItem)
            return false;
        
        m_quantity = Mathf.Max(0, m_quantity - 1);
        return m_quantity == 0;
    }
    
    // Default comparer for Part type.
    public int CompareTo(TavernSequenceItem _compare)
    {
        // A null value means that this object is greater.
        if (_compare.m_priority > m_priority)
            return 1;
        if(_compare.m_priority < m_priority)
            return -1;
        return 0; 
    }
}

[Serializable]
public class TavernSequence
{
    public List<CardSlot> Slots => m_slots;

    public List<TavernSequenceItem> m_sequence = new();
     
    private List<CardSlot> m_slots = new ();
    private int m_availableSlots = 2;
    public float m_refreshTime = 0;
    private float m_refreshCoolDown = 0;
    private bool m_slotsChanged = false;
    private bool m_requiresCardRefresh = false;
    
    public void Update()
    {
        if(m_slots == null || m_slots.Count == 0)
        {
            if(m_refreshCoolDown > 0)
            {
                m_refreshCoolDown -= Time.deltaTime;
            }
            else
            {
                CreateNextCardSet();
                m_refreshCoolDown = 2f;
            }
        }
        else if(NGDirectionCardBase.DraggingCard == null)
        {
            if(m_requiresCardRefresh)
            {
                CreateNextCardSet();
                m_requiresCardRefresh = false;
            }
            
            foreach(var s in m_slots)
            {
                s.Update();
            }
        }
    }
    
    public void OnChoiceCardUsed(NGBusinessGift _gift) 
    {
        OnItemRemoved(_gift.m_power);
        CreateNextCardSet();
    }
    
    public void Clear()
    {
        m_sequence.Clear();
    }
    
    private void OnItemRemoved(string _characterType)
    {
        for(int i = 0; i < m_sequence.Count; ++i)
        {
            var item = m_sequence[i];
            if(item.CurrentCharacterType == _characterType)
            {
                if(item.OnUsedItem())
                {
                    m_sequence.RemoveAt(i);
                    --i;
                }
                return;
            }
        }
    }
    
    public bool HaveSlotsChanged()
    {
        bool changed = m_slotsChanged;
        m_slotsChanged = false;
        return changed;
    }
    
    private void CreateNextCardSet()
    {
        // Create next set of cards here
        if(m_slots != null) 
            foreach(var s in m_slots) s.Clear();
        
        CreateNextCharacterChoice();
    }
    
    public void RemoveFromSequence(string _genericType, int _level)
    {
        _genericType = _genericType.Trim();
        for(int i = 0; i < m_sequence.Count; ++i)
        {
            if(m_sequence[i].GenericType == _genericType && m_sequence[i].Level == _level)
            {
                m_sequence.RemoveAt(i);
                --i;
            }
        }
        m_requiresCardRefresh = true;
    }
    
    public void RemoveCharacterFromSequence(string _characterType)
    {
        _characterType = _characterType.Trim();
        for(int i = 0; i < m_sequence.Count; ++i)
        {
            if(m_sequence[i].IsIndividualType && m_sequence[i].CurrentCharacterType == _characterType)
            {
                m_sequence.RemoveAt(i);
                --i;
            }
        }
        m_requiresCardRefresh = true;
    }
    
    public void AddToSequence(string _genericType, int _level, int _count, int _priority)
    {
        _genericType = _genericType.Trim();
        foreach(var item in m_sequence)
        {
            if(item.IsIndividualType) continue;
            
            if(item.GenericType == _genericType && item.Level == _level && item.Priority == _priority)
            {
                item.AdjustQuantity(_count);
                return;
            }
        }
        m_sequence.Add(new TavernSequenceItem(_genericType, _level, _count, _priority));
        
        m_sequence.Sort();
        
        m_refreshCoolDown = 0;
        m_requiresCardRefresh = true;
    }
    
    public void AddToSequence(string _characterType, int _count, int _priority)
    {
        _characterType = _characterType.Trim();
        foreach(var item in m_sequence)
        {
            if(item.IsIndividualType && item.CurrentCharacterType == _characterType && item.Priority == _priority)
            {
                item.AdjustQuantity(_count);
                return;
            }
        }
        m_sequence.Add(new TavernSequenceItem(_characterType, _count, _priority));
        
        m_sequence.Sort();
        
        m_refreshCoolDown = 0;
        m_requiresCardRefresh = true;
    }
    
    private void CreateNextCharacterChoice()
    {
        DestroySlots();
        
        if(m_sequence.Count == 0)
            return;
            
        for(int i = 0; i < m_sequence.Count; ++i)
        { 
            var item = m_sequence[i];
            var gift = NGBusinessGift.CreateNewWorkerGift(item.CurrentCharacterType);
            if(gift == null) gift = NGBusinessGift.CreateNewHeroGift(item.CurrentCharacterType);
            
            if(gift == null)
            {
                m_sequence.RemoveAt(i);
                --i;
                continue;
            }
            
            var slot = new CardSlot(gift);
            slot.m_lockedDuration = m_refreshTime;
            m_slots.Add(slot);
            
            if(m_slots.Count == m_availableSlots)
                break;
        }
    }
    
    private void DestroySlots()
    {
        m_slotsChanged = true;
        m_slots.Clear();
    }
}


public class BCActionTavern : BCActionBase, ICardHolderChoiceSegment
{
    private static TavernSequence Sequence => GameManager.Me.m_state.m_tavernSequence;
    public static float PauseUpdateTime { get; set; }
    
    public string m_tavernID;
    [KnackField] public float m_refreshTime = 10 * 60;
    public List<CardSlot> Slots { get { return Sequence.Slots; } }

    public void OnChoiceCardUsed(NGBusinessGift _gift) => Sequence.OnChoiceCardUsed(_gift);
    public bool HaveSlotsChanged() => Sequence.HaveSlotsChanged();
    
    private static DebugConsole.Command s_killtimerscmd = new ("killtimers", _s =>
    {
        foreach(var slot in Sequence.Slots)
        {
            slot.m_lockedDuration = .1f;
        }
    });
    
    /*public static List<BCActionTavern> GetTaverns(string _tavernID)
    {
        _tavernID = _tavernID.ToLower();
        bool all = _tavernID.IsNullOrWhiteSpace();
        List<BCActionTavern> list = new ();
        var dispatchBuildings = MABuildingSupport.GetBuildingsWithComponent<BCActionTavern>();
        foreach(var dispatchBuilding in dispatchBuildings)
        {
            var taverns = dispatchBuilding.BuildingComponents<BCActionTavern>();
            foreach(var t in taverns)
            {
                if(all || _tavernID == t.m_tavernID)
                {
                    list.Add(t);
                }
            }
        }
        return list;
    }*/
    
    public override List<MACharacterBase> GetWorkersPresent()
    {
        var workersPresent = base.GetWorkersPresent();
        workersPresent.AddRange((NGManager.Me.m_MAWorkerList.FindAll(x =>
        {
            return x.m_state == NGMovingObject.STATE.MA_HANGOUT & IsInHangOutArea(x.transform.position);
        }) ?? new()));
        return workersPresent;
    }
    
    public override List<MACharacterBase> GetHeroesPresent()
    {
        var heroesPresent = base.GetHeroesPresent();
        heroesPresent.AddRange((NGManager.Me.m_MAHeroList.FindAll(x =>
        {
            return x.m_state == NGMovingObject.STATE.MA_HANGOUT & IsInHangOutArea(x.transform.position);
        }) ?? new()));
        return heroesPresent;
    }

		// RW-30-JUL-25: I believe the Tavern's relatively unique in that its GetWorkersPresent scoops up workers to add to m_workersPresent, without 
		// the worker necessarily having knowledge of it. This means that when the worker is picked up, m_insideMABuilding isn't set, so they don't call Leave.
		// This causes exceptions when a worker who's "hanging out" is picked up. Since the Tavern's taking responsibility for adding workers, it should
		// also take responsibility for removing them when necessary.
		protected override void UpdateWorkers()
		{
			for (var i = m_workersPresent.Count - 1; i >= 0; i--)
			{
				if (m_workersPresent[i].m_state == NGMovingObject.STATE.HELD_BY_PLAYER || m_workersPresent[i].InState(CharacterStates.HeldByPlayer))
				{
					Leave(m_workersPresent[i]);
				}
			}

			base.UpdateWorkers();
		}

    public static void RemoveCharacterFromSequence(string _tavernID, string _characterType)
    {
        Sequence.RemoveCharacterFromSequence(_characterType);
    }
    
    public static void AddCharacterToSequence(string _tavernID, string _characterType, int _count, int _priority)
    {
        Sequence.AddToSequence(_characterType, _count, _priority);
    }
    
    public static void RemoveFromSequence(string _genericType, int _level)
    {
        Sequence.RemoveFromSequence(_genericType, _level);
    }

    public static void AddToSequence(string _genericType, int _level, int _count, int _priority)
    {
        Sequence.AddToSequence(_genericType, _level, _count, _priority);
    }
    
    public static void ClearSequence()
    {
        Sequence.Clear();
    }
}