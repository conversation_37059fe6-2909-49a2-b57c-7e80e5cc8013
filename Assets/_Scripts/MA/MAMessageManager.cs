using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAMessageManager : MonoSingleton<MAMessageManager>
{
    [System.Serializable] public class TMPName
    {
        public string m_name;
        public int m_TMPIndex;
    }
    public List<TMPName> m_tmpNames = new List<TMPName>();
    public float m_showPopupTime = 5f;
    public float m_showPopupTimer;
    public List<WaitForEvent> m_waitForEvents = new ();
    public bool m_useDebugVoices= true;
    public Transform m_debugVoicesFolder;
    public bool IsPopupTimerFinished=> Time.time >= m_showPopupTimer;
    private DebugVoice[] m_debugVoices;
    private static Dictionary<string, int> s_spriteLookup = null;
    
    [System.Serializable] public class WaitForEvent
    {
        public WaitForEvent(string _audioString, Action _atEndAction, int _direction = 0, float _waitTime = 5f)
        {
            m_audioString = _audioString;
            m_action = _atEndAction;
            TriggerEventAudio(_audioString, _direction, _waitTime);
            Me.m_waitForEvents.Add(this);
        }

        public WaitForEvent(string _advisor, string _message, Action _atEndAction, int _direction, float _waitTime)
        {
            m_action = _atEndAction;
            TriggerEventDebugAudio(_advisor, _message, _direction, _waitTime);
            Me.m_waitForEvents.Add(this);
        }

        public WaitForEvent(MAQuestBase _quest, Action _atEndAction)
        {
            m_action = _atEndAction;
            MAMessageManager.Me.StartCoroutine(DismissMessageAfterQuestDialog(_quest));
            Me.m_waitForEvents.Add(this);
        }
     
        public string m_audioString;
        public Action m_action;
        private Coroutine m_dismissMessageCoroutine = null;
        
        
        public void TriggerEventDebugAudio(string _advisor, string _message, int _direction, float _waitTime)
        {
            var debugVoice =  Me.DebugVoiceMessage(_advisor, _message);
            if (debugVoice == null) _waitTime = 3; // muted
            if (_waitTime < 0)
                m_dismissMessageCoroutine = NGBusinessDecisionManager.Me.StartCoroutine(DismissMessageAfterDebugAudio(debugVoice));
            else
                m_dismissMessageCoroutine = NGBusinessDecisionManager.Me.StartCoroutine(DismissMessageAfterTime(_waitTime));
        }

        public void TriggerEventAudio(string _audioID, int _direction = 0, float _waitTime = 5f)
        {
            if (_audioID.IsNullOrWhiteSpace() == false)
            {
                string clip = GameManager.Me.PlaySoundIfCan(_audioID);
                if (clip.IsNullOrWhiteSpace() == false)
                {
                    int id = AudioClipManager.Me.PlayVO(clip, _direction: _direction);
                }
                m_dismissMessageCoroutine = NGBusinessDecisionManager.Me.StartCoroutine(DismissMessageAfterAudio(clip));
            }
            else
            {
                
                m_dismissMessageCoroutine = NGBusinessDecisionManager.Me.StartCoroutine(DismissMessageAfterTime(_waitTime));
            }
        }
        private IEnumerator DismissMessageAfterTime(float delay)
        {
            float elapsedTime = 0f;

            // Wait for either the delay to pass or Tab to be pressed
            while (elapsedTime < delay)
            {
                if (Input.GetKeyDown(KeyCode.Tab))
                {
                    break;
                }

                elapsedTime += Time.deltaTime;
                yield return null; // Wait for the next frame
            }

            // Invoke the action if it exists
            m_action?.Invoke();

            if (MAMessageManager.Me != null)
            {
                MAMessageManager.Me.m_waitForEvents.Remove(this);
            }
        }
        private IEnumerator DismissMessageAfterTimeOld(float _time)
        {
            yield return new WaitForSeconds(_time);
            if(m_action != null)
                m_action();
            MAMessageManager.Me.m_waitForEvents.Remove(this);
        }
        private IEnumerator DismissMessageAfterQuestDialog(MAQuestBase _quest)
        {
            while(MAQuestMessageDialog.s_current )
            {
                yield return null;
            }
            if(m_action != null)
                m_action();
            MAMessageManager.Me.m_waitForEvents.Remove(this);
        }

        private IEnumerator DismissMessageAfterAudio(string _clip)
        {
            yield return new WaitForSeconds(0.5f);

            while(AudioClipManager.Me.IsPlayingVO(_clip) && Input.GetKeyDown(KeyCode.Tab) == false)
            {
                yield return null;
            }
            if(m_action != null)
                m_action();
            MAMessageManager.Me.m_waitForEvents.Remove(this);
        }

        private IEnumerator DismissMessageAfterDebugAudio(DebugVoice _voice)
        {
            yield return new WaitForSeconds(0.5f);
            while (_voice.VoiceActive && Input.GetKeyDown(KeyCode.Tab) == false)
                yield return null;
            if (m_action != null)
                m_action();
            MAMessageManager.Me.m_waitForEvents.Remove(this);
        }
    }

    private void Start()
    {
        if (m_debugVoicesFolder == null)
        {
            var go = GameObject.Find("DebugVoices");
            if(go)
                m_debugVoicesFolder = go.transform;
        };
        if (m_debugVoicesFolder)
        {
            m_debugVoices = m_debugVoicesFolder.GetComponentsInChildren<DebugVoice>();
        }
    }

    public void ShowQuickTip()
    {
        m_showPopupTimer = Time.time + m_showPopupTime;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            b.ShowQuickTip();
        }    
    }

    public DebugVoice DebugVoiceMessage(string _advisor, string _message)
    {
        if(m_useDebugVoices == false) return null;
        var advisor = NGBusinessAdvisor.GetInfo(_advisor);
        if(advisor == null) return null;
        var voice = m_debugVoices.Find(x => x.m_characterName == advisor.m_name);
        if (voice == null) return null;
        voice.SetSpeech(_message);
        return voice;
    }

    public AudioSource GetAudioSource(string _advisor)
    {
        var advisor = NGBusinessAdvisor.GetInfo(_advisor);
        if (advisor == null) return null;
        var voice = m_debugVoices.Find(x => x.m_characterName == advisor.m_name);
        if (voice == null) return null;
        return voice.GetAudioSource();
    }
    
    private static int GetSpriteIndex(string _name)
    {
        if(_name.IsNullOrWhiteSpace())
            return -1;
            
        if(s_spriteLookup == null)
        {
            s_spriteLookup = new();
            foreach(var item in Me.m_tmpNames)
            {
                s_spriteLookup[item.m_name] = item.m_TMPIndex;
            }
        }
        if(s_spriteLookup.TryGetValue(_name, out int spriteIndex))
            return spriteIndex;
        return -1;
    }
    
    public static string GetLockIcon(int _day, string _text = null)
    {
        var index = GetSpriteIndex("Lock");
        if(index > -1)
            return $"<link=\"Lock:Unlocked Day {_day}\"><sprite index={index}> {_text}</link>";
        return "";
    }
    
    public static string GetTMPString(string _name, string _postFix = "", bool _raw = false)
    {
        var index = GetSpriteIndex(_name);
        if(index > -1)
        {
            if(_raw)
                return $"{index}{_postFix}";
             
            return $"<link=\"{index}{_postFix}\"><sprite index={index}{_postFix}></link>";
        }
        return "";
    }

    public static string GetTMPSpriteString(string _name)
    {
        var tmp = Me.m_tmpNames.Find(x => x.m_name == _name);
        if(tmp == null)
            return "";
        return $"<sprite={tmp.m_TMPIndex}>";
    }
    
    public enum ESlotState
    {
        Unoccupied,
        Occupied,
        Highlighted,
    }
    
    public static string GetTMPString(string _tmpType, ESlotState _state, bool _raw = false)
    {
        string str = "";
        switch(_state)
        {
            case ESlotState.Occupied:
                str = GetTMPString(_tmpType, " color=#ffffffff", _raw);
                break;
            case ESlotState.Highlighted:
                str = GetTMPString(_tmpType, " color=#0b7d06ff", _raw);
                break;
            case ESlotState.Unoccupied:
                str = GetTMPString(_tmpType, " color=#000000ff", _raw);
                break;   
        }
        
        return str;
    }
    
    public static string GetFullStar(string _color = "#ff7034ff") { return GetTMPString("StarFull", " color="+_color); }
    public static string GetOutlineStar() { return GetTMPString("StarOutline", " color=#ff7034ff"); }
    public static string GetWorkerIcon(ESlotState _state, bool _raw = false) { return GetTMPString("Worker", _state, _raw); }
    public static string GetBedroomIcon(ESlotState _state, bool _raw = false) { return GetTMPString("Bedroom", _state, _raw); }
    public static string GetHeroBedroomIcon(ESlotState _state, bool _raw = false) { return GetTMPString("HeroBedroom", _state, _raw); }
    public static string GetHeroIcon(ESlotState _state, bool _raw = false) { return GetTMPString("Hero", _state, _raw); }

}

