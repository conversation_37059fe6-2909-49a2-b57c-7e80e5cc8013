using UnityEngine;

public class MAEscortee : MACreatureBase
{
	//[<PERSON><PERSON>("MAEscortee")]
	override public string HumanoidType => "Escortee";
	public override EDeathType DeathCountType => EDeathType.None;

	public override bool IsTimeToGoHome
	{
		get { return false; }
	}

	public override bool IsEnemy
	{
		get { return false; }
	}
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void DoPossessedAction()
	{
		
	}
}
