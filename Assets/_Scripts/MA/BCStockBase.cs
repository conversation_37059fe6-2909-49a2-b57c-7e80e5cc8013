using System.Collections;
using System.Collections.Generic;

using TMPro;
using UnityEngine;

public class BCStockBase : BCBase
{
    private MAStockPositions m_stockPositions;
    [KnackField] public int m_maxStock = 1;
    [KnackField] public int m_stockPadCapacity = -1;
    
    private bool m_debugFoldoutStock = false;
    private bool m_debugFoldoutOrderStockItem = false;
    virtual protected CraneHandler Crane => null;

    protected bool UseCrane => true;
    override protected void Awake()
    {
        base.Awake();
        m_stockPositions = GetComponentInChildren<MAStockPositions>();
        var block = GetComponentInChildren<Block>();
        if (block == null) return;
        block.ShowFoundations(true);
    }

    override public void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        base.Activate(_indexOfComponentType, _quantityInBuilding);
        if (m_stockPositions == null)
        {
            Debug.LogError($"m_stockPositions is null on {m_building.name}");
            return;
        }
        m_stockPositions.SetCapacity(m_stockPadCapacity);
        
        // Create stock
        m_stockPositions.ClearStock();
        foreach(var stock in m_stock.Items)
        {
            if(stock.m_stock <= 0) continue;
            CreateStockBlock(stock.Resource, stock.Stock); 
        }
    }
    
    override public int GetStockSpace() => m_maxStock - m_stock.GetTotalStock();
    
    protected bool CanAcceptResource(NGCarriableResource _res, bool _isTest = false)
    {
        if(GetStockSpace() <= 0) return false;
        
        // 1. Make sure this type is accepted
        if(m_stock.IsCompatible(this, _res, m_building, _isTest) == false) return false;

        if (m_stockPositions == null)
        {
            Debug.LogError($"m_stockPositions is null on {m_building.name}");
            return false;
        }
        // 2. Check we have space on our pads
        var space = m_stockPositions.GetStockSpace();
        if (space == -1 || space > 0)
            return true;
        return false;
    }
    
    override public string GetDebugInfo() => $"{m_stock.GetTotalStock()}/{m_maxStock}";

    virtual public int CreateStockBlock(NGCarriableResource _resource, int _count = 1)
    {
        int created = 0;
        for(int i = 0; i < _count; i++)
        {
            var pickup = m_building.CreatePickupFromResource(_resource, null, true);
            
            if(pickup != null && m_stockPositions.PushStock(pickup) == false)
            {
                // Destroy it if we couldn't add it
                pickup.gameObject.SetActive(false);
                pickup.DestroyMe();
            }
            else
            {
                created++;
            }
        }
        return created;
    }

    override public bool HasDragContent()
    {
        var next = m_stockPositions.GetNextItem();
        return next != null && MAUnlocks.CanPickup(next);
    }
    
    override public GameObject GetDragContent()
    {
        var nextItem = m_stockPositions.GetNextItem();
        
        if(nextItem == null) return null;

        return TakePickup(nextItem.m_contents, false)?.gameObject;
    }
    
    override public ReactPickup TakePickup(NGCarriableResource _resource, bool _killPickup, System.Action<GameObject> _onComplete = null)
    {
        if(ConsumeStock(_resource) == false)
            return null;
        
        // Create a new pickup 
        return m_building.CreatePickupFromResource(_resource, GlobalData.Me.m_pickupsHolder, _killPickup, _onComplete);
    }
    
    override public NGCarriableResource ConsumeOrderProduct(BCActionOrderBase _orderDestination)
    {
        var item = m_stock.GetOrderProducts(_orderDestination);
        if(item != null && ConsumeStock(item))
        {
            return item;
        }
        return null;
    }
    
    override public bool ConsumeStock(NGCarriableResource _resource)
    {
        bool removedStock = false;
        
        var myItem = m_stock.Find(_resource);
        if (myItem == null || myItem.Stock <= 0)
            return false;
        
        if(m_stockPositions.PopStock(_resource))
        {
            myItem.Stock--;
            return true;
        }
        return false;
    }

    public void DestroyStock()
    {
        ClearAllStock();
    }
    
    //TODO: ejection mechanism. double check with design if/how/when
    virtual public void EjectAllStock()
    {
        while(true)
        {
            var nextItem = m_stockPositions.GetNextItem();
            if(nextItem == null)
                return;
            TakePickup(nextItem.m_contents, false, (_o) => FlyPickup(_o));
        }
    }
    
    override public bool AddPickup(ReactPickup _pickup)
    {
        var stockItem = _pickup.m_contents;

        if(m_stock.IsCompatible(this, stockItem, m_building) == false)
            return false;
        
        return CreateStock(stockItem);
    }

    public bool CreateStock(NGCarriableResource _res)
    {
        if(GetStockSpace() <= 0)
            return false;

        if(CreateStockBlock(_res, 1) == 1)
        {
            m_stock.AddOrCreateStock(_res, 1);

            if (NGCarriableResource.s_resourceAudioSwitch.ContainsKey(_res.m_name))
            {
                AudioClipManager.Me.SetSoundSwitch("ResourceType", NGCarriableResource.s_resourceAudioSwitch[_res.m_name], gameObject);   
            }
            else
            {
                AudioClipManager.Me.SetSoundSwitch("ResourceType", "Generic", gameObject);
            }

            AudioClipManager.Me.PlaySound("PlaySound_StockResourceArrive", gameObject);

            return true;
        }
        return false;
    }

    public void FlyPickup(GameObject _obj)
    {
        var pickup = _obj.GetComponent<ReactPickupPersistent>();
        pickup.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
        pickup.transform.position = transform.position + Vector3.up * 3;
        var velocity = Random.onUnitSphere * 8;
        velocity.y = 20;
        pickup.ThrownByPlayer(velocity);
        pickup.m_intendedDestination = m_building?.NotMe; // make sure this building doesn't accept t
    }

    public void ClearAllStock()
    {
        m_stockPositions.ClearStock();
        m_stock.ClearStock();
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCStockPanel(m_info));

    public class BCStockPanel : BCUIPanel
    {
        public override int Priority => 9;
        private List<BCStockBase> m_bases = new();
        
        public override string GetDescription()
        {
            int max = 0;
            int total = 0;
            
            foreach(var b in m_bases)
            {
                max += b.m_maxStock;
                total += b.m_stock.GetTotalStock();
            }
            return $"{total} / {max}";
        }

        public BCStockPanel(MAComponentInfo _info) : base(_info)
        {
        }

        public override void AddComponent(BCBase _component)
        {
            if (_component is BCStockBase)
            {
                m_bases.Add(_component as BCStockBase);
                base.AddComponent(_component);
            }
        }

        override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
        {
            var anyProduct = NGCarriableResource.GetInfo(NGCarriableResource.c_product);
            NGStock allStock = new NGStock();
            foreach(var b in m_bases)
            {
                foreach(var stock in b.m_stock.Items)
                {
                    if(stock.Stock <= 0) continue;
                    
                    if(stock.Resource.IsProduct)
                        allStock.AddOrCreateStock(anyProduct, stock.Stock);
                    else
                        allStock.AddOrCreateStock(stock.Resource, stock.Stock);
                        
                }
            }
            
            if(allStock.GetTotalStock() == 0)
            {
                yield return () => MADesignInfoSheetLine.Create(_holder, $"Empty",null, true);
            }
            else
            {
                foreach(var s in allStock.Items)
                {
                    yield return () => MADesignInfoSheetLine.Create(_holder, $"{s.Resource.TextSprite} {s.Resource.Name}", s.Stock.ToString(), true);
                }
            }            
        }
    }
}

/*
public class StockUIPanel : BCUIPanel
{
    public override string Title => 
    public override string Description => $"Stock In {m_totalStock} / {m_maxCapacity}";
        
    public int m_totalStock = 0;
    public int m_maxCapacity = 0;
        
    private string m_name;
        
    public StockUIPanel(string _name)
    {
        m_name = _name;
    }
}*/
