using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;

public class BCWorkers : BCWorkerBase
{
    public float m_nextDeliverTime = 0f;
    override public string GetDebugInfo() => $"{m_workersAllocated.Count}/{m_maxWorkers}";

    public override List<MACharacterBase> GetWorkersAllocated() => m_workersAllocated;
    public override List<MACharacterBase> GetWorkersPresent() => m_workersPresent;
    
    public override void PreUpdate(BuildingComponentsState _state)
    {
        foreach(var w in m_workersPresent)
        {
            w.SetPowerAvailable(w.ProductionPower*Time.deltaTime);
        }
        base.PreUpdate(_state);
    }

    public override bool Allocate(MACharacterBase _worker)
    {
        if(base.Allocate(_worker))
        {
            _worker.Job = this;
            return true;
        }
        return false;
    }

    public override bool Arrive(MACharacterBase _worker)
    {
        if(base.Arrive(_worker))
        {
            _worker.NGSetAsWorking(false);
            return true;
        }
        return false;
    }

    override public void UpdateInternal(BuildingComponentsState _state)
    {
        base.UpdateInternal(_state);
        UpdateWorkers();
        UpdateCarryResourceToBuilding();
    } 

    void UpdateWorkers()
    {
        if(m_workersPresent.Count == 0) return;
        
        Building.m_isInhabited = true; 
        
        for (var i = m_workersPresent.Count - 1; i >= 0; i--)
        {
            var worker = m_workersPresent[i];

            if(worker.IsTimeToHangout && worker.SendToHangOut())
            {
                Leave(worker);
            }
            else if(worker.IsTimeToGoHome || worker.IsTimeToHangout)
            {
                if(m_building.WorkerArrivesToRest(worker))
                {
                }
                else if(worker.Home != null)
                {
                    worker.SetMoveToComponent(worker.Home, PeepActions.ReturnToRest);
                }
                else
                {
                    worker.SetMoveToPosition(m_building ? m_building.GetDoorPos() : GetDoorPos(), false, PeepActions.WaitingForHome);   
                }
                Leave(worker);
            }
        }
    }
    
    public void UpdateCarryResourceToBuilding()
    {
        var worker = GetAvailableWorker();
        
        if(worker != null)
        {
            var bestStockDestination = m_building.GetBestStockDestination();

            SendOutToDeliver(bestStockDestination, worker);
        }
    }
    
    private bool SendOutToDeliver(MABuilding.BestStockDestinationCheck _destination, MACharacterBase _character)
    {
        if(_destination.Empty) return false;
        
        var pickup = m_building.TakeItemForDelivery(_destination.m_supplyWhat, (o) => {
            var item = o.GetComponent<ReactPickupPersistent>();
            item.m_intendedDestination = _destination.m_building;
            item.AssignToCarrier(_character); // do this after SetMoveToBuilding so the object is enabled and animator is valid
        });
                
        if(pickup != null)
        {
            m_nextDeliverTime = Time.time + NGManager.Me.m_workerLeavesDelay;
            _character.SetMoveToBuilding(_destination.m_building, PeepActions.DeliverToInput);
            Leave(_character);
            return true;
        }
        return false;
    }

    override public MACharacterBase GetAvailableWorker()
    {
        if (Time.time < m_nextDeliverTime)
            return null;
            
        foreach(var w in m_workersPresent)
        {
            if(w.PowerAvailable <= 0f || w.IsLowEnergy()) continue;
            
            return w;
        }
        return null; 
    }
    
    override public float GetWorkerPower()
    {
        float power = 0f;
        foreach(var wp in m_workersAllocated)
        {
            power += wp.PowerAvailable;
        }
        return power;
    }
    
    /*override public bool ConsumePower(ref float _power)
    {
        var usedPower = false;
        foreach(var wp in m_workersPresent)
        {
            float powerToConsume = Mathf.Min(wp.PowerAvailable, _power);
            if(powerToConsume > 0f)
            {
                wp.ConsumePower(powerToConsume);
                _power -= powerToConsume;
            }
            
            if(_power <= 0) return true;
        }
        return false;
    }*/
    
    //override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (BCWorkerPanel.PanelID, () => new BCWorkerPanel());
}

public class BCWorkerPanel : BCUIPanel
{
    public const string PanelID = "workers";
    public List<BCWorkerBase> m_bcWorkers = new();
    public override bool ForceQuantityDisplay => true;

    public BCWorkerPanel() : base(PanelID, "Workstations") 
    {
        // Todo, description needs to be icons
    }
    
    public override string GetDescription()
    {
        BCBase.CombinedStat stat = null;
        foreach (var c in m_bcWorkers) c.GetCombinedValue(ref stat);
        return stat?.GetValue();
    }

    public override void AddComponent(BCBase _component)
    {
        var wc = _component as BCWorkerBase;
        if(wc != null)
        {
            m_bcWorkers.Add(wc);
            m_quantity += wc.m_maxWorkers;
            m_all.Add(wc);
        }
        // Dont call base
    }

    override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
    {
        bool createdTitle = false;
        List<MACharacterBase> characters = new();
        foreach(var workerBase in m_bcWorkers)
        {
            foreach(var character in workerBase.m_workersAllocated)
            {
                if(characters.Contains(character)) continue;
                if(character.Job?.Building != workerBase.Building)
                    continue; 
                
                if(createdTitle == false)
                {
                    MABuildingWorkerPanelLine.CreateTitle(_holder);
                    createdTitle = true;
                }
                
                characters.Add(character);
                yield return () => MABuildingWorkerPanelLine.Create(_holder, character);
            }
        }
        if(characters.Count == 0)
        {
            yield return () => MADesignInfoSheetLine.Create(_holder, "No workers allocated", null, true);
        }
    }
}