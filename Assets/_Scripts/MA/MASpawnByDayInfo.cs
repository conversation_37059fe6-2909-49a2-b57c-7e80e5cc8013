using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class MASpawnByDayInfo  : IEquatable<MASpawnByDayInfo>
{ 
    public static List<MASpawnByDayInfo> s_creatureSpawnByDayInfos = new List<MASpawnByDayInfo>();
    public static List<MASpawnByDayInfo> GetList=>s_creatureSpawnByDayInfos;

    public string DebugDisplayName => $"Day[{m_dayNum:D2}] Creature [{m_creature}] Delay[{m_spawnTimeSeconds}] SpawnType{m_creatureSpawnType} at {m_spawnLocation} to {m_destination}";
    public bool m_debugChanged;
    public static Dictionary<int, List<MASpawnByDayInfo>> s_spawnByDayInfosSortedByDay = new();
    public static List<List<MASpawnByDayInfo>> s_spawnByDayInfosSortedByLocation = new List<List<MASpawnByDayInfo>>();
		public static List<List<MASpawnByDayInfo>> s_spawnByDayInfosDailyOneShotDayAndLocationGrouped = new List<List<MASpawnByDayInfo>>();
    
    public enum CreatureSpawnType
    {
        None = -1,
        OneShot,
        Respawn,
				DailyOneShot,
    }
    
    public string id;
    public string m_dayNumString;
    public int m_dayNum;
    public int m_spawnTimeSeconds;
    public int m_repeatDelaySeconds;
    public int m_count;
    
    [ScanField] public string m_creature;
    
    public string m_type;
    public string m_spawnLocation;
    public string m_destination;
    public int m_numStationary;
    
    public MACreatureInfo CreatureInfo => MACreatureInfo.GetInfo(m_creature);
    public CreatureSpawnType m_creatureSpawnType = CreatureSpawnType.None;

    public Vector3 SpawnLocationPos
    {
        get
        {
            if (m_spawnLocation.IsNullOrWhiteSpace()) return Vector3.zero;
            if (GetPosFromString(m_spawnLocation, out Vector3 _outPos))
                return _outPos;
            return Vector3.zero;
        }    
    }
    
    public Vector3 DestinationPos
    {
        get
        {
            if (m_destination.IsNullOrWhiteSpace()) return Vector3.zero;
            if (GetPosFromString(m_destination, out Vector3 _outPos))
                return _outPos;
            return Vector3.zero;
        }
    }
    
    public static bool PostImport(MASpawnByDayInfo _what)
    {
        var dayNum = _what.m_dayNumString.Split(':');
        if(dayNum.Length < 2)
        {
            Debug.LogError($"Invalid dayNumString - {_what.m_dayNumString}");
            return false;
        }
        if (int.TryParse(dayNum[1], out  _what.m_dayNum) == false)
        {
            Debug.LogError($"m_dayNumString - Bad Number - : {_what.m_dayNumString}");
            return false;
        }
        if (CreatureSpawnType.TryParse(_what.m_type, out CreatureSpawnType _outValSpawnType))
        {
            _what.m_creatureSpawnType = _outValSpawnType;
        }
        else
        { 
            _what.m_creatureSpawnType = CreatureSpawnType.None;
            //Debug.LogError($"MASpawnByDay - PostImport - Failed to parse spawn type: {_what.m_type}");
            return false;
        }

        if (_what.m_spawnLocation.IsNullOrWhiteSpace())
        {
            //Debug.LogError($"MASpawnByDay - PostImport - Spawn Location is blank: {_what.m_type}");

            return false;
        }
        return true;
    }

    public static bool GetPosFromString(string _location, out Vector3 _outPos)
    {
        _outPos = Vector3.zero;

        if (_location.IsNullOrWhiteSpace()) return false;
        
        //In the design the location could be a refence to a spawn point list

        object obj = MAParserSupport.ConvertPos(new string[] {"", _location});
        if (obj != null)
        {
            _outPos = (Vector3) obj;
            return true;
        }

        var bits = _location.Split(new char[] {'[', ']'}, StringSplitOptions.RemoveEmptyEntries);
        if (bits.Length < 2) return false;
        _location = bits[1];

        if (bits[0] == "Building")
        {
            obj = MAParserSupport.ConvertBuilding(new string[] {"", _location});
            if (obj != null)
            {
                _outPos = ((MABuilding) obj).DoorPosOuter;
                return true;
            }
        }

        if (bits[0] == "Decoration")
        {
            obj = MAParserSupport.ConvertDecoration(new string[] {"", _location});
            if (obj != null)
            {
                _outPos = ((NGDecoration) obj).transform.position;
                return true;
            }
        }

				if (bits[0] == "NamedPoint")
        {
            obj = MAParserSupport.ConvertNamedPoint(new string[] {"", _location});
            if (obj != null)
            {
                _outPos = ((NamedPoint)obj).transform.position;
                return true;
            }
        }
        return false;
    }

    
    
    public static List<MASpawnByDayInfo> LoadInfo()
    {
        s_creatureSpawnByDayInfos = NGKnack.ImportKnackInto<MASpawnByDayInfo>(PostImport);
        s_spawnByDayInfosSortedByDay = new();// List<List<MASpawnByDayInfo>>();

        foreach (var spawnByDay in s_creatureSpawnByDayInfos)
        {
            // RW-13-MAR-25: DailyOneShot spawns shouldn't be getting into this list,
            // they've got a different ruleset.
            if (spawnByDay.m_creatureSpawnType != CreatureSpawnType.DailyOneShot)
            {
                List<MASpawnByDayInfo> dayGroups = null;
                if (s_spawnByDayInfosSortedByDay.TryGetValue(spawnByDay.m_dayNum, out dayGroups) &&
                    dayGroups.Count > 0)
                {
                }

                if (dayGroups == null)
                {
                    dayGroups = new List<MASpawnByDayInfo>();
                    s_spawnByDayInfosSortedByDay.Add(spawnByDay.m_dayNum, dayGroups);
                }

                dayGroups.Add(spawnByDay);
            }
        }

				foreach (var spawnByDay in s_creatureSpawnByDayInfos)
        {
					if (spawnByDay.IsAmbientSpawn())
					{
            List<MASpawnByDayInfo> dayAndLocationGroups = s_spawnByDayInfosDailyOneShotDayAndLocationGrouped.Find(o => 
            { 
                return o.Count > 0 && o[0].m_dayNum == spawnByDay.m_dayNum && o[0].m_spawnLocation == spawnByDay.m_spawnLocation;
            });
            
            if(dayAndLocationGroups == null)
            {
                dayAndLocationGroups = new List<MASpawnByDayInfo>();
                s_spawnByDayInfosDailyOneShotDayAndLocationGrouped.Add(dayAndLocationGroups);
            }
            dayAndLocationGroups.Add(spawnByDay);
					}
        }
        
        foreach (var spawnByDay in s_creatureSpawnByDayInfos)
        {
          // RW-13-MAR-25: DailyOneShot spawns shouldn't be getting into this list,
          // they've got a different ruleset.
          if (!spawnByDay.IsAmbientSpawn())
          { 
            List<MASpawnByDayInfo> locationGroups = s_spawnByDayInfosSortedByLocation.Find(o =>
            {
                return o.Count > 0 && o[0].m_spawnLocation == spawnByDay.m_spawnLocation;
            });
            
            if(locationGroups == null)
            {
                locationGroups = new List<MASpawnByDayInfo>();
                s_spawnByDayInfosSortedByLocation.Add(locationGroups);
            }
            locationGroups.Add(spawnByDay);
          }
        }
        
        return s_creatureSpawnByDayInfos;
    }

    public static List<MASpawnByDayInfo> GetInfo(int _dayNum) => s_creatureSpawnByDayInfos.FindAll(o => o.m_dayNum.Equals(_dayNum));

		public bool Equals(MASpawnByDayInfo _other)
		{
			return AreEqual(this, _other);
		}

		public static bool AreEqual(MASpawnByDayInfo _a, MASpawnByDayInfo _b)
		{
			return _a.id == _b.id;
		}

	// RW-09-JUN-25: These are spawns in the world that aren't tied to the whole zombies and night and all that.
	// e.g. bandits. This distinction may not always be appropriate to keep, but it's heplful to capture the current ruleset.
	public bool IsAmbientSpawn()
	{
		return m_creatureSpawnType == CreatureSpawnType.OneShot || m_creatureSpawnType == CreatureSpawnType.DailyOneShot;
	}
}