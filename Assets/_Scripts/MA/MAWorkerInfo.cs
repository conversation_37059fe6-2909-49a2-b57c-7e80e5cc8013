using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class MAWorkerInfo : MAMovingInfoBase
{
    public const string WorkerPrefabPath = "_Prefabs/Workers/";
    public const string CharacterPrefabPath = "_Prefabs/Characters/";
    public static List<MAWorkerInfo> s_workerInfos = new List<MAWorkerInfo>();
    public static List<MAWorkerInfo> GetList=>s_workerInfos;
    public string DebugDisplayName => m_name;

    override public float DeathMana => m_mana;
    
    public enum WorkerTypeEnum
    {
        None = -1,
        Worker,
        Craftsman,
        Tourist,
        QuestGiver,
        <PERSON><PERSON><PERSON>,
        Player,
        Worshipper,
    }

    public enum WorkerGender
    {
        Male,
        Female,
    }
    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_gender;
    public string m_displayName;
    public string m_workerType;
    public string m_subType;
    public string m_prefabName;
    public int m_level;
    public GameObject m_prefab;
    public string m_characterPrefabName;
    public GameObject m_characterPrefab;
    public float m_attack;
    public float m_lowWalkSpeed;
    public float m_highWalkSpeed;
    public bool m_autoFindJob;
    public bool m_autoFindHome;
    public float m_lowSkinColor;
    public float m_highSkinColor;
    public float m_lowBodyColor;
    public float m_highBodyColor;
    public float m_lowHairColor;
    public float m_highHairColor;
    public float m_lowOverallScale;
    public float m_highOverallScale;
    public float m_lowHeightScale;
    public float m_highHeightScale;
    public float m_lowFatScale;
    public float m_highFatScale;
    public float m_tavernCost;
    public float m_tavernCostPerWorkerMultiplier;
    public float m_health;
    public float m_mana;
    public string m_description;
    public string m_spritePath;
    public float m_productionPower;
    public float m_energy;
    
    public string m_persona;
    public string m_likes;
    public string m_dislikes;
    public int m_experience;

    public string m_defaultArmour;
    
    public float m_possessWalkSpeed;
    public float m_possessRunSpeed;
    override public float PossessWalkSpeed => m_possessWalkSpeed;
    override public float PossessRunSpeed => m_possessRunSpeed;
    
    public bool IsMale => m_gender == "Male";
    public WorkerTypeEnum WorkerType
    {
        get
        {
            WorkerTypeEnum type = WorkerTypeEnum.None;
            Enum.TryParse(m_workerType, out type);
            return type;
        }
    } 
   // public float m_health; //TODO: add to knack!
   
    public static bool PostImport(MAWorkerInfo _what)
    {
        if (_what.m_prefabName.IsNullOrWhiteSpace() == false)
        {
            _what.m_prefab = Resources.Load<GameObject>(WorkerPrefabPath + _what.m_prefabName);
        }
        if(_what.m_characterPrefabName.IsNullOrWhiteSpace() == false)
        {
            _what.m_characterPrefab = Resources.Load<GameObject>(CharacterPrefabPath + _what.m_characterPrefabName);
        }
        _what.m_deathDropOptions = _what.m_deathDropFavour.Split("<br />");
        return true;
    }
    public static List<MAWorkerInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_workerInfos = NGKnack.ImportKnackInto<MAWorkerInfo>(PostImport);
            return s_workerInfos;
    }
    public float GetRandomWorkerSpeed() => UnityEngine.Random.Range(m_lowWalkSpeed, m_highWalkSpeed);
    public static List<MAWorkerInfo> GetInfoByType(WorkerTypeEnum _type) => s_workerInfos.FindAll(o => o.m_workerType.Equals(_type.ToString()));
    public static List<MAWorkerInfo> GetInfoByType(string  _type) => s_workerInfos.FindAll(o => o.m_workerType.Equals(_type)); 
    public static MAWorkerInfo GetInfo(string _name) => s_workerInfos.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));

}
