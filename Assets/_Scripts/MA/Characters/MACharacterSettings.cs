using UnityEngine;

[CreateAssetMenu(fileName = "MACharacterSettings", menuName = "Scriptable Objects/MACharacterSettings")]
public class MACharacterSettings : ScriptableObject
{
	public bool m_canBleed = true;
	public bool m_staggerFromEveryHit = true;
	public bool m_canDestroyWalls = false;
	/// <summary>
	/// Character will disregard knack target data types and attack any enemy that attacks its leader
	/// </summary>
	public bool m_isBodyGuard = false;
	public float m_mournTime = 5f;

	[Range(0f, 20f)]
	public float m_directionCorridorWidth = 0f; //TS - 0 = no corridor, 20 = wide corridor
    
	[Header("Combat")]
	[Range(0f, 5f)] public float m_attackCooldown = 1f;//far attack cool down?
	[Range(0f, 5f)] public float m_staggerCooldown = 1f;
	[Range(0f, 10f)] public float m_petrifiedStateMinDuration = 2.5f;
	
	public float m_damageFeltStep = 0.1f; //general damage effect sensitivity
	public float m_damageFeltRagdoll = 0.25f; //knockdown sensitivity
	public float m_damageFeltStagger = 0.15f; //stagger sensitivity
	
	[Range(1, 50)] public float m_addGravityFactor = 15f;
	//[Range(0f, 5f)] public float m_maxAttackDuration = 2.5f;//knack, how long an attack takes
	[Range(0f, 3f)] public float m_homeDistanceSq = 0.5f;//distance to home when despawn is allowed.
	//public ForceMode m_physicsHitForceMode = ForceMode.Impulse;//temp to try out hit-smash effects

	public int m_bloodEmitCountMax = 4;
	// public float m_damageCollisionRelativeVelocityMax = 0.6f;
	// [Range(0f, 1000f)]
	// public float m_attackForce = 210f;
	// [Range(0f, 1000f)]
	// public float m_angularAttackForce = 210f;

	[Header("Other")]
	[Range(0f, 1.5f)] 
	public float m_jumpDelay = 0.0f;
	//[Range(0f, 10f)] 
	//public float m_jumpAnimDuration = 1.33f;//temp setting until we have anims
	
	public float m_jumpDuration = 1.75f; //WEREWOOOOLF
	[Header("Death Stages")]
	public float m_deadTime = 5f;
	public float m_deadDisappearTime = 4f;
	public float m_dyingTime = 3f;

	[Header("Roam For Target && Guard Mode ")]
	public float m_roamingStandAroundMinTime = 1f;
	public float m_roamingStandAroundMaxTime = 5f;
	public float m_roamingWalkAroundMinTime = 5f;
	public float m_roamingWalkAroundMaxTime = 8f;
	
	[Header("Patrol Walls")]
	public float m_patrolWallsStandAroundMinTime = 1f;
	public float m_patrolWallsStandAroundMaxTime = 5f;
	public float m_patrolWallsWalkAroundMinTime = 20f;
	public float m_patrolWallsWalkAroundMaxTime = 40f;
	
	[Header("Guard Mode")]
	/// <summary>
	/// Overrides Search and Vision Radii for guard mode, acts as default when no saved radius is found
	/// </summary>
	public float m_guardModeOverrideRadius = 0f;
	public float m_guardModeMaxRadius = 50f;
	public float m_guardModeMinRadius = 3f;
	public float m_followModeStayRadius = 0f;
	public float m_followModeDistance = 0f;

	
	[Header("Health Bars")]
	[Tooltip("In PossessedMode this is the distance from the 'unpossessed' to the possessed. E.g. if this is a zombie's value -> then, if you possess a hero, you'd see the HB if within this distance. i.e. each character can define its own visibility")]
	[Range(1f, 300f)]
	public float m_possessedHealthBarVisionRadius = 25f; //knack?
	[Tooltip("In God Mode this is the distance of this character in relation to its closest attacker. if within radius health bar is shown")]
	[Range(1f, 300f)]
	public float m_godModeHealthBarVisionRadius = 25f; //knack?
	
	[Range(0.00f, 0.99f)]
	public float m_healAtHomeHealthThreshold = 0.5f; //knack?

	// [Range(0.00f, 100f)] 
	// public float m_patrolAttackRadius = 20f;
	
	[Header("Possession Mode")]
	public bool m_possessionModeNeverStrafe = false;
	
	[Header("Rest, Play")]
	public float m_raiseWantsToPlayPerSecond = 0.005f;

	[Header("Eat")]
	public bool m_canEatPickups = false;
	public float m_eatRadius = 8f;
	
	[Header("Pee")]
	public bool m_canPee = false;
	public float m_peeRadius = 8f;
	public float m_peeRange = 0.5f;
	public bool m_peeToRight = true;
	
	[Header("Poo")]
	public bool m_canPoo = false;
	public float m_pooRadius = 8f;

	[Header("Stuck Check")]
	public float m_noNavCheckTime = -1f;
}
