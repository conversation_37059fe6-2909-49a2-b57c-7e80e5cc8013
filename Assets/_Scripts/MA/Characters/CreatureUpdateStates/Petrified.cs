using System;
using UnityEngine;

namespace MACharacterStates
{
    [Serializable]
    public class Petrified : CommonState
    {
        public Petrified(string _state, MACharacterBase _character) : base(_state, _character)
        {
        }

        public override void OnEnter()
        {
            base.OnEnter();

            m_character.BlendAnimatorLayerWeight("Cower", 1);
            m_character.m_nav.PushPause("petrified", true, true);
            m_character.SetCanBePushed(false);

            var attacker = m_character.GetTargetedByNearbyCreature();
            if (attacker != null)
                m_character.LookAt(attacker.transform.position);
        }

        public override void OnUpdate()
        {
            base.OnUpdate();

            if (m_gameStateData.m_timeInState < m_character.CharacterSettings.m_staggerCooldown)
                return;

            if (m_character.GetTargetedByNearbyCreature() != null)
                return;

            if (EvaluateTarget() == false)
            {
                ApplyState(CharacterStates.RoamForTarget);
            }
        }

        public override void OnExit()
        {
            base.OnExit();

            m_character.BlendAnimatorLayerWeight("Cower", 0);
            m_character.m_nav.PopPause("petrified");
            m_character.SetCanBePushed(true);
        }
    }
}