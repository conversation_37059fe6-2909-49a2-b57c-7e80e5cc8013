using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class CreatureHarvesting : CommonState
	{
		private enum HarvestingPhase
		{
			None = 0,
			Intro,
			Loop,
			Wait,
			Outro
		};
		
		private HarvestingPhase harvestingPhase = HarvestingPhase.None;
		private HarvestingPhase newHarvestingPhase = HarvestingPhase.None;
		private BCChopObject chopObject = null;
		private ReactPickupPersistent pickupResource = null;

		private float timeEvaluateTarget = 0f;

		public CreatureHarvesting(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();

			chopObject = null;
			pickupResource = null;
			harvestingPhase = HarvestingPhase.None;
			newHarvestingPhase = HarvestingPhase.None;

			m_character.m_nav.Unpause();

			if (m_character.Carrying != null)
			{
				BringPickupHome();

				return;
			}

			int hotspotId = m_character.CharacterGameState.m_harvestHotspotId;
			if ((m_character.m_harvestHotspot == null) && (hotspotId >= 0))
				m_character.m_harvestHotspot = TreeHolder.GetTreeObject(hotspotId);
			
			if (m_character.m_harvestHotspot != null)
			{
				var treeHolder = m_character.m_harvestHotspot.GetComponent<TreeHolder>();
				if ((treeHolder == null) || treeHolder.IsLocked)
					m_character.m_harvestHotspot = null;
				
				var co = m_character.m_harvestHotspot.GetComponent<BCChopObject>();
				if (co != null)
				{
					if ((co.m_worker != null) && (co.m_worker != m_character))
						m_character.m_harvestHotspot = null;
				}
			}
			if (m_character.m_harvestHotspot == null)
			{
				foreach (var pickup in GameManager.Me.m_state.m_pickups)
				{
					var pickupObject = pickup.Pickup;
					if (pickupObject.IsReservedBy(m_character))
					{
						m_character.SetMoveToObject(pickupObject.gameObject, PeepActions.CollectPickup, pickupObject.transform.right * 0.5f);
						pickupResource = pickupObject;
					
						return;
					}
				}
				
				SetupHarvestingHotspot();
			}
			if (m_character.m_harvestHotspot != null)
			{
				var treeHolder = m_character.m_harvestHotspot.GetComponent<TreeHolder>();
				chopObject = m_character.m_harvestHotspot.GetComponent<BCChopObject>();
				if (chopObject == null)
					chopObject = m_character.m_harvestHotspot.AddComponent<BCChopObject>();
				chopObject.m_worker = m_character;
				m_character.CharacterGameState.m_harvestHotspotId = treeHolder.m_treeIndex;
				m_character.SetMoveToObject(m_character.m_harvestHotspot, PeepActions.Chop, m_character.m_harvestHotspot.transform.right * 2f);
				newHarvestingPhase = HarvestingPhase.Intro;
			}
			else if (pickupResource == null)
			{
				ApplyState(CharacterStates.GoingHome);
			}
		}

		private void SetupHarvestingHotspot()
		{
			m_character.m_harvestHotspot = TerrainPopulation.Me.NearestInstanceToPosition(m_character.transform.position, m_character.CreatureInfo.m_visionRadius, true,
				(_inst) => {
					var chopObject = _inst.GetComponent<BCChopObject>();
					if (chopObject != null)
					{
						if (!chopObject.IsChopObjectValid)
							return false;
						if (chopObject.IsChoppedDown)
							return false;
						if (chopObject.m_worker != null)
							return false;
					}

					var holder = _inst.GetComponent<TreeHolder>();
					if (holder == null) return false;
					if (!_inst.activeSelf) return false;
					if (string.IsNullOrEmpty(holder.m_treeType))
						return false;
					if(holder.GetPrefab() == null)
						return false;
					if(holder.IsLocked)
						return false;
					
					return true; // TODO - add a check for m_chopLeft on game object _inst here
				});
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (Time.time > timeEvaluateTarget)
			{
				if (EvaluateTarget())
					return;

				timeEvaluateTarget = Time.time + 1f;
			}

			if (!HasAgentArrived())
				return;
			
			if (pickupResource != null)
			{
				pickupResource.AssignToCarrier(m_character, true, true);
				ApplyState(CharacterStates.GoingHome);

				return;
			}

			if (chopObject != null)
			{
				if (harvestingPhase != newHarvestingPhase)
				{
					harvestingPhase = newHarvestingPhase;
					SetupChopPhase();
				}

				return;
			}

			ApplyState(PatrolOrReturnToGuard);
		}

		public override void OnExit()
		{
			base.OnExit();

			m_character.PeepAction = PeepActions.None;
			if (chopObject != null)
				chopObject.m_worker = null;
		}

		protected override string GetAttackStateForTarget()
		{
			TargetObject targetObject = m_character.TargetObject;
			if (targetObject != null)
			{
				bool isVisible = m_character.IsTargetVisible(targetObject.transform.position, out bool isTargetWithinVisionRadius);
				if (isVisible)
				{
					var attack = m_character.GetNextAttackAvailableInCombo();
					var aoc = AttackOrChase(attack);
					if (!string.IsNullOrEmpty(aoc))
						return aoc;
					
					return CharacterStates.ChaseTarget;
				}
			}

			return State;
		}

		private void SetupChopPhase()
		{
			if (harvestingPhase == HarvestingPhase.Intro)
			{
				m_character.LookAt(chopObject.transform.position);

				var animIntro = chopObject.GetChopIntroAnimation();
				if (animIntro != null)
				{
					m_character.PlaySingleAnimation(animIntro, (c) => { StartChopLoopPhase(); }, true, false, 1f);
				}
				else
				{
					StartChopLoopPhase();
				}
			}
			else if (harvestingPhase == HarvestingPhase.Loop)
			{
				m_character.LookAt(chopObject.transform.position);
				
				var animLoop = chopObject.GetChopLoopAnimation();
				if (animLoop != null)
				{
					m_character.PlaySingleAnimation(animLoop, null, true, false, 1f);

					// Set both chopObjectPhase and newChopObjectPhase to ChopObjectPhase.Wait
					// to prevent HarvestingPhase.Wait phase from baing saved, so the restored
					// phase is always HarvestingPhase.Loop
					harvestingPhase = HarvestingPhase.Wait;
					newHarvestingPhase = HarvestingPhase.Wait;
				}
				else
				{
					ApplyChoppingIteration();
				}
			}
			else if (harvestingPhase == HarvestingPhase.Outro)
			{
				var animOutro = chopObject.GetChopOutroAnimation();
				if (animOutro != null)
				{
					m_character.PlaySingleAnimation(animOutro, (c) => { BringPickupHome(); }, true, false, 1f);
				}
				else
				{
					BringPickupHome();
				}
			}
		}

		private void StartChopLoopPhase()
		{
			newHarvestingPhase = HarvestingPhase.Loop;
		}

		public void ApplyChoppingIteration()
		{
			if (chopObject == null)
			{
				m_character.StopWorkerLoopAnimation();
				ResetChopState();
			}
		}

		public void ResourceHit()
		{
			if (chopObject == null)
			{
				Debug.LogError("MAWorker - ChopObject - m_destinationObject == null");
				return;
			}
			
			float chopAmount = Mathf.Min(chopObject.ChopLeft, 0.1f);
			bool collectResource = chopObject.HitObject(chopAmount);
			if(collectResource || chopObject.IsChoppedDown)
			{
				m_character.StopWorkerLoopAnimation();
				if(collectResource)
					CollectChoppedResourse();
				newHarvestingPhase = HarvestingPhase.Outro;
			}
		}

		private void CollectChoppedResourse()
		{
			ResetChopState();
			if (chopObject != null)
			{
				var resource = chopObject.GetCarriableResourceProduced();
				if(resource.IsNone == false)
				{
					var pickup = ReactPickupPersistent.CreateItem(null, resource, 1, null);
					pickup.AssignToCarrier(m_character, true, true);
					pickup.gameObject.SetActive(false);
				}
			}
		}

		private void BringPickupHome()
		{
			ResetChopState();
			
			if (m_character.Carrying != null)
				m_character.Carrying.gameObject.SetActive(true);
			
			ApplyState(CharacterStates.GoingHome);
		}

		private void ResetChopState()
		{
			if (chopObject != null)
				chopObject.m_worker = null;
			m_character.m_harvestHotspot = null;
			m_character.CharacterGameState.m_harvestHotspotId = -1;

			harvestingPhase = HarvestingPhase.None;
			newHarvestingPhase = HarvestingPhase.None;
		}
	}
}
