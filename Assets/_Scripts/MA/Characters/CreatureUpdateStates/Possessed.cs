using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class Possessed : CommonState
	{
		private bool m_didPossessDuringLoad;
		public Possessed(string _state, MACharacterBase _character) : base(_state, _character)
		{
			_character.m_nav.Unpause();
			m_didPossessDuringLoad = GameManager.Me.LoadComplete == false;
		}

		public override void OnEnter()
		{
			base.OnEnter();

			if (GameManager.Me.IsPossessing == false)
			{
				GameManager.Me.PossessObject(m_character, _skipPossessSequence: m_didPossessDuringLoad);
			}
			m_character.m_nav.Unpause();
			
			m_character.BlendAnimatorLayerWeight("Combat", 1);
			m_character.SetCanBePushed(true);
			m_character.SetTargetObj(null);
			m_character.LastPatrolPath = null;
			m_character.m_comboStateUpdate += ComboStateUpdated;
		}

		public void ComboStateUpdated()
		{
			m_character.m_onCollisionWithTarget -= OnPossessedAttackImpact;
			m_character.m_onRangeAttackFired -= OnPossessedRangeAttackFired;
			if (m_character.CurrentAttack != null)
			{
				if (m_character.CurrentAttack.IsProjectile)
					m_character.m_onRangeAttackFired += OnPossessedRangeAttackFired;
				else
					m_character.m_onCollisionWithTarget += OnPossessedAttackImpact;
			}
		}

		private TargetObject m_targetObject = null;

		public override void OnUpdate()
		{
			base.OnUpdate();
			float walkingXPThisFrame = Mathf.Sqrt(m_character.m_nav.m_sqrDistanceMovedLastFrame) *
			                           m_character.CreatureInfo.m_walkingExp;
			if (walkingXPThisFrame - Single.Epsilon * 8 <= 0)
			{
				walkingXPThisFrame = 0;
			}
			
			m_character.AddExperience(walkingXPThisFrame, "possessedMove");
			m_character.BlendAnimatorLayerWeight("Combat", 1);

			if (m_character.TimeSinceLastTargetRefresh >= 5f && m_character.TargetObject != null)
			{
				m_character.SetTargetObj(null);
			}
		}

		private float m_timeTargetingSameObject = 0f;

        public override void OnExit()
        {
            base.OnExit();
			m_character.m_comboStateUpdate -= ComboStateUpdated;

			m_character.m_onCollisionWithTarget -= OnPossessedAttackImpact;
			m_character.m_onRangeAttackFired -= OnPossessedRangeAttackFired;
        }

		protected void OnPossessedAttackImpact()
		{
			if (m_character == null)
				return;
			
			m_character.FireRuneEffectIfExists();
			
			var target = m_character.GetBestTarget();
			m_character.SetTargetObj(target);
			if (m_character.TargetObject == null)
				return;

			m_character.DoAttackOnTarget();

			bool playSlowMotion = false;
			if (target.m_targetObject != null)
			{
				var creature = target.m_targetObject.GetComponent<MACreatureBase>();
				playSlowMotion = creature != null;
			}
			if (playSlowMotion)
				GameManager.Me.PlaySlowMotionIfPossible();
		}

		public void OnPossessedRangeAttackFired()
		{
			if (m_character == null)
				return;
			
			var target = m_character.GetBestTarget();
			m_character.SetTargetObj(target);
			if (m_character.TargetObject == null)
				return;

			m_character.DoRangeAttackOnTarget();
		}

		public void StartBlock()
		{
			if (m_character == null)
				return;
			
			if (m_character.IsBlocking)
				return;
			
			if (!GameManager.Me.CanStartBlock)
				return;
			
			m_character.DoAttackBlock();
		}

		public void StopBlock()
		{
			if (m_character == null)
				return;

			if (!m_character.IsBlocking)
				return;

			// RW-04-MAR-25: Character is parrying. Don't play the out anim of the block
			// until the parry anim's finished.
			if (m_character.CurrentAttack != null)
			{
				return;
			}
			
			m_character.UndoAttackBlock();
		}
	}
}