using System;
using System.Collections.Generic;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class PatrolToWaypoint : RoamForTarget
	{
		public PatrolToWaypoint(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}
		
		private float PatrolSpeed => /*m_gameStateData.m_guardObjectiveWaypoint ?  m_gameStateData.m_attackSpeed : */m_gameStateData.m_walkSpeed;

		private MATimer moveToWaypointTimer = null;

		public override void OnEnter()
		{
			m_roamingStandAroundTime = -1;
			m_roamingWalkAroundTime = -1;
			
			m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			moveToWaypointTimer = null;

			if(m_character.ObjectiveWaypoint != null)
			{
				m_gameStateData.m_speed = PatrolSpeed;
				if(MoveToObjective() == false)
				{//should not happen
                    m_character.ObjectiveWaypoint = null;
                	ApplyState(CharacterStates.RoamForTarget);
					return;
				}
			}
			else
			{			
				ApplyState(CharacterStates.RoamForTarget);
				return;
			}
			m_roamingWalkAroundTime = -1;
		}

		protected override bool TryStartPatrol()
		{
			return false;
		}

		public override void OnUpdate()
		{
			if(m_character.ObjectiveWaypoint == null)
			{
				m_character.ObjectiveWaypoint = null;
				ApplyState(CharacterStates.RoamForTarget);
				return;
			}
			else
			{
				if (m_character.IsHarvestingCreataure)
				{
					if (m_character.Carrying != null)
					{
						ApplyState(CharacterStates.GoingHome);
						
						return;
					}

					foreach (var pickup in GameManager.Me.m_state.m_pickups)
					{
						var pickupObject = pickup.Pickup;
						if (pickupObject == null || pickupObject.IsBeingDragged)
							continue;
						
						var d2 = (pickupObject.transform.position - m_character.transform.position).xzSqrMagnitude();
						if (d2 > (m_character.AttackRange * m_character.AttackRange))
							continue;
						
						if (pickupObject.TryReserve(m_character))
						{
							ApplyState(CharacterStates.CreatureHarvesting);
						
							return;
						}
					}
				}

				bool hasArrived = HasAgentArrived();
				bool isAtWaypoint = m_character.IsAtObjectiveWaypoint();
				if (isAtWaypoint && (moveToWaypointTimer == null))
				{
					moveToWaypointTimer = new MATimer();
					float timeToWaypoint = m_character.m_nav.DistanceToTarget / m_character.m_nav.m_finalSpeed;
					moveToWaypointTimer.Set(timeToWaypoint + 2f);
				}

				bool stopMoving = hasArrived || isAtWaypoint;
				if (IsPatrollingGroup)
					stopMoving = hasArrived || ((moveToWaypointTimer != null) && moveToWaypointTimer.IsFinished);
				
				if (stopMoving) //m_character.m_nav.DistanceToTarget < 30f)
				{
					//if(m_character.IsAtObjectiveWaypoint())
					{
						moveToWaypointTimer = null;

						if (hasArrived == false)
						{
							m_character.m_nav.StopNavigation();
						}

						m_character.LastPatrolPath = null;
						if (m_gameStateData.m_guardObjectiveWaypoint)
						{
							ApplyState(CharacterStates.GuardLocation);
						}
						else
						{
							m_character.ObjectiveWaypoint = null;
							ApplyState(CharacterStates.RoamForTarget);
						}

						return;
					}
					// else
					// {
					//  //arrived but not at objective??
					// }
				}
				else
				{
					if (m_navGenFrame != GlobalData.Me.m_navGenerationFrame)
					{
						Debug.Log($"Patrol Path Regen. {m_character.name}");
						MoveToObjective();
						return;
					}
				}
			}

			base.OnUpdate();
		}

		protected override string GetAttackStateForTarget()
		{
			Transform targetTr = m_character.TargetObject?.transform;
			if(m_character.IsTimeToGoHome)
				return CharacterStates.GoingHome;

			if (m_character.IsEscortedCreataure)
			{
				if (m_character.GetTargetedByNearbyCreature() != null)
					return CharacterStates.Petrified;
			}

			string smashSmash = CheckSmashWallsInPath();
			if(string.IsNullOrWhiteSpace(smashSmash) == false)
				return smashSmash;
			
			if((!m_character.IsTargetAliveAndValid(targetTr) ||
			   !m_character.IsTargetAudible(targetTr.position)))
				return m_character.ObjectiveWaypoint != null
					? CharacterStates.PatrolToWaypoint
					: DefaultState;
			if(!m_character.IsTargetVisible(targetTr.position))
				return m_character.ObjectiveWaypoint != null
					? CharacterStates.PatrolToWaypoint
					: CharacterStates.LookForTarget;
			if(!m_character.IsTargetVisible(targetTr.position, m_character.CreatureInfo.m_visionRadius))
				return m_character.ObjectiveWaypoint != null
					? CharacterStates.PatrolToWaypoint
					: CharacterStates.LookForTarget;
			
			var attack = m_character.GetNextAttackAvailableInCombo();
			string attackOrChase = AttackOrChase(attack);
			if(attackOrChase.IsNullOrWhiteSpace() == false)
			{
				return attackOrChase;
			}

			if (m_character.IsEscortedCreataure)
			{
				if (m_character.IsAtObjectiveWaypoint())
					return CharacterStates.Despawn;
				else
					return PatrolOrReturnToGuard;
			}

			return CharacterStates.ChaseTarget;
		}

		public override void OnExit()
		{
			base.OnExit();

			moveToWaypointTimer = null;

			m_character.m_onPathProcessed -= OnPatrolPathReturned;
			if(m_character.ObjectiveWaypoint != null)
			{
				if (m_character.ObjectiveWaypoint != null &&
				    m_character.LastPatrolPath != null &&
				    m_character.IsAtObjectiveWaypoint())
				{
					if(m_character.m_nav.IsNavigating == false)
					{
						m_character.LastPatrolPath = null;
					}
				}
			}
		}

		private int m_navGenFrame = -1;
		protected bool MoveToObjective()
		{
			if(m_character.ObjectiveWaypoint != null)
			{
				m_roamingStandAroundTime = -1;
				m_character.m_nav.Unpause();

				m_character.m_onPathProcessed -= OnPatrolPathReturned;
				m_character.m_onPathProcessed += OnPatrolPathReturned;

				m_gameStateData.m_speed = PatrolSpeed;
				
				Vector3? pos = null;
				if (IsPatrollingGroup)
					pos = m_character.GroupBehaviour.GetRoamDestinationForCharacter(m_character);
				if (!pos.HasValue)
					pos = m_character.ObjectiveWaypoint;
				
				float destinationRadius = 0f;
				if (m_character.m_rangedComboState != null)
				{
					var rangedAttack = m_character.m_rangedComboState.GetAttackInstanceFromIndex(0);
					if (rangedAttack != null)
						destinationRadius = rangedAttack.AttackRadius * 0.9f;
				}

				m_character.SetMoveToPosition(pos.Value, false, PeepActions.None, destinationRadius);

				m_navGenFrame = GlobalData.Me.m_navGenerationFrame;
					
				return true;
			}
			return false;
		}

		protected void OnPatrolPathReturned(MACreatureBase.TargetReachability _targetReachability)
		{
			switch(_targetReachability)
			{
				case MACreatureBase.TargetReachability.IsReachable:
					m_character.LastPatrolPath = m_character.m_nav.CopyTenthOfPath();
					m_character.m_onPathProcessed -= OnPatrolPathReturned;
					//m_character.PlayAnim("Walk");
					break;
				case MACreatureBase.TargetReachability.IsNotReachable:
					m_character.LastPatrolPath = m_character.m_nav.CopyTenthOfPath();
					m_character.m_onPathProcessed -= OnPatrolPathReturned;
					//m_character.PlayAnim("Walk");
					break;
				case MACreatureBase.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
					m_character.m_onPathProcessed -= OnPatrolPathReturned;
					//m_character.PlayAnim("StopLook");
					break;
				case MACreatureBase.TargetReachability.PathFindingAlreadyInProgress:
					break;
				case MACreatureBase.TargetReachability.PathFailure:
					m_character.m_onPathProcessed -= OnPatrolPathReturned;
					//m_character.PlayAnim("StopLook");
					break;
			}
		}

		protected override void StandAround() { }
	}
}