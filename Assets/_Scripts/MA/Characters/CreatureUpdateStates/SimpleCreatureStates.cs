using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class WorkerState : CharacterBaseState
	{
		public WorkerState(string _state, MACharacterBase _character) : base(_state, _character) { }

		public override void OnEnter()
		{
			base.OnEnter();
			
			MAWorker worker = m_character as <PERSON>Worker;
			if (worker != null && worker.BackUpMovementState != NGMovingObject.STATE.NONE)
			{
				worker.SetState(worker.BackUpMovementState);
				worker.GameStatePerson.m_backupMovementState = (int)NGMovingObject.STATE.NONE;
			}
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
		}
	}
	
	[Serializable]
	public class Idle : CommonState
	{
		public Idle(string _state, MACharacterBase _character) : base(_state, _character) { }

		public override void OnEnter()
		{
			base.OnEnter();
			m_character.SetState(NGMovingObject.STATE.IDLE);
            m_character.BlendAnimatorLayerWeight("Combat", 0);
			m_character.SetTargetObj(null);
		}
	}

	[Serializable]
	public class NullState : CommonState
	{
		public NullState(string _state, MACharacterBase _character) : base(_state, _character)
		{
			_character.m_nav.Unpause();
		}
	}

	[Serializable]
	public class FollowLeader : CommonState
	{
		protected float m_timeEvaluateTarget = 0f;
		protected float m_tickEvaluateTarget = 1;
		public FollowLeader(string _state, MACharacterBase _character) : base(_state, _character)
		{
			_character.m_nav.Unpause();
		}

		public override void OnUpdate()
		{
			m_character.CheckFollowLeader();
			var followState = m_character.FollowLeaderState; 
			if (followState != NGMovingObject.EFollowLeaderState.None)
			{
				if (followState == NGMovingObject.EFollowLeaderState.FollowingWithFreeWill && Time.time > m_timeEvaluateTarget)
				{
					EvaluateTarget();
					m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
				}
			}
		}
	}
}