using System;
using System.Collections;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class WaitingToTeleport : CharacterBaseState
	{
		public WaitingToTeleport(string _state, MACharacterBase _character) : base(_state, _character)
		{

		}

		public override void OnEnter()
		{
			base.OnEnter();
			
			m_character.m_nav.PushPause("WaitingToTeleport", true, true);

			var rc = m_character.m_ragdollController;
			if (rc != null)
			{
				switch (m_character.IsUnconscious())
				{
					case MACharacterBase.Consciousness.Dead:
					case MACharacterBase.Consciousness.Unconscious:
					case MACharacterBase.Consciousness.UnconsciousDead:
						m_character.ActivateRagDoll();
						break;
					default:
						rc.StartAnimatedState();
						break;
				}
			}
		}

        public override void OnExit()
        {
			m_character.m_nav.PopPause("WaitingToTeleport");

            base.OnExit();
        }
	}
}