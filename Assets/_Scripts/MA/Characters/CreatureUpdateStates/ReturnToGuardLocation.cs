using System;
using MACharacterStates;
using UnityEngine;

[Serializable]
public class ReturnToGuardLocation : PatrolToWaypoint
{
    public ReturnToGuardLocation(string _state, MACharacterBase _character) : base(_state, _character)
    {
    }

    public override void OnEnter()
    {
        base.OnEnter();
    }

    protected override string GetAttackStateForTarget()
    {
        Transform targetTr = m_character.TargetObject?.transform;
        if(m_character.IsTimeToGoHome)
            return CharacterStates.GoingHome;

        if (m_character.IsEscortedCreataure)
        {
            if (m_character.GetTargetedByNearbyCreature() != null)
                return CharacterStates.Petrified;
        }

        if ((!m_character.IsTargetAliveAndValid(targetTr) ||
            !m_character.IsTargetAudible(targetTr.position)))
            return m_character.ObjectiveWaypoint != null
                ? PatrolOrReturnToGuard
                : DefaultState;
        if(!m_character.IsTargetVisible(targetTr.position))
            return m_character.ObjectiveWaypoint != null
                ? PatrolOrReturnToGuard
                : CharacterStates.LookForTarget;
        // if(!m_character.IsTargetVisible(targetTr.position, m_character.CharacterSettings.m_patrolAttackRadius))
        //     return m_character.ObjectiveWaypoint != null
        //         ? PatrolOrReturnToGuard
        //         : CharacterStates.LookForTarget;
        var attack = m_character.GetNextAttackAvailableInCombo();
        string attackOrChase = AttackOrChase(attack);
        if(attackOrChase.IsNullOrWhiteSpace() == false)
            return attackOrChase;
        if(m_character.IsAtObjectiveWaypoint())
        {
            if (m_character.IsEscortedCreataure)
                return CharacterStates.Despawn;
            else
                return CharacterStates.ChaseTarget;
        }
        return PatrolOrReturnToGuard;
    }
}