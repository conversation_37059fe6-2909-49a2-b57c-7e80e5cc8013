using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Reflection;
using System.Linq;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MATagManager : MonoSingleton<MATagManager>
{
    [SerializeField]
    public List<TagDefinition> m_tagDefinitions;

    [Range(1, 100)]
    [SerializeField] 
    private float m_cameraDistanceFromFocussedObject = 25.0f;

    [Range(1, 100)] 
    [SerializeField]
    private float m_cameraTransitionTime = 0.4f;
    
    private List<TagCheck> m_tagChecks = new();
    private Dictionary<string, MATag> m_activeTagBehaviours = new();
    
    public float CameraDistanceFromFocussedObject => m_cameraDistanceFromFocussedObject;
    public float CameraTransitionTime => m_cameraTransitionTime;
    
    protected override void Awake()
    {
        base.Awake();
        
        foreach (TagDefinition tagDef in m_tagDefinitions)
        {
            var method = typeof(MATagManager).GetMethod(tagDef.m_methodName,
                BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            
            void Action()
            {
                List<Transform> transforms = null;
                if (MAUnlocks.Me.m_showTags && tagDef.m_enabled)
                {
                    transforms = (List<Transform>)method?.Invoke(this, null);
                }
                MATag.TagInfo newTagInfo = GetTagInfoTemplate(tagDef.m_tagType);
                newTagInfo.m_targetObjects = transforms?.ToArray();
                MATag maTagUIElement = GetOrCreateMATagUIElement(newTagInfo);
            }
            m_tagChecks.Add(new TagCheck(tagDef, Action));
        }
    }
    
    private void Start()
    {        
        StartCoroutine(WaitForGameLoad(0f, OnGameLoad, () =>
        {
            return GameManager.Me.PathsLoaded && GlobalData.Me.BatchTerrainOperationsInProgress == false;
        }));
    }

    private void Update_Internal()
    {
#if UNITY_EDITOR
        foreach (TagDefinition tagDefinition in m_tagDefinitions)
        {
            tagDefinition.UpdateEditorInfo();
        }
#endif

        foreach (var infoTag in m_tagChecks)
        {
            if (infoTag.m_checkTimer.IsFinished)
            {
                infoTag.m_checkTimer.m_onElapsed?.Invoke();
                infoTag.m_checkTimer.Set();
            }
        }
    }

    public bool m_lateUpdate = false;
    private void Update()
    {
        if (m_lateUpdate == false) Update_Internal();
    }
    private void LateUpdate()
    {
        if (m_lateUpdate) Update_Internal();
    }

    protected override void _OnDestroy()
    {
        base._OnDestroy();
        m_tagChecks.Clear();
        m_activeTagBehaviours.Clear();
    }

    public bool SetTagPanelToShow(bool _show)
    {
        var go = UIManager.Me.m_tagPanel.gameObject;
        var wasEnabled = go.activeSelf;
        go.SetActive(_show);
        return wasEnabled;
    }

    private MATag.TagInfo GetTagInfoTemplate(string _tagType)
    {
        TagDefinition tagDef = m_tagDefinitions.Find(x => x.m_tagType == _tagType);
        return new MATag.TagInfo
        {
            m_tagType = _tagType,
            m_icon = tagDef.m_icon,
            m_targetObjects = null,
            m_rightClickInfoPanelUI = tagDef.m_rightClickInfoPanelUI,
        };
    }
    
    private MATag GetOrCreateMATagUIElement(MATag.TagInfo _tagInfo)
    {
        if (m_activeTagBehaviours.TryGetValue(_tagInfo.m_tagType, out MATag maTagUIElement) == false)
        {
            foreach (var tagUI in UIManager.Me.m_tagUI)
            {
                maTagUIElement = tagUI;
                if (maTagUIElement != null) break;
            }

            if (maTagUIElement == null)
            {
                Debug.LogError($"{GetType().Name} - {name} - MATagManager.GetOrCreateMATagUIElement: No MATag found in UIManager.m_tagUI.");
                return null;
            }

            if (m_activeTagBehaviours.Count > 0)
            {
                MATag clone = Instantiate(maTagUIElement, maTagUIElement.transform.parent);
                maTagUIElement = clone;
                maTagUIElement.gameObject.SetActive(true);
            }
            
            m_activeTagBehaviours.Add(_tagInfo.m_tagType, maTagUIElement);
        }

        /*if (_tagInfo.m_targetObjects.Length == 0)
        {
            UIManager.Me.m_centralInfoPanelManager.CloseInfoPanel(_tagInfo.m_rightClickInfoPanelUI);
        }*/
        
        maTagUIElement.Init(_tagInfo);
        return maTagUIElement;
    }


    #region CheckMethods

    /// <summary> Insert a method name into a serialized tag definition in the inspector to have return relevant camera-focus transforms as necessary</summary>
    private List<Transform> CheckHeroOffScreen()
    {
        List<Transform> heroes = new();
        foreach (var hero in NGManager.Me.m_MAHeroList)
        {
            Transform tr = hero.transform;
            if (hero.IsAlive &&
                hero.IsUnconscious() == MACharacterBase.Consciousness.Conscious && 
                hero.MainRenderer != null && 
                (hero.MainRenderer.isVisible == false ||
                false == GameManager.Me.IsVisible(tr.position, hero.m_bodyToBodyCollider.bounds.size)))
            {
                heroes.Add(tr);
            }
        }
        return heroes;
    }

    private List<Transform> CheckGates()
    {
        List<Transform> transforms = new();
        foreach (PathManager.Path pathSetCurrentPath in RoadManager.Me.m_pathSet.CurrentPaths)
        {
            var gates = pathSetCurrentPath.Gates;
            foreach (GateOpener gateOpener in gates)
            {
                if (((DayNight.Me.m_isDawn || DayNight.Me.m_isFullDay) && gateOpener.IsGateOpen == false) ||
                    ((DayNight.Me.m_isDusk || DayNight.Me.m_isFullNight) && gateOpener.IsGateOpen))
                {
                    Transform tr = gateOpener.transform;
                    var districtInfo =
                        DistrictManager.Me.GetDistrictAtPoint(tr.position);
                    if (districtInfo.isUnlocked)
                    {
                        transforms.Add(tr);
                    }
                }
            }
        }

        return transforms;
    }
    
    public List<Transform> CheckRedBalloons()
    {
        List<Transform> balloons = new();//Transform[NGBalloonManager.Me.m_activeBalloons.Count];
        for(int i = 0; i < NGBalloonManager.Me.m_activeBalloons.Count; i++)
        {
            if (NGBalloonManager.Me.m_activeBalloons[i].gameObject.activeInHierarchy)
            {
                var districtInfo = DistrictManager.Me.GetDistrictAtPoint(NGBalloonManager.Me.m_activeBalloons[i].transform.position);
                if (districtInfo.isUnlocked)
                {
                    balloons.Add(NGBalloonManager.Me.m_activeBalloons[i].transform);
                }
            }
        }
        return balloons;
    }
    
    private List<Transform> CheckHeroes()
    {
        List<Transform> heroes = new();
        
        // There was a request to have the tag always visible
        foreach (var hero in NGManager.Me.m_MAHeroList)
        {
            heroes.Add(hero.transform);
        }
        return heroes;
    }

    private List<Transform> CheckDogs()
    {
        List<Transform> dogs = new();

        foreach (var animal in NGManager.Me.m_MAAnimalList)
        {
            if (animal is MADog)
            {
                dogs.Add(animal.transform);
            }
        }
        return dogs;
    }

    private List<Transform> CheckCombatInstances() //TS - checking human list -> but bandits are enemies + human ?!
    {
        bool IsHeroBeingTargetedByEnemies(MACharacterBase _character)
        {
            TargetObject selfAsTarget = _character.TargetObject;
            if (selfAsTarget == null) return false;
            foreach (IDamager damager in selfAsTarget.TargetedBy)
            {
                if (damager is MACharacterBase charDamager && charDamager != null && charDamager.CreatureInfo.CreatureTargets.Contains(_character.CreatureInfo))
                {
                    return true;
                }
            }
            return false;
        }
        List<Transform> charactersInCombat = new();
        foreach (var humanCharacter in NGManager.Me.m_MAHumanList)
        {
            if (humanCharacter.IsInCombat)// || humanCharacter.IsFleeing)
            {
                charactersInCombat.Add(humanCharacter.transform);
            }
        }
        return charactersInCombat;
    }
    
    private List<Transform> CheckCartsWaitingAtGates()
    {
        List<Transform> vehiclesAtGates = new();
        foreach (var vehicle in NGManager.Me.MAVehicles)
        {
            if (vehicle.VehicleState.State == VehicleStateFactory.VehicleState.kWaitingAtEntryGate ||
                vehicle.VehicleState.State == VehicleStateFactory.VehicleState.kWaitingAtExitGate)
            {
                vehiclesAtGates.Add(vehicle.transform);
            }
        }
        return vehiclesAtGates;
    }

    private List<Transform> CheckDownedCharacters()
    {
        List<Transform> charactersDowned = new();
        foreach (var humanCharacter in NGManager.Me.m_MAHumanList)
        {
            if (humanCharacter.CharacterUpdateState.State == CharacterStates.UnconsciousDead)// && humanCharacter.CreatureInfo.m_deadHealthRecoveryRate > 0)
            {
                charactersDowned.Add(humanCharacter.transform);
            }
        }
        return charactersDowned;
    }
    
    private List<Transform> CheckEnemiesInRegion()
    {
        List<Transform> enemiesInUnlockedRegion = new();
        foreach (var creature in NGManager.Me.m_MACreatureList)
        {
            if (!creature.IsEnemy)
                continue;
            
            Transform tr = creature.transform;
            if (creature.IsUnconscious() == MACharacterBase.Consciousness.Conscious && 
                DistrictManager.Me.IsWithinDistrictBounds(tr.position))
            {
                enemiesInUnlockedRegion.Add(tr);
            }
        }
        return enemiesInUnlockedRegion;
    }
    
    private List<Transform> CheckNoOrderInFactory()
    {
        List<Transform> buildingsWithAvailableOrders = new();
        
        // Check for factory with no order
        foreach (var factory in MABuildingSupport.GetBuildingsWithComponent<BCFactory>(true))
        {
            if (factory.Order.IsNullOrEmpty() == false)
                continue;

            // Find all buildings with available orders
            foreach(var building in NGManager.Me.m_maBuildings)
            {
                if(building.HasAvailableOrder())
                    buildingsWithAvailableOrders.Add(building.m_balloonHolder);
            }
            break;
        }
        return buildingsWithAvailableOrders;
    }    
    
    private List<Transform> CheckEnemyInRangeOfFinalObjective()
    {
        const float allowedMetres = 3;
        List<Transform> enemiesNearFinalObjective = new();
        var objCrypt = MASpecialMABuilding.Crypt();
        if (objCrypt == null) return new();
        var crPos = objCrypt.transform.position.GroundPosition();
        foreach (var creature in NGManager.Me.m_MACreatureList)
        {
			if (!creature.IsEnemy)
                continue;

            Transform tr = creature.transform;
            if (creature.CreatureInfo.ComponentTargets.Contains(MAComponentInfo.GetInfoByClass(typeof(BCTardisCrypt))) &&
                creature.IsUnconscious() == MACharacterBase.Consciousness.Conscious && 
                (crPos.DistanceSq(tr.position.GroundPosition()) <= (allowedMetres * allowedMetres) || 
                (creature.TargetObject != null &&
                 creature.TargetObject.Obj == objCrypt &&
                 creature.CanReachTargetWithAttack(creature.CurrentComboState.GetNextAttack(), out var targetPos))))
            {
                enemiesNearFinalObjective.Add(tr);
            }
        }
        return enemiesNearFinalObjective;
    }
    #endregion
    
    private void OnGameLoad()
    {
        foreach (var tag in m_tagChecks)
        {
            tag.m_checkTimer.Set(tag.m_tagDefinition.m_checkFrequency);
        }
    }
    
    private IEnumerator WaitForGameLoad(float _timeSecs, Action _action, Func<bool> _condition)
    {
        yield return new WaitUntil(() => _condition());
        yield return new WaitForSeconds(_timeSecs);
        _action?.Invoke();
    }
    
    public class TagCheck
    {
        public TagDefinition m_tagDefinition = null;
        public MAActionTimer m_checkTimer = null;

        public TagCheck(TagDefinition _tagDefinition, Action _action)
        {
            m_tagDefinition = _tagDefinition;
            m_checkTimer = new MAActionTimer(_tagDefinition.m_checkFrequency, _action);
        }
        
        public TagCheck(TagDefinition _tagDefinition, Action _check, float _checkFrequency)
        {
            m_tagDefinition = _tagDefinition;
            m_checkTimer = new MAActionTimer(_checkFrequency, _check);
        }

        ~TagCheck()
        {
		    FLog.Log("TagCheck");
		    try {
                m_checkTimer = null;
		    } catch (System.Exception e) {}
        }
    }
    
    [Serializable]
    public class TagDefinition
    {
        public bool m_enabled = true;
        public string m_tagType = "";
        public string m_icon = "";
        public string m_rightClickInfoPanelUI;
        public string m_methodName;
        public float m_checkFrequency = 1f;
        

        public void UpdateEditorInfo()
        {
#if UNITY_EDITOR
           // m_name = m_tagType.ToString();
#endif
        }
        
        public TagDefinition(string _tagType, string _icon, string _rightClickInfoPanelUI, string _methodName, float _checkFrequency = 1f, bool _enabled = true)
        {
            //m_name = _tagType.ToString();
            m_enabled = _enabled;
            m_tagType = _tagType;
            m_icon = _icon;
            m_rightClickInfoPanelUI = _rightClickInfoPanelUI;
            m_methodName = _methodName;
            m_checkFrequency = _checkFrequency;
        }
    }

    [Serializable]
    public class MAActionTimer : MATimer
    {
        public MAActionTimer(float _length, Action _onElapsed) : base(_length)
        {
            m_onElapsed = _onElapsed;
        }
        
        public Action m_onElapsed = null;
        
        ~MAActionTimer()
        {
		    FLog.Log("MAActionTimer");
		    try {
                m_onElapsed = null;
		    } catch (System.Exception e) {}
        }
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MATagManager))]
public class MATagManagerEditor : Editor
{
    private Dictionary<MATagManager.TagDefinition, Tuple<string, string, Texture, Texture>> m_tagIcons = new();

    private bool finished = false;
    private bool finished2 = false;
    private bool finished3 = false;

    private bool refresh = false;
    private void OnValidate()
    {
        m_tagIcons.Clear();
        finished = false;
        finished2 = false;
        finished3 = false;
    }

    public override void OnInspectorGUI()
    {
        MATagManager tm = target as MATagManager;
        
        if (finished == false)
        {
            foreach (MATagManager.TagDefinition tmTagDefinition in tm.m_tagDefinitions)
            {
                if(tmTagDefinition.m_enabled) m_tagIcons.TryAdd(tmTagDefinition, null);
            }
            foreach (MATagManager.TagDefinition tmTagDefinition in tm.m_tagDefinitions)
            {
                if(tmTagDefinition.m_enabled) m_tagIcons[tmTagDefinition] = new(tmTagDefinition.m_icon, tmTagDefinition.m_tagType, Resources.Load<Texture>(tmTagDefinition.m_icon), null);
            }

            finished = true;
        }
        else
        {
            if (tm.m_tagDefinitions.FindAll(x => x.m_enabled) is { } res && res.Count != m_tagIcons.Count)
            {
                finished = false;
            }

            if (finished)
            {
                foreach (MATagManager.TagDefinition tmTagDefinition in tm.m_tagDefinitions)
                {
                    if (tmTagDefinition.m_enabled == false) continue;
                    if (tmTagDefinition.m_icon != m_tagIcons[tmTagDefinition].Item1)
                    {
                        finished = false;
                        break;
                    }

                    if (tmTagDefinition.m_tagType != m_tagIcons[tmTagDefinition].Item2)
                    {
                        finished = false;
                        break;
                    }
                }
            }
        }

        if (finished2 == false)
        {
            foreach (MATagManager.TagDefinition tmTagDefinition in tm.m_tagDefinitions)
            {
                if(tmTagDefinition.m_enabled == false) continue;
                m_tagIcons[tmTagDefinition] = new(tmTagDefinition.m_icon, tmTagDefinition.m_tagType,
                    m_tagIcons[tmTagDefinition].Item3,
                    AssetPreview.GetAssetPreview(m_tagIcons[tmTagDefinition].Item3));
            }
            finished2 = true;
        }
        
        if (finished3 == false)
        {
            foreach (var tagicons in m_tagIcons)
            {
                if (tagicons.Value.Item3 != null && AssetPreview.IsLoadingAssetPreview(tagicons.Value.Item3.GetInstanceID()))
                {
                    return;
                }
            }
            finished3 = true;
        }
        
        if (m_tagIcons.Values.Contains(null)) return;
        foreach (var tagMapping in m_tagIcons)
        {
            MATagManager.TagDefinition tagDef = tagMapping.Key;
            if (tagMapping.Key.m_enabled)
            {
                GUILayout.Label($"_____________________________________________________________________________");
                GUILayout.BeginHorizontal();
                GUILayout.Label("", GUILayout.Height(40), GUILayout.Width(40));
                GUI.DrawTexture(GUILayoutUtility.GetLastRect(), tagMapping.Value.Item4);
                GUILayout.BeginVertical();
                GUILayout.Label($"{tagDef.m_tagType} - Icon Path: {tagDef.m_icon}");
                EditorGUILayout.ObjectField($"", tagMapping.Value.Item3, typeof(Transform), true);
                GUILayout.EndVertical();
                GUILayout.EndHorizontal();
            }
        }
        base.OnInspectorGUI();
    }

    private void OnDisable()
    {
        m_tagIcons.Clear();
        finished = false;
        finished2 = false;
        finished3 = false;
    }
}
#endif
