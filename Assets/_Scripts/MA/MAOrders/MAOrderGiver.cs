using UnityEngine;
using System;
using System.Collections.Generic;

[Serializable]
public class MAOrderGiver
{
    public bool HideGiver => m_hide;
    
    public static List<MAOrderGiver> s_maOrderGivers = new();
    public static List<MAOrderGiver> GetList=>s_maOrderGivers;    
    public string DebugDisplayName => m_name;
    
    private static Dictionary<string, MAOrderGiver> s_orderGiverByName = new();
    
    public Sprite PortraitSprite {get; private set;}
    public MAFactionInfo Faction {get; private set;}
    public string id;
    public bool m_debugChanged;
  
    public string m_name = "";
    public string m_displayName = "";
    [ScanField] public string m_mAFactionInfo;
    public string m_portraitSprite;
    public string m_description;
    public float m_reputation1;
    public float m_reputation2;
    public float m_reputation3;
    public float m_reputation4;
    public float m_reputation5;
    public bool m_hide = false;
    
    public string m_reputation1Bonus;
    public string m_reputation2Bonus;
    public string m_reputation3Bonus;
    public string m_reputation4Bonus;
    public string m_reputation5Bonus;
    
    public BonusGift Reputation1Bonus = new();
    public BonusGift Reputation2Bonus = new();
    public BonusGift Reputation3Bonus = new();
    public BonusGift Reputation4Bonus = new();
    public BonusGift Reputation5Bonus = new();
    
    public class BonusGift
    {
        public enum RewardType
        {
            None,
            QuantityMultiplier, // Multipliy future order quantity
            PriceMultiplier, // Future order selling price boost
            FavourMultiplier, // Future reward multiplier
            PeoplesFavour,
            LordsFavour,
            RoyalFavour,
            MysticFavour,
        }
        
        public RewardType m_type;
        public NGCarriableResource m_currencyType;
        public float m_value;
        
        public BonusGift() { }
        
        public void Process(string _type, float _value)
        {
            m_value = _value;
            
            switch(_type)
            {
                case "quantity": m_type = RewardType.QuantityMultiplier; break; 
                case "price": m_type = RewardType.PriceMultiplier; break;
                case "favour": m_type = RewardType.FavourMultiplier; break;
                case "peoplesfavour":
                    m_type = RewardType.PeoplesFavour;
                    m_currencyType = NGCarriableResource.GetInfo("PeoplesFavor");
                    break;
                case "lordsfavour":
                    m_type = RewardType.LordsFavour;
                    m_currencyType = NGCarriableResource.GetInfo("LordsFavour");
                    break;
                case "royalfavour":
                    m_type = RewardType.RoyalFavour;
                    m_currencyType = NGCarriableResource.GetInfo("RoyalFavour");
                    break;
                case "mysticfavour":
                    m_type = RewardType.MysticFavour;
                    m_currencyType = NGCarriableResource.GetInfo("MysticFavour");
                    break;
            }
        }
        
        private string FloatToPercent(float _value)
        {
            return $"+{(_value-1) * 100f:F0}%";
        }
        
        public string GetRewardDescription()
        {
            string value = null;
            switch(m_type)
            {
                case RewardType.QuantityMultiplier: value = $"{FloatToPercent(m_value)} Order Size"; break;
                case RewardType.PriceMultiplier: value = $"{FloatToPercent(m_value)} Price"; break;
                case RewardType.FavourMultiplier: value = $"{FloatToPercent(m_value)} Favours"; break;
                case RewardType.PeoplesFavour: value = $"+{m_value:F0}x<size=150%>{m_currencyType?.TextSprite}</size>"; break;
                case RewardType.LordsFavour: value = $"+{m_value:F0}x<size=150%>{m_currencyType?.TextSprite}</size>"; break;
                case RewardType.RoyalFavour: value = $"+{m_value:F0}x<size=150%>{m_currencyType?.TextSprite}</size>"; break;
                case RewardType.MysticFavour: value = $"+{m_value:F0}x<size=150%>{m_currencyType?.TextSprite}</size>"; break;
            }
            return value;
        }
    }
    
    public (int previousLevel, int newLevel, float previousProgress, float newProgress) AdjustScore(float _score)
    {
        int previousLevel = CalculateReputation();
        var giver = GetGameStateOrderGiver();
        float previousProgress = CalculateReputationRemainder();
        giver.m_reputationScore += _score;
        return (previousLevel, CalculateReputation(), previousProgress, CalculateReputationRemainder());
    }
        
    private GameState_OrderGiver GetGameStateOrderGiver()
    {
        if(GameManager.Me.m_state.m_orderGivers.TryGetValue(m_name, out GameState_OrderGiver data) == false)
        {
            data = new GameState_OrderGiver() { m_name = this.m_name };
            GameManager.Me.m_state.m_orderGivers.Add(m_name, data);
        }
        return data;
    }
    
    public int CalculateReputation()
    {
        var data = GetGameStateOrderGiver();
        if(data.m_reputationScore >= m_reputation5) return 5;
        if(data.m_reputationScore >= m_reputation4) return 4;
        if(data.m_reputationScore >= m_reputation3) return 3;
        if(data.m_reputationScore >= m_reputation2) return 2;
        if(data.m_reputationScore >= m_reputation1) return 1;
        
        return 0;
    }

    public float CalculateReputationRemainder()
    {
        var data = GetGameStateOrderGiver();
        var score= data.m_reputationScore;
        var lower = 0f;
        var upper = 0f;
        if (score >= m_reputation5)
        {
            lower = m_reputation5;
            upper = 999;
        }
        else if (score >= m_reputation4)
        {
            upper = m_reputation5;
            lower = m_reputation4;
        }
        else if(score >= m_reputation3)
        {
            upper = m_reputation4;
            lower = m_reputation3;
        }
        else if(score >= m_reputation2) 
        {
            upper = m_reputation3;
            lower = m_reputation2;
        }
        else if(score >= m_reputation1)
        {
            upper = m_reputation2;
            lower = m_reputation1;
        } 
        else if (score >= 0)
        {
            lower = 0;
            upper = m_reputation1;
        }
        else
        {
            lower = -999f;
            upper = 0;    
        }
        var ss = (score - lower) / (upper - lower);
        return ss;
    }

    public string GetReputationStars(int _override = -1)
    {
        int rating = _override > -1 ? _override : CalculateReputation();
        string value = "";
        
        for(int i = 0; i < 5; ++i)
        {
            if(i < rating)
                value += MAMessageManager.GetFullStar();
            else
                value += MAMessageManager.GetOutlineStar();
        }
        return value;
    }
    
    public float GetFavourRewardMultiplier()
    {
        var bonuses = GetCurrentBonuses();
        foreach(var bonus in bonuses)
        {
            if(bonus.m_type == BonusGift.RewardType.FavourMultiplier)
            {
                return bonus.m_value;
            }
        }
        return 1f;
    }
    
    public float GetOrderQuantityMultiplier()
    {
        var bonuses = GetCurrentBonuses();
        foreach(var bonus in bonuses)
        {
            if(bonus.m_type == BonusGift.RewardType.QuantityMultiplier)
            {
                return bonus.m_value;
            }
        }
        return 1f;
    }
    
    public List<BonusGift> GetCurrentBonuses()
    {
        var reputation = CalculateReputation();
        List<BonusGift> bonuses = new();
        if(reputation > 4) bonuses.Add(Reputation5Bonus);
        if(reputation > 3) bonuses.Add(Reputation4Bonus);
        if(reputation > 2) bonuses.Add(Reputation3Bonus);
        if(reputation > 1) bonuses.Add(Reputation2Bonus);
        if(reputation > 0) bonuses.Add(Reputation1Bonus);
        return bonuses;
    }
    
    public string GetCurrentBonusString()
    {
        string bonusStr = "";
        foreach(var bonus in GetCurrentBonuses())
        {
            bonusStr += bonus.GetRewardDescription() + "\n";
        }
        return bonusStr.IsNullOrWhiteSpace() ? "None" : bonusStr.Trim('\n');
    }
    
    public static MAOrderGiver Get(string _name)
    {
        s_orderGiverByName.TryGetValue(_name.ToLower().Trim(), out var result);
        return result;
    }
    
    public static List<MAOrderGiver> LoadInfo()
    {
        s_orderGiverByName.Clear();
        s_maOrderGivers = NGKnack.ImportKnackInto<MAOrderGiver>(PostImport);
        
        return s_maOrderGivers;
    }
    
    public static bool PostImport(MAOrderGiver _what)
    {
        if(s_orderGiverByName.TryAdd(_what.m_name.ToLower().Trim(), _what) == false)
            return false;
            
        if(_what.m_portraitSprite.IsNullOrWhiteSpace() == false)
            _what.PortraitSprite = Resources.Load<Sprite>(_what.m_portraitSprite);
            
        if(_what.m_mAFactionInfo.IsNullOrWhiteSpace() == false)
            _what.Faction = MAFactionInfo.GetInfo(_what.m_mAFactionInfo);
            
        if(_what.Faction == null)
        {
            Debug.LogError($"MAOrderGiver: {_what.m_name} does not have a valid faction.");
            _what.Faction = MAFactionInfo.GetInfo("Peoples");
        }
        
        _what.m_hide = _what.m_name.Equals("Player");
        ExtractBonuses(_what.Reputation1Bonus, _what.m_reputation1Bonus);
        ExtractBonuses(_what.Reputation2Bonus, _what.m_reputation2Bonus);
        ExtractBonuses(_what.Reputation3Bonus, _what.m_reputation3Bonus);
        ExtractBonuses(_what.Reputation4Bonus, _what.m_reputation4Bonus);
        ExtractBonuses(_what.Reputation5Bonus, _what.m_reputation5Bonus);
        
        return true;
    }
    
    private static void ExtractBonuses(BonusGift _bonus, string _bonuses)
    {
        var bonuses = _bonuses.Split(';', '|', ',', '\n');
        
        foreach(var b in bonuses)
        {
            if(b.IsNullOrWhiteSpace())
                continue;
        
            var parts = b.Split("=");
            if(parts.Length != 2)
                continue; 
            
            _bonus.Process(parts[0].ToLower(), float.Parse(parts[1]));
            return;
        }
    }
}
