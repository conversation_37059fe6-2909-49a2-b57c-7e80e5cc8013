using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class MAOrderInfo
{
	public static List<MAOrderInfo> s_maOrderInfo = new();
	public static List<MAOrderInfo> GetList=>s_maOrderInfo;
	public string DebugDisplayName => m_name;

	public static Dictionary<string, List<MAOrderInfo>> s_ordersByBlockName = new();
	private static Dictionary<string, MAOrderInfo> s_orderInfoByIndexer = new();

	
	public string id;
	public bool m_debugChanged;
	public string m_indexer = "";
	public string m_name = "";
	public string m_blockName;
	public string m_collectType;
	
	[ScanField] public string m_product;
	
	public bool m_useName = false;
    public int m_lowQuantity;
    public int m_highQuantity;
    public float m_lowDesignScore;
    public float m_highDesignScore;
	
	[ScanField] public string m_rewards;
	
    public string m_design;
    public string m_type;
    public MAOrderGiver OrderGiver { get; private set; }
    [ScanField] public string m_mAOrderGiver;
    [ScanField] public string m_tags;
    public string m_tagHint;
    public string m_cardDescription;
    public List<string> Tags { get; private set; }
	
	public MAFactionInfo Faction => OrderGiver?.Faction;

	public bool RepeatingOrder { get { return string.IsNullOrEmpty(m_type) || m_type.ToLower() != "oneshot"; } }
    public List<MARewardOrderInfo> AllRewards;
	
	public string GetHints()
	{
		if(m_tagHint.IsNullOrWhiteSpace() == false)
			return m_tagHint;
		string hints = "";
		foreach(var tag in Tags)
		{
			hints += tag + ", ";
		}
		hints = hints.TrimEnd(',');
		return hints;
	}
	
    public static bool PostImport(MAOrderInfo _what)
    {
	    if(s_orderInfoByIndexer.TryAdd(_what.m_indexer.ToLower().Trim(), _what) == false) return false;
	    
	    if(_what.m_mAOrderGiver.IsNullOrWhiteSpace() == false)
	    {
			_what.OrderGiver = MAOrderGiver.Get(_what.m_mAOrderGiver);
	    }
        
        _what.Tags = new();
        if(_what.m_tags.IsNullOrWhiteSpace() == false)
        {
	        foreach(var s in _what.m_tags.Split('|', '\n', ';'))
	        {
				_what.Tags.Add(s.Trim());
	        }
	    }
        
        _what.AllRewards = new();
        foreach(var s in _what.m_rewards.Split('|', '\n', ';'))
        {
            var r = MARewardOrderInfo.GetInfo(s);
            if(r == null)
            {
//        	    Debug.LogError($"MAOrderInfo - PostImport '{s}' Reward not found in order named: {_what.m_name}");
        	    continue;
            }

            _what.AllRewards.Add(r);
        }

        _what.m_blockName = _what.m_blockName.ToLower().Trim();
        
        if(s_ordersByBlockName.TryGetValue(_what.m_blockName, out var list) == false)
		{
	        list = new List<MAOrderInfo>() {_what};
	        s_ordersByBlockName.Add(_what.m_blockName, list);
		}
        else
        {
	        list.Add(_what);
        }
        
        _what.m_indexer = _what.m_indexer.ToLower().Trim();
	        
        //if(s_maOrderInfo.Count > 0)
        //{
	        //foreach(var order in s_maOrderInfo)
	        //{
	        //}
       // }

        return true;// string.IsNullOrWhiteSpace(_what.m_indexer) == false && string.IsNullOrWhiteSpace(_what.m_faction) && string.IsNullOrWhiteSpace(_what.m_rewards) == false && _what.m_lowQuantity > 0 && _what.m_highQuantity > 0 && _what.m_lowDesignScore > 0 && _what.m_highDesignScore > 0;
    }
    
    public static List<MAOrderInfo> LoadInfo()
    {
	    s_maOrderInfo = NGKnack.ImportKnackInto<MAOrderInfo>(PostImport);
	    List<MAOrderInfo> shopOrderInfo = MAOrderInfo.GetInfosByBlockName("ShopOrder");
	    if(shopOrderInfo == null || shopOrderInfo.Count == 0)
	    {
		    shopOrderInfo = makespoofshoporderinfo("Shop Order");
		    s_maOrderInfo.AddRange(shopOrderInfo);
	    }
	    foreach(var shopOrder in shopOrderInfo)
	    {
		    PostImport(shopOrder);
	    }

	    List<MAOrderInfo> turretOrderInfo = MAOrderInfo.GetInfosByBlockName("TurretOrder");
	    if(turretOrderInfo == null || turretOrderInfo.Count == 0)
	    {
			turretOrderInfo = makespoofturretorderinfo("Turret Order");
			s_maOrderInfo.AddRange(turretOrderInfo);
	    }
	    foreach(var turretOrder in turretOrderInfo)
	    {
		    PostImport(turretOrder);
	    }
	    
	    RemoveInvalidatedOrders();
	    
		return s_maOrderInfo;
    }
    
    // If order IDs have changed in knack
    public static void RemoveInvalidatedOrders()
    {/*
		for(int i = GameManager.Me.m_state.m_ordersOnBoard.Count-1; i > -1; --i)
		{
			if(GameManager.Me.m_state.m_ordersOnBoard[i].HasLostLinkToOrderInfo())  GameManager.Me.m_state.m_ordersOnBoard.RemoveAt(i); 
	    }
		for(int i = GameManager.Me.m_state.m_currentOrderSequenceOffBoard.Count-1; i > -1; --i)
		{
			if(GameManager.Me.m_state.m_currentOrderSequenceOffBoard[i].HasLostLinkToOrderInfo())  GameManager.Me.m_state.m_currentOrderSequenceOffBoard.RemoveAt(i); 
		}*/
	    if (GameManager.Me)
	    {
			for(int i = GameManager.Me.m_state.m_orderDataHistory.Count-1; i > -1; --i)
			{
				if(GameManager.Me.m_state.m_orderDataHistory[i].HasLostLinkToOrderInfo())  GameManager.Me.m_state.m_orderDataHistory.RemoveAt(i); 
			}
	    }
    }
    
    public float GetBlockScoreMultiplier(NGBlockInfo _blockInfo)
    {
		return 1f;
    }
    
    public static MAOrderInfo GetInfo(string _indexer)
	{
		if(_indexer.IsNullOrWhiteSpace())
			return null;
		if(s_orderInfoByIndexer.TryGetValue(_indexer.ToLower().Trim(), out var info))
			return info;
		return null;
	}
    
    public static List<MAOrderInfo> GetInfosByBlockName(string _blockName)
    {		
	    _blockName = _blockName.ToLower().Trim();
	    if(s_ordersByBlockName.TryGetValue(_blockName, out var orders) == false || orders == null || orders.Count == 0)
	    {
		    Debug.LogError("TriggerOrderSequence - No orders found for block name: " + _blockName);
		    return null;
	    }
		return orders;
    }
    
    //public string OrderText => $"name: '{m_name}' - faction: '{m_faction}' - product: '{m_product}' - lowQuantity: '{m_lowQuantity}' - highQuantity: '{m_highQuantity}' - rewards {m_rewards.Length} - design {m_design} - orderNutrition {m_nutrition}";

    static List<MAOrderInfo> makespoofshoporderinfo(string _name)
    {
	    return makespooforderinfo(_name, "");
    }
    
    static  List<MAOrderInfo> makespoofturretorderinfo(string _name)
    {
	    return makespooforderinfo(_name, "Projectile");
    }
    
    static List<MAOrderInfo> makespooforderinfo(string _name, string _product)
    {
	    MAOrderInfo maOrderInfo = new MAOrderInfo();
	    maOrderInfo.m_lowQuantity = 2;
	    maOrderInfo.m_highQuantity = 10;
	    maOrderInfo.m_lowDesignScore = 0f;
	    maOrderInfo.m_highDesignScore = 0f;
	    maOrderInfo.m_name = _name;//"TurretOrder";
	    maOrderInfo.m_indexer = $"{_name} 1";
	    while(MAOrderInfo.GetInfo(maOrderInfo.m_indexer.ToLower().Trim()) != null) //spoof unique indexer
	    {
		    char s = maOrderInfo.m_indexer[maOrderInfo.m_indexer.Length - 1];
		    int i = int.Parse(s.ToString());
		    maOrderInfo.m_indexer = maOrderInfo.m_indexer.Remove(maOrderInfo.m_indexer.Length - 1);
		    maOrderInfo.m_indexer += ++i;
	    }
	    maOrderInfo.m_blockName = _name;
	    maOrderInfo.m_rewards = "";
	    maOrderInfo.m_product = _product;   

	    return new List<MAOrderInfo>(){maOrderInfo};
    }
    
}
