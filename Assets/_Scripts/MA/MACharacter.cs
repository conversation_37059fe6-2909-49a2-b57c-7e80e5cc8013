using System;
using System.Collections.Generic;
using UnityEngine;

public class MACharacter : <PERSON>haracterBaseV2
{
    public enum WorkerState 
    {
        GoToWork,
        GoHome,
        RunAway,
        BeingAttacked,
        Working,
        Sleeping
    }

    [Serializable]
    public class StateDataBase
    {
        public Action stateFunction;
    }

    [Serializable] public class GoToWorkStateData : StateDataBase
    {
        public MABuilding workplaceLocation; 
    }
    
    [Serializable] public class GoHomeStateData : StateDataBase
    {
        public MABuilding homeLocation; 
    }
    public WorkerState currentState;
    private Dictionary<WorkerState, StateDataBase> stateData = new Dictionary<WorkerState, StateDataBase>();
    public List<StateDataBase> m_stateData = new List<StateDataBase>();
    void Start() 
    {
        stateData[WorkerState.GoToWork] = new GoToWorkStateData() { stateFunction = HandleGoToWork }; 
    }
    
    

    void Update()
    {
        // State-specific logic based on currentState
        switch (currentState)
        {
            case WorkerState.GoToWork:
                HandleGoToWork();
                break;
            // ... handle other states
        }
    }

    void HandleGoToWork()
    {
        var data = (GoToWorkStateData)stateData[WorkerState.GoToWork];
        if(StateMoveToBuilding(data.workplaceLocation))
        {
            TransitionToState(WorkerState.Working);
        }
    }
    public void TransitionToState(WorkerState newState)
    {
        // Perform any necessary cleanup or transitions for the current state
        // ...

        currentState = newState;

        // Perform any necessary setup for the new state
        // ...
    }
    
    virtual protected bool StateMoveToBuilding(MABuilding _building)
    {
        if(HasAgentArrived() == false)
        {
            if(_building != null && NGCommanderBase.s_heldBuilding == _building)
            {
                if(m_stoppedForBuildingPlacement == false)
                {
                    m_nav.PushPause("BuildingPlacement", true, true);
                    m_stoppedForBuildingPlacement = true;
                }
            }
            else
            {
                if(m_stoppedForBuildingPlacement)
                {
                    m_nav.PopPause("BuildingPlacement");
                    m_stoppedForBuildingPlacement = false;
                }
            }
            return false;
        }
        return true;
    }

    
    [Serializable] private class SaveData 
    {
        public WorkerState currentState;
        public Dictionary<WorkerState, StateDataBase> stateData;
    }
    public void Save() 
    {
        var saveData = new SaveData 
        {
            currentState = this.currentState,
            stateData = this.stateData 
        };

        string json = JsonUtility.ToJson(saveData);
        PlayerPrefs.SetString("MAWorkerData", json); // Or save to file
    }
    public void Load() 
    {
        if (PlayerPrefs.HasKey("MAWorkerData")) 
        {
            string json = PlayerPrefs.GetString("MAWorkerData");
            var saveData = JsonUtility.FromJson<SaveData>(json);

            this.currentState = saveData.currentState;
            this.stateData = saveData.stateData;

            // You might need to reinitialize some MonoBehaviour references
            // or perform other setup based on loaded state data
        }
    }
}
