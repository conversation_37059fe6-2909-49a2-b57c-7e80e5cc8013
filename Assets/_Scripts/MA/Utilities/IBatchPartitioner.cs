using System;
using System.Collections.Generic;
using JBooth.MicroSplat;
using UnityEngine;

public interface IBatchPartitioner
{
    public Component Component();
    public List<List<Transform>> GetExcludedTransforms();
    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced);
}

public interface IBatchWhenSame
{
    public Component Component();
    public bool IsApplicable(int _smi);
    public int BatchHash();
    public void OnBatch(Component _new);
}

public class MSBOAdaptor : IBatchWhenSame
{
    private MicroSplatBlendableObject m_MSBO;
    public MSBOAdaptor(MicroSplatBlendableObject _msbo)
    {
        m_MSBO = _msbo;
    }
    
    public Component Component() => m_MSBO;
    public bool IsApplicable(int _smi) => true;
    public int BatchHash()
    {
        int hash = 0;
        if (m_MSBO != null)
        {
            void AddToHash<T>(T _value) => hash = hash * 31 ^ (_value?.GetHashCode() ?? 0);
            AddToHash(m_MSBO.blendContrast);
            AddToHash(m_MSBO.blendCurve);
            AddToHash(m_MSBO.blendDistance);
            AddToHash(m_MSBO.doSnow);
            AddToHash(m_MSBO.doTerrainBlend);
            AddToHash(m_MSBO.matrixBlend);
            AddToHash(m_MSBO.noiseScale);
            AddToHash(m_MSBO.normalBlendDistance);
            AddToHash(m_MSBO.normalFromObject);
            AddToHash(m_MSBO.slopeContrast);
            AddToHash(m_MSBO.slopeFilter);
            AddToHash(m_MSBO.slopeNoise);
            AddToHash(m_MSBO.snowDampening);
            AddToHash(m_MSBO.snowWidth);
        }
        return hash;
    }

    public void OnBatch(Component _new)
    {
        ((MicroSplatBlendableObject)_new).Sync();
    }
}