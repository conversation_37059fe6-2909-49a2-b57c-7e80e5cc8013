using UnityEngine;

namespace BehaviourDesign
{
    public abstract class CharacterState
    {
        protected Character character;

        public CharacterState() { }

        public CharacterState(Character character)
        {
            this.character = character;
        }

        public virtual void Enter() { }
        public virtual void Update() { }
        public virtual void Exit() { }
    }
    public class Character : MonoBehaviour
    {
        // ... (other properties and methods)

        public CharacterState currentState;
        public GameObject target;

        void Update()
        {
            if (currentState != null)
            {
                currentState.Update();
            }
        }

        public void TransitionToState(CharacterState newState)
        {
            if (currentState != null)
            {
                currentState.Exit();
            }

            currentState = newState;
            currentState.Enter();
        }

        public void MoveTowards(Vector3 _position)
        {
            
        }
        public void PlayAnimation(string _animation)
        {
            
        }
    }
    public class ZombieIdleState : CharacterState
    {
        // ...
    }
    public class ZombieAttackingState : CharacterState
    {
        public ZombieAttackingState(Character character) : base(character) { }
    }

    public class ZombieChasingState : CharacterState
    {
        public override void Update()
        {
            // Chase the target
            character.MoveTowards(character.target.transform.position);

            // Play chasing animation
            character.PlayAnimation("Chase");

            // Check if close enough to attack
            if (character.target != null && Vector3.Distance(character.transform.position, character.target.transform.position) < 2f)
            {
                character.TransitionToState(new ZombieAttackingState(character));
            }
        }
    }
}
