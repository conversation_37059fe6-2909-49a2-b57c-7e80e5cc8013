using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCBeacon : BCActionBase
{
    [Save] private float m_mana;
    [Save] private bool m_hasLocalUnlock;
    [Save] private bool m_isComplete;
    [KnackField] public float m_manaToFill = 100;
    [KnackField] public float m_localUnlockRadius = 8;
    [Save] MACharacterBase m_instigator;
    
    private float m_lastManaLevel = 0;
    private Color m_lastMeterColour = Color.black;
    private float m_initialFlash = -1;
    private bool m_isActive = false;
    private int m_validState = -1;
    private Animator m_beaconAnimator = null;

    public static bool HaveUnlockedBeaconThisSession = false;
    
    public bool IsActive {
        get {
            int expectedBlocks = 1; // base
            foreach (var bh in GameManager.Me.m_state.m_buildHelpers)
            {
                if (bh.m_buildingID == m_building.m_linkUID)
                {
                    if (GameState_WildBlock.Find(bh.m_wildBlockID) != null)
                        return false;
                    ++ expectedBlocks;
                }
            }
            if (m_building.GetComponentsInChildren<Block>().Length != expectedBlocks)
                return false; // wait until all blocks are actually attached
            return true;
        }
    }
    public bool IsValid {
        get 
        {
            bool haveHeightHelpers = false;
            foreach (var bh in GameManager.Me.m_state.m_buildHelpers)
            {
                if (bh.m_buildingID == m_building.m_linkUID && bh.m_height < 0)
                {
                    haveHeightHelpers = true;
                    break;
                }
            }
            if (haveHeightHelpers == false) return true;
            // check that each buildHelper with a negative required height is at the correct index 
            var (blocks, _) = DesignTableManager.Me.GetBlockHierarchy(null, m_building.Visuals.gameObject);
            for (int i = 0; i < blocks.Count; ++i)
            {
                var bh = blocks[i].GetComponent<BuildHelper>();
                if (bh == null || bh.Height >= 0) continue;
                var requiredHeight = -bh.Height;
                if (requiredHeight != i) return false;
            }
            return true;
        }
    }

    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        base.Activate(_indexOfComponentType, _quantityInBuilding);
        if (m_isComplete) return;
        var (id, unlocked) = DistrictManager.Me.GetDistrictAtPoint(m_building.transform.position);
        if (unlocked == false)
        {
            if (m_hasLocalUnlock)
                CreateLocalUnlock();
            else
                CreateUnlockVisual();
        }
        ShowMana();
        m_building.ShouldJiggle = false;
        m_building.DisableContextMenu = true;
        m_beaconAnimator = GetComponentInChildren<Animator>();
    }
    
    void Update()
    {
        CheckComplete();
        ShowMana();
        UpdateLightningConnections();
        UpdateManaFocus();
        CheckCircleIcon();
    }

    private bool m_wasCharacterInRange = false;
    void CheckCircleIcon()
    {
        if (m_isActive) return;
        var chr = GameManager.Me.PossessedCharacter;
        if (chr == null) return;
        var isInRange = IsInRange(chr.transform.position);
        if (isInRange != m_wasCharacterInRange)
        {
            m_wasCharacterInRange = isInRange;
            SetAnimatorStage(isInRange ? 1 : 0);
        }
    }

    void CheckComplete()
    {
        if (m_isActive && m_validState == 1) return;
        bool isActive = IsActive;
        if (isActive != m_isActive)
        {
            if (isActive && m_validState == -1)
            {
                m_validState = IsValid ? 1 : 0;
                if (m_validState == 1)
                {
                    HaveUnlockedBeaconThisSession = true;
                    // ready to power up, lock!
                    foreach (var bh in m_building.GetComponentsInChildren<BuildHelper>())
                        Destroy(bh);
                    foreach (var db in m_building.GetComponentsInChildren<DTDragBlock>())
                        Destroy(db);
                }
                m_initialFlash = 1;
                EndBeaconUnlockInteractionAnim(m_building.transform.right);
                StopLightningConnections();
            }
            else if (isActive == false && m_validState != -1)
                m_validState = -1;
        }
        m_isActive = isActive;
    }

    private Vector3 m_manaFocus, m_eyeFocus;
    private void ShowMana()
    {
        var fraction = m_mana / m_manaToFill;
        var fractionReal = fraction;
        fraction = m_lastManaLevel = Mathf.Lerp(m_lastManaLevel, fraction, .1f);
        var highestPoint = m_building.GetHighestPoint();
        var lowestPoint = m_building.transform.position;
        var max = highestPoint.y - lowestPoint.y;
        var fill = max * fraction;

        Color clr;
        if (m_isComplete) clr = new Color(.3f, .3f, 8);
        else if (fractionReal < .9999f) clr = new Color(8, .3f, .3f);
        else clr = new Color(.3f, 8, .3f);
        clr = m_lastMeterColour = Color.Lerp(m_lastMeterColour, clr, .1f);

        if (m_initialFlash > 0)
        {
            float flash = m_initialFlash, speed = 1.5f;
            fill = max;
            if (m_validState == 1)
            {
                // valid, white repeatedly
                const float c_flashes = 4;
                speed *= 1.0f / c_flashes;
                fill = max;
                flash = Mathf.Repeat(flash * c_flashes, 1f);
            }
            clr = new Color(8 * flash, 8 * flash, 8 * flash);
            m_initialFlash = Mathf.Max(0, m_initialFlash - Time.deltaTime * speed);
        }

        foreach (var mv in m_building.GetComponentsInChildren<MeterVisual>())
        {
            mv.SetFill(fill, lowestPoint, Vector3.up);
            mv.SetColour(clr);
        }
        m_manaFocus = lowestPoint;
        var smoothed = fraction * fraction * (3 - fraction - fraction);
        m_manaFocus.y = Mathf.Lerp(lowestPoint.y, highestPoint.y, smoothed * .4f);
        m_eyeFocus = lowestPoint;
        m_eyeFocus.y = Mathf.Lerp(lowestPoint.y, highestPoint.y, smoothed * .85f);
    }

    private void SetAnimatorStage(int _stage)
    {
        var stageTrigger = $"Stage{_stage}";
        m_beaconAnimator.SetTrigger(stageTrigger);
    }

    private bool m_isControllingCamera = false;

    private IEnumerator Co_EndSequence()
    {
        SetAnimatorStage(4);
 
        yield return new WaitForSeconds(2f);

        SetAnimatorStage(5);

        var fwd = m_building.transform.right;
        var camPos = m_eyeFocus + fwd * 20 + Vector3.up * 2;
        var camFocus = m_eyeFocus;
        GameManager.Me.UpdateCameraPanSequence(camPos, camFocus - camPos, "");

        yield return new WaitForSeconds(2f);
        GameManager.Me.EndCameraPanSequence();
        FireUnlock();
    }

    private void UpdateManaFocus()
    {
        if (m_isComplete)
        {
            if (m_isControllingCamera)
            {
                m_isControllingCamera = false;
                if (GameManager.Me.LoadComplete == false)
                    GameManager.Me.EndCameraPanSequence();
                else
                {
                    Starburst.ShowStarburst("Completed", transform);
                    StartCoroutine(Co_EndSequence());
                }
            }
            return;
        }
        if (m_isActive == false) return;
        if (m_isControllingCamera == false)
        {
            if (m_beaconAnimator == null)
                m_beaconAnimator = GetComponentInChildren<Animator>(); // happens on load
            m_isControllingCamera = true;
            GameManager.Me.StartCameraPanSequence();
        }

        var fwd = m_building.transform.right;
        var camPos = m_building.transform.position + fwd * 45 + Vector3.up * 25;
        var camFocus = m_manaFocus;
        GameManager.Me.UpdateCameraPanSequence(camPos, camFocus - camPos, "");
    }    
    
    private Dictionary<Block, LineRenderer> m_connections = new();
    private void CreateLightningConnections()
    {
        // create lightning connections to all BuildHelpers
        foreach (var bh in GameManager.Me.m_state.m_buildHelpers)
        {
            if (bh.m_buildingID == m_building.m_linkUID)
            {
                var wb = GameState_WildBlock.Find(bh.m_wildBlockID);
                if (wb != null && wb.Obj != null)
                {
                    var go = Instantiate(Resources.Load<GameObject>("PowerEffects/BuildHelperConnection"));
                    go.transform.SetParent(transform);
                    var lr = go.GetComponent<LineRenderer>();
                    m_connections[wb.Obj.GetComponent<Block>()] = lr;
                }
            }
        }
        UpdateLightningConnections();
    }

    private void UpdateLightningConnections()
    {
        foreach (var kvp in m_connections)
        {
            var block = kvp.Key;
            if (block != null)
            {
                var lr = kvp.Value;
                if (lr != null)
                {
                    MAPowerEffectLightning.FillLineWithLightning(m_instigator.transform.position + Vector3.up * 2.8f, block.transform.position, lr, block.GetInstanceID(), .666f, .8f, .4f, .4f, 1);
                }
            }
        }
    }

    private void StopLightningConnections()
    {
        foreach (var kvp in m_connections)
        {
            var lr = kvp.Value;
            if (lr != null)
            {
                lr.enabled = false;
                Destroy(lr.gameObject);
            }
        }
        m_connections.Clear();
    }

    private void CreateLocalUnlock()
    {
        //m_building.gameObject.AddComponent<BuildHelperConnectionVisuals>();
        DistrictManager.Me.AddLocalUnlock(m_building.transform.position, m_localUnlockRadius);
        m_building.gameObject.IgnoreDistrictFilter(true);
        m_hasLocalUnlock = true;
        // allow interaction with all associated BuildHelpers
        foreach (var bh in GameManager.Me.m_state.m_buildHelpers)
        {
            if (bh.m_buildingID == m_building.m_linkUID)
            {
                var wb = GameState_WildBlock.Find(bh.m_wildBlockID);
                if (wb != null && wb.Obj != null)
                {
                    wb.m_hasEverBeenUnlocked = true;
                    wb.Obj.IgnoreDistrictFilter(true);
                }
            }
        }
    }

    private void CreateUnlockVisual()
    {
        MABeaconUnlockBlob.Create(this);
    }

    private Vector3 PedestalCenter => m_beaconAnimator.transform.position;
    public bool IsInRange(Vector3 _pos)
    {
        const float c_range = 2;
        var center = PedestalCenter;
        return (_pos - center).xzSqrMagnitude() < c_range * c_range;
    }

    public void SmashUnlockBlob(MACharacterBase _instigator)
    {
        m_instigator = _instigator;
        PlayBeaconUnlockInteractionAnim();
    }

    const string c_beaconUnlockPauseId = "BeaconUnlock";

    public void PlayBeaconUnlockInteractionAnim()
    {
        if (m_instigator == null) return;
        //GameManager.Me.LockPossessCamera();
        m_instigator.m_nav.PushPause(c_beaconUnlockPauseId);
        m_instigator.TemporarilyDisablePossession = true;
        m_instigator.transform.position = PedestalCenter.NewY(m_instigator.transform.position.y);
        m_instigator.transform.LookAt(m_building.transform.position.NewY(m_instigator.transform.position.y), Vector3.up);
        m_instigator.PlaySingleAnimation(NGManager.Me.m_beaconActivateHeroAnim, (b) => {
            Starburst.ShowStarburst("Activated", transform);
            SetAnimatorStage(2);
            CreateLocalUnlock();
            StartCoroutine(Co_SetSuperBright(m_instigator.gameObject, 0, 1, .25f));
            m_instigator.PlayerWorkerLoopAnimation(NGManager.Me.m_beaconActivateHeroResultIn, NGManager.Me.m_beaconActivateHeroResultLoop, null, (b) => {
                CreateLightningConnections();
            }, null, null, m_instigator.m_transform, NGMovingObject.AnimationTransformMatchType.None);
            GameManager.Me.Unpossess();
        });
    }
    
    public static IEnumerator Co_SetSuperBright(GameObject _go, float _start, float _end, float _duration)
    {
        var rnds = _go.GetComponentsInChildren<Renderer>();
        for (float t = 0; ; t += Time.deltaTime)
        {
            var f = t / _duration;
            bool finished = false;
            if (f >= 1)
            {
                f = 1;
                finished = true;
            }
            f = Mathf.Lerp(_start, _end, f);
            var superBright = new Color(f * 10, f * 10, f * 10, 1);
            foreach (var rnd in rnds)
            {
                if (rnd == null || rnd.sharedMaterial == null) continue;
                rnd.sharedMaterial.SetColor("_EmisivColor", superBright);
            }
            if (finished) break;
            yield return null;
        }
    }

    public void EndBeaconUnlockInteractionAnim(Vector3 _direction)
    {
        if (m_instigator == null) return;
        m_instigator.StopWorkerLoopAnimation(true);
        Starburst.ShowStarburst("ThrowHero", transform);
        SetAnimatorStage(3);
        m_instigator.ActivateRagDoll((_direction + Vector3.up * NGManager.Me.m_beaconHeroThrowUpFactor) * NGManager.Me.m_beaconHeroThrowSpeed, (System.Action<bool>) ((_interrupted) => {
            m_instigator.m_nav.PopPause(c_beaconUnlockPauseId);
            m_instigator.TemporarilyDisablePossession = false;
        }));
        StartCoroutine(Co_SetSuperBright(m_instigator.gameObject, 1, 0, .5f));
    }

    private void DestroyLocalUnlock()
    {
        DistrictManager.Me.RemoveLocalUnlock(m_building.transform.position);
        m_hasLocalUnlock = false;
    }

    void FireUnlock()
    {
        var districtName = m_building.Name;
        string districtID = null;
        bool unlocked = false;
        if (districtName.StartsWith("Beacon "))
        {
            districtName = districtName.Substring(7);
            if (DistrictManager.Me.GetDistrictByID(districtName) != null)
            {
                districtID = districtName;
                unlocked = DistrictManager.Me.IsDistrictUnlocked(districtID);
            }
        }
        if (districtID == null)
            (districtID, unlocked) = DistrictManager.Me.GetDistrictAtPoint(m_building.transform.position, true);
        if (districtID != null && unlocked == false)
        {
            DistrictManager.Me.UnlockDistrictByID(districtID);
            this.DoAfter(2f, DestroyLocalUnlock);
        }
    }
    
    private int m_lastTapFrame;
    override public bool UpdateTapWork()
    {
        if (m_isComplete) return false;
        if (m_isActive == false) return false;
        var tappedLastFrame = m_lastTapFrame == Time.frameCount - 1;
        m_lastTapFrame = Time.frameCount;
        if (m_mana >= m_manaToFill)
        {
            if (NGManager.Me.m_beaconsRequireFinalUnlockTap == false || tappedLastFrame == false)
            {
                // shut down
                m_isComplete = true;
            }
            return true;
        }
        var state = GameManager.Me.m_state;
        const float c_manaFillTime = 2;
        var remainingRoom = m_manaToFill - m_mana;
        var fillRate = m_manaToFill / NGManager.Me.m_beaconManaFillTime;
        var fillAmount = Mathf.Min(state.m_powerMana, fillRate * Time.deltaTime, remainingRoom);
        state.m_powerMana -= fillAmount;
        PlayerHandManager.Me.SetOverbright();
        PlayerHandManager.Me.ForcePowerUIDisplay();
        m_mana += fillAmount;
        return true;
    }

    public bool HasBeenActivated() => m_hasLocalUnlock || m_isComplete;
    
    public bool IsComplete() => m_isComplete;
}
