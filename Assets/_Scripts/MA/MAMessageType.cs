using System.Collections;
using System.Collections.Generic;
using UnityEngine;

   [System.Serializable]
    public class MAMessageType
    {
        public const string PrefabLocation = "_Prefabs/MessagePrefabs/";
        public const string HolderLocation = "Canvas/HUD/ScreenLocations";
        public static List<MAMessageType> s_messageTypes = new();
        public static List<MAMessageType> GetList => s_messageTypes;
        public string DebugDisplayName => m_indexer;
        public string id;
        public bool m_debugChanged;
        public string m_indexer;
        public string m_name;
        public string m_type;
        public string m_prefabName;
        public string m_holderName;

        public GameObject m_prefab;
        public Transform m_holder;
        public static bool PostImport(MAMessageType _what)
        {
            if (_what.m_prefabName.IsNullOrWhiteSpace() == false)
            {
                _what.m_prefab = Resources.Load<GameObject>($"{PrefabLocation}{_what.m_prefabName}");
                if (_what.m_prefab == null)
                {
                    Debug.LogError($"Message Prefab {PrefabLocation}{_what.m_prefabName} not found");
                    return false;
                }                
            }
            if(_what.m_holderName.IsNullOrWhiteSpace() == false)
            {
                var parent = GameObject.Find($"{HolderLocation}");
                if (parent == null)
                {
                    Debug.LogError($"Message Holder {HolderLocation}/{_what.m_holderName} not found");
                    return false;
                }
                _what.m_holder = parent.transform.Find(_what.m_holderName);
                if(_what.m_holder == null)
                {
                    Debug.LogError($"Message Holder {HolderLocation}/{_what.m_holderName} not found");
                    return false;
                }
            }
            return true;
        }

        public static List<MAMessageType> LoadInfo()
        {
            s_messageTypes = NGKnack.ImportKnackInto<MAMessageType>(PostImport);
            return s_messageTypes;  
        }
        public static MAMessageType GetInfo(string _indexer)
        {
            return s_messageTypes.Find(x => x.m_indexer == _indexer);
        }
    }
