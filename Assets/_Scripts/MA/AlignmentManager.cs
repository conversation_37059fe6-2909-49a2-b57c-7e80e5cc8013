using UnityEngine;

public class AlignmentManager : MonoSingleton<AlignmentManager>
{
     private static float s_spoofAlignment = -10;
     private static DebugConsole.Command s_spoofAlignmentCmd = new ("spoofalignment", _s => s_spoofAlignment = floatinv.Parse(_s), "Override the alignment with the specified value (0 is neutral, -10 is no override)", "<float,-10,10>");
     
     public bool IsEvil => Alignment < NGManager.Me.m_evilAlignmentThreshold;
     public bool IsGood => Alignment > NGManager.Me.m_goodAlignmentThreshold;
     public bool IsNeutral => IsEvil == false && IsGood == false;
     public float Alignment
     { 
          get 
          {
               if(s_spoofAlignment > -10)
                    return s_spoofAlignment; 
               return GameManager.Me.m_state.m_alignment.m_value; 
          } 
     }
     
     public void Start()
     {
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
          DayNight.Me.RegisterNewDayCallback(OnNewDay);
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
     }
     
     public void OnNewDay()
     {
          if(NGPlayer.Me.m_cash.Balance < 0)
          {
               ApplyAction("NegativeBalance", -0.005f, "Cash");
          }
          // Update homeless counts
          foreach(var worker in NGManager.Me.m_MAWorkerList)
          {    
               if(worker.Home == null)
               {
                    worker.GameStatePerson.m_nightsHomeless++;
               }
               else
               {
                    worker.GameStatePerson.m_nightsHomeless = 0;
               }
                    
               // If newly homeless
               if(worker.GameStatePerson.m_nightsHomeless == 1)
               {
                    ApplyAction("HomelessWorker", -0.005f, "");
               }
          }
          
          var gameInfo = GameManager.Me.m_state.m_gameInfo;
          if(NGManager.Me.m_MAHeroList.Count > 0 && gameInfo.m_heroKnockedDownToday == false && gameInfo.m_failedThisDay == false)
          {
               ApplyAction("HeroSurvivedNight", 0.01f, "");
          }
          
          gameInfo.m_failedThisDay = false;
          gameInfo.m_heroKnockedDownToday = false;
//          MANightChallengeManager.Me.SetupNewNightRewards();

     }
     
     public string GetAlignmentType()
     {
          if(IsEvil) return "Evil";
          if(IsGood) return "Good";
          return "Neutral";
     }
     
     public void CharacterDied(IDamageReceiver.DamageSource _source, MACharacterBase _character)
     {
          float deathValue = _character.GetMovingInfo().m_deathAlignment;
          var context = _character.CreatureInfo.m_name;

          if (_source.HasFlag(IDamageReceiver.DamageSource.Possession))
          {
               ApplyAction("DeathByPossession", deathValue, context);
          }
          else if (_source.HasFlag(IDamageReceiver.DamageSource.HandPower))
          {
               ApplyAction("DeathByHandPower", deathValue, context);
          }
          else if (_source.HasFlag(IDamageReceiver.DamageSource.ThrownByHand))
          {
               ApplyAction("DeathByThrowing", deathValue, context);
          }
          else if (_source.HasFlag(IDamageReceiver.DamageSource.ThrownObject))
          {
               ApplyAction("DeathByObject", deathValue, context);
          }
          else if (_source.HasFlag(IDamageReceiver.DamageSource.AI | IDamageReceiver.DamageSource.Turret))
          {
               ApplyAction("DeathByTurret", deathValue, context);
          }
          else
          {
               ApplyAction("Death", deathValue, context);
          }
     }
     
     private float GetModifiedValue(MAAlignmentAction _info, float _baseValue, int _actionCount)
     {
          if(_info == null) 
               return _baseValue;
      
          // Check if we need to decay    
          var value = _baseValue * _info.m_multiplier;
          if (_info.m_decayThreshold < 0 || _actionCount <= _info.m_decayThreshold)
               return value;
          
          // Decay value
          int decaySteps = _actionCount - _info.m_decayThreshold;
          return value * Mathf.Exp(-_info.m_decayRate * decaySteps);
     }
     
     private GameState_Alignment.ActionRecord GetOrCreateActionRecord(string _uid)
     {
          if(GameManager.Me.m_state.m_alignment.m_actionHistory.TryGetValue(_uid, out var actionHistory) == false)
          {
               actionHistory = new GameState_Alignment.ActionRecord();
               GameManager.Me.m_state.m_alignment.m_actionHistory[_uid] = actionHistory;
          }
          return actionHistory;
     }
     
     public void ApplyAction(string _action, float _value, string _context)
     {
          string uniqueActionReference = $"{_action}:{_context}";
          
          var actionRecord = GetOrCreateActionRecord(uniqueActionReference);
          float finalValue = GetModifiedValue(MAAlignmentAction.Get(_action), _value, actionRecord.m_count);
          
          // Update specific action history
          actionRecord.m_value += finalValue;
          actionRecord.m_count++; 
          
          // Update overall alignment
          GameManager.Me.m_state.m_alignment.m_value += finalValue;
     }
}
