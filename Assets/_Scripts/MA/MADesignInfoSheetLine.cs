using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MADesignInfoSheetLine : MonoBehaviour
{
    public TMP_Text m_name;
    public Image m_icon;
    public TMP_Text m_count;
    public Image m_bottomLine;

    public void ClickedLine()
    {
    }
    
    void Activate(string _name, Sprite _icon, int _count, bool _bottomLine)
    {
    
        m_name.text = _name;
        if(_icon)
            m_icon.sprite = _icon;
        else
            m_icon.gameObject.SetActive(false);
        m_count.text = _count.ToString();
        m_bottomLine.gameObject.SetActive(_bottomLine);
    }
    void Activate(string _name, string _details, bool _bottomLine)
    {
        m_name.text = _name;
        m_count.text = _details;
        if(m_icon)
            m_icon.gameObject.SetActive(false);
        //m_count.gameObject.SetActive(false);
        m_bottomLine.gameObject.SetActive(_bottomLine);
    }
    public static MADesignInfoSheetLine Create(Transform _holder, string _name, Sprite _icon, int _count, bool _bottomLine)
    {
        var prefab = Resources.Load<MADesignInfoSheetLine>("_Prefabs/Dialogs/MADesignInfoSheetLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MADesignInfoSheetLine>();
        instance.Activate(_name, _icon, _count, _bottomLine);
        return instance;
    }
    public static MADesignInfoSheetLine Create(Transform _holder, string _name, string _details, bool _bottomLine)
    {
        var prefab = Resources.Load<MADesignInfoSheetLine>("_Prefabs/Dialogs/MADesignInfoBuildSheetLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MADesignInfoSheetLine>();
        instance.Activate(_name, _details, _bottomLine);
        return instance;
    }
}
