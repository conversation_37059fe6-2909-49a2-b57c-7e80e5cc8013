using System;
using UnityEngine;

public class MARoadPoint : MonoBeh<PERSON><PERSON>
{
    public enum MainRoadPointType
    {
        StartPoint,
        // OutsideEntryGate,
        // InsideEntryGate,
        // InsideExitGate,
        // OutsideExitGate,
        EndPoint,
    }
    
    public MainRoadPointType m_pointType;
    
    private GateOpener m_accessGate;

    public GateOpener AccessGate(Vector3 _target)
    {
        if (m_accessGate == null)
            m_accessGate = GateOpener.ChooseBestGateFromPoints(transform.position, _target);
        return m_accessGate;
    }
}
