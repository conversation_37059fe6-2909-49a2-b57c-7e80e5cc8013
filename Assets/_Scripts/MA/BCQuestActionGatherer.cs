using System.Collections.Generic;
using UnityEngine;

public class BCQuestActionGatherer : BCActionGatherer
{
	public List<MAFlowCharacter> m_characters = new();
    public GameObject m_treesParent = null;
    public override bool ShowWarning => false;

    public List<TreeHolder> m_treeHolders = new();

    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
	    base.Activate(_indexOfComponentType, _quantityInBuilding);

    }

    public override bool AskForInputStock()
	{
		var freeWorker = m_characters.Find(x => x.m_state == NGMovingObject.STATE.IDLE || x.m_state == NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
		if(freeWorker == null) return false;
	
		(GameObject go, PeepActions action) what = GetPickupObject(freeWorker);
		if(what.go)
		{
			freeWorker.SetMoveToObject(what.go, what.action);
			//m_building.Leave(freeWorker);
			return true;
		}
		return false;
	}

    public override bool Arrive(MACharacterBase _worker)
    {
        return base.Arrive(_worker);
    }

    override public NGStock GetInputStock() => m_stock;
    
    private bool IsGoodTree(TreeHolder _tree)
    {
        var chopObject = _tree.GetComponent<BCChopObject>();
        if (chopObject != null)
        {
            if (_tree == null) return false;
            
            if (!_tree.gameObject.activeSelf) return false;
            if (_tree.m_treeType != m_treeHolderType)
                return false;
            //if (_tree.GetPrefab() == null)
           //     return false;
            if (_tree.IsLocked)
                return false;
                
            if (!chopObject.IsChopObjectValid)
                return  false;
            if (chopObject.IsChoppedDown)
                return false;
            if (chopObject.m_worker != null)
                return false;
        }
        return true;
    }

    public override MACharacterBase GetAvailableWorker()
    {
        return m_characters.Find(x => x.m_state == NGMovingObject.STATE.IDLE);
    }
    
    public override GameObject GetNextResource()
    {
	    var go = base.GetNextResource();
	    if(m_treesParent == null)
	        return null;
	    GameObject treeObj = null;
	    foreach (var tree in m_treeHolders)
	    { 
	        if (IsGoodTree(tree))
	        {
	            return tree.gameObject;
	        }
	    }
	    return null;
    }
}
