using System;
using UnityEngine;
using System.Collections.Generic;

[Serializable]
public class MAAlignmentAction
{
    public string m_action;
    public float m_multiplier;
    public int m_decayThreshold; // Only start diminishing after this many attempts
    public float m_decayRate;
    
    
    public static List<MAAlignmentAction> s_allActions = new List<MAAlignmentAction>();
    public static Dictionary<string, MAAlignmentAction> s_actionLookup = new();
    public static List<MAAlignmentAction> GetList => s_allActions;
    public string DebugDisplayName => m_action;
    public string id;
    public bool m_debugChanged;
    public static bool PostImportARecord(MAAlignmentAction _what)
    {
        return true;
    }
    
    public static MAAlignmentAction Get(string _action)
    {
        s_actionLookup.TryGetValue(_action, out var result);
        return result;
    }
    
        
    public static List<MAAlignmentAction> LoadInfo()
    {
        s_allActions = NGKnack.ImportKnackInto<MAAlignmentAction>(PostImportARecord);
        
        // Check for duplicates
        s_actionLookup.Clear();
        foreach(var action in s_allActions)
        {
            s_actionLookup[action.m_action] = action;
        }
        
        return s_allActions;
    }
}
