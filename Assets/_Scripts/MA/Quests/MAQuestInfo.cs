using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Analytics;

public class MAQuestInfo : MonoBehaviour
{
    public static List<MAQuestInfo> s_maQuestInfo = new();

    public enum QuestType
    {
        None,
        Skill,
        Design,
        Posesss,
        Collect,
        Decision,
        Build,
        Combat,
    }
    public string m_name;   //Name of the quest
    public string m_description; //Decription of the quest
    public string m_typeString; // quest type decoded at load to m_type
    [ScanField] public string m_toRegion; // points to the region that the quest is in
    public string m_ownerString; // this is a call to KnackSupport usually SwawnQuest(Character[61], MAQuestInfo)
    [ScanField] public string m_toFlow; // points to flow that will be spawned when the player accepts the quest decoded to m_flow at load
    [ScanField] public string m_toRewards; // points to all rewards that will be given when the quest is completed. Decoded to m_rewards at load
    
    public QuestType m_type;
    public ReactDistrictTable m_region;
    public List<NGBusinessFlow> m_flow;
    public List<NGBusinessGift> m_rewards;
    
    public void PopulateDistricts(string _name)
    {
        var districts = s_maQuestInfo.FindAll(o=>o.m_name == _name);
        foreach (var q in s_maQuestInfo)
        {
            if (q.m_region == null || q.m_region.m_districtName.Equals(_name) == false) continue;
            var owner = q.m_ownerString;
            if (owner.IsNullOrWhiteSpace() == false)
            {
                if (owner.Contains("Quest[") == false)
                {
                    owner.Replace("(", $"(Quest[{q.m_name}]");
                }
                if (MAParserSupport.TryParse(owner, out var result) == false)
                {
                    Debug.LogError($"MAQuestInfo - PopulateDistricts {q.m_ownerString} bad syntax in quest named: {q.m_name}");
                    continue;
                }
            }
        }
    }

    public static MAQuestInfo GetInfo(string _name)
    {
        return s_maQuestInfo.Find(o=>o.name == _name);
    }
    public static bool PostImport(MAQuestInfo _what)
    {
        Enum.TryParse<QuestType>(_what.m_typeString, out _what.m_type);
        _what.m_region = ReactDistrictTable.GetInfo(_what.m_toRegion);
        _what.m_flow = NGBusinessFlow.GetInfo(_what.m_toFlow);
        _what.m_rewards = new();
        foreach(var g in _what.m_toRewards.Split('|', '\n', ';'))
        {
            var r = NGBusinessGift.GetInfo(g);
            if(r == null)
            {
                Debug.LogError($"MAQuestInfo - PostImport '{g}' Reward not found in quest named: {_what.m_name}");
                continue;
            }
            _what.m_rewards.Add(r);
        }
        return true;
    }
    public static List<MAQuestInfo> LoadInfo()
    {
        s_maQuestInfo = NGKnack.ImportKnackInto<MAQuestInfo>(PostImport);

        return s_maQuestInfo;
    }
}
