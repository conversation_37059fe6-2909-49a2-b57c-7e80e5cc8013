using System.Collections;
using UnityEngine;
using UnityEngine.Events;

public class MAQuestStigmataTrapRelease : MonoBehaviour
{
    public enum TrapReleaseState
    {
        Disabled = 0,
        Active,
        Partial,
        Full,
        Count
    }

    public Animator m_plateAnim;
    public UnityEvent m_onPartialPressed;

    private float m_partialTriggerResetDelay = 4.0f;

    private TrapReleaseState m_state = TrapReleaseState.Disabled;
    private int m_stateID;
    private float m_partialTriggerResetTime = 0.0f;
    private MACharacterBase m_releasingCharacter = null;
    public MACharacterBase ReleasingCharacter{ get => m_releasingCharacter; }

    public TrapReleaseState State
    {
        get => m_state;
        set
        {
            if (m_state != value)
            {
                m_state = value;

                switch (m_state)
                {
                    case TrapReleaseState.Partial:
                        m_onPartialPressed?.Invoke();
                        m_partialTriggerResetTime = Time.time + m_partialTriggerResetDelay;
                        break;
                    default:
                        break;
                }

                m_plateAnim.SetInteger(m_stateID, (int)m_state);
            }
        }
    }

    private void Awake()
    {
        m_stateID = Animator.StringToHash("State");
    }

    private void OnTriggerEnter(Collider other)
    {
        CheckTriggered(other);
    }

    private void OnTriggerStay(Collider other)
    {
        CheckTriggered(other);
    }

    private void OnTriggerExit(Collider other)
    {
        switch (m_state)
        {
            case TrapReleaseState.Partial:
                if(GetColliderType(other, out MACharacterBase character) == TrapReleaseState.Partial)
                {
                    State = TrapReleaseState.Active;
                }
                break;
            default:
                break;
        }
    }

    private void CheckTriggered(Collider _other)
    {
        if (Time.time >= m_partialTriggerResetTime)
        {
            switch (m_state)
            {
                case TrapReleaseState.Active:
                case TrapReleaseState.Partial:
                    {
                        TrapReleaseState type = GetColliderType(_other, out MACharacterBase character);

                        if (type < TrapReleaseState.Count)
                        {
                            State = type;

                            if (type == TrapReleaseState.Full)
                            {
                                m_releasingCharacter = character;
                            }
                        }

                        break;
                    }
                default:
                    break;
            }
        }
    }

    private TrapReleaseState GetColliderType(Collider _other, out MACharacterBase _character)
    {
        TrapReleaseState type = TrapReleaseState.Count;

        _character = _other.GetComponent<MACharacterBase>();

        if(_character != null && GameManager.Me.PossessedCharacter == _character)
        {
            type = TrapReleaseState.Partial;

            if(_character is MAHeroBase || _character is MAWorker)
            {
                type = TrapReleaseState.Full;
            }
        }

        return type;
    }
}
