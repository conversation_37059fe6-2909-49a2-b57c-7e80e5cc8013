using System;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MAQuestStigmata : MAQuestBase
{
    public MAQuestStigmataTrap m_trap;
    
    [Header("Possessabale Spawns")]
    public string m_placedPossessableCharacterType = "QuestChicken";
    public int m_numPossessableSpecialSpawns = 5;
    [Range(0.25f, 25f)]
    public float m_spawnerWalkSpeed = 5f;
    public float m_safeSpawnInterval = 3f; 
    
    [Header("Possessabable Behaviour")]
    public float m_possessionRadius = 5;
    
    [SerializeField]
    private Transform m_possessionCentre = null;

    [SerializeField]
    private BuildingNavBlocker m_navBlockerHand = null, m_navBlockerBall = null, m_navBlockerTrap = null;
    private GameObject m_navBlockerHandObj = null, m_navBlockerBallObj = null, m_navBlockerTrapObj = null;
    
    [Header("Waypoint Sequences")] 
    public float m_wayPointRadius = 0.5f;
    [SerializeField]
    private List<WaitPointSequence> m_sequences;
    
    [ReadOnlyInspector]
    public List<PlacedCharacterSaveData> m_placedCharacterSaveDatas = new();

    public bool m_debugDrawGizmos = false;
    
    private float m_spawnDelay = 0f; 
    
    private bool m_showingKeyboardShortCut = false;

    private bool m_somethingWasPossessed = false;
    private bool m_showFreeHandDecision = false;
    
    private List<MACharacterBase> m_possessablesNearby = new();
    private List<MACharacterBase> m_questPlacedPossessableCharacters = new();
    private List<MACreatureInfo> m_scheduledSpawns = new();

    private MAAnimalHouse m_closestAnimalHouse = null;
    private NGMovingObject m_previousPossessed = null;
    
    private Vector3 HandPos => m_possessionCentre != null ? m_possessionCentre.position : m_trap.m_handTarget.transform.position;
    
    protected override void Awake()
    {
        base.Awake();
        
        m_navBlockerBallObj = m_navBlockerBall.gameObject;
        m_navBlockerTrapObj = m_navBlockerTrap.gameObject;
        m_navBlockerHandObj = m_navBlockerHand.gameObject;
        m_navBlockerBallObj.SetActive(false);
        m_navBlockerTrapObj.SetActive(false);
        m_navBlockerHandObj.SetActive(false);
        
        m_debugDrawGizmos = false;

        for (var iSequence = m_sequences.Count - 1; iSequence >= 0; iSequence--)
        {
            for (var iWayPoint = m_sequences[iSequence].m_sequence.Count - 1; iWayPoint >= 0; iWayPoint--)
            {
                if (m_sequences[iSequence].m_sequence[iWayPoint].m_pos == null)
                {
                    Debug.LogError($"{GetType().Name} - Removing Sequence WayPoint index [{iSequence}][{iWayPoint}]");
                    m_sequences[iSequence].m_sequence.RemoveAt(iWayPoint);
                }

                if (m_sequences[iSequence].m_sequence.Count == 0)
                {
                    Debug.LogError($"{GetType().Name} - Removing Sequence index [{iSequence}]");
                    m_sequences.RemoveAt(iSequence);
                }
            }
        }
    }

    private void Update()
    {
        UpdateSpawns();
        UpdateTrap();
        UpdateNearbyPossessables();
        UpdateChickens();
        UpdateNavBlockers();
    }

    private void UpdateNavBlockers()
    {
        bool inProgress = m_status == QuestStatus.InProgress;
        bool trapActive = m_trap.State is
            MAQuestStigmataTrap.TrapState.Triggered or
            MAQuestStigmataTrap.TrapState.Trapped or
            MAQuestStigmataTrap.TrapState.Released;
        m_navBlockerBallObj.SetActive(inProgress && m_trap.State is MAQuestStigmataTrap.TrapState.Armed or MAQuestStigmataTrap.TrapState.Triggered);
        m_navBlockerTrapObj.SetActive(inProgress && trapActive);
        m_navBlockerHandObj.SetActive(inProgress && trapActive);
    }
    
    private void RefreshCharacterSaveData()
    {
        for (var i1 = m_placedCharacterSaveDatas.Count - 1; i1 >= 0; i1--)
        {
            if (NGManager.Me.FindCharacterByID(m_placedCharacterSaveDatas[i1].m_charID) == null)
            {
                m_placedCharacterSaveDatas.RemoveAt(i1);
            }
        }
    }

    private void UpdateChickens()
    {
        for (var i = m_questPlacedPossessableCharacters.Count - 1; i >= 0; i--)
        {
            if (m_questPlacedPossessableCharacters[i] == null)
            {
                m_questPlacedPossessableCharacters.RemoveAt(i);
                RefreshCharacterSaveData();
            }
            else if(Mathf.Approximately(m_questPlacedPossessableCharacters[i].CharacterGameState.m_walkSpeed, m_spawnerWalkSpeed) == false)
            {
                m_questPlacedPossessableCharacters[i].CharacterGameState.m_walkSpeed = m_spawnerWalkSpeed;
            }
        }

        if (m_status == QuestStatus.InProgress)
        {
            if (m_closestAnimalHouse == null)
            {
                float bestDist = Single.MaxValue;
                foreach (MABuilding meMaBuilding in NGManager.Me.m_maBuildings)
                {
                    MAAnimalHouse aHouse = meMaBuilding.GetComponentInChildren<MAAnimalHouse>();
                    if (aHouse != null)
                    {
                        float d = aHouse.transform.position.DistanceXZSq(transform.position);
                        if (d <= bestDist)
                        {
                            bestDist = d;
                            m_closestAnimalHouse = aHouse;
                        }
                    }
                }
            }
            
            if (m_trap.State == MAQuestStigmataTrap.TrapState.Trapped &&
                m_questPlacedPossessableCharacters.Count + m_scheduledSpawns.Count < m_numPossessableSpecialSpawns &&//populate
                m_closestAnimalHouse != null &&
                m_closestAnimalHouse.Building != null && 
                m_placedPossessableCharacterType.IsNullOrWhiteSpace() == false)
            {
                MACreatureInfo creatInfo = MACreatureInfo.GetInfo(m_placedPossessableCharacterType);
                if (m_spawnDelay <= 0)
                {
                    Spawn(creatInfo);
                }
                else
                {
                    m_scheduledSpawns.Add(creatInfo);
                }
            }
            else if (m_questPlacedPossessableCharacters.Count > m_numPossessableSpecialSpawns)
            {
                var possessable = m_questPlacedPossessableCharacters.PickRandom();
                Despawn(possessable);
            }
        }

        for (var iChar = m_questPlacedPossessableCharacters.Count - 1; iChar >= 0; iChar--)
        {
            var possessChar = m_questPlacedPossessableCharacters[iChar];
            if (possessChar.m_insideMABuilding == null && possessChar.IsNotBusy)
            {
                if (possessChar.IsAtObjectiveWaypoint() &&
                    possessChar.IsOrWillBeInState(CharacterStates.GuardLocation))
                {
                    var wayPoint = GetWaypoint(possessChar);
                    if (wayPoint != null)
                    {
                        if(possessChar.CharacterGameState.m_timeInState >= wayPoint.m_timeToWait) //GetWaitPoint(m_iCurrentSequence, m_iCurrentWaitPoint).m_timeToWait))
                        {
                            var nextWayPoint = GetNextWaypoint(possessChar);
                            possessChar.SetToGuard( nextWayPoint.m_pos.position, m_wayPointRadius);
                        }
                    }
                    else
                    {
                        Despawn(possessChar);
                    }
                }
            }
        }
    }

    private void UpdateTrap()
    {
        if (m_status == QuestStatus.InProgress)
        {
            switch (m_trap.State)
            {
                case MAQuestStigmataTrap.TrapState.Armed:
                    if (m_trap.CheckTriggered())
                    {
                        TriggerTrap();
                    }
                    break;
                case MAQuestStigmataTrap.TrapState.Trapped:
                    if(m_trap.m_trapRelease.State == MAQuestStigmataTrapRelease.TrapReleaseState.Full)
                    {
                        ReleaseTrap();
                    }
                    break;
                default:
                    break;
            }
        }
    }
    
    private void UpdateNearbyPossessables()
    {
        bool containedPrev = m_possessablesNearby.Count > 0;

        m_possessablesNearby.Clear();

        if (m_status != QuestStatus.InProgress || m_trap.State != MAQuestStigmataTrap.TrapState.Trapped)
        {
            if (m_showingKeyboardShortCut)
            {
                KeyboardShortcutManager.Me.PopShortcuts();
                m_showingKeyboardShortCut = false;
            }
            return;
        }
        
        if (m_status == QuestStatus.InProgress &&
            /*m_trap.State == MAQuestStigmataTrap.TrapState.Triggered &&*/
            GameManager.Me.IsPossessing == false)
        {
            foreach (MACharacterBase maCharacterBase in NGManager.Me.m_MACharacterList)
            {
                if (maCharacterBase.transform.position.DistanceXZSq(HandPos) <=
                    m_possessionRadius * m_possessionRadius)
                {
                    m_possessablesNearby.Add(maCharacterBase);
                }
            }
        }
        
        if (containedPrev && m_possessablesNearby.Count == 0)
        {
            KeyboardShortcutManager.Me.PopShortcuts();
            m_showingKeyboardShortCut = false;
        }
        else if (containedPrev == false && m_possessablesNearby.Count > 0)
        {
            if (m_trap.State == MAQuestStigmataTrap.TrapState.Trapped)
            {
                KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.Possess);
                m_showingKeyboardShortCut = true;
            }
        }
    }

    private void UpdateSpawns()
    {
        if (m_scheduledSpawns.Count > 0 && m_spawnDelay <= 0)
        {
            Spawn(m_scheduledSpawns[0]);
            m_scheduledSpawns.RemoveAt(0);
        }
        else if (m_spawnDelay > 0)
        {
            m_spawnDelay -= Time.deltaTime;
        }
    }
    
    private void Spawn(MACreatureInfo _info)
    {
        var newPossessable = MACreatureControl.Me.SpawnNewCreatureAtBuilding(_info, m_closestAnimalHouse.Building);
        m_questPlacedPossessableCharacters.Add(newPossessable);
        m_spawnDelay = Mathf.Clamp(m_safeSpawnInterval,0, Single.MaxValue);
        newPossessable.CharacterGameState.m_walkSpeed = m_spawnerWalkSpeed;
        newPossessable.SetSpeed(newPossessable.CharacterGameState.m_walkSpeed);
        m_placedCharacterSaveDatas.Add(new PlacedCharacterSaveData()
        {
            m_charID = newPossessable.m_ID,
            m_iCurrentSequence = 0,
            m_iCurrentWaitPoint = 0,
        });
        newPossessable.SetToGuard( GetWaypoint(newPossessable).m_pos.position, m_wayPointRadius);
    }

    [Serializable]
    public class BackupPossessUnlockState
    {
        public bool m_isValid = false;
        public bool m_possessAnimals = false;
        public bool m_possessBuildings = false; 
        public bool m_possessCreatures = false; 
        public bool m_possessHeroes = false; 
        public bool m_possessTourists = false; 
        public bool m_possessWorkers = false; 
    }

    private BackupPossessUnlockState m_backupPossessUnlockState = new();

    private void TriggerTrap()
    {
        MAUnlocks unlocks = MAUnlocks.Me;
        m_backupPossessUnlockState.m_possessAnimals = unlocks.m_possessAnimals;
        m_backupPossessUnlockState.m_possessBuildings = unlocks.m_possessBuildings;
        m_backupPossessUnlockState.m_possessCreatures = unlocks.m_possessCreatures;
        m_backupPossessUnlockState.m_possessHeroes = unlocks.m_possessHeroes;
        m_backupPossessUnlockState.m_possessTourists = unlocks.m_possessTourists;
        m_backupPossessUnlockState.m_possessWorkers = unlocks.m_possessWorkers;

        Debug.Log("Lock");
        m_backupPossessUnlockState.m_isValid = true;
        
        unlocks.m_possessAnimals = false;
        unlocks.m_possessBuildings = false;
        unlocks.m_possessCreatures = false;
        unlocks.m_possessHeroes = false;
        unlocks.m_possessTourists = false;
        unlocks.m_possessWorkers = false;

        m_trap.State = MAQuestStigmataTrap.TrapState.Triggered;
    }

    private void ReleaseTrap()
    {
        m_trap.State = MAQuestStigmataTrap.TrapState.Released;
    }

    override protected void OnPostLoad()
    {
        // do not call base OnPostLoad(), no quest giver
        InitReferences();
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        switch(m_status)
        {
            case QuestStatus.InProgress:
                m_trap.gameObject.SetActive(true);
                GameManager.Me.m_onPossess -= OnPossess;
                GameManager.Me.m_onPossess += OnPossess;
                break;
            case QuestStatus.Active:
                m_scheduledSpawns.Clear();
                break;
            case QuestStatus.Completed:
                if(m_showingKeyboardShortCut) KeyboardShortcutManager.Me.PopShortcuts();
                m_scheduledSpawns.Clear();
                UpdateNavBlockers();
                break;
        }
    }

    private void OnPossess(bool _start, NGLegacyBase _object)
    {
        if (m_status != QuestStatus.InProgress)
        {
            if (_start)
            {
            }
            else
            {
               // m_previousPossessed = _object as NGMovingObject;
                if (GameManager.Me.IsPossessing == false)
                {
                    foreach (MACharacterBase questPlacedPossessableCharacter in m_questPlacedPossessableCharacters)
                    {
                        MACharacterBase poss = questPlacedPossessableCharacter as MACharacterBase;
                        Despawn(poss);
                    }
                }

                GameManager.Me.m_onPossess -= OnPossess;
                return;
            }
        }
        else
        {       
            if (_start == false)
            {
                var chr = _object as MACharacterBase;
                if (chr != null && m_questPlacedPossessableCharacters.Contains(chr) && chr.CreatureInfo.m_name.Equals(m_placedPossessableCharacterType, StringComparison.OrdinalIgnoreCase))
                {
                    Despawn(chr);
                }

                if (m_trap.State == MAQuestStigmataTrap.TrapState.Trapped)
                {
                    Debug.Log("Lock");
                    
                    MAUnlocks unlocks = MAUnlocks.Me;
                    m_backupPossessUnlockState.m_possessAnimals = unlocks.m_possessAnimals;
                    m_backupPossessUnlockState.m_possessBuildings = unlocks.m_possessBuildings;
                    m_backupPossessUnlockState.m_possessCreatures = unlocks.m_possessCreatures;
                    m_backupPossessUnlockState.m_possessHeroes = unlocks.m_possessHeroes;
                    m_backupPossessUnlockState.m_possessTourists = unlocks.m_possessTourists;
                    m_backupPossessUnlockState.m_possessWorkers = unlocks.m_possessWorkers;

                    m_backupPossessUnlockState.m_isValid = true;
        
                    unlocks.m_possessAnimals = false;
                    unlocks.m_possessBuildings = false;
                    unlocks.m_possessCreatures = false;
                    unlocks.m_possessHeroes = false;
                    unlocks.m_possessTourists = false;
                    unlocks.m_possessWorkers = false;
                }
            }
            else
            {
                if (m_trap.State == MAQuestStigmataTrap.TrapState.Trapped)
                {
                    MAUnlocks unlocks = MAUnlocks.Me;
                    if (m_backupPossessUnlockState.m_isValid)
                    {
                        Debug.Log("Unlock");
                        unlocks.m_possessAnimals = m_backupPossessUnlockState.m_possessAnimals;
                        unlocks.m_possessBuildings = m_backupPossessUnlockState.m_possessBuildings;
                        unlocks.m_possessCreatures = m_backupPossessUnlockState.m_possessCreatures;
                        unlocks.m_possessHeroes = m_backupPossessUnlockState.m_possessHeroes;
                        unlocks.m_possessTourists = m_backupPossessUnlockState.m_possessTourists;
                        unlocks.m_possessWorkers = m_backupPossessUnlockState.m_possessWorkers;
                    }

                    m_backupPossessUnlockState.m_isValid = false;
                }
            }

            m_previousPossessed = _object as NGMovingObject;
        }
    }

    private void Despawn(MACharacterBase _toRemove)
    {
        MACharacterStateFactory.ApplyCharacterState(CharacterStates.GoingHome, _toRemove);
        m_questPlacedPossessableCharacters.Remove(_toRemove);
        m_placedCharacterSaveDatas.RemoveAll(x => x.m_charID == _toRemove.m_ID);
    }
    

    private void InitReferences()
    {
        m_questPlacedPossessableCharacters.Clear();
        if (m_closestAnimalHouse != null)
        {
            foreach (MAAnimal maAnimal in NGManager.Me.m_MAAnimalList)
            {
                if (maAnimal.GameState.m_homeComponentId > 0)
                {
                    int iHomeComp = m_closestAnimalHouse.Building.m_components.FindIndex(comp =>
                        comp.m_uid == maAnimal.GameState.m_homeComponentId &&
                        maAnimal.CreatureInfo == MACreatureInfo.GetInfo(m_placedPossessableCharacterType));
                    if (iHomeComp > -1)
                    {
                        int iRes = m_placedCharacterSaveDatas.FindIndex(x => x.m_charID == maAnimal.m_ID);
                        if (iRes == -1)
                        {
                            maAnimal.DestroyMe();
                            continue;
                        }
                        
                        m_questPlacedPossessableCharacters.Add(maAnimal);
                        maAnimal.CharacterGameState.m_walkSpeed = m_spawnerWalkSpeed;
                        maAnimal.SetSpeed(maAnimal.CharacterGameState.m_walkSpeed);
                    }
                }
            }
        }

        RefreshCharacterSaveData();
    }

    public override List<NGMovingObject> GetPossessableMovers()
    {
        if (Status != QuestStatus.InProgress) return new();
        if (GameManager.Me.IsPossessing == false)
        {
            var possessableMovers = base.GetPossessableMovers();
            possessableMovers.AddRange(m_possessablesNearby);
            return possessableMovers;
        }

        if (GameManager.Me.PossessedCharacter != null)
        {
            List<NGMovingObject> possessables = new();
            var pos = GameManager.Me.PossessedCharacter.transform.position;
            foreach (MACharacterBase maAnimal in NGManager.Me.m_MAAnimalList)
            {
                if (maAnimal.IsPossessed == false && maAnimal.transform.position.DistanceXZSq(pos) <=
                    m_possessionRadius * m_possessionRadius)
                {
                    possessables.Add(maAnimal);
                }
            }     
            
            foreach (MACharacterBase maHuman in NGManager.Me.m_MAHumanList)
            {
                if (maHuman.IsPossessed == false && maHuman.transform.position.DistanceXZSq(pos) <=
                    m_possessionRadius * m_possessionRadius)
                {
                    possessables.Add(maHuman);
                }
            }
            return possessables;
        }

        return new();
    }

    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("BallHeld"))
        {
            return m_trap.IsBallHeld() ? 0.0f : 1.0f;
        }
        if (_objective.Contains("TrapTriggered"))
        {
            return m_trap.State == MAQuestStigmataTrap.TrapState.Triggered ? 0.0f : 1.0f;
        }
        return 1.0f;
    }

    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');
        
        if (split[0].Contains("BallHeld"))
        {
            return m_trap.IsBallHeld();
        }
        
        if (split[0].Contains("TrapTriggered"))
        {
            return m_trap.State == MAQuestStigmataTrap.TrapState.Triggered;
        }
        
        if (split[0].Contains("TrapPartialReleased"))
        {
            return m_trap.m_trapRelease.State == MAQuestStigmataTrapRelease.TrapReleaseState.Partial;
        }
        
        if (split[0].Contains("TrapReleased"))
        {
            return m_trap.m_trapRelease.State == MAQuestStigmataTrapRelease.TrapReleaseState.Full;
        }
        
        if (split[0].Contains("HumanPossessed"))
        {
            // Is a human character possessed while the trap is triggered
            // TODO: Add all valid character types
            if (m_trap.State == MAQuestStigmataTrap.TrapState.Trapped && GameManager.Me.PossessedCharacter != null)
            {
                return (GameManager.Me.PossessedCharacter is MAHeroBase) ||
                       (GameManager.Me.PossessedCharacter is MAWorker);
            }
        }
        
        if (split[0].Contains("ChickenPossessed"))
        {
            // Is a chicken possessed while the trap is triggered
            if (m_trap.State == MAQuestStigmataTrap.TrapState.Trapped && GameManager.Me.PossessedCharacter != null)
            {
                return (GameManager.Me.PossessedCharacter is MAChicken);
            }
        }
        
        if (split[0].Contains("SomethingPossessed"))
        {
            if (GameManager.Me.PossessedCharacter != null)
            {
                return true;
            }
        }
        
        if (split[0].Contains("ExitPossessionToHand"))
        {

            if (GameManager.Me.PossessedCharacter != null)
            {
                m_somethingWasPossessed = true;
            }
            if (GameManager.Me.PossessedCharacter == null && m_somethingWasPossessed && m_trap.State == MAQuestStigmataTrap.TrapState.Trapped)
            {
                m_somethingWasPossessed = false;
                return true;
            }
        }
        
        if (split[0].Contains("IsBallEnabled"))
        {
            return m_trap.IsBallPickupEnabled;
        }
        
        if (split[0].Contains("ShowFreeHandDecision"))
        {
            return m_showFreeHandDecision;
        }

        return false;
    }

    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("EnableBallPickup"))
        {
            bool isEnabled = false;

            if (bool.TryParse(split[1], out isEnabled))
            {
                m_trap.IsBallPickupEnabled = isEnabled;
            }
        }
        
        if (split[0].Contains("ShowFreeHandDecision"))
        {
            m_showFreeHandDecision = true;
        }
    }
    
    public WaitPointSequence.WaitPoint GetWaypoint(MACharacterBase _character)//, int _iSequence, int _iWaitPoint)
    {
        int iCharacterData = m_placedCharacterSaveDatas.FindIndex(x => x.m_charID == _character.m_ID);
        if (iCharacterData > -1)
        {
            var characterData = m_placedCharacterSaveDatas[iCharacterData];
            if (m_sequences.Count <= characterData.m_iCurrentSequence || characterData.m_iCurrentSequence < 0 ||
                m_sequences[characterData.m_iCurrentSequence].m_sequence.Count <= characterData.m_iCurrentWaitPoint ||
                characterData.m_iCurrentWaitPoint < 0)
            {
                m_placedCharacterSaveDatas.RemoveAt(iCharacterData);
            }
            else
            {
                return m_sequences[characterData.m_iCurrentSequence].m_sequence[characterData.m_iCurrentWaitPoint];//.m_pos.position;
            }
        }
        Debug.LogError($"{GetType().Name} - Missing character save data. Id: {_character.m_ID}");
        return null;//Vector3.zero;
    }
    
    public WaitPointSequence.WaitPoint GetNextWaypoint(MACharacterBase _character)
    {
        var characterData = m_placedCharacterSaveDatas.Find(x => x.m_charID == _character.m_ID);
        if (characterData != null)
        {
            if (characterData.m_iCurrentWaitPoint + 1 >= m_sequences[characterData.m_iCurrentSequence].m_sequence.Count)
            {
                characterData.m_iCurrentWaitPoint = 0;
                characterData.m_iCurrentSequence = characterData.m_iCurrentSequence + 1 >= m_sequences.Count ? 0 : (characterData.m_iCurrentSequence + 1);
            }
            else
            {
                ++characterData.m_iCurrentWaitPoint;
            }
            return m_sequences[characterData.m_iCurrentSequence].m_sequence[characterData.m_iCurrentWaitPoint];
        }
        Debug.LogError($"{GetType().Name} - Missing character save data. Id: {_character.m_ID}");
        return null;
    }
    
    public WaitPointSequence.WaitPoint GetWaitPoint(int _iSequence, int _iWaitPoint)
    {
        return m_sequences[_iSequence].m_sequence[_iWaitPoint];
    }

    [Serializable]
    public class WaitPointSequence
    {
        public string m_name;
        public List<WaitPoint> m_sequence;
        
        [Serializable]
        public class WaitPoint
        {
            public string m_name;
            public Transform m_pos;
            public float m_timeToWait;
        }  
    }

    [Serializable]
    public class PlacedCharacterSaveData
    {
        public int m_charID;
        public int m_iCurrentSequence;
        public int m_iCurrentWaitPoint;
    }

    public class SaveLoadQuestStigmataContainer : SaveLoadQuestBaseContainer
    {
        public MAQuestStigmataTrap.TrapState m_trapState;
        public float m_spawnDelay = 0f;
        public int m_isBallPickupEnabled;
        public List<PlacedCharacterSaveData> m_placedCharacterSaveDatas = new();
        public BackupPossessUnlockState m_backupPossessUnlockState = new();
        
        public SaveLoadQuestStigmataContainer() : base() { }
        public SaveLoadQuestStigmataContainer(MAQuestBase _base) : base(_base) { }
    }
    
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestStigmataContainer(this);

        saveContainer.m_trapState = m_trap.State;
        saveContainer.m_spawnDelay = m_spawnDelay;
        saveContainer.m_isBallPickupEnabled = m_trap.IsBallPickupEnabled ? 1 : 0;
        saveContainer.m_placedCharacterSaveDatas = m_placedCharacterSaveDatas;
        saveContainer.m_backupPossessUnlockState = m_backupPossessUnlockState;
        
        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestStigmataContainer;

        if (saveContainer != null)
        {
            m_trap.SetLoadState(saveContainer.m_trapState);
            m_spawnDelay = saveContainer.m_spawnDelay;
            m_trap.IsBallPickupEnabled = saveContainer.m_isBallPickupEnabled == 1;
            m_placedCharacterSaveDatas = saveContainer.m_placedCharacterSaveDatas;
            m_backupPossessUnlockState = saveContainer.m_backupPossessUnlockState;
        }
    }

#if UNITY_EDITOR
    public void DebugDraw(SceneView _s)
    {
        if (Selection.activeObject != gameObject)
        {
            OnDrawGizmosSelected();
        }
    }
#endif
    
    private void OnDrawGizmosSelected()
    {
#if UNITY_EDITOR
        if(m_debugDrawGizmos == false) return;
        
        Color backupCol = Handles.color;
        Color newCol = GetPossessableMovers().Count == 0
            ? Color.white
            : (m_possessablesNearby.Count == 0 ? Color.yellow : Color.green);
        newCol.a = 0.5f;
        Handles.color = newCol;
        Handles.DrawSolidDisc(HandPos, Vector3.up, m_possessionRadius);
        
        var points = new List<Vector3>();
        int iSeq = 0;
        foreach (var seq in m_sequences)
        {
            foreach (var waitPoint in seq.m_sequence)
            {
                if (waitPoint.m_pos != null)
                {
                    points.Add(waitPoint.m_pos.position);
                    Handles.color = Color.white + ((Color.blue - Color.white) * (iSeq / m_sequences.Count));
                    Handles.DrawSolidDisc(waitPoint.m_pos.position + Vector3.up * 0.1f, Vector3.up, 0.1f);
                    if (waitPoint.m_pos.position.DistanceXZSq(HandPos) <= m_possessionRadius * m_possessionRadius)
                    {
                        Handles.color = Color.red;
                        Handles.DrawSolidDisc(waitPoint.m_pos.position + Vector3.up * 0.05f, Vector3.up, 0.2f);
                    }
                }
            }
            iSeq++;
        }
        Handles.color = backupCol;
#endif
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAQuestStigmata))]
public class MAQuestStigmataEditor : Editor
{
    public bool m_debugDrawGizmos = false;
    
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        if(Application.isPlaying && GameManager.Me.LoadComplete)
        {
            var b = target as MAQuestStigmata;
            GUILayout.Label("Quest Status");
            for (int i = 0; i < (int)MAQuestBase.QuestStatus.Last; i++)
            {
                if (GUILayout.Button($"Set Quest Status To '{((MAQuestBase.QuestStatus)i).ToString()}'"))
                {
                    b.SetQuestStatus((MAQuestBase.QuestStatus)i);
                }
            }       
            GUILayout.Label("Trap State");
            for (int i = 0; i < (int)MAQuestStigmataTrap.TrapState.Last; i++)
            {
                if (GUILayout.Button($"Set Trap State To '{((MAQuestStigmataTrap.TrapState)i).ToString()}'") && b.m_trap != null)
                {
                    b.m_trap.State = (MAQuestStigmataTrap.TrapState)i;
                }
            }
            if (GUILayout.Button($"Hand Bleed"))
            {
                b.m_trap.OnBladeStrikeEvent();
            }
            
            if (m_debugDrawGizmos != b.m_debugDrawGizmos)
            {
                m_debugDrawGizmos = b.m_debugDrawGizmos;
                if (m_debugDrawGizmos)
                {
                    SceneView.onSceneGUIDelegate -= b.DebugDraw;
                    SceneView.onSceneGUIDelegate += b.DebugDraw;
                }
                else
                {
                    SceneView.onSceneGUIDelegate -= b.DebugDraw;
                }
            }
        }
    }
}
#endif