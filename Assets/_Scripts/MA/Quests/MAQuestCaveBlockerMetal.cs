
using UnityEngine;

public class MAQuestCaveBlockerMetal : MAQuestBase
{
    public int m_swordRackUID;
    public BuildingNavBlocker m_caveEntranceBlocker;
    public BoxCollider m_caveEntranceColliderBox;
    
    private MABuilding m_swordRack;
    private <PERSON><PERSON><PERSON><PERSON>haracter m_quest<PERSON>haracter;

    protected override void OnPostLoad()
    {
        InitReferences();
    }
    
    private void InitReferences()
    {
        m_caveEntranceBlocker = GameObject.Find("MoAVisuals/Caves/Cave_Mine_Entrance/MANavBlocker").GetComponent<BuildingNavBlocker>();
        m_caveEntranceColliderBox = GameObject.Find("MoAVisuals/Caves/Cave_Mine_Entrance/Colliders/Collier (2)").GetComponent<BoxCollider>();
    }

    private void SetSwordRack()
    {
        m_swordRack = MABuilding.FindBuilding(m_swordRackUID.ToString());
        if (m_swordRack == null)
        {
            Debug.Log("MAQuestCaveBlockerMetal - Could not find swordRack");
        }
    }

    private void SetQuestCharacter()
    {
        m_questCharacter = MAFlowCharacter.FindCharacter(m_questWorkerInfoName);
        if (m_questCharacter == null)
        {
            Debug.Log("MAQuestCaveBlockerMetal - Could not find questCharacter");
        }
    }

    // private void SpawnSwordRack()
    // {
    //     var prefab = Resources.Load($"_Prefabs/Quests/QuestSwordRack"); 
    //     GameObject ob = Object.Instantiate(prefab) as GameObject;
    //     m_swordRack = ob.GetComponent<MABuilding>();
    // }

    private void AttachSwordToQuestGiver()
    {
        SetSwordRack();
        SetQuestCharacter();
            
        if (m_swordRack != null && m_questCharacter != null)
        {
            BCQuestStockIn stockIn = m_swordRack.GetComponentInChildren<BCQuestStockIn>();
            NGCarriableResource weapon = stockIn.GetResource();
            if (weapon != null)
            {
                m_questCharacter.SetWeaponDesign(weapon.GetProduct().m_design);
                stockIn.DestroyStock();
            }
        }
    }
    
    private void EnableCaveEntranceBlocker(bool _isActive)
    {
        PathBlock pathBlock = m_caveEntranceBlocker.GetComponent<PathBlock>();

        if(pathBlock != null)
        {
            pathBlock.m_isActive = _isActive;
        }
        
        m_caveEntranceColliderBox.gameObject.SetActive(_isActive);
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("AttachSwordToQuestGiver"))
        {
            AttachSwordToQuestGiver();
        }
        else if (split[0].Contains("EnableCaveEntranceBlocker"))
        {
            bool enabled = false;

            if (bool.TryParse(split[1], out enabled))
            {
                EnableCaveEntranceBlocker(enabled);
            }
        }
    }
}
