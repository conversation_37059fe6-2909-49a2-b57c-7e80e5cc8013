using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class MAQuestBase : MonoBehaviour
{
    public enum QuestStatus
    {
        Active,
        InProgress, // Accepted
        Completed,
        Invalid,
        Intro, // introduction dialogue before quest is accepted or not
        Last 
    }

    public enum QuestInteractType
    {
        GodMode,
        Possessed,
        Both
    }

    [System.Serializable] public class QuestResourceReward
    {
        public NGCarriableResource m_resource;
        public int m_amount;
    }
    public QuestStatus Status { get { return m_status; } }
    
    [Header("MAQuestBase")]
    public string m_id;                         //Quest ID for saving and loading
    public string m_questWorkerInfoName;
    public string m_businessFlowBlockName;
    public Transform m_questGiverHolder;         //The quest giver holder
    public Transform m_overrideScrollHolder;
    public QuestInteractType m_questInteractType;
 
    protected  MAQuestGiver m_questGiver;
    protected int m_stateID;
    public List<QuestResourceReward> m_questCompleteResources = new ();
    public List<NGBusinessGift> m_questCompleteGifts = new ();
    protected QuestStatus m_status;
    protected Dictionary<string, MACharacterBase> m_lipSyncers = new Dictionary<string, MACharacterBase>();
    [HideInInspector] public bool m_debugWindowBool = false;
    [HideInInspector] public string m_challengeText;
    [HideInInspector] public string m_challengeSpiteName;
    private List<MAParserSection> m_maParserSectionFlows = new List<MAParserSection>();
    private List<string> m_sectionFlowFileNames = new List<string>();  

    private int m_hasSpawnedQuestGiver = 0;

    public bool IsActive => MAQuestManager.Me.IsActive(m_id); // A quest must have this so that the script can tell if the quest is active or not
    
    //Base class for saving and loading quests
    [System.Serializable]
    public class SaveLoadQuestBaseContainer         
    {
        public SaveLoadQuestBaseContainer() {}
        public SaveLoadQuestBaseContainer(MAQuestBase _base)
        {
            m_id = _base.m_id;
            if(_base != null)
            {
                foreach (var r in _base.m_questCompleteResources)
                {
                    m_resourceReward.Add(r);
                }

                foreach (var r in _base.m_questCompleteGifts)
                {
                    m_giftReward.Add(r.m_name);
                }
            }
            m_status = _base.m_status;
            m_questGiver = _base.m_questGiver;
            m_hasSpawnedQuestGiver = _base.m_hasSpawnedQuestGiver;
            
            foreach(var flow in _base.m_maParserSectionFlows)
            {
                m_sectionFlowFileNames.Add(flow.m_fileName);
            }
        }
        [Save] public string m_id;
        [Save] public List<QuestResourceReward> m_resourceReward = new ();
        [Save] public List<string> m_giftReward = new ();
        [Save] public QuestStatus m_status;
        [Save] public MAQuestGiver m_questGiver;
        [Save] public string m_sectionFlowFileName; //KW: remove this when ready to break old save data
        [Save] public List<string> m_sectionFlowFileNames = new List<string>();
        [Save] public int m_hasSpawnedQuestGiver;
    }

    public void DebugShowWindow()
    {
#if UNITY_EDITOR
        EditorGUI.indentLevel++;

        var style = new GUIStyle(EditorStyles.textArea){richText = true};
        EditorGUILayout.LabelField($"m_questWorkerInfoName:",m_questWorkerInfoName, style);
        EditorGUILayout.LabelField($"m_businessFlowBlockName:",m_businessFlowBlockName, style);
        //EditorGUILayout.LabelField($"m_businessAdvisorInfoName:",m_businessAdvisorInfoName, style);
        var selectedPopup = 0;
        selectedPopup = EditorGUILayout.IntPopup($"Actions:", selectedPopup, new string[] { "None", "Inspect", "Start", "Abort"}, new int[] { 0, 1, 2, 3});
        switch (selectedPopup)
        {
            case 0:
                break;
            case 1:
                SceneView sceneView = SceneView.lastActiveSceneView;
                var s = sceneView.size;
                if (sceneView != null)
                {
                    sceneView.LookAt(transform.position);
                    sceneView.size = 16;
                }
                OnZoom();
                break;
            case 2:
                ActivateQuest();
                break;
            case 3:
                OnCancelChallenge();
                break;
        }
        EditorGUI.indentLevel--;
#endif
    }

    virtual protected void Awake()
    {
        m_stateID = Animator.StringToHash("State");
    }

    //Start registers the quest and find the quest spawn point
    virtual protected void Start() 
    {
        MAQuestManager.Me.AddQuest(this);
        StartCoroutine(AfterLoad());
    }

    public IEnumerator AfterLoad()
    {
        yield return new WaitUntil(() => ((GameManager.Me && GameManager.Me.LoadComplete)));

        foreach(var sectionFlowFilename in m_sectionFlowFileNames)
        {
            if (sectionFlowFilename.IsNullOrWhiteSpace() == false)
            {
                AddSectionFlow(MAParserManager.Me.FindByFileName(sectionFlowFilename));
            }
        }

        OnPostLoad();
        SetQuestGiverParent();
        SetQuestStatus(m_status);    
    }

    virtual protected void OnPostLoad()
    {
        if (m_questGiver == null && (m_hasSpawnedQuestGiver != 1 || m_id.Equals("MAQuestSimonStones")))
        {
            var workerInfo = MAWorkerInfo.GetInfo(m_questWorkerInfoName);
            if (workerInfo != null)
            {
                m_questGiver = MAQuestGiver.Create(workerInfo, m_questGiverHolder.position);
                m_questGiver.CharacterGameState.m_immortal = true;
                m_hasSpawnedQuestGiver = 1;
            }
        }
        m_questGiver?.CreateScroll(OnClickedScroll, m_overrideScrollHolder, m_questInteractType);
    }

    virtual protected void SetQuestGiverParent()
    {
        if(m_questGiver != null)
        {
            m_questGiver.transform.SetParent(m_questGiverHolder);
            m_questGiver.transform.eulerAngles = m_questGiverHolder.eulerAngles;
        }
    }
    
    public void ClearQuestRewards()
    {
        m_questCompleteResources.Clear();
        m_questCompleteGifts.Clear();
    }

    public void OnClickedScroll()
    { 
        ActivateQuest();
    }

    private void ClearSectionFlows()
    {
        foreach(var sectionFlow in m_maParserSectionFlows)
        {
            ClearSectionFlow(sectionFlow);
        }

        m_maParserSectionFlows.Clear();
    }

    private void ClearSectionFlow(MAParserSection _sectionFlow)
    {
        _sectionFlow.ClearCurrents();
        MAParserManager.Me.m_sections.Remove(_sectionFlow);
    }

    private MAParserSection FindSectionFlow(string _fileName)
    {
        return m_maParserSectionFlows.Find(f => f.m_fileName == _fileName);
    }

    public void AddSectionFlow(MAParserSection _maParserSectionFlow)
    {
        if (_maParserSectionFlow != null)
        {
            _maParserSectionFlow.m_currentQuest = this;
            _maParserSectionFlow.m_questName = m_id;

            m_maParserSectionFlows.Add(_maParserSectionFlow);
        }
    }

    virtual public void SetQuestStatus(QuestStatus _status)
    {
        m_status = _status;

        //if(_status == QuestStatus.Active)
        //{
        //    AbortFlow();
        //}

        //KW: ?. does not always work with UnityEngine.Objects
        if (m_questGiver != null && m_questGiver.m_questScroll != null)
        {
            m_questGiver.m_questScroll.SetQuestStatus(_status);
        }
    }

    // this is called by script that activates the quest
    public virtual void ActivateQuest() 
    {
        if (m_status != QuestStatus.Active || FindSectionFlow(MAParserManager.GetSectionFileName(m_businessFlowBlockName).fileName) != null)
            return;
        AddSectionFlow(MAParserManager.Me.ExecuteSection(m_businessFlowBlockName));
        OnActivateQuest();
    }

    protected virtual void OnActivateQuest()
    {
    }
     
    public bool CheckQuestAccepted()
    {
        return m_status == QuestStatus.InProgress || m_status == QuestStatus.Completed;
    }
    
    public bool CheckQuestIntro()
    {
        return m_status == QuestStatus.Intro;
    }
    
    //This function is called by the quest manager to check if the quest is complete
    public virtual bool CheckQuestComplete()
    {
        return m_status == QuestStatus.Completed;
    }

    public virtual bool CheckQuestInProgress()
    {
        return m_status == QuestStatus.InProgress;
    }
    
    public void ShowAcceptDialog(string _challengeText, string _challengeSpiteName)
    {
        var acceptText = "Do you accept the challenge?";
        if (_challengeText.IsNullOrWhiteSpace() == false)
        {
            acceptText = _challengeText;
        }
        m_challengeText = _challengeText;
        m_challengeSpiteName = _challengeSpiteName;
    }
    
    public virtual void ShowChallengeReminderDialog()
    {
        var sprite = NGTutorialMessageBase.GetPoseSprite(m_challengeSpiteName);

        MAQuestMessageDialog.Create("Quest", m_challengeText,
            m_questCompleteResources,
            m_questCompleteGifts,
            sprite,
            null,
            OnCancelChallenge,
            OnZoom,
            () => {});
    }
    
    virtual public void OnAcceptChallenge()
    {
        SetQuestStatus(QuestStatus.InProgress);
    }
    
    virtual public void OnCancelChallenge()
    {
        SetQuestStatus(QuestStatus.Active);

        string firstFlowFileName = "";

        //KW: If there is no quest giver, this should have been spawned from code so rerun the first flow
        if(m_questGiver == null && m_maParserSectionFlows.Count > 0)
        {
            firstFlowFileName = m_maParserSectionFlows[0].m_fileName;
        }

        ClearSectionFlows();

        if(!firstFlowFileName.IsNullOrWhiteSpace())
        {
            AddSectionFlow(MAParserManager.Me.ExecuteSection(firstFlowFileName));
        }
    }

    virtual public void OnFailChallenge()
    {
        SetQuestStatus(QuestStatus.Invalid);

        ClearSectionFlows();
    }

    virtual public void OnZoom()
    {
        if(m_questGiver != null)
            MAParser.MoveCamera(m_questGiver.transform.position, 20f);
        else
            MAParser.MoveCamera(transform.position, 20f);
    }
    
    public virtual void GiveRewards(bool _completeQuest = true) //This function is called by the quest manager to give the rewards for the quest
    {
        foreach (var r in m_questCompleteResources)
        {
            switch (r.m_resource.m_name)
            {
                case "Money":
                    NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Given, r.m_amount, $"Quest ");
                    break;
                case "LordsFavour":
                    NGPlayer.Me.m_lordsFavors.Add(CurrencyContainer.TransactionType.Given, r.m_amount, $"Quest ");
                    break;
                case "PeoplesFavor":
                    NGPlayer.Me.m_commonersFavors.Add(CurrencyContainer.TransactionType.Given, r.m_amount, $"Quest ");
                    break;
                case "RoyalFavour":
                    NGPlayer.Me.m_royalFavors.Add(CurrencyContainer.TransactionType.Given, r.m_amount, $"Quest ");
                    break;
            }
        }

        foreach (var g in m_questCompleteGifts)
        {
            NGBusinessGiftsPanel.CreateOrCall(new List<NGBusinessGift>() {g});
        }

        if (_completeQuest)
        {
            SetQuestStatus(QuestStatus.Completed);
        }
    }
    
    //This function is called by the quest manager to save the quest
    public virtual SaveLoadQuestBaseContainer Save()
    {
        return new SaveLoadQuestBaseContainer(this);
    }   

    //This function is called by the quest manager to load the quest
    public virtual void Load(SaveLoadQuestBaseContainer _l)
    {
       // ActivateQuestGiver();
        foreach (var r in _l.m_resourceReward)
        {
            m_questCompleteResources.Add(r);
        }
        foreach(var g in _l.m_giftReward)
        {
            m_questCompleteGifts.Add(NGBusinessGift.GetInfo(g));
        }
        m_status = _l.m_status;
        m_questGiver = _l.m_questGiver;
        m_hasSpawnedQuestGiver = _l.m_hasSpawnedQuestGiver;
        SetQuestGiverParent();
        m_sectionFlowFileNames = _l.m_sectionFlowFileNames;

        //KW: copy over old flow name for now
        if(!_l.m_sectionFlowFileName.IsNullOrWhiteSpace() && !m_sectionFlowFileNames.Contains(_l.m_sectionFlowFileName))
        {
            m_sectionFlowFileNames.Add(_l.m_sectionFlowFileName);
        }
    }

    public virtual void SetQuestGiverState(int _questGiverState)
    {
        if(m_questGiver != null && m_questGiver.m_anim != null)
        {
            m_questGiver.m_anim.SetInteger(m_stateID, _questGiverState);
        }
    }

    public virtual void OnDisplayMessage(MAMessage _message)
    {
        MACharacterBase lipSyncer = GetLipSyncer(_message.m_advisor);

        if (lipSyncer != null)
        {
            AudioSource debugAudioSource = null;

            if(_message.m_audioID.IsNullOrWhiteSpace() == true)
            {
                debugAudioSource = MAMessageManager.Me.GetAudioSource(_message.m_advisor);
            }

            lipSyncer.SetLipSync(true, debugAudioSource);
        }
    }

    public virtual void OnDestroyMessage(MAMessage _message)
    {
        MACharacterBase lipSyncer = GetLipSyncer(_message.m_advisor);

        if (lipSyncer != null)
        {
            lipSyncer.SetLipSync(false);
        }
    }

    private MACharacterBase GetLipSyncer(string _advisor)
    {
        if (m_lipSyncers.ContainsKey(_advisor))
        {
            //KW: Check that character has not been destroyed
            if (m_lipSyncers[_advisor] == null)
            {
                m_lipSyncers.Remove(_advisor);
            }
            else
            {
                return m_lipSyncers[_advisor];
            }
        }

        return null;
    }

    public virtual void QuestObjectiveActivate(string _objective)
    {

    }

    public virtual float QuestObjectiveValue(string _objective)
    {
        return 1.0f;
    }

    public virtual void TriggerQuestEvent(string _event)
    {
        
    }
    
    public virtual bool WaitForQuestEvent(string _event)
    {
        return false;
    }
    
    public virtual List<MABuilding> GetPossessableBuildings()
    {
        return new();
    }
    
    public virtual List<NGMovingObject> GetPossessableMovers()
    {
        return new();
    }

    public virtual bool ShouldShowMapIcon()
    {
        if(m_status == QuestStatus.Active)
        {       
            if (m_questGiver != null)
            {
                //KW: for scrolls spawned by quest givers
                if (m_questGiver.m_questScroll != null && m_questGiver.m_questScroll.gameObject.activeInHierarchy)
                {
                    return true;
                }
            }
            else if(m_maParserSectionFlows.Count > 0)
            {
                //KW: for scrolls spawned by quest flows (assuming that the quest flow will have called SetCurrentQuest when launched and it has spawned a scroll)
                return true;
            }
        }

        return false;
    }
    
    protected IEnumerator DelayedAction(System.Action action, float delay)
    {
        yield return new WaitForSeconds(delay);
        action.Invoke();
    }

    protected void SetHeadTrackerTarget(MAFlowCharacter _character, Transform _target)
    {
        HeadTracker ht = _character.GetComponent<HeadTracker>();

        if (ht != null)
        {
            ht.Override(_target);
        }
    }
}
