using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class MAQuestDogCotters : MAQuestDog
{
    public List <MAQuestInteraction> m_pickupBottles;
    private bool m_isBottleCollected = false;
    private int m_bottlesCollectedCount = 0;
    
    private float m_pickUpMessageDelay = 1.5f;
    public float m_NormalOverlayStrength = 4.0f;
    private bool m_pickupActive = false;

    public void OnPickupBottle(int _bottleIndex)
    {
        if (m_pickupActive)
            return;
        m_pickupActive = true;
        
        m_pickupBottles[_bottleIndex].SetInteractive(false);
        m_pickupBottles[_bottleIndex].SetCollidersEnable(false);

        if(Camera.main != null)
        {
            var pickupHolder = Camera.main.transform.GetComponentInChildren<MAPickupHolder>();

            if(pickupHolder != null)
            {
                if(m_bottlesCollectedCount < m_pickupBottles.Count - 1)
                {
                    EnableBottleCrack(m_pickupBottles[_bottleIndex].gameObject);
                }
                pickupHolder.Pickup(m_pickupBottles[_bottleIndex].gameObject, () => OnPickupComplete(_bottleIndex));
                m_bottlesCollectedCount++;
                
                StartCoroutine(DelayedAction(() => 
                {
                    m_isBottleCollected = true;
                }, m_pickUpMessageDelay));
                
                return;
            }
        }
        
        OnPickupComplete(_bottleIndex);
    }

    public void OnPickupComplete(int _bottleIndex)
    {
        m_pickupBottles[_bottleIndex].gameObject.SetActive(false);
        m_pickupBottles[_bottleIndex].SetInteracted(true);

        if(m_dog != null)
        {
            m_dog.AddItemsCollected();
        }
        m_pickupActive = false;
    }
    
    void EnableBottleCrack(GameObject _bottle)
    {
        Renderer renderer = _bottle.GetComponentInChildren<Renderer>();

        if (renderer != null)
        {
            Material material = renderer.material;
            if (material.HasProperty("_NormalOverlayStrength"))
            {
                material.SetFloat("_NormalOverlayStrength", m_NormalOverlayStrength);
            }
        }
    }

    public override void QuestObjectiveActivate(string _objective)
    {
        if (_objective.Contains("CollectBottle"))
        {
            foreach (var b in m_pickupBottles)
            {
                b.SetInteractive(true, m_dog);
            }

            m_isBottleCollected = false;
        }
    }

    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("CollectBottle"))
        {
            return m_isBottleCollected ? 0.0f : 1.0f;
        }

        return 1.0f;
    }
    
    
    
    public class SaveLoadQuestDogForemanContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestDogForemanContainer() : base() { }
        public SaveLoadQuestDogForemanContainer(MAQuestBase _base) : base(_base) { }
        [Save] public ScentTrailSaveState m_scentTrailSaveState;
        [Save] public List<MAQuestInteraction.SaveState> m_interactionSaveStates;
        [Save] public int m_disableRunningMusic;
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestDogForemanContainer(this);

        saveContainer.m_scentTrailSaveState = GetScentTrailSaveState();
        saveContainer.m_interactionSaveStates = GetInteractionSaveStates();
        saveContainer.m_disableRunningMusic = IsRunningMusicDisabled() ? 1 : 0;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestDogForemanContainer;
        if (saveContainer != null)
        {
            SetScentTrailSaveState(saveContainer.m_scentTrailSaveState);
            SetInteractionSaveStates(saveContainer.m_interactionSaveStates);
            SetRunningMusicDisabled(saveContainer.m_disableRunningMusic == 1);
        }
    }

    private List<MAQuestInteraction.SaveState> GetInteractionSaveStates()
    {
        List<MAQuestInteraction.SaveState> interactionSaveStates = new List<MAQuestInteraction.SaveState>();

        for(int i = 0; i < (int)m_pickupBottles.Count; i++)
        {
            interactionSaveStates.Add(m_pickupBottles[i].GetSaveState());
        }

        return interactionSaveStates;
    }

    private void SetInteractionSaveStates(List<MAQuestInteraction.SaveState> _interactionSaveStates)
    {
        if (_interactionSaveStates != null)
        {
            for (int i = 0; i < (int)m_pickupBottles.Count; i++)
            {
                if (i < _interactionSaveStates.Count)
                {
                    m_pickupBottles[i].SetSaveState(_interactionSaveStates[i]);

                    if (m_pickupBottles[i].HasInteracted())
                    {
                        m_pickupBottles[i].gameObject.SetActive(false);
                        m_pickupBottles[i].SetCollidersEnable(false);
                    }
                }
            }
        }
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_dog != null)
        {
            for (int i = 0; i < (int)m_pickupBottles.Count; i++)
            {
                if (m_pickupBottles[i].IsInteractive())
                {
                    m_pickupBottles[i].SetInteractive(true, m_dog);
                }
            }
        }
    }

    public override List<NGMovingObject> GetPossessableMovers()
    {
        var requiredPossessables = base.GetPossessableMovers();

        if (MAUnlocks.Me.m_possessedCanTransferPossession == false)
        {
            if (GameManager.Me.IsPossessing && GameManager.Me.PossessedCharacter != m_dog)
            {
                requiredPossessables.Add(m_dog);
            }
        }

        return requiredPossessables;
    }
}
