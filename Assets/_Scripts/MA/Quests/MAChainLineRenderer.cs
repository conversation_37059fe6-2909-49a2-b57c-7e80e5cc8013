using UnityEngine;

[RequireComponent(typeof(LineRenderer))]
public class MAChainLineRenderer : MonoBehaviour
{
    public Transform[] m_links;
    private LineRenderer m_lineRenderer;
    private Vector3[] m_positions;

    void Awake()
    {
        m_lineRenderer = GetComponent<LineRenderer>();
        m_lineRenderer.positionCount = m_links.Length;
        m_positions = new Vector3[m_links.Length];
    }

    void LateUpdate()
    {
        for(int i = 0; i < m_links.Length; i++)
        {
            m_positions[i] = m_links[i].position;
        }

        m_lineRenderer.SetPositions(m_positions);
    }
}
