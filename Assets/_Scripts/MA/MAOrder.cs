using System;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

[Serializable]
public class BuildingComponentLink
{
	public BCBase Component => m_component;
	public long UID => m_uid;
	
	private BCBase m_component;
	
	[SerializeField] private long m_uid;
	
	public void Set(BCBase _component)
	{
		if(_component == null) return;
		m_component = _component;
		m_uid = _component.m_uid;
	}
	
	public void Clear()
	{
		m_component = null;
		m_uid = 0;
	}
	
	public bool PostLoad()
	{
		if(m_uid < 0) return false;
		m_component = MABuilding.LookupComponent(m_uid);
		return m_component != null;
	}
}

[Serializable]
public class BuildingLink
{
	public MABuilding Building => m_building;
	private MABuilding m_building;
	public int BuildingUId => m_uid;
	
	[SerializeField] private int m_uid;
	
	public void Set(MABuilding _building)
	{
		if(_building == null) return;
		m_building = _building;
		m_uid = _building.m_linkUID;
	}
	
	public void Clear()
	{
		m_building = null;
		m_uid = -1;
	}
	
	public bool PostLoad()
	{
		if(m_uid == -1) return false;
		m_building = NGManager.Me.FindBuildingByID(m_uid);
		return m_building != null;
	}
}

[Serializable]
public class MAOrder
{
	public string m_productID = null;
	public int m_orderQuantity = 0;
	public float m_orderQuality = 0;
	public string m_orderType;
	public bool m_locked;
	public string m_productLine;
	public string m_sequenceType = "";
	public bool m_extraSlot = false;
	
	public int Manufactured => m_numProductsManufactured;
	public int Dispatched => m_numProductsDispatched;
	public float NormalizedOrderReputationScore => m_lastNormalizedReputationScore;
	[NonSerialized] private GameState_Product m_gameProduct;
	[SerializeField] private int m_numProductsManufactured = 0;
	[SerializeField] private int m_numProductsDelivered = 0;
	[SerializeField] private int m_numProductsDispatched = 0;
	[SerializeField] private bool m_rewardRecieved = false;
	[SerializeField] private int m_orderId;
	[SerializeField] private float m_lastReputationScore = 0f;
	[SerializeField] private int m_lastReputationOrders = -1;
	[SerializeField] private float m_favourRewardMultiplier = 1f;
	[SerializeField] private float m_productPriceMultiplier = 1f;
	[SerializeField] private SDictionary<string,int> m_bonusRewards = new();
	[SerializeField] private int m_isParsed = 0;
	[SerializeField] private List<string> m_rewardsChosen = new();
	[SerializeField] private string m_orderInfoIndexer = "";
	[SerializeField] private GameState_Design m_partialDesign = null;
	[SerializeField] private float m_lastNormalizedReputationScore;
	[SerializeField] private BuildingLink m_assignedToBuilding = new();
	[SerializeField] private BuildingComponentLink m_assignedTo = new();
	[SerializeField] private BuildingComponentLink m_owner = new();
	
	private MAOrderInfo m_orderInfo;
	
	public float ProductPriceMultiplier => m_productPriceMultiplier;

	public const float c_reputationOffset = 0.2f;
	public bool AdjustReputation(float _normalizedReputationScore, Action _onDialogueClose)
	{
		m_lastNormalizedReputationScore = _normalizedReputationScore;
		var reputationScore = _normalizedReputationScore - c_reputationOffset; // Allows for negative feedback
		var adjustedScore = reputationScore;
		
		var manufactured = Mathf.Min(m_orderQuantity, m_numProductsManufactured);
		// Unapply previous unearn score
		if(m_lastReputationOrders >= 0)
		{
			var previousScorePerItem = m_lastReputationScore / m_orderQuantity;
			var currentScorePerItem = reputationScore / m_orderQuantity;
			float unmadeCount = m_orderQuantity - manufactured;
			
			adjustedScore = (currentScorePerItem * unmadeCount) - (previousScorePerItem * unmadeCount);
		}
		
		m_lastReputationOrders = manufactured;
		m_lastReputationScore = reputationScore;
		
		if(adjustedScore == 0)
			return false;
		
		var levelInfo = OrderInfo.OrderGiver.AdjustScore(adjustedScore);
		
		if(levelInfo.previousLevel != levelInfo.newLevel || Mathf.Abs(levelInfo.previousProgress-levelInfo.newProgress) > 0.0001f)
		{
			MAReputationChangeDialogue.Create(levelInfo.previousLevel, levelInfo.newLevel, levelInfo.previousProgress, levelInfo.newProgress, OrderInfo.OrderGiver, _onDialogueClose);
			
			return true;
		}
		return false;
	}
	
	public List<string> GetDesignTags()
	{
		var tags = new List<string>();
		
		if(HasPlayerDesigned)
		{
			var dsi = NGDesignInterface.Get(Design, this);
			foreach(var part in dsi.Parts)
			{
				var block = part.m_block;
				if(block == null || block.m_tagArray == null) continue;
				
				foreach(var tag in block.m_tagArray)
				{
					tags.Add(tag);
				}
			}
		}
		return tags;
	}
	
	public string OrderInfoIndexer { get { return m_orderInfoIndexer; } }
	
	public static readonly MAOrder EmptyOrder = new MAOrder(null);
	
	public static MAOrder GetCurrentOrHistoricOrder(string _name)
	{
		foreach(var order in GameManager.Me.m_state.m_orders.m_values)
			if(order.OrderInfoIndexer == _name)
				return order;
				
		foreach(var order in GameManager.Me.m_state.m_orderDataHistory)
			if(order.OrderInfoIndexer == _name)
				return order;
				
		return null;
	}

	public float GetProductPrice()
	{
		var product = GameProduct;
		if(product == null) return 0f;
		
		return NGDesignInterface.Get(product).SellingPrice;
	}
		
	public bool ShowQualityString()
	{
		if(m_owner.Component is BCActionWeaponsmith || m_owner.Component is BCActionArmourer)
			return m_orderQuality.IsZero() == false;
		return true;
	}
	
	public string GetQualityString(bool _color)
	{
		return MADesignGuage.GetNameValue(ProductLine, m_orderQuality, _color);
	}
	public string GetDescription()
	{
		var quantity = IsInfinateOrder ? "infinite" : m_orderQuantity.ToString();
		var quality = GetQualityString(true);
        
		if(OrderInfo.m_cardDescription.IsNullOrWhiteSpace())
		{
			string info = $"Order for {quantity} {quality} {ProductDisplayName}";
			if(OrderInfo.OrderGiver != null && OrderInfo.OrderGiver.m_displayName.IsNullOrWhiteSpace())
				info += ".";
			else
				info += $", from {OrderInfo.OrderGiver.m_displayName}.";
                
			if(Rewards.Length > 0)
				info += "They will give you the rewards shown below on completion.";
            
			return info; 
		}

		string desc = OrderInfo.m_cardDescription;
		desc = desc.Replace("[quality]", quality);
		desc = desc.Replace("[quantity]", quantity);
		return desc;
	}
		
	public GameState_Product GameProduct
	{
		get
		{
			if(m_gameProduct == null && m_productID.IsNullOrWhiteSpace() == false)
			{
				m_gameProduct = GameManager.Me.m_state.m_products.Find(x => x.m_uniqueID == m_productID);
			}
			return m_gameProduct;
		}
	}
	
	public bool IsDesignValid(GameState_Design _design)
	{
		if(_design.HasDesign == false) return false;
		
		if(m_partialDesign == null)
			return true;
		if(m_partialDesign.m_design.Equals(_design.m_design))
			return false;
			
		return true;
	}
	
	public GameState_Design Design {
		get 
		{
			if(GameProduct != null)
				return GameProduct.Design;
			if(m_partialDesign != null)
				return m_partialDesign;
			return null;
		}
	}
	
	public float GetQualityCeiling()
	{
		if(m_orderQuality < 0.2f) return 0.2f;
		if(m_orderQuality < 0.4f) return 0.4f;
		if(m_orderQuality < 0.6f) return 0.6f;
		if(m_orderQuality < 0.8f) return 0.8f;
		return 1f;
	}
	
	public string ProductLine => m_productLine;
	public MAOrderInfo OrderInfo => m_orderInfo ??= MAOrderInfo.GetInfo(m_orderInfoIndexer);
	public NGBusinessGift TemplateBusinessGift => ConvertOrderToBusinessGift(this);
	
	public MABuilding AssignedBuilding => m_assignedToBuilding.Building;
	public MABuilding OwnerBuilding => m_owner.Component?.Building;
	
	public int OrderId => m_orderId;
	public bool IsInfinateOrder => m_orderQuantity == Int32.MaxValue;
	public int RemainingToManufacture => IsInfinateOrder ? Int32.MaxValue : Mathf.Clamp(0, m_orderQuantity - m_numProductsManufactured, Int32.MaxValue);
	public int RemainingQuantity => IsInfinateOrder ? Int32.MaxValue : Mathf.Clamp(0, m_orderQuantity - m_numProductsDispatched, Int32.MaxValue);
	public bool IsValid => this != EmptyOrder && m_orderId != -1 && m_isParsed == 1 && m_orderQuantity > 0;
	public bool IsAvailable => IsValid && IsComplete == false && AssignedBuilding == null && m_locked == false;
	public bool CanAssign => IsValid && IsComplete == false && m_locked == false;
	public bool HasPlayerDesigned => IsValid && GameProduct != null && GameProduct.HasDesign && GameProduct.Design.HasDesign;
	public bool HasDesign => IsValid && Design != null && Design.HasDesign;
	public bool IsComplete => m_numProductsDispatched >= m_orderQuantity;
	
	public void UpdateComponentReferences()
	{
		m_owner.PostLoad();
		m_assignedToBuilding.PostLoad();
		
		// Migrating to new system
		m_assignedTo.PostLoad();
		if(m_assignedTo.Component != null)
		{
			if(m_assignedTo.Component?.Building)
			{
				m_assignedTo.Component?.Building.ReceiveOrder(this);
			}
			m_assignedTo.Clear();
		}
		// -----
	}
			
	public bool IsOwner(BCBase _base)
	{
		return m_owner.UID == _base.m_uid;
	}
	
	public void SetOwner(BCBase _owner)
	{
		m_owner.Set(_owner);
	}
	
	public void RemoveFromSaveData()
	{
		GameManager.Me.m_state.m_orders.RemoveKey(m_orderId);
	}
	
	public void Return()
	{
		AssignedBuilding?.RemoveOrder(this);
		OnOrderRemoved();
	}
	
	public void ItemDispatched() 
	{
		m_numProductsDispatched++;
		
		if(IsComplete)
		{
			Return();
		}
	}
	
	/*private void DestroyAllCompletedOrderProducts()
	{
		// Destory building stock
		foreach(var building in NGManager.Me.m_maBuildings)
		{
			building.RemoveCompletedOrderStock();
		}
		
		// Destroy persistent pickups
		foreach(var pickup in GameManager.Me.m_state.m_pickups)
		{
			// If the pickup is being carried dont destroy it
			if(pickup.m_holderObject > -1) continue;
			
			var order = pickup.Pickup.GetOrder();
			if(order != null && order.IsComplete)
			{
				pickup.Pickup.DestroyMe();
			}
		}
	}*/
	
	public void ItemManufactured()
	{
		var info = ProductInfo;
		if(info != null)
		{
			switch(info.m_prefabName)
			{
				case "Weapons":
					GameManager.Me.m_state.m_gameInfo.m_numWeaponsMade++;
				break;
			}
		}
		
		m_numProductsManufactured++;
		if(m_numProductsManufactured >= m_orderQuantity)
		{
			AssignedBuilding?.RemoveOrder(this);
		}
	}
	
	public void ItemDelivered()
	{
		m_numProductsDelivered++;
		if(m_numProductsDelivered >= m_orderQuantity)
		{
			GiveReward();
		}
	}
	
	public bool CanDeliverTo(MABuilding _building)
	{
		if(_building == null) return false;
		
		if(OwnerBuilding == null)
		{
			foreach(var orderBase in _building.BuildingComponents<BCActionOrderBase>(true))
			{
				if(orderBase.CanAcceptItemsFromOrder(this))
					return true;
			}
		}
		
		return _building == OwnerBuilding;
	}
	
	private void GiveNGReward(string _resourceName, float _value)
	{
		if(_value <= 0) return;
		
		switch (_resourceName.ToLower())
		{
			case "money":
				NGPlayer.Me.m_cash?.Add(CurrencyContainer.TransactionType.Earned,
					Mathf.RoundToInt(_value), "Order Completion");
				break;
			case "peoplesfavor":
				NGPlayer.Me.m_commonersFavors?.Add(CurrencyContainer.TransactionType.Earned,
					Mathf.RoundToInt(_value), "Order Completion");
				break;
			case "lordsfavour":
				NGPlayer.Me.m_lordsFavors?.Add(CurrencyContainer.TransactionType.Earned,
					Mathf.RoundToInt(_value), "Order Completion");
				break;
			case "royalfavour":
				NGPlayer.Me.m_royalFavors?.Add(CurrencyContainer.TransactionType.Earned,
					Mathf.RoundToInt(_value), "Order Completion");
				break;
			case "mysticfavour":
				NGPlayer.Me.m_mysticFavors?.Add(CurrencyContainer.TransactionType.Earned, 
					Mathf.RoundToInt(_value), "Order Completion");
				break;
		}
	}
	
	public void GiveReward()
	{
		if(m_rewardRecieved) return;
		
		List<NGBusinessGift> giftsToAward = new List<NGBusinessGift>();
		MARewardOrderInfo[] rewards = Rewards;
		
		var currencyRewards = GetCurrencyRewards();
		foreach(var reward in currencyRewards)
		{
			GiveNGReward(reward.Key.m_name, reward.Value);
		}

		foreach(MARewardOrderInfo reward in rewards)
		{
			if(string.IsNullOrWhiteSpace(reward.m_giftReward.ToLowerInvariant()) == false)
			{
				if(reward.GiftReward == null) continue;
				
				giftsToAward.Add(reward.GiftReward);
			}
		}

		if(giftsToAward.Count > 0)
		{
			NGBusinessGiftsPanel.CreateOrCall(giftsToAward);
		}
		m_rewardRecieved = true;
	}
	
	public void Complete()
	{
		m_numProductsDispatched = m_orderQuantity;
		m_numProductsDelivered = m_orderQuantity;

		GiveReward();
		
		Return();
	}
	
	public string TaskDescription
    {
	    get
	    {
		    string outStr = $"{RemainingQuantity} / {m_orderQuantity} ";
		    MAOrderInfo orderInfo = OrderInfo;
			outStr += $"{(orderInfo != null ? ($"{AddSIfPlural(m_productLine, RemainingQuantity)}\n{LocalizeKnack.TranslateLocalisedString("of Quality")}: {m_orderQuality}") : "n/a")}";
			return outStr;
	    }
    }
    
    public bool HasLostLinkToOrderInfo()
    {
		if(OrderInfo == null)
		{
			Debug.LogError($"MAOrder has lost its link to MAOrderInfo -> {m_orderInfoIndexer}");
			return true;
		}
		return false;
    }
    
    public string DisplayName => LocalizeKnack.TranslateLocalisedString(OrderInfo?.m_name ?? "");
    
    public string ProductDisplayName
    {
	    get
	    {
		    if (this == EmptyOrder) return "n/a";
		    MAOrderInfo orderInfo = OrderInfo;
		    if(orderInfo == null || ProductInfo == null) return "n/a";
		    return RemainingQuantity > 1 ? ProductInfo.m_title : ProductInfo.m_titleSingular;
	    }
    }

    public NGProductInfo ProductInfo => NGProductInfo.GetInfo(ProductLine);
	
	public MARewardOrderInfo[] Rewards
	{
		get
		{
			List<MARewardOrderInfo> rewards = new();
			for(int i =  m_rewardsChosen.Count - 1; i >= 0; i--)
			{
				MARewardOrderInfo rewardOrderInfo = OrderInfo.AllRewards.Find(x => x.m_name == m_rewardsChosen[i]);
				if(rewardOrderInfo != null) rewards.Insert(0, rewardOrderInfo);
			}
			return rewards.ToArray();
		}
	}
	
	public string GetState()
	{
		if(IsComplete) return "Complete";
		if(AssignedBuilding == null) return "No Factory Assigned";
		if(HasDesign == false) return "Requires Design";
		return "In Progress";
	}
	
	public Dictionary<NGCarriableResource,int> GetCurrencyRewards()
	{
		Dictionary<NGCarriableResource, int> allRewards = new();
		
		foreach(var r in Rewards)
		{
			if(r.m_currencyRewardValue <= 0) continue;
			
			var value = r.m_currencyRewardValue;
			
			if(r.CurrencyReward.IsFavour) 
				value *= m_favourRewardMultiplier;
			
			if(allRewards.ContainsKey(r.CurrencyReward))
				allRewards[r.CurrencyReward] += (int)value;
			else
				allRewards[r.CurrencyReward] = (int)value;
		}
		
		foreach(var key in m_bonusRewards.Keys)
		{
			var res = NGCarriableResource.GetInfo(key);
			if(res == null) continue;
			
			if(allRewards.ContainsKey(res))
				allRewards[res] += m_bonusRewards[key];
			else
				allRewards[res] = m_bonusRewards[key];
		}
		return allRewards;
	}
	
	public string GetRewardString()
	{
		string rewards = null;
		var currencyRewards = GetCurrencyRewards();
		
		foreach(var reward in currencyRewards)
		{			
			rewards += $"{reward.Value}x<size=150%>{reward.Key.TextSprite}</size> ";
		}
		return rewards;
	}

	public MAOrder(MAOrderInfo _maOrderInfo)
	{
		Init(_maOrderInfo);
	}

	public MAOrder() { }

	private MAOrder(int _orderId)
	{
		m_orderId = _orderId;
	}
	
	public void PostLoad()
	{
		if(IsValid == false || IsComplete) return;
		
		UpdateComponentReferences();

		// Make sure the order link hasn't been lost, if it has, remove the link so that a player can reassign the order
		if(AssignedBuilding != null && AssignedBuilding.Order != this)
		{
			Debug.LogError($"Order {m_orderId} has lost it's link to component {m_owner.UID}");
			Return();
		}
	}
	
	public void OnOrderRemoved()
	{
		m_assignedToBuilding.Clear();
		
		if(m_owner.Component)
		{
			if(m_owner.Component.ClearOrderDesignOnReturn)
			{
				SetProduct(null);
			}
			
			if(m_owner.Component.ResetOrderQuantityOnReturn)
			{
				m_numProductsManufactured = 0;
			}
		}
	}
	
	public bool Assign(MABuilding _building)
	{
		if(_building == null || IsValid == false)
			return false;
		
		Return();
		m_assignedToBuilding.Set(_building);
		return true;
	}
	
	public static MAOrder FindOrderById(int _orderId)
    {
		if(GameManager.Me.m_state.m_orders.TryGetValue(_orderId, out var order))
			return order;
	    return EmptyOrder;
    }

	private bool AssertOrderInfo(MAOrderInfo _orderInfo)
	{
		return !(_orderInfo == null || string.IsNullOrWhiteSpace(_orderInfo.m_name) ||
		         string.IsNullOrWhiteSpace(_orderInfo.m_indexer) || string.IsNullOrWhiteSpace(_orderInfo.m_blockName));// ||
		         /*_orderInfo.Faction == null ||*//* _orderInfo.Product == null*///);
	}
		
	public void SetProduct(GameState_Product _product)
	{
		if(_product == null)
		{
			m_productID = null;
			m_gameProduct = null;
		}
		else
		{
			m_productID = _product.m_uniqueID;
			m_gameProduct = _product;
		}
	}
	
	private void TrySetPartialDesign()
	{
		var info = OrderInfo;
		if(info.m_design.IsNullOrWhiteSpace())
			return;
			
		// Create and register design
		m_partialDesign = new GameState_Design(info.m_design);
	}

	private void Init(MAOrderInfo _orderInfo)
	{
		if(AssertOrderInfo(_orderInfo) == false)
		{
			if(_orderInfo != null)
			{
				Debug.LogError(
					$"MAOrder - Init - OrderInfo is null or has invalid/empty/null fields. OrderInfo: {_orderInfo.m_name}");
			}
			m_orderId = -1;
			return;
		}
		
		m_orderId = ++GameManager.Me.m_state.m_highestOrderId;
		GameManager.Me.m_state.m_orders.Add(m_orderId, this);

		if(_orderInfo.m_product.ToLower() == "random")
		{
			m_productLine = NGProductInfo.GetRandomFromUnlocked().m_prefabName;
		}
		else
			m_productLine = _orderInfo.m_product;

		m_orderInfoIndexer = _orderInfo.m_indexer.ToLower().Trim();

		TrySetPartialDesign();

		if(_orderInfo.m_lowQuantity == -1 || _orderInfo.m_highQuantity == -1)
			m_orderQuantity = Int32.MaxValue;
		else
		{
			m_orderQuantity = Random.Range(_orderInfo.m_lowQuantity, _orderInfo.m_highQuantity);
			m_orderQuantity = (int)(m_orderQuantity * _orderInfo.OrderGiver.GetOrderQuantityMultiplier());
		}
		
		m_favourRewardMultiplier = _orderInfo.OrderGiver.GetFavourRewardMultiplier();
		
		m_orderQuality = CalculateOrderQuality(_orderInfo);
		
		var rewardInfos = _orderInfo.AllRewards;
		if(rewardInfos.Count > 0)
		{
			for(int i = 0; i < rewardInfos.Count; i++)
			{
				float percentageChance = rewardInfos[i].m_percentChance;
				percentageChance = percentageChance <= 0f ? 1f : percentageChance;
				if(percentageChance >= 1f || Random.Range(0f, 1f) < percentageChance)
				{
					m_rewardsChosen.Add(rewardInfos[i].m_name);
				}
			}

			if(m_rewardsChosen.Count == 0)
			{
				m_rewardsChosen.Add(rewardInfos[Random.Range(0, rewardInfos.Count)].m_name);
			}
		}

		m_isParsed = 1;
	}

	private float CalculateOrderQuality(MAOrderInfo _orderInfo)
	{
		var randomQuality = Random.Range(_orderInfo.m_lowDesignScore, _orderInfo.m_highDesignScore);
		if(randomQuality < 0.2f) return 0f;
		if(randomQuality < 0.4f) return 0.2f;
		if(randomQuality < 0.6f) return 0.4f;
		if(randomQuality < 0.8f) return 0.6f;
		return 0.8f;
	}
	
	public class MAOrderBusinessGift : NGBusinessGift
	{
		public MAOrder m_order = EmptyOrder;
		public override void SetupContent(NGDirectionCardBase _card)
		{
			base.SetupContent(_card);
			NGOrderTile orderTile = _card as NGOrderTile;
			if(orderTile != null)
			{
				orderTile.SetupOrderUI(m_order);
			}
		}
	}
	
	private NGBusinessGift ConvertOrderToBusinessGift(MAOrder _order)
	{
		MAOrderBusinessGift businessGift = new();
		businessGift.m_order = _order;
		businessGift.m_name = _order.OrderInfo.m_name;
		businessGift.m_type = MAOrderDataManager.c_giftTypeName;
		businessGift.Type = NGBusinessGift.GiftType.Order;
		businessGift.m_giftTitle = _order.OrderInfo.m_name;
		businessGift.m_cardTitle = "Expansion";
		businessGift.m_cardPower = "";
		businessGift.m_cardPrice = 0;
		businessGift.m_quantity = 1;
		businessGift.m_spritePath = "";
		businessGift.m_description = $"UI-template businessGift for order {businessGift.m_name}";
		businessGift.m_power = $"";
		businessGift.m_buildingDesign = "";
		businessGift.m_upgradeImports = "";
		businessGift.m_canBePutInVault = false;
		businessGift.m_buildingsToUpgrade = "MABuilding";
		businessGift.m_componentsToUpgrade = "ActionFactory"; //apply to factory for production

		NGBusinessGift.PostImport(businessGift);
		return businessGift;
	}
	
	protected bool Equals(MAOrder _other)
	{
		return _other != null && m_orderId == _other.m_orderId;
	}

	public override bool Equals(object _obj)
	{
		return Equals(_obj as MAOrder);
	}

	public override int GetHashCode()
	{
		return m_orderId;
	}

	public static bool operator ==(MAOrder _a, MAOrder _b)
	{
		bool aNull = ReferenceEquals(_a, null);
		if(aNull) return ReferenceEquals(_b, null);
		return _a.Equals(_b);
	}

	public static bool operator !=(MAOrder _a, MAOrder _b)
	{
		bool aNull = ReferenceEquals(_a, null);
		if(aNull) return ReferenceEquals(_b, null) == false;
		return _a.Equals(_b) == false;
	}

	[System.Serializable]
	public class OrderRewardType
	{
		public string m_rewardId = "";
		public int m_rewardQuantity = 0;
		public string RewardText => $"{m_rewardQuantity.ToString()} {m_rewardId}";
	}

	public string DebugOutputString
	{
		get
		{
			MAOrderInfo orderInfo = OrderInfo;
			string outString =
				$"m_orderId = {m_orderId}\n" +
				$"IsValid = {IsValid}\n" +
				$"IsComplete = {IsComplete}\n" +
				$"m_isParsed = {m_isParsed}\n" +
				$"m_factionName = {(orderInfo != null && orderInfo.Faction != null ? orderInfo.Faction.m_factionName : "n/a")}\n" +
				$"m_orderQuantity = {RemainingQuantity} / {m_orderQuantity}\n" +
				$"m_productsAddedToDelivery = {m_numProductsDelivered}\n" +
				$"Chosen Rewards Count= {Rewards?.Length ?? 0}\n" +
				$"Rewards: {(Rewards != null ? new System.Func<string>(() => { string outStr = ""; System.Array.ForEach(Rewards, x => outStr += x.RewardText + ", "); return outStr; })() : null)}\n" +
				$"\n";
			return outString;
		}
	}

	private static string AddSIfPlural(string _str, int _quantity)
	{
		return $"{_str}{(_quantity > 1 && _str.ToLower()[_str.Length-1] != 's' ? "s" : "")}";
	}
}
