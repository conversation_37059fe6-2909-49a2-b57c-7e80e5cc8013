using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MABloodControl : MonoSingleton<MABloodControl>
{
    public GameObject[] m_bloodFX;
    public GameObject m_bloodAttach;

    public Transform m_bloodDecalHolder = null;
		
	public bool m_infiniteDecals;
	public Light m_dirLight;

	public int m_iEffect = 0; 
	public int m_activeBloodDecalCount;

	public float m_bloodScale = 1f;
    
	private Vector3 m_direction;
	
	public bool m_emitBloodOnClick = true;

	private void Update()
	{
		if(m_emitBloodOnClick && GameManager.Me.LoadComplete && Input.GetMouseButtonDown(0))
		{
			if(Camera.main != null)
			{
				var ray = Camera.main.ScreenPointToRay(Input.mousePosition);
				EmitBlood(ray);
			}
		}
	}

	public void EmitBlood(Vector3 _origin, Vector3 _dir, int _emitCount = 1)
	{
		//int layerMask = LayerMask.GetMask("CreatureToHuman");
       
		Debug.Log(_origin);
		var ray = new Ray(_origin, _dir);//Camera.main.ScreenPointToRay(Input.mousePosition));
		EmitBlood(ray, _emitCount);
	}

	public LayerMask m_layerMask;
	//public string m_layerMaskName = "CreatureToHuman";
	public void EmitBlood(Ray _ray, int _emitCount = 1)
	{
		//Debug.Log("BloodControl - EmitBlood - pos: _ray.origin);
		//GameManager.Me.AddGizmoLine("blood2",_ray.origin, _ray.origin + _ray.direction * 10f, Color.red, 0.1f);
		RaycastHit hit;
		float dist = 100f;
		int layerMask = m_layerMask;// LayerMask.GetMask(m_layerMaskName);
		if (Physics.Raycast(_ray, out hit, dist, layerMask))//, 100f, layerMask))
		{
			//GameManager.Me.AddGizmoLine("blood1",_ray.origin, _ray.origin + _ray.direction * dist, Color.green);
			EmitBloodAtHit(hit, _emitCount);
		}
	}

	[SerializeField]
	private int m_maxBloodDecals = 20;
	public void EmitBloodAtHit(Vector3 _point, Vector3 _normal, Transform _transform, int _emitCount = 1, System.Action _onComplete = null)
	{
		if(m_activeBloodDecalCount >= m_maxBloodDecals) return;
		
		float angle = Mathf.Atan2(_normal.x, _normal.z) * Mathf.Rad2Deg + 180;
		float randDegreesMargin = 25f;

		var nearestBone =
			GetNearestObjectOnBody(_transform.GetComponentInParent<NGMovingObject>()?.transform ?? _transform,
				_point); //TODO: test this, not sure if that's the way it works with our workers
		
		float timeToComplete = 0;
		while(_emitCount > 0)
		{
			angle *= ((-1) + Random.value * 2) * randDegreesMargin * ((-1) + Random.value * 2);
			
			_emitCount--;
			//var effectIdx = Random.Range(0, BloodFX.Length);
			if(m_iEffect < 0 || m_iEffect >= m_bloodFX.Length - 1) m_iEffect = 0;

			Transform parent = m_bloodDecalHolder;
			GameObject instance = null;
			if(parent != null)
				instance = Instantiate(m_bloodFX[m_iEffect], _point, Quaternion.Euler(0, angle + 90, 0),
					m_bloodDecalHolder);
			else
				instance = Instantiate(m_bloodFX[m_iEffect], _point, Quaternion.Euler(0, angle + 90, 0));

			m_iEffect++;
			m_activeBloodDecalCount++;
			var settings = instance.GetComponent<BFX_BloodSettings>();
			//settings.FreezeDecalDisappearance = InfiniteDecal;

			if(m_dirLight)
				settings.LightIntensityMultiplier = m_dirLight.intensity;
			
			if(nearestBone != null)
			{
				//Debug.Log($"nearestBone {nearestBone.name} - {nearestBone.Path()}");
				var attachBloodInstance = Instantiate(MABloodControl.Me.m_bloodAttach);
				var bloodT = attachBloodInstance.transform;
				bloodT.position = _point;
				bloodT.localRotation = Quaternion.identity;
				bloodT.localScale = Vector3.one * UnityEngine.Random.Range(0.75f, 1.2f) * m_bloodScale;
				bloodT.LookAt(_point + _normal, Vector3.up);//m_direction);
				bloodT.Rotate(90, 0, 0);
				bloodT.transform.parent = nearestBone;

				if(!m_infiniteDecals)  Destroy(attachBloodInstance, settings.DecalLifeTimeSeconds);
			}

			if(!m_infiniteDecals)
			{
				StartCoroutine(DestroyDecal(instance, settings.DecalLifeTimeSeconds));
			}
			
			timeToComplete = Mathf.Max(timeToComplete, settings.DecalLifeTimeSeconds);
		}
		if (_onComplete != null)
			Utility.After(timeToComplete, _onComplete);
	}

	public void EmitBloodAtHit(RaycastHit _hit, int _emitCount = 1)
	{
		EmitBloodAtHit(_hit.point, _hit.normal, _hit.transform, _emitCount);
	}
	
	public void EmitBloodAtTransform(Vector3 _point, Vector3 _normal, Transform _transform, int _emitCount = 1, System.Action _onComplete = null)
	{
		if(m_activeBloodDecalCount >= m_maxBloodDecals) return;

		m_infiniteDecals = true;
		
		float angle = Mathf.Atan2(_normal.x, _normal.z) * Mathf.Rad2Deg + 180;
		float randDegreesMargin = 25f;

		var nearestBone =
			GetNearestObjectOnBody(_transform.GetComponentInParent<NGMovingObject>()?.transform ?? _transform,
				_point); //TODO: test this, not sure if that's the way it works with our workers
		
		float timeToComplete = 0;
		while(_emitCount > 0)
		{
			angle *= ((-1) + Random.value * 2) * randDegreesMargin * ((-1) + Random.value * 2);
			
			_emitCount--;
			//var effectIdx = Random.Range(0, BloodFX.Length);
			if(m_iEffect < 0 || m_iEffect >= m_bloodFX.Length - 1) m_iEffect = 0;

			Transform parent = m_bloodDecalHolder;
			GameObject instance = null;
			if(parent != null)
				instance = Instantiate(m_bloodFX[m_iEffect], _point, Quaternion.Euler(0, angle + 90, 0),
					m_bloodDecalHolder);
			else
				instance = Instantiate(m_bloodFX[m_iEffect], _point, Quaternion.Euler(0, angle + 90, 0));

			m_iEffect++;
			m_activeBloodDecalCount++;
			var settings = instance.GetComponent<BFX_BloodSettings>();
			//settings.FreezeDecalDisappearance = InfiniteDecal;

			if(m_dirLight)
				settings.LightIntensityMultiplier = m_dirLight.intensity;
			
			if(nearestBone != null)
			{
				//Debug.Log($"nearestBone {nearestBone.name} - {nearestBone.Path()}");
				var attachBloodInstance = Instantiate(MABloodControl.Me.m_bloodAttach);
				var bloodT = attachBloodInstance.transform;
				bloodT.position = _point;
				// bloodT.localRotation = _transform.localRotation;
				// bloodT.localScale = new Vector3(100f, 1f, 100f);
				bloodT.Rotate(90, 0, 0);
				bloodT.LookAt(_point + _normal, Vector3.up);
				
				bloodT.transform.parent = nearestBone;

				// if(!m_infiniteDecals)  Destroy(attachBloodInstance, settings.DecalLifeTimeSeconds);
			}

			// if(!m_infiniteDecals)
			// {
			// 	StartCoroutine(DestroyDecal(instance, settings.DecalLifeTimeSeconds));
			// }

			timeToComplete = Mathf.Max(timeToComplete, 999999999999999f);//settings.DecalLifeTimeSeconds);
		}
		if (_onComplete != null)
			Utility.After(timeToComplete, _onComplete);
	}
	
	private Transform GetNearestObjectOnBody(Transform _hit, Vector3 _hitPos)
	{
		var closestPosSqrd = 100f * 100f;
		Transform closestBone = null;
		var childs = _hit.GetComponentsInChildren<Transform>();

		foreach (var child in childs)
		{
			var distSqrd = (child.position - _hitPos).sqrMagnitude;
			if (distSqrd < closestPosSqrd)
			{
				closestPosSqrd = distSqrd;
				closestBone = child;
			}
		}

		var distRootSqrd = (_hit.position - _hitPos).sqrMagnitude;
		if (distRootSqrd < closestPosSqrd)
		{
			closestPosSqrd = distRootSqrd;
			closestBone = _hit;
		}
		return closestBone;
	}

	private IEnumerator DestroyDecal(GameObject _instance, float _secs)
	{
		yield return new WaitForSeconds(_secs);
		if(_instance != null)
		{
			Destroy(_instance); 
			m_activeBloodDecalCount--;
		}
	}
}
