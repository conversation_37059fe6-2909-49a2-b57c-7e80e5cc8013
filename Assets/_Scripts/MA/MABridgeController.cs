using UnityEngine;
using System;
using System.Collections.Generic;

[Serializable]
public class GameState_Bridge
{
    public string m_name;
    public MABridgeController.State m_state;
}

public class MABridgeController : MonoBehaviour
{
    private static List<MABridgeController> s_allBridges = new();
    
    public string m_name;
    public State m_initialBridgeState;
    
    public List<GameObject> m_passableActiveObjects = new();
    public List<GameObject> m_impassableActiveObjects = new();
    public Animator m_animator;
    
    private GameState_Bridge m_data;
    
    public static void SetBridgeState(string _name, State _state)
    {
        bool found = false;
        _name = _name.ToLower();
        foreach(var b in s_allBridges)
        {
            if(b.m_name.ToLower() == _name)
            {
                found = true;
                b.SetState(_state);
            }
        }
        if(found == false)
        {
            Debug.LogError("Unable to find bridge " + _name);
        }
    }
    
    public static void LoadAll()
    {
        foreach(var b in s_allBridges)
        {
            b.Load();
        }
    }
    
    public void Awake()
    {
        if(m_animator != null)
            m_animator.enabled = false;
            
        if(s_allBridges.Contains(this))
            return;
            
        s_allBridges.Add(this);
        
        if(GameManager.Me != null && GameManager.Me.LoadComplete)
            Load();
    }
            
    public enum State
    {
        Passable = 0,
        Impassable = 1,
    }
    
    public void Load()
    {
        foreach(var data in GameManager.Me.m_state.m_bridgeStates)
        {
            if(data.m_name != m_name) continue;
            m_data = data;
            break;
        }
        
        if(m_data == null)
        {
            m_data = new GameState_Bridge()
            {
                m_name = m_name,
                m_state = m_initialBridgeState
            };
            
            // Only add to save if we have a valid name
            if(m_name.IsNullOrWhiteSpace() == false)
            {
                GameManager.Me.m_state.m_bridgeStates.Add(m_data);
            }
        }
        
        SetState(m_data.m_state, true);
    }
    
    public void SetState(State _state, bool _force = false)
    {
        if(m_data.m_state == _state && _force == false) return;
        
        m_data.m_state = _state;

        switch (_state)
        {
            case State.Passable:
                foreach(var o in m_passableActiveObjects) o.SetActive(true);
                foreach(var o in m_impassableActiveObjects) o.SetActive(false);
                if(m_animator != null) m_animator.enabled = true;
                break;
                
            case State.Impassable:
                foreach(var o in m_passableActiveObjects) o.SetActive(false);
                foreach(var o in m_impassableActiveObjects) o.SetActive(true);
                GlobalData.Me.SetNavGenerationFrameToCurrent();
                break;
        }
    }
}
