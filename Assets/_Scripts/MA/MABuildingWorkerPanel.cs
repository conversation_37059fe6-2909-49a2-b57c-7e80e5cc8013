using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MABuildingWorkerPanel : NGBuildingInfoGUIComponentBase
{
    public Transform m_tableHolder;
    public MABuildingWorkerPanelLine m_workerLine;

    private MABuilding m_MABuilding;
    private int m_lastHash;
    private DisplayType m_displayType;
    private float m_nextUpdateTime;
    
    public void Update()
    {
        if(Time.realtimeSinceStartup > m_nextUpdateTime)
            RefreshView();
    }

    public void Activate(DisplayType _type, MABuilding _building)
    {
        //base.Activate();
        m_displayType = _type;
        
        m_MABuilding = _building;
        
        RefreshView();
    }
    
    void RefreshView()
    {
        m_tableHolder.DestroyChildren();
        
        switch(m_displayType)
        {
            case DisplayType.Residents: m_title.text = "Residents"; break;
            case DisplayType.Workers: m_title.text = "Workers"; break;
        }
        
        m_nextUpdateTime = Time.realtimeSinceStartup + 1f;
        if (m_MABuilding == null) return;
        
        List<MACharacterBase> characters = new();
        
        bool createdTitle = false;
        
        foreach(var workerBase in m_MABuilding.BuildingComponents<BCWorkerBase>(true))
        {
            foreach(var character in workerBase.m_workersAllocated)
            {
                if(characters.Contains(character)) continue;
                
                switch(m_displayType)
                {
                    case DisplayType.Residents:
                        if(character.Home?.Building != m_MABuilding)
                            continue;
                        break;
                        
                    case DisplayType.Workers:
                        if(character.Job?.Building != m_MABuilding)
                            continue; 
                        break;
                }
                
                if(createdTitle == false)
                {
                    MABuildingWorkerPanelLine.CreateTitle(m_tableHolder);
                    createdTitle = true;
                }
                
                characters.Add(character);
                MABuildingWorkerPanelLine.Create(m_tableHolder, character);
            }
        }
    }
    
    public enum DisplayType
    {
        Residents,
        Workers,
    }
    
    public static MABuildingWorkerPanel Create(DisplayType _type, Transform _holder, MABuilding _building)
    {
        var prefab = Resources.Load<MABuildingWorkerPanel>("_Prefabs/Dialogs/MABuildingWorkerPanel");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_type, _building);
        return instance; 
    } 
}
