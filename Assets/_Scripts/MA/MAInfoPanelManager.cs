using UnityEngine;

public class MAInfoPanelManager : MonoBehaviour
{
    private Transform m_currentTransformFocus = null;
    public Transform CurrentTransformFocus => m_currentTransformFocus;
    
    public void UpdateInfoPanelIfOpen(string _uiName, Transform _object)
    {
        MAInfoPanelUI[] panels = GetComponentsInChildren<MAInfoPanelUI>(true);
        bool open = false;
        foreach (var infoPanelUI in panels)
        {
            if (infoPanelUI.IsOpen)
            {
                open = true;
            }
        }

        if (open == false) return;

        OpenInfoPanel(_uiName, _object);
    }

    public void ToggleInfoPanel(string _uiName, Transform _object)
    {
        foreach (var infoPanelUI in GetComponentsInChildren<MAInfoPanelUI>(true))
        {
            if (infoPanelUI.m_name == _uiName)
            {
                if(infoPanelUI.IsOpen)
                {
                    infoPanelUI.Close();
                }
                else
                {
                    infoPanelUI.Open(_object);
                }
            }
            else
            {
                infoPanelUI.Close();
            }
        }
    }
    
    public void OpenInfoPanel(string _uiName, Transform _object)
    {
        m_currentTransformFocus = _object;
        foreach (var infoPanelUI in GetComponentsInChildren<MAInfoPanelUI>(true))
        {
            if (infoPanelUI.m_name == _uiName)
            {
                infoPanelUI.Open(_object);
            }
            else
            {
                infoPanelUI.Close();
            }
        }
    }
    
    public void CloseInfoPanel(string _uiName)
    {
        m_currentTransformFocus = null;
        foreach (var infoPanelUI in GetComponentsInChildren<MAInfoPanelUI>(true))
        {
            if (infoPanelUI.m_name == _uiName)
            {
                infoPanelUI.Close();
            }
        }
    }
}
