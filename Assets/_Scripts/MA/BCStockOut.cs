using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(BCStockOut))]
public class NGBCStockOutInspector : MonoEditorDebug.MonoBehaviourEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var myScript = (BCStockOut) target;
        if (GUILayout.Button($"Clear Stock"))
        {
            myScript.ClearAllStock();
        }
    }
}
#endif

public class BCStockOut : BCStockBase
{
    public override void UpdateInternal(BuildingComponentsState _state)
    {
        MoveStockFromBuilding();
    }
    
    private void MoveStockFromBuilding()
    {
        if (GetStockSpace() <= 0) return;

        var stock = m_building.ConsumeStockForStockOut();
        if (stock != null)
        {
            CreateStock(stock);
        }
    }

    override public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false)
    {
        return 0f;
    }

    override protected CraneHandler Crane => UseCrane ? m_building?.OutputCrane : null;
}
