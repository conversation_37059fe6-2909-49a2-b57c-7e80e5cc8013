using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MABuildingInfo : MonoBehaviour
{
    public MABedroomInfo m_bedroomPrefab;
    private MABuilding m_building;
    
    void Update()
    {
        
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public void Activate(MABuilding _building)
    {
        m_building = _building;
        foreach (var bc in m_building.m_components)
        {
            
        }
    }

    public static MABuildingInfo Create(MABuilding _building, Transform _holder)
    {
        var go = Instantiate(NGManager.Me.m_MABuildingInfoPrefab, _holder);
        var bi = go.GetComponent<MABuildingInfo>();
        bi.Activate(_building);
        return bi;
    }
}
