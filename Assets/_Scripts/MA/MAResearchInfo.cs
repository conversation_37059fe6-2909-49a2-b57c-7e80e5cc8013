using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

#if UNITY_EDITOR
using UnityEditor;
#endif

[System.Serializable]
public class MAResearchInfo 
{
    public const string IconPath = "_art/Icons/";
    public static List<MAResearchInfo> s_researchInfos = new List<MAResearchInfo>();
    
    public static IList GetTheList() => s_researchInfos;
    public static List<MAResearchInfo> GetList=>s_researchInfos;
    public string DebugDisplayName => $"{m_key}:{m_name}";

    public static Dictionary<MAFactionInfo.FactionType, List<MAResearchInfo>> s_factionResearchDict = new Dictionary<MAFactionInfo.FactionType, List<MAResearchInfo>>();
    public string id;
    public bool m_debugChanged;
    public string m_key;
    public string m_name;
    public string m_index;
    public string m_factionType;
    [<PERSON>an<PERSON>ield] private string m_linkTo;
    public string m_giftCard;
    public string m_unlock;
    public string m_linkToString;
    public int m_dollarCost;
    public int m_factionCost;
    public string m_icon;
    public string m_title;
    public string m_description;
    public string m_position;
    public string m_scale;
    public string m_updateTime;
    public bool m_activatedAtStart;
    public int m_spriteAtlas;
    public bool m_isLocked;
    public string m_newDisplayName;
    public string m_newDescription;
    public string m_newDetails;
    public string m_newDetailsLine;
    public string m_newUsage; 
    public string m_newHint;
    public string m_newWarning;
    public string m_newLore;
    public Sprite IconSprite => Resources.Load<Sprite>($"{IconPath}{m_icon}");
    public MAFactionInfo.FactionType m_faction;
   // public MAResearchItem m_displayedItem;
    public List<MAResearchInfo> m_linkToList = new List<MAResearchInfo>();
    public bool m_debugSelectedForWindow = false;
    public bool m_acquired = false;
    public bool m_highLight = false;
    public bool IsAcquired => m_acquired;

    public string LinkTo
    {
        get { return m_linkTo; }
        set
        {
            m_linkTo = value;
            m_changed = true;
        }
    }

    public bool m_changed = false;
    public Vector3 PositionVector => (IsValidPosition) ? m_position.ToVector3() : Vector3.zero;
    public bool IsBlank => m_name.IsNullOrWhiteSpace();
    public Vector3 ScaleVector => (m_scale.IsNullOrWhiteSpace()) ? m_scale.ToVector3() : Vector3.one;

    public bool IsValidPosition => m_position.IsNullOrWhiteSpace() == false;

    public static bool PostImport(MAResearchInfo _what)
    {

        _what.m_faction = MAFactionInfo.FactionType.None;
        if (_what.m_factionType.IsNullOrWhiteSpace() == false)
        {
            if (Enum.TryParse(_what.m_factionType, out _what.m_faction) == false)
                Debug.LogError($"MAResearchInfo.LoadInfo incorrect faction type {_what.m_factionType}");
        }
        if(_what.m_activatedAtStart)
            _what.m_acquired = true;
        if (s_factionResearchDict.ContainsKey(_what.m_faction))
            s_factionResearchDict[_what.m_faction].Add(_what);
        else
            s_factionResearchDict.Add(_what.m_faction, new List<MAResearchInfo>() {_what});
        return true;
    }

    public static List<MAResearchInfo> LoadInfo() // Must be loaded after blocks
    {
        s_researchInfos = NGKnack.ImportKnackInto<MAResearchInfo>(PostImport);
        s_researchInfos.Sort((x, y) => x.m_key.CompareTo(y.m_key));
        foreach (var faction in s_factionResearchDict)
            faction.Value.Sort((x, y) => x.m_key.CompareTo(y.m_key));
        foreach (var ri in s_researchInfos)
        {
            if (ri.m_linkToString.IsNullOrWhiteSpace() == false)
            {
                foreach (var l in ri.m_linkToString.Split(';', '\n', '|'))
                {
                    var i = GetInfoKey(l);
                    if (i != null)
                        ri.m_linkToList.Add(i);
                }
            }
        }

        return s_researchInfos;
    }

    public static int GetPlayerCanAffordCount()
    {
        var affordCount = 0;
        foreach(var ri in s_researchInfos)
        {
            if (ri.m_acquired) continue;
            if(MAResearchManagerUI.IsFactionLocked(ri.m_faction)) continue;
            if (ri.IsUnlocked() == false) continue;
            
            if (ri.CanPlayerAfford()) 
                affordCount++;
        }

        return affordCount;
    }
    public bool CanPlayerAfford()
    {
        bool OkayToBuy = true;
        if (m_factionCost > 0)
        {
            if (NGPlayer.Me.GetFavors(m_faction) < m_factionCost)
            {
                return false;
            }
        }

        if (m_dollarCost > 0)
        {
            if (NGPlayer.Me.m_cash.Balance < m_dollarCost)
            {
                return false;
            }
        }

        return true;
    }

    public bool IsUnlocked()
    {
        if(m_isLocked) 
            return false;
            
        bool InLinkList = false;
        foreach (var ri in s_factionResearchDict[m_faction])
        {
            if (ri.m_linkToList.Contains(this))
            {
                if (ri.IsAcquired)
                    return true;
                InLinkList = true;
            }
        }

        if (InLinkList)
            return false;
        return true;
    }

    public bool BuyItem()
    {
        if(IsUnlocked() == false)
            return false;
            
        if (m_factionCost > 0)
            NGPlayer.Me.SpendFavors(m_faction, m_factionCost, "Research");
        if (m_dollarCost > 0)
            NGPlayer.Me.m_cash.Spend(m_dollarCost, "Research", "", "");
        if (m_unlock.IsNullOrWhiteSpace() == false)
            MAUnlocks.Change(m_unlock);
        if (m_giftCard.IsNullOrWhiteSpace() == false)
        {
            var gifts = new List<NGBusinessGift>();
            foreach (var g in m_giftCard.Split(',', '|', ';'))
            {
                var gift = NGBusinessGift.GetInfo(g);
                gifts.Add(gift);
            }

            if (gifts.Count > 0)
                NGBusinessGiftsPanel.CreateOrCall(gifts);
        }

        m_acquired = true;
        return true;
    }
    
#if UNITY_EDITOR
    bool ShowAndUpdateText(string _name, ref string _text, GUIStyle _style)
    {
        var newText = _text;
        newText = EditorGUILayout.TextField(_name, newText, _style);
        if (newText != _text)
        {
            _text = newText;
            m_changed = true;
            return true;
        }

        return false;
    }

    bool ShowAndUpdateInt(string _name, ref int _num, GUIStyle _style)
    {
        var newNum = _num;
        newNum = EditorGUILayout.IntField(_name, newNum, _style);
        if (newNum != _num)
        {
            _num = newNum;
            m_changed = true;
            return true;
        }

        return false;
    }

    enum ShowWindoGUIEmum
    {
        False = 0,
        True = 1
    };

    //"False",
    //"True"
    //};  
    public bool ShowWindowGUI(GUIStyle _labelStyle, MAResearchItemUI _fromItem = null)
    {
        //var labelStyle = new GUIStyle(EditorStyles.textArea) {richText = true};
        ShowAndUpdateText("Name:", ref m_name, _labelStyle);
        ShowAndUpdateText($"Title:", ref m_title, _labelStyle);
        var newText = EditorGUILayout.TextField("Description: ", m_description, GUILayout.MinHeight(30), GUILayout.ExpandHeight(true)); 
        if(newText != m_description)
        {
            m_description = newText;
            m_changed = true;
        }
        //ShowAndUpdateText($"Description: ", ref m_description, _labelStyle);
        ShowAndUpdateInt($"Dollar Cost: ", ref m_dollarCost, _labelStyle);
        ShowAndUpdateInt($"Faction Cost: ", ref m_factionCost, _labelStyle);
        ShowAndUpdateText($"GiftCard: ", ref m_giftCard, _labelStyle);
        ShowAndUpdateText($"Unlock: ", ref m_unlock, _labelStyle);
        ShowAndUpdateText($"Icon Name: ", ref m_icon, _labelStyle);
        ShowWindoGUIEmum tf = (m_activatedAtStart) ? ShowWindoGUIEmum.True : ShowWindoGUIEmum.False;
        var tfResult = EditorGUILayout.EnumFlagsField("ActivateAtStart", tf, _labelStyle);
        if ((ShowWindoGUIEmum)tfResult != tf)
        {
            m_activatedAtStart = !m_activatedAtStart;
            m_changed = true;
        }

        EditorGUILayout.LabelField("Links To:", _labelStyle);
        EditorGUI.indentLevel++;
        bool removedLine = false;

        if (m_linkToList.Count > 0)
        {
            foreach (var l in m_linkToList)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(l.m_name, _labelStyle);
                if (GUILayout.Button("Remove"))
                {
                    m_linkToList.Remove(l);
                    m_changed = true;
                    removedLine = true;
                    MAResearchManagerUI.Me.Refresh();
                }

                EditorGUILayout.EndHorizontal();
            }
        }
        else
        {
            EditorGUILayout.LabelField("None", _labelStyle);
        }

        if (MAResearchManagerUI.Me && _fromItem)
        {
            int count = 0;
            foreach (var ti in MAResearchManagerUI.Me.m_researchItems)
                if (ti.m_info != null && ti.m_info != this)
                    count++;
            if (count > 0)
            {
                var newString = new string[count + 1];
                var newInt = new int[count + 1];
                var index = 0;
                newString[index] = "None";
                foreach (var ti in MAResearchManagerUI.Me.m_researchItems)
                {
                    if (ti.m_info != null && ti.m_info != this)
                    {
                        newString[index] = ti.m_info.m_name;
                        newInt[index] = index++;
                    }
                }

                var selectPopup = EditorGUILayout.IntPopup($"Add Link:", 0, newString, newInt);
                if (selectPopup != 0)
                {
                    var selectedInfo = MAResearchInfo.GetInfo(newString[selectPopup]);
                    var selectedItem = MAResearchManagerUI.Me.m_researchItems.Find(o => o.m_info == selectedInfo);
                    _fromItem.AddConnection(selectedItem);
                    m_changed = true;
                    MAResearchManagerUI.Me.Refresh();
                }
            }
        }
    
        EditorGUI.indentLevel--;
        return m_changed;
    }
#endif
    public static void SaveAll(ref string _s)
    {
        var sb = "";
        foreach (var ri in s_researchInfos)
        {
            sb+=$"{ri.m_key}|{ri.m_acquired},";
        }

        _s = sb.TrimEnd(',');
    }
    public static void LoadAll(string _data)
    {
        if (_data == null) return;
        var split = _data.Split(',');
        foreach (var s in split)
        {
            var split2 = s.Split('|');
            var ri = GetInfoKey(split2[0]);
            if (ri != null)
            {
                bool.TryParse(split2[1], out ri.m_acquired);
            }
        }
    }
    public static MAResearchInfo GetInfo(string _name) => s_researchInfos.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
    public static MAResearchInfo GetInfoKey(string _name) => s_researchInfos.Find(o => o.m_key.Equals(_name, StringComparison.OrdinalIgnoreCase));
    public static MAResearchInfo GetInfoByUnlock(string _id) => s_researchInfos.Find(o => o.m_unlock.Contains(_id, StringComparison.OrdinalIgnoreCase)); 
    public static MAResearchInfo AddNewInfo(MAFactionInfo.FactionType _faction)
    {
        var newInfo = new MAResearchInfo() {m_faction = _faction, m_name = "New", m_factionType = _faction.ToString(), m_giftCard = "", id="",};
        s_researchInfos.Add(newInfo);
        if(s_factionResearchDict.ContainsKey(_faction) == false)
            s_factionResearchDict.Add(_faction, new List<MAResearchInfo>() {newInfo});
        else
            s_factionResearchDict[_faction].Add(newInfo);
        return newInfo;
    }
}
