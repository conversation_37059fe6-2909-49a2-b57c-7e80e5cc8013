using UnityEngine;

namespace VehicleStates
{
    [System.Serializable]
    public class InitialSpawnState : VehicleBaseState
    {
        public InitialSpawnState(MAVehicle _vehicle)
        {
            State = VehicleStateFactory.VehicleState.kInitialSpawn;
            m_stateControlledVehicle = _vehicle;
        }

        public override void OnEnter()
        {
            m_stateControlledVehicle.gameObject.SetActive(true);

            Transform tr = m_stateControlledVehicle.transform;
            
            var searchFromPos = m_stateControlledVehicle.Home ? m_stateControlledVehicle.Home.transform.position : tr.position;
            Vector3 pos = NGManager.Me.GetRoadStart(searchFromPos).transform.position;
            var toHome = (m_stateControlledVehicle.Home.transform.position - pos).GetXZNorm();
            for (int i = 0; i < NGManager.Me.m_maVehicles.Count; ++i)
            {
                if ((NGManager.Me.m_maVehicles[i].transform.position - pos).xzSqrMagnitude() < .5f * .5f)
                {
                    pos += toHome * 8;
                    i = -1;
                }
            }
            pos = pos.SetYToHeight();
            tr.position = pos + Vector3.up;

            m_stateControlledVehicle.Init();
        }

        public override void OnUpdate()
        {
            VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kMovingToHome, m_stateControlledVehicle);
        }

        public override void OnExit()
        {
            base.OnExit();
        }
    }
}