using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Random = UnityEngine.Random;

public class MATourist : MAWorker
{

	public override bool CanAssignHome => false;
	public override bool CanAssignJob => false;
	public override KeyboardShortcutManager.EShortcutType KeyboardShortcut => KeyboardShortcutManager.EShortcutType.HeldObjects;
	
	public class Decisions
	{
		public enum DecisionType
		{
			VisitBuilding,
			VisitDecoration,
		}
		
		public DecisionType m_decisionType;
		public string m_lookFor;
		public string m_animation;
	}

	public int m_sessions;
	public Vector3 m_leavePos;
	public List<Decisions> m_decisions = new List<Decisions>()
	{
		new Decisions() { m_decisionType = Decisions.DecisionType.VisitBuilding, m_lookFor = "ActionHouse", m_animation = "WorkerBrushOff" },
		new Decisions() { m_decisionType = Decisions.DecisionType.VisitDecoration, m_lookFor = "", m_animation = "" },

	};	
	public Decisions m_currentDecision;
	private bool m_waitForAnimation = false;
	private float m_stopLoopingAnimationAfter = -1;
	
#if UNITY_EDITOR
	override public void DebugShowGUIDetails(MACharacterWindow _window, GUIStyle _labelStyle)
	{
		base.DebugShowGUIDetails(_window, _labelStyle);
		if (m_currentDecision != null)
		{
			EditorGUILayout.LabelField($"Decision Type: ", m_currentDecision.m_decisionType.ToString(), _labelStyle);
			EditorGUILayout.LabelField($"Decision LookFor: ", m_currentDecision.m_lookFor, _labelStyle);
			EditorGUILayout.LabelField($"Decision Animation: ", m_currentDecision.m_animation, _labelStyle);
		}
	}
#endif

	public override EDeathType DeathCountType => EDeathType.None;
	override protected GameObject CharacterPrefab => (m_workerInfo == null || m_workerInfo.m_characterPrefab == null) ? GlobalData.Me.m_touristPrefab : m_workerInfo.m_characterPrefab;

    protected override void Start()
    {
        base.Start();
	    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
    }
    
    override protected void UpdateState()
    {
    	switch (m_state)
    	{
            case STATE.MA_LEAVING:
	            if (StateMoveToPosition())
	            {
		            DestroyMe();
	            }
	            break;
            case STATE.MA_DECIDE_WHAT_TO_DO:
	            DecideWhatToDo();
	            break;
            case STATE.MA_WAITING_FOR_ANIMATION:
            
				if(m_stopLoopingAnimationAfter >= 0)
				{
					m_stopLoopingAnimationAfter -= Time.deltaTime;
					if(m_stopLoopingAnimationAfter <= 0)
					{
						StopWorkerLoopAnimation();
						m_stopLoopingAnimationAfter = -1;
					}
	            }
				break;
            
            default:
            	base.UpdateState();
	            break;
        }
    }
    
    public override void SetDefaultAction()
    {
	    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
    }
    
    override protected bool StateMoveToBuilding()
    {        
	    if (base.StateMoveToBuilding() == false) return false;
	    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
	    return true;
    }
    // override protected bool StateMoveToCommander()
    // {
	   //  UpdateTargetedByNearbyCreature();
    //     if (m_agent && HasAgentArrived()) return true;
    //     return false;
    // }
    override protected bool StateMoveToPosition()
    {
	    if (m_nav && HasAgentArrived())
	    {
			switch (PeepAction)
			{
				case PeepActions.Despawn:
				case PeepActions.DespawnRun:
					DestroyMe();
					return true;
			}
			
			if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
				return false;
			
			return PlayNextAnimation();
		}
		
	    if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
		    return false;
		
        return false;
    }
    
    protected override void StateMoveToOutsideBuildingComplete()
    {
	    switch (PeepAction)
	    {
		    case PeepActions.Working:
			    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
			    break;
		    default:
			    base.StateMoveToOutsideBuildingComplete();
			    return;
	    }
    }
    
    override public void StateWorking()
    {
    }
    
    override public void NGSetAsWorking(bool _isVisibleInside)
    {
	    PeepAction = PeepActions.Working;
	    SetState(STATE.WORKING);
	    gameObject.SetActive(_isVisibleInside);
	    
	    IEnumerator StayInsideFor()
	    {
		    yield return new WaitForSeconds(3f);
		    SetMoveToPosition(m_insideMABuilding.DoorPosOuter, false, PeepActions.Working);
	    }
	    
	    MATouristManager.Me.StartCoroutine(StayInsideFor());
    }

    bool PlayNextAnimation()
    {
	    if(m_state == STATE.MA_DEAD)
	    {
		    return true;
	    }
	    if(m_animationStringsToPlay.Count == 0)
	    {
		    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
		    return true;
	    }
	    
	    // Looping animations will never finish on their own
	    var clip = AnimationOverride.LookupClip(m_animationStringsToPlay[0], out var attachData, out var blendTime);
	    if(clip != null && clip.isLooping)
	    {
		    m_stopLoopingAnimationAfter = clip.length * Random.Range(1,10);
	    }
	    PlaySingleAnimation(m_animationStringsToPlay[0], (c) => { PlayNextAnimation(); });
	    m_animationStringsToPlay.RemoveAt(0);
	    SetState(STATE.MA_WAITING_FOR_ANIMATION);
	    return false;
    }
    
    private void DecideWhatToDo(bool _requiresReset = false)
    {
		if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
			return;
	    
	    if (m_sessions <= 0)
	    {
		    SetMoveToPosition(m_leavePos, false, PeepActions.Working);
		    SetState(STATE.MA_LEAVING);
		    return;
	    }

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	    var randChoice = Random.Range(0, m_decisions.Count);
		m_currentDecision = m_decisions[randChoice];
		switch (m_currentDecision.m_decisionType)
		{
			case Decisions.DecisionType.VisitBuilding:
				if(SetupVisitBuilding(m_currentDecision))
					m_sessions--;
				break;
			case Decisions.DecisionType.VisitDecoration:
				if(SetupVisitDecoration(m_currentDecision))
					m_sessions--;
				break;
		}
#endif
    }
    public List<string> m_animationStringsToPlay = new List<string>();
    void ParseAnimString(string _animString)
    {
		m_animationStringsToPlay.Clear();
	    if (_animString.IsNullOrWhiteSpace()) return;
	    var anims = _animString.Split('\n',',');
	    foreach(var a in anims)
	    {
		    var choice = a.Split('|');
		    var choiceIndex = 0;
		    if(choice.Length > 1)
		    {
			    choiceIndex = Random.Range(0, choice.Length);
		    }
		    m_animationStringsToPlay.Add(choice[choiceIndex]);
	    }
	}

    bool SetupVisitBuilding(Decisions _decision)
    {
	    if (m_currentDecision.m_lookFor.IsNullOrWhiteSpace() == false)
	    {
		    MABuilding bestBuilding = null;
		    var bestDistance = float.MaxValue;
			
		    var cInfo = MAComponentInfo.GetInfo(m_currentDecision.m_lookFor);
		    var i = 0;
		    for(int j = Random.Range(0, NGManager.Me.m_maBuildings.Count); i < NGManager.Me.m_maBuildings.Count; i++, j++)
		    {
			    if (j >= NGManager.Me.m_maBuildings.Count)
				    j = 0;
			    var building = NGManager.Me.m_maBuildings[j];
			    if (building.HasBuildingComponent(cInfo.m_classType))
			    {
				    var distance = Vector3.Distance(transform.position, building.transform.position);
				    if (distance < bestDistance)
				    {
					    bestDistance = distance;
					    bestBuilding = building;
					    if(Random.value < 0.2f)
						    break;
				    }
			    }
		    }
		    if (bestBuilding == null)
			    return false;
		   //MoveToBuilding(bestBuilding,PeepActions.Working);
		    SetMoveToPosition(bestBuilding.DoorPosOuter, false, PeepActions.Working);
	    }
	    return true;
    }

    bool SetupVisitDecoration(Decisions _decision)
    {
		if (MATouristManager.Me.m_decorations.Count == 0) return false;
	    var bestDecoration = MATouristManager.Me.m_decorations[Random.Range(0, MATouristManager.Me.m_decorations.Count)]; 
	    var info = NGDecorationInfoManager.NGDecorationInfo.GetInfo(bestDecoration.Name);
	    if (info == null) return false;
	    ParseAnimString(info.m_touristAnim);
	    var pos = GetPositionOnCircle(bestDecoration.transform.position, 1, Random.Range(0, 360));
	    SetMoveToPosition(bestDecoration.transform.position, false, PeepActions.Working);
	    return true;
    }
    
    override public void InitialiseVisuals(CharacterVisuals.Type _type)
    {
	    base.InitialiseVisuals(_type);

	    //SetSpeed(10f);
    }
    public Vector3 GetPositionOnCircle(Vector3 center, float m_circleRadius, float angleInRadians)
    {
	    // Convert degrees to radians (Unity uses radians for trigonometric functions)
	    //float angleInRadians = angleInDegrees * Mathf.Deg2Rad;

	    // Calculate the position on the circle using the cosine and sine of the angle
	    float xPos = center.x + m_circleRadius * Mathf.Cos(angleInRadians);
	    float yPos = center.y + m_circleRadius * Mathf.Sin(angleInRadians);
	    float zPos = center.z; // Assuming a flat circle on the XZ plane, adjust for 3D if needed

	    // Return the calculated position
	    return new Vector3(xPos, yPos, zPos);
    }

    public override float GetDesiredSpeed()
    {
	    float speedFactor = (1 + Random.Range(-1 / 30f, 1 / 30f)) *
	                        ((PeepAction == PeepActions.Flee || PeepAction == PeepActions.DespawnRun) ? m_runMultiplier : 1f);
	    return m_gameState.m_speed * speedFactor;
    }

    virtual protected void InitState()
    {
	    if (m_currentDecision != null)
	    {
		    switch(m_currentDecision.m_decisionType)
		    {
			    case Decisions.DecisionType.VisitBuilding:
				    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
				    break;
			    case Decisions.DecisionType.VisitDecoration:
				    SetState(STATE.MA_DECIDE_WHAT_TO_DO);
				    break;
		    }
	    }
    }

    protected void ActivateTourist(int _sessions, Vector3 _leavePos, Decisions _startDecision)
	{
	    m_sessions = _sessions;
	    m_leavePos = _leavePos;
	    m_currentDecision = _startDecision;
	    
	    TouristGameState.m_leavePos = _leavePos;
	    InitState();
	}

	override protected void OnDestroy()
	{
		if(MATouristManager.Me != null)
			MATouristManager.Me.m_tourists.Remove(this); // TODO - some cases aren't going through DestroyMe
		base.OnDestroy();
	}

	override public void DestroyMe()
    {
	    MATouristManager.Me.m_tourists.Remove(this);
	    NGManager.Me.m_MAHumanList.Remove(this);
	    NGManager.Me.m_MACharacterList.Remove(this);
	    base.DestroyMe();
    }
    
	protected override GameState_Person CreateNewGameState() { return new GameState_Tourist(); }
    public GameState_Tourist TouristGameState { get { return m_gameState as GameState_Tourist; } }
    
    public static MATourist Load(GameState_Tourist _stateData)
    {
	    var tourist = MAWorker.Load(_stateData) as MATourist;
	    
	    tourist.m_leavePos = _stateData.m_leavePos;
	    
	    if(tourist != null)
			MATouristManager.Me.m_tourists.Add(tourist);
	    return tourist;
    }
    
    public static MATourist Create(MAWorkerInfo _workerInfo, Vector3 _pos, int _sessions, Decisions _startDecision)
    {
	    var tourist = MAWorker.Create(_workerInfo, _pos) as MATourist;
	    _pos = _pos.GroundPosition();
	    tourist.ActivateTourist(_sessions, _pos, _startDecision);
	    return tourist;
    }

}

#if UNITY_EDITOR
[CustomEditor(typeof(MATourist))]
public class MATouristEditor : MAWorkerEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
}
#endif
