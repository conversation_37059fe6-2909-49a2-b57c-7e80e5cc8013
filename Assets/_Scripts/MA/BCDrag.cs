using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCDrag : BCBase
{
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        if (m_building.gameObject.GetComponent<BuildingPickupBehaviour>() == null)
        {
            m_building.gameObject.AddComponent(typeof(BuildingPickupBehaviour));
            m_building.GetComponent<BuildingPickupBehaviour>().enabled = true;
        }

        if (m_building.gameObject.GetComponent<Pickup>() == null)
        {
            var t = m_building.gameObject.AddComponent(typeof(Pickup));
            m_building.GetComponent<Pickup>().enabled = true;
            
        }
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }

    override protected void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
    {
        base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
        
        if(_previousOwner != null)
        {
            if (_quantityRemainingInBuilding <= 0 && _previousOwner.gameObject.GetComponent<BuildingPickupBehaviour>())
            {
                Destroy(_previousOwner.gameObject.GetComponent<BuildingPickupBehaviour>());
            }
        }
    }

    override public bool HasDragContent()
    {
        foreach (var cmp in m_building.BuildingComponents<BCStockOut>(true))
        {
            if(cmp.HasDragContent()) return true;
        }
        
        foreach(var cmp in m_building.ActionComponents)
        {
            if(cmp.HasDragContent()) return true;
        }
        
        foreach (var s in m_building.BuildingComponents<BCBedroom>())
        {
            if(s.HasDragContent()) return true;
        }
        return false;
    }
    
    override public GameObject GetDragContent()
    {
        if (enabled == false) return null;
        
        var stockCmps = m_building.BuildingComponents<BCStockOut>(true);
        foreach (var cmp in stockCmps)
        {
            var obj = cmp.GetDragContent();
            if (obj)
            {
                var pickup = obj.GetComponent<ReactPickup>();
                var resource = pickup.Contents;
                int totalStockOfThisType = 0;
                foreach (var cmpStock in stockCmps)
                    totalStockOfThisType += cmpStock.GetStock().GetTotalStock(resource);
                int extraToGrab = Mathf.FloorToInt((totalStockOfThisType + 1) * MAUnlocks.Me.m_dragFromBuildingPercent) - 1;
                for (int i = 0; i < extraToGrab; ++i)
                {
                    foreach (var cmpStock in stockCmps)
                    {
                        var objNext = cmpStock.TakePickup(resource, false);
                        if (objNext != null)
                        {
                            var nextPickup = objNext.GetComponent<ReactPickup>();
                            pickup.m_nextInChain = nextPickup;
                            pickup = nextPickup;
                            break;
                        }
                    }
                }
                return obj.gameObject;
            }
        }
        
        // Do factories first
        foreach (var component in m_building.ActionComponents)
        {
            if(component is not BCFactory) continue;
            
            var obj = component.GetDragContent();
            if (obj) return obj.gameObject;
        }
        
        foreach (var component in m_building.ActionComponents)
        {
            if(component is BCFactory) continue;
            
            var obj = component.GetDragContent();
            if (obj) return obj.gameObject;
        }
        
        //Check for idle workers
        foreach (var s in m_building.BuildingComponents<BCBedroom>())
        {
            var obj = s.GetDragContent();
            if(obj != null) return obj;
        }
        return null;
    }

}
