using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class MAAttackSkill
{
	public static List<MAAttackSkill> s_attackSkills = new List<MAAttackSkill>();
	public static List<MAAttackSkill> GetList=>s_attackSkills;
	public string DebugDisplayName => m_id;
	public bool m_debugChanged;

	public static MAAttackSkill GetByID(string _id)
	{
		for (int i=0; i<s_attackSkills.Count; i++)
		{
			if (_id == s_attackSkills[i].ID)
			{
				return s_attackSkills[i];
			}
		}
		return null;
	}

	public string id;
	[SerializeField] string m_id;

	[SerializeField] float m_damage;
	[SerializeField] float m_buildingDamage;
	[SerializeField] bool m_canInterrupt;
	[SerializeField] bool m_canMove;
	[SerializeField] float m_knockbackPower;
	[SerializeField] float m_explosionPower;
	[SerializeField] float m_attackWidth;
	[SerializeField] float m_attackRadius;
	[SerializeField] string m_visualsUsed;
	[SerializeField] string m_runeEffect;
	[SerializeField] bool m_weaponGlow;
	[SerializeField] bool m_isBranch;
	[SerializeField] bool m_isProjectile;
	[SerializeField] float m_hitChance;

	public string ID => m_id;

	public float Damage => m_damage;
	public float BuildingDamage => m_buildingDamage;
	public bool CanInterrupt => m_canInterrupt;
	public bool CanMove => m_canMove;
	public float KnockbackPower => m_knockbackPower;
	public float ExplosionPower => m_explosionPower;
	public float AttackWidth => m_attackWidth;
	public float AttackRadius => m_attackRadius;
	public string VisualsUsed => m_visualsUsed;
	public string RuneEffect => m_runeEffect;
	public bool UsesWeaponGlow => m_weaponGlow;
	public bool IsBranch => m_isBranch;
	public bool IsProjectile => m_isProjectile;

	public float HitChance
	{
		get
		{
			if (!IsProjectile)
			{
				return 1f;
			}
			return m_hitChance;
		}
	}

	public static bool PostImport(MAAttackSkill _what)
	{
		_what.m_isBranch = _what.m_id.StartsWith("B:");
		return true;
	}

	public static List<MAAttackSkill> LoadInfo()
	{
		s_attackSkills = NGKnack.ImportKnackInto<MAAttackSkill>(PostImport);
		return s_attackSkills;
	}
}

// RW-20-MAR-25: The MAAttackSkill represents the constant data associated with an attack.
// However, the damage it does might vary depending on the character using it. This represents
// the attack as used by that character.
// The whole point here is NOT to expose the AttackSkill so people can't end up using the 
// unboosted damage accidentally.
public class MAAttackInstance
{
	MAAttackSkill m_attackSkill;
	MACharacterBase m_attacker;
	int m_indexInCombo;

	public float Damage => m_attackSkill.Damage * m_attacker.GetDamageMultiplier();
	public float BuildingDamage => m_attackSkill.BuildingDamage * m_attacker.GetDamageMultiplier();
	public bool CanInterrupt => m_attackSkill.CanInterrupt;
	public bool CanMove => m_attackSkill.CanMove;
	public float KnockbackPower => m_attackSkill.KnockbackPower;
	public float ExplosionPower => m_attackSkill.ExplosionPower;
	public float AttackWidth => m_attackSkill.AttackWidth;
	public float AttackRadius => m_attackSkill.AttackRadius;
	public string VisualsUsed => m_attackSkill.VisualsUsed;
	public string RuneEffect => m_attackSkill.RuneEffect;
	public bool UsesWeaponGlow => m_attackSkill.UsesWeaponGlow;
	public bool IsBranch => m_attackSkill.IsBranch;
	public bool IsProjectile => m_attackSkill.IsProjectile;
	public float HitChance => m_attackSkill.HitChance;
	public MACharacterBase Attacker { get => m_attacker; }
	public int IndexInCombo => m_indexInCombo;
	

	public MAAttackInstance(MAAttackSkill _attack, MACharacterBase _attacker, int _indexInCombo)
	{
		m_attackSkill = _attack;
		m_attacker = _attacker;
		m_indexInCombo = _indexInCombo;
	}


}