using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAFightVisualsControl : MonoSingleton<MAFightVisualsControl>
{
    public GameObject[] m_scratchFX;
	public GameObject[] m_slashFX;
		
	public bool m_infiniteDecals;
	public Light m_dirLight;

	public int m_iEffect = 0; 
	public int m_activeDecalCount;

	public float m_decalScale = 1f;

	public LayerMask m_layerMask;

	[SerializeField]
	private int m_maxDecals = 20;

	public void ShowDecalAtHit(MACharacterBase _attacker, Vector3 _point, Vector3 _normal, Vector3 _attackDir, Transform _transform, int _emitCount = 1)
	{
		if(m_activeDecalCount >= m_maxDecals) 
			return;

		var fx = _attacker is MAZombie ? m_scratchFX : m_slashFX; //Temp logic

		_normal.y = 0;
		var side = (_attackDir - Vector3.Dot(_attackDir, _normal) * _normal).normalized;
		var up = Vector3.Cross(side, _normal);

		while (_emitCount-- > 0)
		{
			if (m_iEffect < 0 || m_iEffect >= fx.Length - 1)
				m_iEffect = 0;

			var rot = Quaternion.LookRotation(_point + _normal, up);
			var instance = Instantiate(fx[m_iEffect], _point, rot, _transform);

			var settings = instance.GetComponent<BFX_BloodSettings>();
			if (m_dirLight)
				settings.LightIntensityMultiplier = m_dirLight.intensity;

			if (!m_infiniteDecals)
				StartCoroutine(DestroyDecal(instance, settings.DecalLifeTimeSeconds));

			m_iEffect++;
			if (++m_activeDecalCount >= m_maxDecals) 
				return;
		}
	}

	private IEnumerator DestroyDecal(GameObject _instance, float _secs)
	{
		yield return new WaitForSeconds(_secs);
		if(_instance != null)
		{
			Destroy(_instance); 
			m_activeDecalCount--;
		}
	}
}
