using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MAVehicleControl : MonoSingleton<MAVehicleControl>
{
    [SerializeField] 
    private List<MAVehicle> m_vehicleTypeList = new List<MAVehicle>();
    
    [SerializeField] private float m_generalSpeedMod = 0.5f;
    
    public float GeneralSpeedMod => m_generalSpeedMod;

    public string[] VehicleTypes
    {
        get
        {
            string[] typeList = new string[m_vehicleTypeList.Count];
            for(int i = 0; i < m_vehicleTypeList.Count; i++)
                typeList[i] = m_vehicleTypeList[i].GetType().Name;
            return typeList;
        }
    }

    private void Update()
    {
        if (NGManager.Me != null)
        {
            for(int i = NGManager.Me.MAVehicles.Count - 1; i >= 0; i--)
            {
                MAVehicle vehicle = NGManager.Me.MAVehicles[i];
                if(vehicle == null)
                {
                    NGManager.Me.MAVehicles.RemoveAt(i);
                    continue;
                }
                vehicle.VehicleState.OnUpdate();
            }
        }
    }

    public void LoadSavedVehicles(List<GameState_MAVehicle> _vehicleData)
    {
        for(int i = 0; i < _vehicleData.Count; i++)
        {
            SpawnVehicleWithData(_vehicleData[i]);
        }
    }
    
    public MADeliveryCart SpawnNewDeliveryCart()
    {
        GameState_MAVehicle gameState_maVehicle = new GameState_MAVehicle();
        gameState_maVehicle.m_type = nameof(MADeliveryCart);
        gameState_maVehicle.m_maxCargoSpace = NGManager.Me.m_defaultVanCapacity;
        gameState_maVehicle.m_health = 1f;
        gameState_maVehicle.m_energy = 1f;
        GameManager.Me.m_state.m_vehicles.Add(gameState_maVehicle);
        return SpawnVehicleWithData(gameState_maVehicle) as MADeliveryCart;
    }
    
    public MAVehicle SpawnVehicleByPrefabName(string _maVehicleTypeName)
    {
        MAVehicle vehicleType = GetVehiclePrefabByTypeName(_maVehicleTypeName);
        if(vehicleType == null)
        {
            Debug.LogError($"MAVehicleControl - vehicleType {_maVehicleTypeName} ref is null or does not exist");
            return null;
        }
        
        MAVehicle go = Instantiate(vehicleType, GlobalData.Me.m_characterHolder, true);
        if(go == null)
        {
            Debug.LogError($"MAVehicleControl - vehicleType {vehicleType.Name} instantiates to null");
            return null;
        }
        
        return go;
    }
    
    private T SpawnVehicle<T>() where T : MAVehicle
    {
        T maVehicle = SpawnVehicleByType(typeof(T)) as T;
        if(maVehicle == null)
        {
            Debug.LogError($"MAVehicleControl - vehicleType {typeof(T).Name} ref is null or does not exist");
            return null;
        }
        
        return maVehicle;
    }
    
    private MAVehicle SpawnVehicleWithData(GameState_MAVehicle _gameStateMaVehicle)
    {
        MAVehicle vehicle = MAVehicle.Create(_gameStateMaVehicle.m_type);
        if(_gameStateMaVehicle.m_id <= 0)
        {
            vehicle.AllocateID();
            _gameStateMaVehicle.m_id = vehicle.m_ID;
        }
        vehicle.SetVehicleData(_gameStateMaVehicle);
        return vehicle;
    }
    
    private MAVehicle SpawnVehicleByType(Type _maVehicleType)
    {
        return SpawnVehicleByPrefabName(_maVehicleType.Name);
    }

    private MAVehicle GetVehiclePrefabByTypeName(string _maVehicleType)
    {
        int iVehicle = m_vehicleTypeList.FindIndex(x => x.GetType().Name == _maVehicleType);
        if(iVehicle == -1)
        {
            Debug.LogError($"MAVehicleControl - GetVehiclePrefabByTypeName {_maVehicleType} does not exist");
            return null;
        }
        return m_vehicleTypeList[iVehicle];
    }
    
    private T GetVehiclePrefabByType<T>() where T : MAVehicle
    {
        return GetVehiclePrefabByTypeName(nameof(T)) as T;
    }

    [Serializable]
    public class VehicleMetadata
    {
        public float m_waitingTimePathBlocked = 2f;
        public float m_waitingTimeAtDestination = 0f;
        public float m_waitingTimeForDeliveriesOffMap = 5f;
        public float m_speedMod = 1f;
    }

    public void EditorSpawnVehicle(string _vehicleType)
    {
        MAVehicle.Create(_vehicleType);
    }
}

#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(MAVehicleControl))]
public class MAVehicleControlEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        MAVehicleControl vehicleControl = (MAVehicleControl)target;
        
        if(Application.isPlaying)
        {
            foreach(string maVehicleType in vehicleControl.VehicleTypes)
            {
                if(GUILayout.Button($"Spawn {maVehicleType}"))
                {
                    vehicleControl.EditorSpawnVehicle(maVehicleType);
                }
            }
            // if(GUILayout.Button("Spawn Ad Cart"))
            // {
            //     vehicleControl.SpawnAdvertVehicle();
            // }        
            // if(GUILayout.Button("Spawn Ad Cart"))
            // {
            //     vehicleControl.SpawnAdvertVehicle();
            // }
        }
    }
}
#endif
