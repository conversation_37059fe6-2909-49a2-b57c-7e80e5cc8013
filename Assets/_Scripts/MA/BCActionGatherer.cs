using System.Collections.Generic;
using UnityEngine;

public class BCActionGatherer : BCActionProducer
{
    [KnackField] public string m_treeHolderType;
    [KnackField] public float m_treeRadius = 10f;
    [KnackField] public float m_harvestRegrowDawns = 1f;
    [KnackField] public float m_workerHarvestRate = 0.1f;
    [KnackField] public float m_energyRequiedToHarvest = 1f;
    [KnackField] public float m_minResourceFlyTime = 0.2f;
    [KnackField] public float m_maxResourceFlyTime = 2f;
    
    private BCChopObject m_tapChopObject = null;
    private float m_nextPickupTime = 0f;
    private string m_lastCantMakeReason = "";
    override public int CantMakePriority => 10;
    
    public override bool ShowWarning => m_hasResourcesInRange == false || base.ShowWarning;

    public bool m_hasResourcesInRange = false;

    public override void OnBuildingMoved()
    {
        GetNextResource();   // This will update m_hasResourcesInRange
    }

    protected override void SetupStockRequirements()
    {
        base.SetupStockRequirements();
        GetNextResource();  // This will update m_hasResourcesInRange
    }
    
    private ReactPickupPersistent GetDroppedResource()
    {
        var resType = BCChopObject.TypeToCarriableResource(m_treeHolderType);
        foreach(var pickupData in GameManager.Me.m_state.m_pickups)
        {
            var pickup = pickupData.Pickup;
            if(pickup == null || pickup.m_holder != null || pickup.Contents != resType || pickup.IsBeingDragged || pickup.m_collisionStyle == NGMovingObject.COLLISIONSTYLE.TRIGGER)
                continue;
            
            var d2 = (pickup.transform.position - m_building.transform.position).xzSqrMagnitude();
            if(d2 > (m_treeRadius*m_treeRadius))
                continue;
            
            return pickup;
        }
        return null;
    }
    
    public virtual (GameObject go, PeepActions action) GetPickupObject(MACharacterBase _worker)
    {
        if (HasStockSpace() == false)
            return (null, PeepActions.None);
        
        var droppedPickup = GetDroppedResource();
        if(droppedPickup && droppedPickup.TryReserve(_worker))
            return (droppedPickup.gameObject, PeepActions.CollectPickup);
        
        var treeObject = GetNextResource();
        if(treeObject)
            return (treeObject, PeepActions.Chop);
            
        return (null, PeepActions.None);
    }
    
    public override string GetCantMakeReason()
    {
        return m_lastCantMakeReason;
    }
    
    private float GetResourceFlyTime()
    {
         return Mathf.Lerp(m_maxResourceFlyTime, m_minResourceFlyTime, (m_building.GetWorkerSpeedMultiplier()-1) / 4f);
    }

    private bool m_hasClearedResourcesByTapping = false;
    
    override public bool UpdateTapWork()
    {
        m_lastCantMakeReason = "";
        
        if (m_building.IsPaused)
        {
            m_lastCantMakeReason = "building paused"; 
            return false;
        }

        // 1. Wait for objects in transit
        if(m_nextPickupTime > Time.time)
            return true;

        // 2. Check if we have enough to produce the harvested resource
        if(IsReadyToProduce(out var doneWork, true))
        {
            UpdateProduction(null, true);
            return true; // We're processing the input stock
        }
        
        // 3. Check if we have stock space to gather more resources 
        if (HasStockSpace() == false && m_stock.GetTotalStock() > 0)
        {
            m_lastCantMakeReason = "no stock space";
            return false;
        }
        
        var droppedResource = GetDroppedResource();
        if(droppedResource)
        {
            var flyTime = GetResourceFlyTime();
            m_building.DroppedFromHand(droppedResource, Vector3.zero, transform.position, flyTime);
            m_nextPickupTime = Time.time + flyTime;
            return true;
        }

        // 4. Try and gather more resources
        if(m_tapChopObject != null && m_tapChopObject.IsChoppedDown)
        {
            m_tapChopObject = null;
        }
        
        if (m_tapChopObject == null)
        {
            var tree = GetNextResource();
            if (tree == null)
            {
                if(m_hasClearedResourcesByTapping)
                {
                    if(m_treeHolderType !=  TreeHolder.c_trees)
                    {
                        AlignmentManager.Me.ApplyAction("ClearedHotspot", 0.002f, m_treeHolderType);
                    }
                    m_hasClearedResourcesByTapping = false;
                }
                m_lastCantMakeReason = $"no {m_treeHolderType?.ToLower()} available to gather";
                return false; // No resource left
            }
            
            m_tapChopObject = tree.GetComponentInChildren<BCChopObject>();
            if (m_tapChopObject == null)
            {
                m_tapChopObject = tree.gameObject.AddComponent<BCChopObject>();
            }
        }
        
        float powerRequired = m_tapChopObject.HitsUntilHarvest() * m_energyRequiedToHarvest;
        float powerRemaining = powerRequired;
        
        m_building.ConsumePower(ref powerRemaining);
        
        float powerUsed = Mathf.Max(0, powerRequired - powerRemaining);
        float harvestAmount = powerUsed / m_energyRequiedToHarvest;
        
        if (m_tapChopObject.HitObject(harvestAmount))
        {
            var flyTime = GetResourceFlyTime();
            m_nextPickupTime = Time.time + flyTime;

            var resource = m_tapChopObject.GetCarriableResourceProduced();
            var pickup = ReactPickupPersistent.CreateItem(m_building, resource, 1, m_building);
            pickup.transform.position = m_tapChopObject.transform.position;
            pickup.transform.localRotation = Quaternion.identity;
            
            m_building.DroppedFromHand(pickup, Vector3.zero, transform.position, flyTime);
            
            m_tapChopObject = null;
            GetComponent<Block>().PlayCreateOutputAudio();
            
            m_hasClearedResourcesByTapping = true;
        }

        return true;
    }

    protected override string GetDescriptionPrefix()
    {
        var harvestItem = NGCarriableResource.GetInfo(m_input)?.TextSprite;
        GetHarvestResourceState(out var total, out var remaining);
        
        if(total == 0)
            return $"<color=orange>No <size=150%>{harvestItem}</size> within harvistable range</color>";
            
        return $"{remaining} of {total} <size=150%>{harvestItem}</size> available to harvist";
    }
    
    public virtual void GetHarvestResourceState(out int _total, out int _remaining)
    {
        _total = 0;
        _remaining = 0;
        
        var instances = TerrainPopulation.Me.InstancesInRange(m_building.transform.position, m_treeRadius, true);

        foreach(var _inst in instances)
        {
            if (!DistrictManager.Me.IsWithinDistrictBounds(_inst.transform.position))
                continue;
                
            var holder = _inst.GetComponent<TreeHolder>();
            if (holder == null || !_inst.activeSelf || holder.IsLocked || holder.m_treeType != m_treeHolderType || holder.GetPrefab() == null)
                continue;
            
            var chopObject = _inst.GetComponent<BCChopObject>();
            
            if (chopObject != null)
            {
                _remaining += chopObject.ResourcesRemaining();
            }
            else
            {
                _remaining += holder.TotalResourceProduced;
            }
            
            _total += holder.TotalResourceProduced;
        }

        m_hasResourcesInRange = _total > 0;
    }
    
    public virtual GameObject GetNextResource()
    {
        m_hasResourcesInRange = false;
        // TODO cache this list of TreeHolders, only refresh when the component or building moves
        GameObject obj = TerrainPopulation.Me.NearestInstanceToPosition(m_building.transform.position, m_treeRadius, true, (_inst) => {
            if (!DistrictManager.Me.IsWithinDistrictBounds(_inst.transform.position))
                return false;
            
            var holder = _inst.GetComponent<TreeHolder>();
            if (holder == null || !_inst.activeSelf || holder.IsLocked || holder.m_treeType != m_treeHolderType || holder.GetPrefab() == null)
                return false;
                
            m_hasResourcesInRange = true;
                
            var chopObject = _inst.GetComponent<BCChopObject>();
            if (chopObject != null)
            {
                if (!chopObject.IsChopObjectValid)
                    return false;
                if (chopObject.IsChoppedDown)
                    return false;
                if (chopObject.m_worker != null)
                    return false;
            }
            return true;
        });
        return obj;
    }
    
    public bool HasStockSpace()
    {
        if(m_inputStock.CanMake() == false) return true;
        return m_building.GetStockSpace(typeof(BCStockIn)) > 0;
    }

    public override bool AskForInputStock()
    {
        if(base.AskForInputStock())
            return true;
            
        if(m_workersPresent.Count == 0)
            return false;
        var worker = m_workersPresent[0];

        (GameObject go, PeepActions action) what = GetPickupObject(worker);
        if(what.go)
        {
            worker.SetMoveToObject(what.go, what.action);
            m_building.Leave(worker);
            return true;
        }
        return false;
    }
}
