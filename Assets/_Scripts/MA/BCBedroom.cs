using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class BCBedroom : BCActionBase
{
    [KnackField]public float m_restPerSecond = 0.05f;
    
    private Balloon m_balloon = null;
#if UNITY_EDITOR    
    override public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
    {
        if(_showBase)
            base.DebugShowGUIDetails(_labelStyle, _showBase);
    }
#endif

    public override void GetCombinedValue(ref CombinedStat _value)
    {
        if(_value == null) _value = new BedroomSlotCombinedStat();
        base.GetCombinedValue(ref _value);
    }
    
    public override List<MACharacterBase> GetWorkersAllocated() => m_workersAllocated;
    public override List<MACharacterBase> GetWorkersPresent() => m_workersPresent;
    
    override public void UpdateInternal(BuildingComponentsState _state)
    {
        UpdateWorkerResting();
        UpdateWorkerBalloons();
    }
    
    public override bool Allocate(MACharacterBase _worker)
    {
        // GL - 300625 - we don't want the base Allocate call to assign the job because subsequently assigning the job will remove this worker from the m_workersAllocated list
        // This could be achieved by putting a test inside the base Allocate method, but that's an alien dependency
        // We could pass a flag through to the base but that's an addition to every case just to handle this one case
        // We can't call base.base.Allocate since that's not supported in C# and would inject a dependency on the class hierarchy in any case
        // We could abstract the work that is always done from the work that is sometimes done, but that's a big change for a small problem
        // Probably the cleanest is to unassign Job after returning true from base; ugly but doesn't require a change to the class hierarchy
        if(base.Allocate(_worker))
        {
            if (_worker.Job == this) _worker.DeallocateJob(); // GL - 300625 - BCActionBase.Allocate sets the job, clear it but check in case something else assigned a correct job to it
            _worker.Home = this;
            return true;
        }
        return false;
    }

    override public bool Arrive(MACharacterBase _worker)
    {
        if(base.Arrive(_worker))
        {
            _worker.NGSetAsResting();
            return true;
        }
        return false;
    }
    
    public override bool HasDragContent()
    {
        var worker = GetIdleWorker();
        return MAUnlocks.CanPickup(worker);
    }

    public override GameObject GetDragContent()
    {
        return GetIdleWorker()?.gameObject;
    }

    void UpdateWorkerBalloons()
    {
        int joblessCount = 0;
        for(var i = m_workersAllocated.Count - 1; i >= 0; i--)
        {
            if(m_workersAllocated[i].Job == null)
            {
                joblessCount++;
            }
        }
        if(joblessCount > 0)
        {
            if(m_balloon == null)
            {
                m_balloon = m_building.CreateBalloon(NGBalloonManager.BalloonType.Red, "Need Job", null, WaitForWorkerAssign);
            }
        }
    }
    
    void UpdateWorkerResting()
    {
        if(m_workersPresent.Count == 0) return;
        
        Building.m_isInhabited = true;
        
        var multiplier = m_building.GetBedroomMultiplier();
        for (var i = m_workersPresent.Count - 1; i >= 0; i--)
        {
            ShowChimneySmoke();
            
            var worker = m_workersPresent[i] as MAWorker;
            worker.Energy = Mathf.Clamp(worker.Energy+Time.deltaTime * m_restPerSecond*multiplier, 0f, worker.MaxEnergy);
            
            if (worker.UpdateThreatAlarm())
            {
                if (worker.m_state != NGMovingObject.STATE.HIDING)
                {
                    worker.PeepAction = PeepActions.Flee;
                    m_building.WorkerArrivesToHide(worker);
                }
                continue;
            }
            
            if (worker.IsTimeToGoHome || worker.IsMaxEnergy() == false)
            {
                if (worker.m_state == NGMovingObject.STATE.HIDING)
                {
                    worker.PeepAction = PeepActions.ReturnToRest;
                    m_building.WorkerArrivedReturnToRest(worker);
                }
                continue;
            }

            if (m_building.WorkerArrivesToWork(worker))
            {
                // Not much to do here
            }
            else if (worker.Job)
            {
                worker.SetMoveToComponent(worker.Job, PeepActions.ReturnToWork);
            }
            else
            {
                worker.SetMoveToPosition(m_building.DoorPosOuter, false, PeepActions.WaitingForWork);//changed from direct to indirect due to recent building/move changes
            }
            Leave(worker);
        }
    }

    void WaitForWorkerAssign(Balloon _balloon)
    {
        foreach (var w in m_workersAllocated)
        {
            if (w.Job == false)
                return;
        }
        _balloon.DestroyMe();
    }

    override public MAWorker GetIdleWorker()
    {
        foreach (var c in m_workersAllocated)
        {
            MAWorker w = c as MAWorker;
            if (w != null && w.m_state == NGMovingObject.STATE.MA_WAITING_FOR_WORK)
            {
                return w;
            }
        }

        return null;
    }

    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (BCResidentsPanel.PanelID, () => new BCResidentsPanel());
}
   
public class BCResidentsPanel : BCUIPanel
{
    public const string PanelID = "residents";
    public List<BCBedroom> m_bcBedrooms = new();
    public override bool ForceQuantityDisplay => true;
    
    public BCResidentsPanel() : base(PanelID, "Worker Bedrooms") { }

    public override string GetDescription()
    {
        BCBase.CombinedStat stat = null;
        foreach (var c in m_bcBedrooms) c.GetCombinedValue(ref stat);
        return stat?.GetValue();
    }
    
    public override void AddComponent(BCBase _component)
    {
        BCBedroom bc = _component as BCBedroom;
        if(bc != null)
        {
            m_bcBedrooms.Add(bc);
            m_quantity += bc.m_maxWorkers;
            m_all.Add(bc);
        }
        // Dont call base
    }
    
    override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
    {
        bool createdTitle = false;
        List<MACharacterBase> characters = new();
        foreach(var workerBase in m_bcBedrooms)
        {
            foreach(var character in workerBase.m_workersAllocated)
            {
                if(characters.Contains(character)) continue;
                if(character.Home?.Building != workerBase.Building)
                    continue;
                
                if(createdTitle == false)
                {
                    MABuildingWorkerPanelLine.CreateTitle(_holder);
                    createdTitle = true;
                }
                
                characters.Add(character);
                yield return () => MABuildingWorkerPanelLine.Create(_holder, character);
            }
        }
        if(characters.Count == 0)
        {
            yield return () => MADesignInfoSheetLine.Create(_holder, "No Workers to list", null, true);
        }
    }
}
