using System;
using UnityEngine;
using System.Collections.Generic;

public static class MACharacterStateFactory
{
	public static bool ApplyInitialState(string _state, MACharacterBase _character)
	{
		if(_character == null)
			return false;

		if(_character.StateLibrary().TryGetValue(_state, out Func<MACharacterBase, CharacterBaseState> createState))
		{
			_character.SetScheduledState(createState(_character));
		}
		else
		{
			Debug.LogError(
				$"MACharacterStateFactory - {_character?.name} - ApplyInitialState - Could not find state of type {_state.ToString()} for creature id {_character.m_ID}");
			return false;
		}

		return true;
	}

	public static bool ApplyCharacterState(string _state, MACharacterBase _character)
	{
		CharacterBaseState currentState = _character.CharacterUpdateState;

		if(_character.StateLibrary().TryGetValue(_state, out Func<MACharacterBase, CharacterBaseState> createState))
		{
			currentState?.OnExit();
			_character.SetScheduledState(createState(_character));
		}
		else
		{
			Debug.LogError(
				$"MACharacterStateFactory - {_character?.name} - ApplyState - Could not find state of type {_state}");
			return false;
		}

		if (currentState != null && currentState.State == _state)
		{
			Debug.Log($"MACharacterStateFactory - {_character?.name} - ApplyState - Trying to apply same state as exists: {_state}");
			return false;
		}
		return true;
	}
}


[Serializable]
public abstract class CharacterBaseState : BaseState
{
	[ReadOnlyInspector] [SerializeField] protected MACharacterBase m_character = null;
	[ReadOnlyInspector] [SerializeField] protected GameState_Character m_gameStateData = null;

	public Action<string, MACharacterBase> OnStateFinished = null;
	
	public virtual bool IsPatrolState => false;

	private HashSet<string> responsiveStates = new HashSet<string>
	{

	};

	private HashSet<string> draggedStates = new HashSet<string>
	{
		CharacterStates.HeldByPlayer
	};

	private HashSet<string> ragdolledStates = new HashSet<string>
	{
		CharacterStates.Dying,
		CharacterStates.Dead,
		CharacterStates.Unconscious,
		CharacterStates.UnconsciousDead,
		// CharacterStates.KnockedDown,
		// CharacterStates.Dying,
		// CharacterStates.Dead
	};

	private HashSet<string> noneStates = new HashSet<string>
	{
		CharacterStates.KnockedDown,
		CharacterStates.StandUp,
		CharacterStates.WaitingToTeleport
	};

	public CharacterBaseState(string _state, MACharacterBase _character) : base(_state)
	{
		m_character = _character;

		if(m_character != null)
		{
			m_gameStateData = m_character.CharacterGameState;
			if (m_gameStateData != null)
			{
				m_gameStateData.m_timeInState = 0;
			}
		}
	}
	
	public override void OnEnter()
	{
		base.OnEnter();
		//Debug.Log($"'{m_character?.Name}' - OnEnter - {State.ToString()}");
	}
	
	public override void OnUpdate()
	{
		if (m_gameStateData != null) m_gameStateData.m_timeInState += Time.deltaTime;
	}

	public override void OnExit()
	{
		base.OnExit();
		OnStateFinished?.Invoke(State, m_character);
		OnStateFinished = null;
	}

	public override bool ApplyState(string _state)
	{
		return MACharacterStateFactory.ApplyCharacterState(_state, m_character);
	}

	public override void SetupCharacterState()
	{
		if(m_character == null)
		{
			return;
		}

		m_character.ApplyAudioState();

		var rc = m_character.GetComponentInChildren<RagdollController>();
		if(rc != null)
		{
			if(responsiveStates.Contains(State))
			{
				rc.StartResponsiveState();
			}
			else if(draggedStates.Contains(State))
			{
				rc.StartDraggedState();
			}
			else if(ragdolledStates.Contains(State))
			{
				m_character.ActivateRagDoll();
			}
			else if(!noneStates.Contains(State))
			{
				rc.StartAnimatedState();
			}
		}
	}
}