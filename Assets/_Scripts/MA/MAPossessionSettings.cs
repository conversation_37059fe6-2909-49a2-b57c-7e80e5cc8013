using System;
using UnityEngine;

[CreateAssetMenu(fileName = "PossessionSettings", menuName = "Scriptable Objects/PossessionSettings")]
public class MAPossessionSettings : ScriptableObject
{
	public float m_cameraMovementYSmooth = 0.9f; //smooth camera position change from frame to frame
	public float m_cameraMovementXZSmooth = 0.5f;
	
	public bool m_useCharacterControls = true;
	public bool m_enableCameraCollisions = true;
	
	public float m_possessedCameraBlend = .2f;
	public float m_possessedCameraSmoothing = 1f;
	public float m_possessedCameraXSensitivity = -20f;
	public float m_possessedCameraYSensitivity =  10f;
	public float m_possessedCameraMaxFrameYTurn = 45f;
	public float m_possessedCameraYSmoothRate = 0.1f;//TS - found this in GameManager_possession but isn't being used at time of writingS
	public float m_possessedCharacterTurnRate = 120f;
	public bool m_possessionStrafingMode = true;
	[Header("Camera Tilt Contraints")]
	public float m_possessedCameraMaxTilt = 45f;
	public float m_possessedCameraMinTilt = -45f;
	public float m_possessedCameraInitialTiltFactor = 0.5f;
	[Header("Rotation Speed by Input")]
	public float m_possessedKeyCameraSpeedRotate = 1000;//500 default non-possess
	public float m_possessedNonStrafingCharacterRotate = 0.125f;//default non-character 0.1f
	[Header("Turret-Building Only")]
	public Vector3 m_possessionCameraAngleOffset = new Vector3(0f, 5f, 0f);
	public Vector4 m_possessedTurretPos = new Vector4(-4, 10, 12, -.5f);
	public Vector3 m_possessedTurretSpeed = new Vector3(-.5f, .5f, 0);
	
	[Header("Zoom")]
	[SerializeField]
	private float m_possessionCameraDistanceMin = 6;
	public float PossessionCameraDistanceMin => Mathf.Clamp(m_possessionCameraDistanceMin, 0.1f, m_possessionCameraDistanceMax);
	
	[SerializeField]
	private float m_possessionCameraDistanceMax = 10;
	public float PossessionCameraDistanceMax => Mathf.Clamp(m_possessionCameraDistanceMax, m_possessionCameraDistanceMin, Single.MaxValue);
	
	[SerializeField]
	[Range(0f, 1f)]
	private float m_initialCameraDistanceOnPossess = 0f;
	public float InitialCameraDistanceOnPossess => Mathf.Clamp(m_initialCameraDistanceOnPossess, 0.0f, 1f);
	
	[SerializeField]
	[Range(0f, 1f)] 
	[Tooltip("Magnitude/Speed of in-out zoom while possessed. e.g. with MouseWheel")]
	private float m_possessionCameraZoomScale = 0.5f;
	public float PossessionCameraZoomScale => Mathf.Clamp(m_possessionCameraZoomScale, 0.0f, 1f);
	
	[Header("Positioning")]
	[Tooltip("m_possessionCameraHeight only used if no head height or head bone transform is available ")]
	public float m_possessionCameraHeight = 3.0f;//used if no head height or head bone transform is available 
	public float m_possessionCameraRightOffset = 1f;
	
	[Header("Reset Follow Cam")]
	public bool m_snapCameraTo3rdPersonWhenNoInput = false;
	public float m_snapCameraTo3rdPersonWhenNoInputForSecs = 2f;
	public float m_snapCameraTo3rdPersonSlerpScale = 0.05f;
	
	
	[Header("Clamp Follow Cam")]
	public bool m_clampFreeFollowCamToAngle = false;
	public float m_possessedCameraMinMaxTurnRearExclusion = 15f;

	private void OnValidate()
	{
		//prevent gimbal lock
		m_possessionCameraDistanceMin = Mathf.Clamp(m_possessionCameraDistanceMin, 0.1f, m_possessionCameraDistanceMax);
		m_possessionCameraDistanceMax = Mathf.Clamp(m_possessionCameraDistanceMax, m_possessionCameraDistanceMin, Single.MaxValue);
		m_initialCameraDistanceOnPossess = Mathf.Clamp(m_initialCameraDistanceOnPossess, 0f, 1f);
		m_possessionCameraZoomScale = Mathf.Clamp(m_possessionCameraZoomScale, 0f, 1f);
	}
	
	public MAPossessionSettings() { }
}