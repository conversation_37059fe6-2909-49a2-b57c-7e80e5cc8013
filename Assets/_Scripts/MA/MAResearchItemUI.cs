using System;
using System.Collections;
using System.Collections.Generic;
using System.Net;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MAResearchItemUI : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, I<PERSON>nd<PERSON>rag<PERSON><PERSON><PERSON>, IPointerClickHandler
{
    public const string PrefabName = "_Prefabs/Research/MAResearchItemUIV0.4";
    public enum ItemState
    {
        None,
        Spawner,
        Create,
        Dragging,
        Blank,
        Selected
    }
    public Animator m_animator;
    public Color m_newLineColor = Color.white;
    public bool IsAssigned => (m_info == null || m_info.IsBlank == false);
    public MAResearchInfo m_info = null;
    public Transform m_inputLineTarget;
    public Transform m_outputLineTarget;
    public Transform m_inputLineEntry;
    public Transform m_outputLineExit;
    public Transform m_editButton;
    public Vector3 m_lockedScale = new Vector3(0.75f, 0.75f, 0.75f);
    public GameObject InputLineTarget => m_inputLineTarget == null ? gameObject : m_inputLineTarget.gameObject;
    public GameObject OutputLineTarget => m_outputLineTarget == null ? gameObject : m_outputLineTarget.gameObject;
    public GameObject InputLineEntry => m_inputLineEntry == null ? null : m_inputLineEntry.gameObject;
    public GameObject OutputLineExit => m_outputLineExit == null ? null : m_outputLineExit.gameObject;
    private Vector3 m_dragOffset;
    private ItemState m_state = ItemState.None;
    private ItemState m_oldState = ItemState.Blank;
    private MAResearchItemUI m_tempConnection;
    private MAUILine m_tempConnectionLine;
    MAResearchDragConnector m_DragConnector;

    //public List<MAUILine> m_lines = new List<MAUILine>();
    public MAResearchItemUISubItem m_acquiredSubItem;
    public MAResearchItemUISubItem m_lockedSubItem;
    public MAResearchItemUISubItem m_cannotAffordSubItem;
    public MAResearchItemUISubItem m_canAffordSubItem;
    public void Refresh()
    {
        Activate(m_info, m_state);
    }

    private void Start()
    {
        m_animator = GetComponent<Animator>();
    }

    void Update()
    {
        Activate();
    }
    void SetInactiveExcept(MAResearchItemUISubItem _subItem)
    {
        if (m_acquiredSubItem != _subItem)
            m_acquiredSubItem.Activate(m_info, false);
        if (m_lockedSubItem != _subItem)
            m_lockedSubItem.Activate(m_info, false);
        if (m_cannotAffordSubItem != _subItem)
            m_cannotAffordSubItem.Activate(m_info, false);
        if (m_canAffordSubItem != _subItem)
            m_canAffordSubItem.Activate(m_info, false);
        if(_subItem != null)
        {
            _subItem.Activate(m_info, true);
        }
    }
    public void Activate()
    {
        Activate(m_info, m_state);
    }
    void Activate(MAResearchInfo _info, ItemState _state)
    {
        m_info = _info;
        m_state = _state;
        
        if (m_animator)
            m_animator.SetBool("Highlight", m_info.m_highLight);
  
        //choose the correct sub item to display
        if (m_acquiredSubItem == null || m_state == ItemState.Spawner)
            return;
        
        if (m_info == null)
        {
            SetInactiveExcept(m_canAffordSubItem);
            name = "Blank";
        }
        else
        {
            name = m_info.m_name;
            m_info.m_position = transform.localPosition.ToString();
            m_info.m_scale = transform.localScale.ToString();
            m_info.m_changed = true;
            
            if (m_info.IsAcquired)
            {
                SetInactiveExcept(m_acquiredSubItem);
            }
            else if (m_info.IsUnlocked() == false)
            {
                SetInactiveExcept(m_lockedSubItem);
            }
            else if (m_info.CanPlayerAfford() == false)
            {
                SetInactiveExcept(m_cannotAffordSubItem);
            }
            else
            {
                SetInactiveExcept(m_canAffordSubItem);
            }
  
        }
        SetEditButton();
    }
    void SetEditButton()
    {
        if (m_editButton == null) return;
        #if UNITY_EDITOR
        m_editButton.gameObject.SetActive(true);
        #else
        m_editButton.gameObject.SetActive(false);
        #endif
    }
    // public void OnPointerUp(PointerEventData eventData)
    // {
    //     if (eventData.button == PointerEventData.InputButton.Left) // Check for left mouse button
    //     {
    //         if(m_DragConnector)
    //             m_DragConnector.ReleasedOver(this);
    //         m_DragConnector = null;
    //     }
    // }
    public void AddConnection(MAResearchItemUI _item)
    {
        if (m_info != null && _item.m_info != null)
        {
            if (m_info.IsBlank == false && _item.m_info.IsBlank == false)
            {
                m_info.m_linkToList.Add(_item.m_info);
                m_info.m_changed = true;
            }
        }
    }
    
    public void DestroyMe()
    {
        MAResearchManagerUI.Me.RemoveItem(this);
        if(m_tempConnectionLine)
            m_tempConnectionLine.DestroyMe();
            
        Destroy(gameObject);
    }
    
    public MAResearchItemUI AssignResearchItem(MAResearchInfo _info)
    {
        Activate(_info, m_state);
        if (m_tempConnection)
        {
            m_tempConnection.AddConnection(this);
            m_tempConnectionLine?.DestroyMe();
            m_tempConnection = null;
            m_tempConnectionLine = null;
        }

        if (_info != null)
        {
            m_info.m_changed = true;
            MAResearchManagerUI.Me.Refresh();
        }
        return this;
    }
    
    public Vector3? GetLocalPoint(PointerEventData eventData)
    {
        if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                MAResearchManagerUI.Me.m_researchItemHolder, 
                eventData.position, 
                eventData.pressEventCamera, 
                out var localClickPosition))
        {
            return localClickPosition;
        }
        return null;
    }

    void DisplayResearchItemInfo()
    {
    //    if(m_info.m_isLocked)
    //        return;
            
        MAResearchItemInfo.Create(this);
    }

    public void EnableDrag(bool _enable)
    {
        var ds = GetComponent<DragSimple>();
        if(ds) ds.enabled = _enable;
    }
    public void CreateDragConnector(bool _clickToAttach)
    {
        m_DragConnector = MAResearchDragConnector.Create(transform.parent, this, _clickToAttach);
    }
    public void OnPointerClick(PointerEventData eventData)
    {
        if(m_state == ItemState.Dragging) return;
        if (MAResearchManagerUI.Me.m_editMode == false)
        {
            DisplayResearchItemInfo();
            return;            
        }
        // if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
        //     MAResearchManagerUI.Me.m_researchItemHolder, 
        //     eventData.position, 
        //     eventData.pressEventCamera, 
        //     out var localClickPosition))
        // {
        //     if(m_info == null || m_info.IsBlank)
        //         MAResearchWindowList.Create(MAResearchManagerUI.Me.m_faction,this, transform.position);
        //     else
        //         CreateItemWindow(localClickPosition);
        // }
    }

    public void CreateItemWindow(Vector3 _localClickPosition)
    {
#if UNITY_EDITOR
        MAResearchItemWindow.Create(this, _localClickPosition);
#endif
    }
    public void OnBeginDrag(PointerEventData eventData)
    {
        if (MAResearchManagerUI.Me.m_editMode == false || !MAResearchManagerUI.Me.NodeDragMode) return;
        switch(m_state)
        {
            case ItemState.Spawner:
                OnBeginDragCopyFromSpawner(eventData, false);
                break;
            default:
                if (MAResearchManagerUI.Me.m_dragConnects.isOn)
                {
                    CreateDragConnector(false);
                    break;
                }
                else if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift) || MAResearchManagerUI.Me.m_dragCreates.isOn)
                {
                    OnBeginDragCopy(eventData, true);
                    break;
                }
                
                OnBeginDragMove(eventData);
                break;
        }
        m_oldState = m_state;
        m_state = ItemState.Dragging;
    }
    public void OnBeginDragCopy(PointerEventData eventData, bool _drawConnectingLine)
    {
        GameObject copiedItem = Instantiate(gameObject);
        MAResearchItemUI copiedScript = copiedItem.GetComponent<MAResearchItemUI>();
        MAResearchManagerUI.Me.CorrectLine(this, copiedScript);
        copiedScript.m_info = m_info;
        m_info = null;
        name = "Blank";
        MAResearchManagerUI.Me.AddItem(copiedScript);
        copiedItem.transform.SetParent(transform.parent, false);
        transform.SetParent(MAResearchManagerUI.Me.m_researchItemHolder, false);
        transform.SetAsLastSibling();
        AssignResearchItem(null);
        m_state = ItemState.Blank;

        MAResearchManagerUI.Me.Refresh();
        if (_drawConnectingLine)
        {
            if (m_tempConnectionLine)
                m_tempConnectionLine.DestroyMe();
            m_tempConnection = copiedScript;
            m_tempConnectionLine = MAUILine.Create(gameObject, null, copiedScript.gameObject, null, MAResearchManagerUI.Me.m_lineHolder, 1f, m_newLineColor, true);
            
        }

        m_state = ItemState.Dragging;
        
        Vector3? localMousePos = copiedScript.GetLocalPoint(eventData);
        if (localMousePos != null)
        {
            copiedScript.m_dragOffset = localMousePos.Value - copiedItem.transform.localPosition;
        }
        else
        {
            copiedScript.m_dragOffset = Vector3.zero;
        }

        // 6. Set the dragging item reference in the ResearchManager to the copy
        MAResearchManagerUI.Me.m_draggingItem = this; 

        // 7. Bring the copy to the front
        copiedItem.transform.SetAsLastSibling();
    }
    
     public void OnBeginDragCopyFromSpawner(PointerEventData eventData, bool _drawConnectingLine)
    {
        // 1. Create a copy of the GameObject
        GameObject copiedItem = Instantiate(gameObject);
        MAResearchItemUI copiedScript = copiedItem.GetComponent<MAResearchItemUI>();
     //   copiedScript.m_info = m_info;
        m_info = null;
        MAResearchManagerUI.Me.AddItem(this);

        copiedItem.transform.SetParent(transform.parent, false);
        transform.SetParent(MAResearchManagerUI.Me.m_researchItemHolder, false);
        transform.SetAsLastSibling();
        AssignResearchItem(null);
        m_state = ItemState.Blank;

        // 2. Get the MAResearchItemUI component from the copy
        if (_drawConnectingLine)
        {
            if (m_tempConnectionLine)
                m_tempConnectionLine.DestroyMe();
            m_tempConnection = copiedScript;
            m_tempConnectionLine = MAUILine.Create(copiedScript.gameObject, null, gameObject, null, MAResearchManagerUI.Me.m_lineHolder, 1f, m_newLineColor, true);
            
        }

        m_state = ItemState.Dragging;
        
        Vector3? localMousePos = GetLocalPoint(eventData);
        if (localMousePos != null)
        {
            copiedScript.m_dragOffset = localMousePos.Value - copiedItem.transform.localPosition;
        }
        else
        {
            copiedScript.m_dragOffset = Vector3.zero;
        }

        // 6. Set the dragging item reference in the ResearchManager to the copy
        MAResearchManagerUI.Me.m_draggingItem = this; 

        // 7. Bring the copy to the front
        copiedItem.transform.SetAsLastSibling();
    }

    public void OnBeginDragMove(PointerEventData eventData)
    {
    
        MAResearchManagerUI.Me.m_draggingItem = this;
        
        var localMousePos = GetLocalPoint(eventData);
        
        if(localMousePos != null)
        {
            m_dragOffset = localMousePos.Value - transform.localPosition;
        }
        else
        {
            m_dragOffset = Vector3.zero;
        }
        
        // Bring the element to the front
        transform.SetAsLastSibling();
    }

    public void OnDrag(PointerEventData eventData)
    {
        if(MAResearchManagerUI.Me.m_dragConnects.isOn == true || MAResearchManagerUI.Me.m_editMode == false || !MAResearchManagerUI.Me.NodeDragMode) return;
        var localMousePos = GetLocalPoint(eventData);
        if(localMousePos != null)
        {
            transform.localPosition = localMousePos.Value - m_dragOffset;
        }
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        if(MAResearchManagerUI.Me.m_editMode == false || !MAResearchManagerUI.Me.NodeDragMode) return;

        MAResearchManagerUI.Me.m_draggingItem = null;
        if (m_oldState == ItemState.Dragging)
            m_state = ItemState.Blank;
        else
            m_state = m_oldState;
        if(m_info == null || m_info.IsBlank)
        {
#if UNITY_EDITOR
            MAResearchWindowList.Create(MAResearchManagerUI.m_faction,this, transform.position);
#endif
        }
        else
        {
            m_info.m_position = transform.localPosition.ToString();
            m_info.m_scale = transform.localScale.ToString();
        }

    }
    public static MAResearchItemUI Create(Transform _holder, Vector3 _pos, Vector3 _scale, ItemState _state, MAResearchInfo _info)
    {
        var name = PrefabName;
        if(_info != null)
            name += _info.m_faction.ToString();
        var prefab = Resources.Load<MAResearchItemUI>(name);
        var instance = Instantiate(prefab, _holder);
        instance.transform.localPosition = _pos;
        instance.transform.localScale = _scale;
        instance.Activate(_info, _state);
        return instance;
    }
}
