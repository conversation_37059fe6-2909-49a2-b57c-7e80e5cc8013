using System;
using UnityEngine;

public abstract class BaseUIController : MonoBehaviour {

	private const string open_keyword = "is_open";

	private Animator _animator;
	protected bool isClosing = false;

	[SerializeField]
	private GameObject closeBackground;
    protected int m_sound;

	public event Action onClose;
	public event Action onShow;
	public event Action onClickedBackground;

	protected virtual void Awake() {
		_animator = GetComponent<Animator>();

		IUIExtensionComponent[] uiExtensionComponents = GetComponentsInChildren<IUIExtensionComponent>(true);

		foreach(IUIExtensionComponent extenComponent in uiExtensionComponents) {
			extenComponent.Setup(this);
		}
	}

	protected virtual void Start() {

	}

	//it would be better to register update per class instead of doing this in the base class, but that would require execution manager (updater) written
	protected virtual void Update() {
	}

	public virtual void Refresh() {
	}

	//Hide is not used in the game yet, because we mainly use Close to destroy unused popup
	//if we wanted to optimize it, we should use Hide popup instead and 'pool it as such', but that functionality is not in yet, cuz we don't use it.
	//make this function virtual and sort out the problems before using it in a 'pooled' manner
	public void Hide() {
		gameObject.SetActive(false);
	}

	public virtual void Show() {
		onShow?.Invoke();

		gameObject.SetActive(true);

		if(_animator != null) {
			_animator.SetBool(open_keyword, true);
		}
	}

	public void Close(bool _playCloseSound = true) {
		if(!isClosing) {
			Close_Internal(_playCloseSound);
		}
	}

	public void ClosedFromBackground() {
		if(!isClosing) {
			onClickedBackground?.Invoke();
		}
	}

	protected virtual void Close_Internal(bool _playCloseSound = true) {
		onClose?.Invoke();

		if(_animator == null) {
			InfoPlaqueManager.Me.CloseUI(this);
		} else {
			isClosing = true;
			ActionStateMachineBehaviour actionStateBehaviour = _animator.GetBehaviour<ActionStateMachineBehaviour>();
			if (actionStateBehaviour == null) {
				InfoPlaqueManager.Me.CloseUI(this);
			} else {
				actionStateBehaviour.onStateLoopEnded += OnStateLoopEnded;
			}
			_animator.SetBool(open_keyword, false);
		}
		if (_playCloseSound)
			m_sound = AudioClipManager.Me.PlayUISound("PlaySound_PauseMenu_CLOSE");
	}

	private void OnStateLoopEnded() {
		ActionStateMachineBehaviour actionStateBehaviour = _animator.GetBehaviour<ActionStateMachineBehaviour>();
		actionStateBehaviour.onStateLoopEnded -= OnStateLoopEnded;

		InfoPlaqueManager.Me.CloseUI(this);
	}

	public virtual void OnCloseComplete() { }

	protected virtual void OnDestroy() {
		onShow = null;
		onClose = null;
	}

	public void ToggleCloseBackground(bool enable) {
		if (closeBackground != null)
		{
			closeBackground.SetActive(enable);
		}
	}
}