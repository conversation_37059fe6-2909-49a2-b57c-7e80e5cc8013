using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ContextMenuButton : MAGUIBase {

	[SerializeField] TextMeshProUGUI m_buttonText;
	[SerializeField] RectTransform m_arrow;
    [SerializeField] Sprite m_iconUpgrade;
    [SerializeField] Sprite m_iconFinish;
    [SerializeField] Sprite m_iconManage;
	[SerializeField] Image m_icon;
	[SerializeField] private Image m_highlight;
	public enum ButtonDisplayType {
		MANAGE,
		UPGRADE,
		FINISH,
		DEFAULT
	}
	public Button m_button;
	public Action OnClickCallback { get { return m_onClickCallback; } set { m_onClickCallback = value; } }
	public Func<bool> InteractableCallback;
	private ContextMenuData.ButtonType m_buttonType;
	public ContextMenuData.ButtonType ButtonType { get { return m_buttonType; } }
	Action m_onClickCallback = null;
	private float m_ignoreClickUntil;
	private string m_audioHook;
	
	void Awake()
	{
		m_ignoreClickUntil = Time.unscaledTime + .2f;
	}

	void Start()
	{
		if(m_buttonText)
			Activate($"Button{m_buttonText.text}");
		else
			Activate($"Button");
	}
	
	//---------------------------------------------------------------------------------------------------------
	public void OnClick()
	{
		if (GameManager.Me.IsAnyTransitionInProgress) return; // if we're transitioning into anything then ignore button presses
		if (Time.unscaledTime < m_ignoreClickUntil) return;
		
		InfoPlaqueManager.Me.InvokeOnInfoPlaqueButtonClicked();
		ContextMenuManager.Me.RemoveCurrentMenu();
		if(NGBusinessDecisionManager.Me != null)
			NGBusinessDecisionManager.Me.AnOkayClicked(m_button);

        if (m_onClickCallback != null)
        {
	        if (m_audioHook != null)
				AudioClipManager.Me.PlaySound($"PlaySound_BuildingContextMenu_{m_audioHook}", GameManager.Me.gameObject);
            OnMouseDown();
            m_onClickCallback();
        }
    }

	override public void Highlight(bool _active)
	{
		m_highlight.gameObject.SetActive(_active);
	}


    //---------------------------------------------------------------------------------------------------------
    public void DisableButton()
	{
		gameObject.SetActive(false);
	}

	//---------------------------------------------------------------------------------------------------------
	public void SetButtonInteractable(bool _interactable)
	{
		if(InteractableCallback == null)
			m_button.interactable = _interactable;
	}

	public void SetButtonInteractableCondition(Func<bool> _condition)
	{
		InteractableCallback = _condition;
		Update();
	}

    private void Update()
	{
		if(InteractableCallback != null)
			m_button.interactable = InteractableCallback();
	}

	//---------------------------------------------------------------------------------------------------------
	public void SetText (string _text)
	{
		m_buttonText.text = _text;
		if(_text == Localizer.Get(TERM.GUI_INFO)){
			SetDisplay(ButtonDisplayType.MANAGE);
        }
        else if (_text == Localizer.Get(TERM.GUI_UPGRADE))
        {
            SetDisplay(ButtonDisplayType.UPGRADE);
        }
        else if (_text.Contains("Finish Build"))
        {
            SetDisplay(ButtonDisplayType.FINISH);
        }
        else {
			SetDisplay(ButtonDisplayType.DEFAULT);
		}
	}
	//---------------------------------------------------------------------------------------------------------
	public void SetButtonType(ContextMenuData.ButtonType _buttonType)
	{
		m_buttonType = _buttonType;
	}
	
	//---------------------------------------------------------------------------------------------------------
	public void SetAudioHook(string _hook)
	{
		m_audioHook = _hook;
	}

	//---------------------------------------------------------------------------------------------------------
	public void SetPosition(Vector2 _position, ContextMenuData.EDirection _direction)
	{
		RectTransform rectTransform = GetComponent<RectTransform>();
		rectTransform.anchoredPosition = _position;

		SetArrowPosition(_direction);
	}

	//---------------------------------------------------------------------------------------------------------
	void SetArrowPosition(ContextMenuData.EDirection _direction)
	{
		switch (_direction)
		{
			default:
			case ContextMenuData.EDirection.Down:
				m_arrow.anchorMin = new Vector2(0.5f, 0f);
				m_arrow.anchorMax = new Vector2(0.5f, 0f);
				m_arrow.anchoredPosition = new Vector2(0f, 5f);
				m_arrow.eulerAngles = Vector3.forward * 180f;
				break;
			case ContextMenuData.EDirection.Up:
				m_arrow.anchorMin = new Vector2(0.5f, 1f);
				m_arrow.anchorMax = new Vector2(0.5f, 1f);
				m_arrow.anchoredPosition = new Vector2(0f, -5f);
				m_arrow.eulerAngles = Vector3.zero;
				break;
			case ContextMenuData.EDirection.Left:
				m_arrow.anchorMin = new Vector2(0f, 0.5f);
				m_arrow.anchorMax = new Vector2(0f, 0.5f);
				m_arrow.anchoredPosition = new Vector2(5f, 0f);
				m_arrow.eulerAngles = Vector3.forward * 90f;
				break;
			case ContextMenuData.EDirection.Right:
				m_arrow.anchorMin = new Vector2(1f, 0.5f);
				m_arrow.anchorMax = new Vector2(1f, 0.5f);
				m_arrow.anchoredPosition = new Vector2(-5f, 0f);
				m_arrow.eulerAngles = Vector3.forward * -90f;
				break;
			case ContextMenuData.EDirection.LeftUp:
				m_arrow.anchorMin = new Vector2(0.2f, 0.95f);
				m_arrow.anchorMax = new Vector2(0.2f, 0.95f);
				m_arrow.anchoredPosition = new Vector2(0f, -5f);
				m_arrow.eulerAngles = Vector3.forward * 25f;
				break;
			case ContextMenuData.EDirection.RightUp:
				m_arrow.anchorMin = new Vector2(0.8f, 0.95f);
				m_arrow.anchorMax = new Vector2(0.8f, 0.95f);
				m_arrow.anchoredPosition = new Vector2(0f, -5f);
				m_arrow.eulerAngles = Vector3.forward * -25f;
				break;
		}
	}
	//---------------------------------------------------------------------------------------------------------
	private void SetDisplay(ButtonDisplayType _icon){
		if(m_icon == null)
			return;
		switch(_icon){
			case ButtonDisplayType.MANAGE:
				m_icon.sprite = m_iconManage;
				m_icon.gameObject.SetActive(true);
				break;
            case ButtonDisplayType.UPGRADE:
                m_icon.sprite = m_iconUpgrade;
                m_icon.gameObject.SetActive(true);
                break;
            case ButtonDisplayType.FINISH:
                m_icon.sprite = m_iconFinish;
                m_icon.gameObject.SetActive(true);
                break;
            default:
				m_icon.gameObject.SetActive(false);
				break;
		}
	}

	//---------------------------------------------------------------------------------------------------------
}
