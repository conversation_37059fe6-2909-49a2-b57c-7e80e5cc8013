using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PickupSetup
{
	public Transform m_holder = null;
	public float m_quantity = 0;
	public bool m_killPickup = false;
	//public NGCarriableResource m_resource;
	//public int m_productIndex = -1;
	public System.Action<GameObject> m_onComplete = null;
	/*public GameState_Product ProductData
	{
		get => m_productIndex == -1 ? null : GameManager.Me.m_state.m_products[m_productIndex];
		set => m_productIndex = value == null ? -1 : GameManager.Me.m_state.m_products.FindIndex(x => x.m_uniqueID == value.m_uniqueID);
	}*/
}

public class ReactPickup : NGMovingObject
{
	protected float m_quantity;
	public NGCommanderBase m_intendedDestination;
	protected bool m_isReady = false;
	public bool IsReady => m_isReady;
	
	public ReactPickup m_nextInChain = null;
	
	private static Dictionary<string, Bounds> s_bounds = new();

	public virtual bool IsBeingDragged { get { return false; } set { } }
	
	public Vector3 BottomOffset { get { CalculateOffset(); return m_bottomOffset; } }
	public Vector3 TopOffset { get { CalculateOffset(); return m_topOffset; } }
	
	
	private void CalculateOffset()
	{
		if(m_calculatedOffset) return;
		
		if(m_calculatedOffset == false)
		{
			var bounds = ManagedBlock.GetTotalVisualBounds(gameObject);
			float variation = 0.05f;
			var x = Random.Range(-variation, variation);
			var z = Random.Range(-variation, variation);
			float gapRemoval = 0.1f;
			m_bottomOffset = new Vector3(x, bounds.extents.y - bounds.center.y, z);
			m_topOffset = new Vector3(x, bounds.center.y + bounds.extents.y - gapRemoval, z);
			m_calculatedOffset = true;
		}
	}
	
	private Vector3 m_topOffset;
	private Vector3 m_bottomOffset;
	private bool m_calculatedOffset = false;
	
	public virtual bool GetLinkedOrder(out MAOrder _order)
	{
		_order = MAOrder.EmptyOrder;
		return false;
	}
		
	public override ReactPickup Carrying
	{
		get { return this; }
	}

	public float Quantity
	{
		get { return m_quantity; }
		set { m_quantity = value; }
	}

	public NGCarriableResource m_contents;

	public NGCarriableResource Contents
	{
		get { return m_contents; }
		set { m_contents = value; }
	}

	public float m_maxDistFromSpawnFactory = 0;
	public float m_timeSinceCreation = 0;
	public NGMovingObject m_holder;
	public string TitleText => Contents.m_title;
	public NGStock ContentsAsStock => new NGStock(Contents, (int) m_quantity);

override public string WhatAmIText { get { return TitleText; } }
	public string TypeString => m_contents.ToString();

	public	MeshRenderer	m_productPanel;
	public NGCommanderBase m_spawnedFrom;

	public System.Action<ReactPickup, Collider, bool> m_onCollisionEnter = null;
	
	protected COLLISIONSTYLE? m_overrideCollisionstyleOnPickup = null;
	public NGCommanderBase m_hasCollidedWith = null;
	override public void HasCollidedWith(NGCommanderBase _what)
	{
		m_hasCollidedWith = _what;
	}
    protected override void Update()
    {
		if (m_spawnedFrom != null && IsBeingDragged) { // GL - 160621 - if this pickup came from a building then track the max distance from the building for return checks
			var hitPoint = InputUtilities.GetCursorToTerrainPos(0); // TODO - should be actual touch id
			if(hitPoint.sqrMagnitude < 1e22f)
				m_maxDistFromSpawnFactory = Mathf.Max(m_maxDistFromSpawnFactory, (m_spawnedFrom.DoorPosInner - hitPoint).xzMagnitude());
		}
		m_timeSinceCreation += Time.deltaTime;
		base.Update();
	}

    public virtual void ShowUpgradeCelebration(Transform _holder) { }

	public MAOrder GetOrder()
	{
		NGReactPickupAny reactPickupAny = this as NGReactPickupAny;
		if (reactPickupAny != null && reactPickupAny.IsProduct && reactPickupAny.m_product.GetLinkedOrder(out MAOrder order))
		{
			return order;
		}
		return null;
	}
	    
	virtual public bool ShouldBeCleanedUp()
	{
		return IsDormant;
	}
	public bool IsDormant
	{
		get
		{
			var rb = GetComponentInChildren<Rigidbody>();
			if (rb == null)
				return false; //This is probably the fake pickup created above a factory to show it has produced a product
			if (m_collisionStyle == COLLISIONSTYLE.DEFAULT && rb.IsSleeping())
				return true;
			return false;
		}
	}

	public override bool SetupHeldByPlayer(NGCommanderBase _originator)
	{
		m_originator = _originator;
		SetCollisionStyle(COLLISIONSTYLE.KINEMATIC);
		return true;
	}

	public void SetOverrideCollisionStyleOnPickup(COLLISIONSTYLE _expectedCollisionStyle)
	{
		m_overrideCollisionstyleOnPickup = _expectedCollisionStyle;
	}

	override public void DestroyMe()
	{
		NGMovingObject holder = m_holder;
		Drop(GlobalData.Me.m_pickupsHolder);
		base.DestroyMe();
	}

	public virtual void AssignToCarrier(NGMovingObject _carrier, bool _setCarriedObj = true, bool _savePickup = false)
	{
		if(_carrier.Carrying != null)
			_carrier.DestroyCarriedObject();
		
		if(m_overrideCollisionstyleOnPickup == null)
		{
			SetCollisionStyle(COLLISIONSTYLE.NOPHYSICS);
		}
		else
		{
			SetCollisionStyle((COLLISIONSTYLE)m_overrideCollisionstyleOnPickup);
			m_overrideCollisionstyleOnPickup = null;
		}
		
		m_holder = _carrier;
		if (_setCarriedObj)
			_carrier.SetCarriedObject(this);
		var holding = _carrier.Visuals.HoldingTransform;
		if (holding != null)
			SetUpWithHolder(holding);
	}

	public void SetUpWithHolder(Transform _holdingTransform)
	{
		transform.SetParent(_holdingTransform, false);
		transform.localPosition = Vector3.zero;
		transform.localEulerAngles = Vector3.zero;
	}

	public void Thrown(Vector3 _velocity, Transform targetFactory, COLLISIONSTYLE _collisionstyle = COLLISIONSTYLE.TRIGGER )
	{
		transform.SetParent(GlobalData.Me.m_pickupsHolder);
		m_holder.SetCarriedObject(null);
		m_holder.GetComponentInChildren<Animator>()?.SetBool(GlobalData.CarryProductAnim, false);
		m_holder.m_cleaningPickup = null;
		m_holder = null;
		
		if (gameObject.GetComponent<Pickup>() == null)
			gameObject.AddComponent<Pickup>();

		var rb = gameObject.GetComponentInChildren<Rigidbody>();
		if (rb == null)
			rb = gameObject.AddComponent<Rigidbody>();
		rb.linearVelocity = _velocity;
		SetCollisionStyle(_collisionstyle);
		
		OnDrop(); // GL - 111019 - switch shadows back on
	}

	virtual public bool DroppedByPlayerAt(NGCommanderBase _hitCommander)
	{
		OnDrop();
		if (_hitCommander != null)
			SetCollisionStyle(COLLISIONSTYLE.TRIGGER);
		return true;
	}
	protected virtual Transform GetHoldingHandTransform(NGMovingObject _object) {
		return _object.Visuals.HoldingTransform;
	}

	public void AdjustQuantities(NGCommanderBase _reciever)
	{
		/*int toAdd;
		if (_reciever == null || _reciever == m_spawnedFrom)
			toAdd = (int)m_spawnedFrom.NumOutputs;
		else
		{
			int numShort = 0;//(int)(_reciever.MaxInputs - _reciever.InputsAre.GetStock(m_contents) - Quantity);
			if (numShort == 0)
				return;
			int stock = (int)m_spawnedFrom.NumOutputs;
			int space = (int)m_spawnedFrom.MaxOutputs - stock;
			toAdd = Mathf.Clamp(numShort, -space, stock);
		}
		if (toAdd == 0)
			return;
		Quantity += toAdd;
		m_spawnedFrom.AddProducts(-toAdd);
		RecalculateScale();*/
	}

	public void RecalculateScale()
	{
		transform.localScale = Vector3.one * ScaleForQuantity(Quantity);
	}

	public float ScaleForQuantity(float _quantity)
	{
		float minScale = 1f;
		float maxScale = 4f;
		if(_quantity <= 0) return minScale;
		return Mathf.Lerp(minScale, maxScale, _quantity / 1000f);
	}

	public void SetQuantity(int _quantity)
	{
		Quantity = _quantity;
		if (_quantity <= 0)
		{
			gameObject.SetActive(false);
			Destroy(gameObject);
			return;
		}
		RecalculateScale();
	}

	// Carried object is about to be destroyed.
	public virtual void Consume()
	{

	}

	public virtual void Drop(Transform _newParent)
	{
		if(_newParent != null) transform.SetParent(_newParent);
		if(m_holder != null)
		{
			m_holder.StopCleaningUp();
			m_holder.SetCarriedObject(null);
			m_holder.GetComponentInChildren<Animator>()?.SetBool(GlobalData.CarryProductAnim, false);
			m_holder = null;
		}

		if (gameObject.GetComponent<Pickup>() == null)
			gameObject.AddComponent<Pickup>();

		if (gameObject.GetComponentInChildren<Rigidbody>() == null)
			gameObject.AddComponent<Rigidbody>();
		
		SetCollisionStyle(COLLISIONSTYLE.DEFAULT);
	}

	public virtual void Interrupted(NGMovingObject _object)
	{

	}

    override protected void OnDestroy()
    {
	    m_onCollisionEnter = null;
		base.OnDestroy();
    }

	virtual protected void OnTriggerEnter(Collider collider)
	{
		InternalCollisionEnter(collider.gameObject);
		
		m_onCollisionEnter?.Invoke(this, collider, true);
	}

	protected void OnCollisionEnter(Collision collision)
	{
		InternalCollisionEnter(collision.gameObject);
		
		m_onCollisionEnter?.Invoke(this, collision.collider, false);
	}
	
	virtual protected void InternalCollisionEnter(GameObject collision)
	{
		if (m_collisionStyle == COLLISIONSTYLE.NOPHYSICS) return; // being carried, ignore
		var cBase = collision.gameObject.GetComponentInParent<NGCommanderBase>();
		
		if (m_intendedDestination != null && cBase != m_intendedDestination)
			return;
		
		Rigidbody rb = GetComponent<Rigidbody>();
		Collider col = GetComponent<Collider>();
		 
		if(rb != null && col != null)
		{
            float h = GameManager.Me.HeightAtPoint(rb.position);
			float delta = h - rb.position.y;
			if(delta >= 0)
			{
				Debug.LogWarning("Collision under ground!!!");
				transform.position -= rb.linearVelocity * 0.0167f;
                rb.linearVelocity *= 0.5f;
			}
		}
		
		MABuilding building = cBase as MABuilding;
		if(building != null)
		{
			building.AcceptThrownDrop(this);
		}
	}
	protected void OnTriggerExit(Collider collision)
	{
		InternalCollisionExit(collision.gameObject);
	}
    protected void OnCollisionExit(Collision collision)
	{
		InternalCollisionExit(collision.gameObject);
	}
	protected void InternalCollisionExit(GameObject collision)
	{
	}
	public virtual void Initialise(NGCommanderBase _spawnedFrom, NGCarriableResource _contents, float _quantity)
	{
		m_isReady = true;
		m_spawnedFrom = _spawnedFrom;
		m_quantity = _quantity;
		m_contents = _contents;
		Name = _contents.Name;
	}

	public void StartRemoveShadows()
	{
		GameManager.Me.StartCoroutine(Co_RemoveShadows(gameObject));
	}

	public static IEnumerator Co_RemoveShadows(GameObject go)
	{
		yield return null;
		if (go != null)
		{
			foreach (MeshRenderer renderer in go.GetComponentsInChildren<MeshRenderer>(true))
				renderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
		}
	}

	public void AddQuantity(float _quantity)
	{
		m_quantity += _quantity;
	}

	public float GetQuantity()
	{
		return m_quantity;
	}

	// Overriding so pickups aren't added to the culling system.
	protected override void OnEnable() 
	{
		var rigidbody = GetComponent<Rigidbody>();
		if (rigidbody != null)
			rigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
	}
	protected override void OnDisable() {}
	override public bool IsOkayToCollide(NGCommanderBase _object) { return true; }

	override public float GetReturnToBuildingPower() 
	{ 
		if (NGManager.Me.m_useTimeForReturn)
			return Mathf.Lerp(Mathf.Clamp01(m_timeSinceCreation - NGManager.Me.m_returnTimeThreshhold), 1, (m_timeSinceCreation > NGManager.Me.m_returnTimeThreshholdShort) ? 0.0001f : 0f) * NGManager.Me.m_returnMaxPower;
		if (m_spawnedFrom == null)
			return base.GetReturnToBuildingPower();
		return Mathf.Clamp01((m_maxDistFromSpawnFactory - NGManager.Me.m_returnDistThreshhold) * NGManager.Me.m_returnDistScaling);
	}

	protected bool m_isPrimary = true;
	protected bool m_isChaining = false;
	public void SetupChain(int _index = 0)
	{
		if (m_nextInChain == null) return;
		if (_index == 0)
			StartCoroutine(Co_RunChain());
		m_nextInChain.m_intendedDestination = m_intendedDestination;
		m_nextInChain.SetupChain(++_index);
	}
	public void EndChain() => m_isChaining = false;
	private IEnumerator Co_RunChain()
	{
		m_isChaining = true;
		var chain = new List<ReactPickup>();
		var entry = this;
		while (entry != null)
		{
			entry.m_isPrimary = false;
			chain.Add(entry);
			entry = entry.m_nextInChain;
		}
		m_isPrimary = true;
		while (m_isChaining)
		{
			const float c_chainLinkLength = .4f;
			for (int i = chain.Count - 1; i > 0; --i)
				chain[i].transform.position = Vector3.Lerp(chain[i].transform.position, chain[i - 1].transform.position + Vector3.up * -c_chainLinkLength, .5f);
			for (int i = 1; i < chain.Count; ++i)
			{
				var toParent = chain[i - 1].transform.position - chain[i].transform.position;
				if (toParent.sqrMagnitude > c_chainLinkLength * c_chainLinkLength)
				{
					toParent = toParent.normalized * c_chainLinkLength;
					chain[i].transform.position = chain[i - 1].transform.position - toParent;
				}
				chain[i].transform.up = toParent;
			}
			yield return null;
		}
	}
}
