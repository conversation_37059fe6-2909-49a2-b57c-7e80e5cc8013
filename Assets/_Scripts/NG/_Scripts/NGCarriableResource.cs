using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class NGCarriableResource
{
   public static List<NGCarriableResource> s_resources = new List<NGCarriableResource>();
   public static List<NGCarriableResource> GetList => s_resources;
   public static Dictionary<string, NGCarriableResource> s_carriableResources = new ();
   public string DebugDisplayName => m_name;
   
   public const string c_timber = "Timber";
   
   public const string c_none = "None";
   public const string c_product = "Product";
   public const string c_metal = "Metal";
   public const string c_flour = "Flour";
   public const string c_fabric = "Fabric";
   private const string c_pathToSprite = "_Art/Sprites/CarriableResources/";
   private const string c_pathToPrefab = "_Prefabs/RawMaterials/";
   
   public override string ToString() { return Name; }
   public bool m_debugChanged;
   public string id;
   public string m_name;
   public string m_sprite;
   public string m_prefab;
   public string m_title;
   public string m_description;
   public string m_spriteColour;
   public string m_bezierColour;
   public Color m_color;
   public string m_textSprite;
   public string Name => m_name;
   public string TextSprite => MAMessageManager.GetTMPString(m_textSprite);
   public string SpriteIndex => MAMessageManager.GetTMPString(m_textSprite, "", true);
   public bool IsProduct => m_name != null && m_name.StartsWith(c_product);
   public bool IsAnyProduct => m_name != null && m_name.Equals(c_product);
   
   public bool IsFavour => m_name != null && (m_name.Contains("Favour") || m_name.Contains("Favor"));
   public bool IsWood => m_name != null && (m_name.Equals("Wood") || m_name.Equals("RefinedWood"));
   public bool IsTimber => m_name != null && m_name.Equals(c_timber);
   
   public bool IsOre => m_name != null && (m_name.Equals("Ore") || m_name.Equals("RefinedOre"));
   public bool IsMetal => m_name != null && m_name.Equals("Metal");
   
   public bool IsWheat => m_name != null && (m_name.Equals("Wheat") || m_name.Equals("RefinedWheat"));
   public bool IsFlour => m_name != null && m_name.Equals("Flour");
   
   public bool IsCotton => m_name != null && (m_name.Equals("Cotton") || m_name.Equals("RefinedCotton"));
   public bool IsFabric => m_name != null && m_name.Equals("Fabric");
   
   public bool IsNone => Name.Equals(c_none);
   public string m_uniqueId = "";
   public static bool operator== (NGCarriableResource _o1, NGCarriableResource _o2) { return (_o1?.Name) == (_o2?.Name); }
   public static bool operator!= (NGCarriableResource _o1, NGCarriableResource _o2) { return (_o1?.Name) != (_o2?.Name); }
   public bool IsTopLevelResource = false; 
   
    public static Dictionary<string, string> s_resourceAudioSwitch = new Dictionary<string, string>()
    {
        { "Cotton", "Cotton" },
        { "Fabric", "Fabric" },
        { "Flour", "Flour" },
        { "Metal", "Metal" },
        { "Ore", "Ore" },
        { "Product", "Product" },
        { "RawResourceClay", "RawResourceClay" },
        { "RefinedCotton", "RefinedCotton" },
        { "RefinedOre", "RefinedOre" },
        { "RefinedWheat", "RefinedWheat" },
        { "RefinedWood", "RefinedWood" },
        { "Silica", "Silica" },
        { "Timber", "Timber" },
        { "Wheat", "Wheat" }
    };

    public bool IsFromCompletedOrder()
   {
      var product = GetProduct();
      if(product == null) return false;
         
      var order = product.GetLinkedOrder();
      if(order == null) return false;
         
      if(order.IsComplete == false) return false;
      
      return true;
   }
   
   [NonSerialized] private GameState_Product m_linkedProduct = null;
   
   public Sprite SpriteImage() { return Resources.Load<Sprite>(c_pathToSprite + m_sprite);}
   
   public GameState_Product GetProduct()
   {
      if(m_linkedProduct != null) return m_linkedProduct;
      if(IsProduct == false) return null;
      if(m_uniqueId.IsNullOrWhiteSpace()) return null;
      m_linkedProduct = GameManager.Me.m_state.m_products.Find(p=>p.m_uniqueID == m_uniqueId);
      return m_linkedProduct;
   }

   public static Sprite RawMaterialSpriteImage(string _name)
   {
      return Resources.Load<Sprite>(c_pathToSprite + _name);
   }

   public Color BezierColour()
   {
      return Utility.HexToColor(m_bezierColour);
   }

   public static NGCarriableResource GetInfo(string _string)
   {
      if(_string == null) _string = c_none;
      
      s_carriableResources.TryGetValue(_string, out var resource);
      
      if(resource == null && _string.Contains(":"))
      {
         var parts = _string.Split(":");
         if(parts.Length == 2)
         {
            if(s_carriableResources.TryGetValue(parts[0], out var resourceToClone))
            {
               resource = resourceToClone.MemberwiseClone() as NGCarriableResource;
               resource.m_name = _string;
               resource.m_uniqueId = parts[1]; 
               s_carriableResources[_string] = resource;
            }
         }
      }
      
      if(resource == null)
      {
         Debug.LogError($"Cannot find CarriableResource '{_string}'");
         return s_carriableResources[c_none];
      }
      return resource;
   }

   public static GameObject GetPrefabForResource(NGCarriableResource _type)
   {
      if(_type.m_prefab.IsNullOrWhiteSpace()) 
         return null;
      var prefab = ResManager.Load<GameObject>(c_pathToPrefab + _type.m_prefab);
      if (prefab != null)
         return prefab;
      Debug.LogError($"There is no prefab for the carriable resource {_type.Name} at this path - {c_pathToPrefab} + {_type.m_prefab}");
      return null;
   }
   
   public static GameObject GetPrefabForResource(string _type)
   {
      var resource = GetInfo(_type);
      return GetPrefabForResource(resource);
   }

   public static bool PostImport(NGCarriableResource _what)
   {
      if (_what.Name.IsNullOrWhiteSpace())
      {
         Debug.LogError($"NGCarriableResource entry has no name, please update the Knack table NGCarriableResource");
         return false;
      }
      if(s_carriableResources.ContainsKey(_what.Name))
      {
         Debug.LogError($"Duplicate entry in the NGCarriableResource table - {_what.Name}");
         return false;
      }

      if (_what.m_spriteColour.IsNullOrWhiteSpace() == false)
      {
         ColorUtility.TryParseHtmlString(_what.m_spriteColour, out _what.m_color);
      }
      
      _what.IsTopLevelResource = _what.IsFlour || _what.IsFabric || _what.IsMetal;

      s_carriableResources.Add(_what.Name, _what);
      return true;
   }
   
   public static List<NGCarriableResource> LoadInfo()
   {
      s_carriableResources.Clear();
      s_resources = NGKnack.ImportKnackInto<NGCarriableResource>(PostImport);
      return s_resources;
   }
}
