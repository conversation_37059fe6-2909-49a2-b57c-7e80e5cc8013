using System.Reflection;
using UnityEngine;
using UnityEngine.Serialization;

public class MAUnlocks : MonoSingleton<MAUnlocks>
{
    private static bool m_isLoaded = false;

    #region Build
    [Header("Build")]
    public bool m_buildHeroGuild = false;
    public bool m_buildLumberMill;
    public bool m_buildTurret;
    public bool m_buildWeaponsmith;
    public bool m_buildArmorer;  
    public bool m_wallBuilding;
    #endregion
    #region Design
    [Header("Design")]
    public bool m_weirdDesigns;
    public bool m_designBuildings;
    public bool m_designFood;
    public bool m_designPaints;
    public bool m_designPatterns;
    public bool m_designRoads;
    public bool m_designStickers;
    public bool m_designWalls;
    public bool m_designWeapons;
    public bool m_designArmor;
    #endregion    

    #region HandPowers
    [Header("HandPowers")]
    public bool m_handPowerCuddle;
    public bool m_handPowerDig;
    public bool m_handPowerDragFromWarehouse;
    public bool m_handPowerFireball;
    public bool m_handPowerFlamethrower;
    public bool m_handPowerHitTroll;
    public bool m_handPowerHitWerewolf;
    public bool m_handPowerHitZombie;
    public bool m_handPowerLightning;
    public bool m_handPowerPickupCotton;
    public bool m_handPowerPickupFabric;
    public bool m_handPowerPickupFlour;
    public bool m_handPowerPickupHero;
    public bool m_handPowerPickupIron;
    public bool m_handPowerPickupLogs;
    public bool m_handPowerPickupMetal;
    public bool m_handPowerPickupOre;
    public bool m_handPowerPickupProduce;
    public bool m_handPowerPickupProduct;
    public bool m_handPowerPickupRawProduce;
    public bool m_handPowerPickupTimber;
    public bool m_handPowerPickupWheat;
    public bool m_handPowerPickupWood;
    public bool m_handPowerPickupWorkers;
    public bool m_handPowerUproot;
    public bool m_handPowerWaterBlob;
    public bool m_handPowerWaterSpout;
    public bool m_handPowerWhip;
    public bool m_handPowers;
    public float m_handPowerDigLevel = 1f;
    public float m_handPowerFireballLevel = 1f;
    public float m_handPowerFlamethrowerLevel = 1f;
    public float m_handPowerLightningLevel = 1f;
    public float m_handPowerWaterBlobLevel = 1f;
    public float m_handPowerWaterSpoutLevel = 1f;
    public float m_handPowerWhipLevel = 1f;
    #endregion HandPowers
    #region HandGestures
    [Header("HandGestures")]
    public bool m_handGestureAHole;
    public bool m_handGestureCrossFingers;
    public bool m_handGestureFOff;
    public bool m_handGestureHangLoose;
    public bool m_handGestureLoser;
    public bool m_handGesturePeace;
    public bool m_handGestureThumbsDown;
    public bool m_handGestureThumbsUp;
    public bool m_handGestureWanker;
    #endregion HandGestures
    #region Possess
    [Header("Possess")]
    public bool m_possessAnimals;
    public bool m_possessBuildings;
    public bool m_possessCreatures;
    public bool m_possessHeroes;
    public bool m_possessTourists;
    public bool m_possessWorkers;
    public bool m_possessedCanTagWorkers;
    public bool m_possessedCanTagCreatures;
    public bool m_possessedCanTagTourists;
    public bool m_possessedCanTagHeroes;
    public bool m_possessedCanTagAnimals;
    public bool m_possessedCanTagQuests;
    public bool m_possessedCanTransferPossession;
    #endregion Possess
    [Header("Misc")]
    public bool m_marketForces;
    public bool m_culture;
    public bool m_demolishBolders;
    public bool m_harvestMysticRunes;
    public bool m_unlockBeacons;
    public bool m_unlockDeliveryCart;
    public bool m_patrolMode;
    public bool m_moveBuildings;
    public bool m_pickupOptions;
    public bool m_townSatisfaction;
    public bool m_workerAutoFindJob;
    public float m_blockBedroomLevel = 1;
    public float m_blockFactoryLevel = 1;
    public float m_blockWorkerStationLevel = 1;
    public float m_dragFromBuildingPercent = .1f;
    
    public bool m_usePossessSequence = false;
    
    public float m_orderRefresh = 10f;
    public int m_orderBoardCapacity = 1;
    public bool m_showTags = false;
    public bool m_workersRestTime = false;
    public bool m_researchTabCommoners = false;
    public bool m_researchTabRoyal = false;
    public bool m_researchTabLords = false;
    public bool m_researchTabMystic = false;
    public bool m_endOfDayInfo = false;
    [Header("Balance")]
    public float m_tapHoldMultiplier = 1f;
    
    public float m_foodSalesPriceMarkup = 1f;
    public float m_clothesSalesPriceMarkup = 1f;
    public float m_weaponsSalesPriceMarkup = 1f;
    public float m_armourSalesPriceMarkup = 1f;
    
    public float m_manaStorageMultiplier = 1f;
    
    public float MaxPowerMana => m_manaStorageMultiplier * NGManager.Me.m_defaultPowerManaMax;
    
    public int WheatUpgradeLevel
    {
        set { m_wheatUpgradeLevel = value; TreeHolder.UpdateUnlocks(TreeHolder.c_wheat); }
        get => m_wheatUpgradeLevel;
    }
    
    public int m_wheatUpgradeLevel = 0;
    
    public float GetSalesPriceMarkup(string _productLine)
    {
        var value = 1f;
        switch(_productLine.ToLower())
        {
            case "food": value = m_foodSalesPriceMarkup; break;
            case "weapons": value = m_weaponsSalesPriceMarkup; break;
            case "armour": value = m_armourSalesPriceMarkup; break;
            case "clothes": value = m_clothesSalesPriceMarkup; break;
        }
        return value * NGManager.Me.m_productBlockMultiplier;
    }
    
    public bool BuildHeroGuild
    {
        get => m_buildHeroGuild;
        set
        {
            MAParser.UnlockDrawer("Heroes Guild", value);
            m_buildHeroGuild = value;
        }
    }
    public bool BuildTurret
    {
        get => m_buildTurret;
        set
        {
            MAParser.UnlockDrawer("Turret", value);
            m_buildTurret = value;
        }
    }
    public bool BuildLumberMill
    {
        get => m_buildLumberMill;
        set
        {
            MAParser.UnlockDrawer("Lumber Mill", value);
            m_buildLumberMill = value;
        }
    } 
    public bool BuildWeaponsmith
    {
        get => m_buildWeaponsmith;
        set
        {
            MAParser.Unlock("m_designWeapons=true");
            MAParser.UnlockDrawer("Weaponsmith", value);
            MAParser.UnlockDrawer("Weapons", value);
            m_buildWeaponsmith = value;
        }
    }

    public bool BuildArmorer
    {
        get => m_buildArmorer;
        set
        {
            MAParser.Unlock("m_designArmor=true");
            MAParser.UnlockDrawer("Armourer", value);
            MAParser.UnlockDrawer("Armour", value);
            m_buildArmorer = value;
        }
    }
    
    public bool HarvestMysticRunes
    {
        get => m_harvestMysticRunes;
        set => m_harvestMysticRunes = value;
    }
    public bool UnlockLightning
    {
        get=>m_handPowerLightning;
        set=>m_handPowerLightning = value;
    }
    public bool UnlockBeacons
    {
        get=>m_unlockBeacons;
        set=>m_unlockBeacons = value;
    }
    
    public static void Change(string _nameAndValue)
    {
        var split = _nameAndValue.Split('=');
        if (split.Length == 2)
        {
            Change(split[0], split[1]);
            return;
        }
        split = _nameAndValue.Split('+');
        if(split.Length == 2)
        {
            Add(split[0], split[1]);
            return;
        }
        split = _nameAndValue.Split('-');
        if(split.Length == 2)
        {
            Subtract(split[0], split[1]);
            return;
        }
    }
    
    private static MemberInfo GetMemberInfo(string _name)
    {
        _name = _name.Trim();
        var members = typeof(MAUnlocks).GetMember(_name);
        var member = members.Length > 0 ? members[0] : null;
        
        if(member == null)
        {
            Debug.LogError($"Unable to find {_name} in MAUnlocks"); 
        }
        return member;
    }
    
    public static bool Add(string _name, string _value)
    {
        var member = GetMemberInfo(_name);
        if (member == null) return false;

        var value = GetValue(member, Me);
        if (value == null) return false;

        var type = member.GetUnderlyingType();

        if (type == typeof(float) && float.TryParse(_value, out var f))
        {
            if (SetValue(member, Me, (float)value + f)) return true;
        }

        if (type == typeof(int) && int.TryParse(_value, out var i))
        {
            if (SetValue(member, Me, (int)value + i)) return true;
        }
        
        return false;
    }
    
    public static bool Subtract(string _name, string _value)
    {
        var member = GetMemberInfo(_name);
        if (member == null) return false;

        var value = GetValue(member, Me);
        if (value == null) return false;

        var type = member.GetUnderlyingType();

        if (type == typeof(float) && float.TryParse(_value, out var f))
        {
            if (SetValue(member, Me, (float)value - f)) return true;
        }

        if (type == typeof(int) && int.TryParse(_value, out var i))
        {
            if (SetValue(member, Me, (int)value - i)) return true;
        }

        return false;
    }
    public static bool IsUnlocked(string _name)
    {
        var member = GetMemberInfo(_name);
        if (member == null) return false;
        var value = GetValue(member, Me);
        if (value == null) return false;
        return (bool)value;
    }
    
    public static object GetObject(string _name)
    {
        var member = GetMemberInfo(_name);
        if (member == null) return false;

        var value = GetValue(member, Me);
        if (value == null) return false;
        
        return value;
    }

    public static bool Change(string _name, string _value)
    {
        _name = _name.Trim();
        var member = GetMemberInfo(_name);
        if (member == null) return false;
        var ret = SetValue(member, _value);
        if (ret)
        {
            var prefix = $"{_name}=";
            foreach(var ri in MAResearchInfo.s_researchInfos)
            {
                if(ri.m_unlock.Contains(prefix))
                {
                    if( member.GetUnderlyingType() == typeof(bool))
                        ri.m_acquired = bool.Parse(_value);
                }
            }
        }
        return ret;
    }
    
    private static bool SetValue(MemberInfo _info, string _value)
    {
        if(_info == null) return false;

        var type = _info.GetUnderlyingType();
        
        if(type == typeof(bool) && bool.TryParse(_value, out var b))
        {
            if(SetValue(_info, Me, b)) return true;
        }
        
        if(type == typeof(float) && float.TryParse(_value, out var f))
        {
            if(SetValue(_info, Me, f)) return true;
        }
        
        if(type == typeof(string))
        {
            if(SetValue(_info, Me, _value)) return true;
        }
        
        if(type == typeof(int) && int.TryParse(_value, out var i))
        {
            if(SetValue(_info, Me, i)) return true;
        }
        
        return false;
    }

    private static object GetValue(MemberInfo _info, Object _target)
    {
        switch (_info.MemberType)
        {
            case MemberTypes.Field:
                return ((FieldInfo)_info).GetValue(_target);
            case MemberTypes.Property:
                return ((PropertyInfo)_info).GetValue(_target);
        }
        return null;
    }
    
    private static bool SetValue(MemberInfo _info, Object _target, object _value)
    {
        switch (_info.MemberType)
        {
            case MemberTypes.Field:
                ((FieldInfo)_info).SetValue(_target, _value);
                return true;
            case MemberTypes.Property:
                ((PropertyInfo)_info).SetValue(_target, _value, null);
                return true;
        }
        return false;
    }

    public object GetValue(string _what)
    {
        var field = typeof(MAUnlocks).GetField(_what);
        if (field != null)
        {
            return field.GetValue(Me);
        }
        var property = typeof(MAUnlocks).GetProperty(_what);
        if (property == null)
        {
            MAParser.ParserError($"No such field/property as '{_what}' in MAUnlocks");
            return null;
        }
        return property.GetValue(Me);
    }

    public static void Save(ref string _s)
    {
#if UNITY_EDITOR
        if(GameManager.s_isWritingSeedSave)
            _s = "";
        else
#endif
            _s = JsonUtility.ToJson(Me);
    }
    public static void Load(string _l)
    {
        JsonUtility.FromJsonOverwrite(_l, Me);
        m_isLoaded = true;
    } 

    // static helpers
#if UNITY_EDITOR || DEVELOPMENT_BUILD
    private static DebugConsole.Command s_unlockDebugCommand = new ("maunlock", _s =>
    {
        if(Me == null) return;
        string s = _s.RemoveWhiteSpaceAndToLower();
        var fields = typeof(MAUnlocks).GetFields();
        if (s == "*")
        {
            foreach (FieldInfo fieldInfo in fields)
            {
                fieldInfo.SetValue(Me, true);
            }
            return;
        }
        
        if (s.StartsWith("m_") == false) s = s.Insert(0, "m_");
        int iField = System.Array.FindIndex(fields, x => x.Name.RemoveWhiteSpaceAndToLower().Equals(s));
        if (iField == -1)
        {
            Debug.LogError($"MAUnlocks - 'maunlock' debug command -> no field with name '{_s}' found");
        }
        fields[iField].SetValue(Me, true);
    });
#endif
    
#if UNITY_EDITOR || DEVELOPMENT_BUILD
    private static DebugConsole.Command s_overrideunlocks = new ("overridelocks", _s => Utility.SetOrToggle(ref OverrideLocks, _s));
    private static bool OverrideLocks = false; // Until we put all the unlocks in
#else
    private static bool OverrideLocks => false;
#endif
    
    public static bool CanHit(MACreatureBase _creature)
    {
        if (OverrideLocks) return true;
        if (_creature is MAZombie) return Me.m_handPowerHitZombie;
        if (_creature is MAWerewolf) return Me.m_handPowerHitWerewolf;
        if (_creature is MATroll) return Me.m_handPowerHitTroll;
        return false;
    }

    public static bool CanPickup(NGMovingObject _obj)
    {
        if (OverrideLocks) return true;
        if (_obj is MATourist) return false;
        if (_obj is MAWorker) return Me.m_handPowerPickupWorkers;
        if (_obj is MAHeroBase) return Me.m_handPowerPickupHero;
        if (_obj is ReactPickupPersistent rpp) return CanPickup(rpp.Contents);
        return false;
    }
    
    public static bool CanPickup(NGCarriableResource _res)
    {
        if (OverrideLocks) return true;
        if(_res.IsTimber) return Me.m_handPowerPickupTimber;
        if (_res.IsWood) return Me.m_handPowerPickupWood;
        if (_res.IsOre) return Me.m_handPowerPickupOre;
        if (_res.IsMetal) return Me.m_handPowerPickupIron || Me.m_handPowerPickupMetal;
        if (_res.IsWheat) return Me.m_handPowerPickupRawProduce || Me.m_handPowerPickupWheat;
        if (_res.IsFlour) return Me.m_handPowerPickupProduce || Me.m_handPowerPickupFlour;
        if (_res.IsFabric) return Me.m_handPowerPickupFabric;
        if (_res.IsCotton) return Me.m_handPowerPickupCotton;
        if (_res.IsProduct) return Me.m_handPowerPickupProduct;
        // TODO - return Me.m_handPowerPickupLogs;
        return false;
    }
    
    public static bool CanUsePaints => OverrideLocks || Me.m_designPaints;
    public static bool CanUsePatterns => OverrideLocks || Me.m_designPatterns;
    public static bool CanUseStickers => OverrideLocks || Me.m_designStickers;
    public static bool CanDesignBuildings => Me.m_designBuildings;
}
