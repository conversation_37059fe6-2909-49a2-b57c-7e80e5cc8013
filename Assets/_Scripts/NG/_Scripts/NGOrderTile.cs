using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NGOrderTile : NGDirectionCardBase
{
    protected const string c_borderSpritePath = "MA/_Art/CardBorders/";
    [Header("Order Tile")]
    public Image m_factionIcon = null;
    public Image m_orderBorder = null;
    public Image m_partialDesignProvided = null;
    public bool m_scaleDownWhileDragging = true;    
    public RewardElementUI m_rewardPrefab;
    public Transform m_rewardHolder;
    public OrderTileView m_standardView;
    public OrderTileView m_simpleView;
    public OrderTileView m_detailedView;
    public Transform m_productionIcon;
    public GameObject m_dynamicContent = null;
    public INGDecisionCardHolder.ECardView m_forceView = INGDecisionCardHolder.ECardView.None;
    
    private GameObject m_progressBarObject = null;
    private MAOrder m_order = MAOrder.EmptyOrder;
    
    public int OrderId => m_order.OrderId;
    
    public GameObject LockRoot => m_dynamicContent;
    
    public MAOrder Order => m_order;
    
    public override bool ScaleCardDownOnDrag => m_scaleDownWhileDragging;
    public override void DragAttemptStarted()
    {
        m_viewOverride = INGDecisionCardHolder.ECardView.None;
    }

    public static string FactionToAudioSwitch(string _faction)
    {
        switch (_faction)
        {
            case "Peoples":
                return "Faction_People";
            case "Lords":
                return "Faction_Lords";
            case "Royal":
                return "Faction_Kings";
            default:
                return "Faction_None";
        }
    }
    
    override public string DragAudioSwitch => FactionToAudioSwitch(m_order.OrderInfo.Faction.m_name);
    override public string DragAudio => "PlaySound_OrderCard_Take";
    override public string DragCancelAudio => "PlaySound_OrderCard_Return";
    override public string DragAssignedAudio => "PlaySound_OrderCard_Release";

    protected override bool TryDropOnTarget(MABuilding _building)
    {
        if(_building == null && Order.IsNullOrEmpty() == false)
            Order.Return();
        
        if(_building == null) return false;
        if(m_order.IsNullOrEmpty()) return false;
        if(m_order.AssignedBuilding == _building) return false; // Make sure we don't apply the order to the same building
        
        return true; 
    }
    
    protected override void Awake()
    {
        base.Awake();
        m_progressBarObject = m_progressBar.gameObject;
        m_dynamicContent.SetActive(false);
    }

    protected override void Update()
    {
        base.Update();
        UpdateDynamicInfo();
    }

    public void SetupOrderUI(MAOrder _maOrder, float _fractionGained = -1, float _completion = -1)
    {
        if(_maOrder.IsValid == false)
        {
            Debug.LogError($"{GetType().Name} - SetupOrderUI - invalid order provided. '{_maOrder.DisplayName}'. Details below:" +
                           $"\n {_maOrder.DebugOutputString}");
            return;
        }
        
        if(_maOrder.OrderInfo.Faction != null)
        {
            m_factionIcon.sprite = _maOrder.OrderInfo.Faction.m_iconSprite;
        }

        m_order = _maOrder;

        UpdatePartialDesign(_maOrder.OrderInfo.m_design);
        
        UpdateDynamicInfo();

        SetupRewardsInfoUI(_maOrder.Rewards);
    }
    
    private void UpdatePartialDesign(string _design)
    {
        if(string.IsNullOrWhiteSpace(_design) == false)
        {
            if(m_order != null && m_order.GameProduct != null)
            {
                m_partialDesignProvided.sprite = GameManager.Me.GetDesignSprite(m_order.GameProduct.Design, CaptureObjectImage.Use.Product,
                (Sprite _sprite) =>
                {
                    if(_sprite != null)
                    {
                        m_partialDesignProvided.sprite = _sprite;
                        m_partialDesignProvided.enabled = true;
                    }
                    else
                    {
                        m_partialDesignProvided.gameObject.SetActive(false);
                        m_partialDesignProvided.enabled = false;
                    }
                });
            }
        }
        else
        { 
            m_partialDesignProvided.gameObject.SetActive(false);  
        }
    }
    
    override public bool TryReleaseOverCustomHolder(INGDecisionCardHolder _holder)
    {
        return false;
    }

    private INGDecisionCardHolder.ECardView m_viewOverride = INGDecisionCardHolder.ECardView.None;
    
    private INGDecisionCardHolder.ECardView GetCurrentView()
    {
        if(m_forceView != INGDecisionCardHolder.ECardView.None) return m_forceView;
        if(m_viewOverride != INGDecisionCardHolder.ECardView.None) return m_viewOverride;
        if(m_fromHolder == null) return INGDecisionCardHolder.ECardView.Standard;
        return m_fromHolder.CardView;
    }
   
    public void SetViewOverride(INGDecisionCardHolder.ECardView _view)
    {
        m_viewOverride = _view;
    }
    
    protected override bool CreateCardInfoGUI()
    {
        if(m_fromHolder == null)
            return false;
            
        if(m_fromHolder.ShowOrderBoardGUIOnCardClick && Order.OwnerBuilding != null)
        {
            MAOrderBoardUI.Create(m_order);
            return true;
        }
        
        var currentView = GetCurrentView();
        if(currentView != INGDecisionCardHolder.ECardView.Simple)
        {
            if(currentView == INGDecisionCardHolder.ECardView.Detailed)
                SetViewOverride(INGDecisionCardHolder.ECardView.Standard);
            else if(currentView == INGDecisionCardHolder.ECardView.Standard)
                SetViewOverride(INGDecisionCardHolder.ECardView.Detailed);
                
            SetCardView(GetCurrentView());
        }
        
        return true;
    }

    public override void GiveReward()
    {
        base.GiveReward();
        MABuilding building = m_destFactory as MABuilding;

        if(building == null)
            return;

        MAOrder maOrder = m_order;

        if(maOrder.CanAssign)
        {
            BCFactory[] factoryComponent = building.GetComponentsInChildren<BCFactory>(true);
            if(factoryComponent.Length > 0)
            {
                Debug.Assert(maOrder.m_orderQuantity > 0, $"Warning, Order id {maOrder.OrderId} has <= 0 quantity {maOrder.m_orderQuantity}");
                building.ReceiveOrder(maOrder);
            }
        }
    }
    
    public void SetupRewardsInfoUI(MARewardOrderInfo[] _rewards)
    {
        m_rewardHolder.DestroyChildren();
        
        if(_rewards == null || _rewards.Length == 0)
            return;
        
        var currencyRewards = m_order.GetCurrencyRewards();
        foreach(var reward in currencyRewards)
        {
            Sprite icon = reward.Key.SpriteImage();
            int quantity = reward.Value;
            if(quantity > 0)
            {
                var rewardElement = Instantiate(m_rewardPrefab, m_rewardHolder);
                rewardElement.SetIcon(icon);
                rewardElement.SetIconQuantity(quantity);
            }
        }
        
        foreach(var reward in _rewards)
        {
            if(reward.GiftReward != null && string.IsNullOrWhiteSpace(reward.GiftReward.id) == false)
            {
                var icon = reward.GiftReward.GetSprite;
                if(icon != null)
                {
                    var rewardElement = Instantiate(m_rewardPrefab, m_rewardHolder);
                    rewardElement.SetIcon(icon);
                    rewardElement.SetIconQuantity(1);
                }
            }
        }
    }

    // for Product Testing room: we display a card which shows incremental gains over time
    string RewardProgress(int _total, float _fractionGained, float _completion)
    {
        if (_fractionGained < 0) return "";
        if (_fractionGained > 0)
        {
            var count = Mathf.RoundToInt(_total * _fractionGained * _completion);
            if (count > 0)
            {
                return $"<color=#00ff00>  +{count}</color>";
            }
            return "";
        }
        return $"<color=#ff0000>  +0</color>";
    }

    /// <summary>
    /// is this a display card sitting on a world display sign, product testing display or design table?
    /// </summary>
    /*public bool IsInfoCard
    {
        get
        {
            if(m_order.AssignedBuilding != null)
            {
                List<BCBase> orderInfoBoards =
                    m_order.AssignedBuilding.ComponentLists<BCOrderInfo>();
                if(orderInfoBoards.Count > 0)
                {
                    foreach(BCBase maBuildingComponentBase in orderInfoBoards)
                    {
                        BCOrderInfo orderInfoComponent = maBuildingComponentBase as BCOrderInfo;
                        if(orderInfoComponent.OrderCardDisplay.OrderInfoCard == this) return true;
                    }
                }

                if(ProductTestingManager.Me.m_orderTile == this) return true;
                if(GetComponentInParent<MAOrderCardDoubleSided>() != null) return true;
            }
            return false;
        }
    }*/

    private void SetCardViewActive(OrderTileView _view, bool _visible)
    {
        if(_view == null) return;
        if(_view.gameObject.activeSelf == _visible) return;
        _view.gameObject.SetActive(_visible);
    }
    
    private void SetCardView(INGDecisionCardHolder.ECardView _view)
    {
        switch(_view)
        {
            case INGDecisionCardHolder.ECardView.Detailed:
                SetCardViewActive(m_detailedView, true);
                SetCardViewActive(m_simpleView, false);
                SetCardViewActive(m_standardView, false);            
                break;
            case INGDecisionCardHolder.ECardView.Simple:
                SetCardViewActive(m_detailedView, false);
                SetCardViewActive(m_simpleView, true);
                SetCardViewActive(m_standardView, false);
                break;
            case INGDecisionCardHolder.ECardView.Standard:
                SetCardViewActive(m_detailedView, false);
                SetCardViewActive(m_simpleView, false);
                SetCardViewActive(m_standardView, true);
                break;
        }
    }
    private float speedMultiplier = 150f;  // Scale power to rotation speed
    private float acceleration = 80f; 
    private float m_energy = 0f;
    private float m_currentSpeed = 0f;
    private void UpdateDynamicInfo()
    {
        var cardView = GetCurrentView();
        SetCardView(cardView);

        if(Order.IsValid)
        {
            bool lockCard = m_order.IsAvailable == false && m_fromHolder != null && m_fromHolder.CardView != INGDecisionCardHolder.ECardView.Simple; //&& IsInfoCard == false;
            m_disableInteraction = lockCard;
            
            bool enableDynamicContent = m_order.IsAvailable == false && m_fromHolder != null && (m_fromHolder.CardView != INGDecisionCardHolder.ECardView.Detailed && m_viewOverride != INGDecisionCardHolder.ECardView.Detailed);
            if(m_dynamicContent.activeSelf != enableDynamicContent)
                m_dynamicContent.SetActive(enableDynamicContent);
            
            var assignedBuilding = m_order.AssignedBuilding;
            if (assignedBuilding != null && Order.HasPlayerDesigned)
            {
                foreach(var factory in assignedBuilding.BuildingComponents<BCFactory>())
                {
                    m_energy += factory.PreviousPowerUsed * speedMultiplier;
                }
                
                m_currentSpeed = Mathf.Lerp(m_currentSpeed, m_energy, Time.deltaTime * acceleration);
                
                var speed = m_currentSpeed * Time.deltaTime;
                m_energy -= speed;
                m_productionIcon.Rotate(0f, 0f, speed);
                
                //m_progressBarObject.SetActive(true);
                //m_progressBar.fillAmount = assignedBuilding.GetProductScore();
            }
            else
            {
                //m_progressBarObject.SetActive(false);
            }
        }
    }
    
    public new static NGOrderTile Create(GameObject _prefab, NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder, Transform _holder)
    {
        GameObject go = Instantiate(_prefab, _holder);
        NGOrderTile bd = go.GetComponent<NGOrderTile>();
        bd.Activate(_gift, _maParserSection, _fromHolder);
        return bd;
    }
    
    public new static NGOrderTile Setup(NGOrderTile _tile, NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder, Transform _holder)
    {
        _tile.Activate(_gift, _maParserSection, _fromHolder);
        _tile.transform.SetParent(_holder, false);
        return _tile;
    }
}