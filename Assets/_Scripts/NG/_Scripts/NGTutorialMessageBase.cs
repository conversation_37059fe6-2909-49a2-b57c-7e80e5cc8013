using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGTutorialMessageBase : MonoBehaviour
{
    [System.Serializable]
    public class TutorialMessageSetup
    {
        public string m_type;
        public NGTutorialMessageBase m_prefab;
        public Transform m_holder;
        public Camera m_imageCamera;
        public GameObject m_imageProp;
    }
    List<Transform> m_deactivatedChildren = new List<Transform>();
    private void Start()
    {
        foreach (Transform t in transform.parent)
        {
            if(t == transform) continue;
            if (t.gameObject.activeSelf)
            {
                m_deactivatedChildren.Add(t);
                t.gameObject.SetActive(false);
            }
        }
    }

    virtual public void DestroyMe()
    {
        foreach(var t in m_deactivatedChildren)
        {
            if (t == null)
            {
                Debug.LogError("NGTutorialbase Null transform in deactivated children");
                continue;
            }
            if(t != null && t.gameObject != null)
                t.gameObject.SetActive(true);
        }
        Destroy(gameObject);
    }

    virtual protected void OnDestroy()
    {
        DestroyMe();
    }

    public bool m_buttonFinished = false;

    virtual public bool IsSubtitle => false;
    virtual public bool IsFinished() => m_buttonFinished;
    virtual public bool IsOKToContinue() => false;
    virtual public bool CanDestroy(string _type) => true;
   virtual protected void Activate(string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
    }
    virtual public void Reactivate(string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        return;
        if (_type.Contains("Mentor"))
        {
            //NGTutorialManager.Me.ShowMentorPose(_pose, NGTutorialManager.Me.m_hideVC);
            //NGTutorialManager.Me.FireMentorAnimation();
        }
        else
        {
            //NGTutorialManager.Me.ShowVCPose(_pose, NGTutorialManager.Me.m_hideMentor);
            //NGTutorialManager.Me.FireVCAnimation();
        }
    }

    virtual protected void SetVisualsCamera(Camera _cam, GameObject _prop)
    {
        
    }

    virtual public void ClickedButton()
    {
        
    }

    public static Sprite GetPoseSprite(string _pose)
    {
        if (_pose.IsNullOrWhiteSpace() == false)
        {
            if (_pose.Contains("/"))
                return Resources.Load<Sprite>(_pose);
            else
                return  Resources.Load<Sprite>($"_Art/Characters/Advisors/{_pose}");
        }

        return null;
    }
    
    public void Reactivate(MAMessage _message)
    {
        Reactivate(_message.m_message, "", "", _message.m_type.m_type, _message.m_pose);
    }

    public static NGTutorialMessageBase Create(MAMessage _message)
    {
        return Create(_message.m_message, "", "", _message.m_type.m_type, _message.m_pose, _message.m_type.m_prefab, _message.m_type.m_holder);
    }

    public static NGTutorialMessageBase Create(string _message, string _type, string _pose)
    {
        return Create(_message, "", "", _type, _pose);
    }
    public static NGTutorialMessageBase Create(string _preBarText, string _trackFuncString ,string _postBarText, string _type, string _pose, GameObject _prefab = null, Transform _holder = null)
    {
        if (SettingsUIController.Subtitles == false && _type == "Banner")
            return null;
        TutorialMessageSetup info = null;
        if (_prefab == null)
        {
            info = NGManager.Me.m_NGTutorialMessagePrefabs.Find(o => o.m_type.Equals(_type));
            if (info == null)
            {
                if (_type == "JustAudio") return null;
                Debug.LogError($"Message type of {_type} not implemented in message {_preBarText}:");
                return null;
            }
            if (info.m_prefab == null || info.m_type == null)
            {
                Debug.LogError($"Message type of {_preBarText} not setup in NGTutorialManager.Me.m_NGTutorialMessagePrefabs");
                return null;
            }
            _prefab = info.m_prefab.gameObject; 
            _holder = info.m_holder;           
        }

        try
        {
            var go = Instantiate(_prefab, _holder);
            var tmb = go.GetComponent<NGTutorialMessageBase>();
            tmb.Activate( _preBarText, _postBarText, _trackFuncString, _type, _pose);
            if(info != null)
                tmb.SetVisualsCamera(info.m_imageCamera, info.m_imageProp);
            return tmb;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
   
    }

  /*  private static DebugConsole.Command s_showvc = new DebugConsole.Command("showvc", _s =>
    {
        if (_s == ".") _s = "Enthusiastic";
        NGTutorialManager.Me.ShowVCPose(_s, NGTutorialManager.Me.m_hideMentor);
        NGTutorialManager.Me.FireVCAnimation();
        NGTutorialManager.Me.m_hideMentor = true;
    });*/
}
