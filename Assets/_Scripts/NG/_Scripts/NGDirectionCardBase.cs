using System;
using System.Collections;
using System.Collections.Generic;
using Cans.Analytics;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;

public class NGDirectionCardBase : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IBegin<PERSON><PERSON><PERSON><PERSON><PERSON>, IEndDrag<PERSON><PERSON>ler, IDragCard, IBusinessCard
{
    public class NGCardInfoHolder
    {
        public string m_title;
        public string m_description;
        public float m_cost;
        public int m_quantity;
        public string m_catagory;
        
        // public NGCardInfoHolder(NGTradingManager.SellableItem _nft)
        // {
        //     m_title = _nft.m_title;
        //     m_description = "";
        //     m_cost = _nft.m_price;
        //     m_quantity = _nft.m_quantity;
        //     m_catagory = _nft.m_catagory.ToString();
        // }
        public NGCardInfoHolder(string _title, string _description, float _cost, int _quantity, string _catagory) 
        {
            m_title = _title;
            m_description = _description;
            m_cost = _cost;
            m_quantity = _quantity;
            m_catagory = _catagory;
        }
    }
    
    public BuildingCardHolderSegmentSlot FromCardSlot
    {
        get
        {
            var orderTile = this as NGOrderTile;
            MABuilding returnBuilding = null;
            if(m_fromHolder as MAOrderCardDisplay != null)
            {
                returnBuilding = orderTile?.Order?.OwnerBuilding;
            }
            else if(m_fromHolder as MAOrderBoardUI != null)
            {
                //returnBuilding = (m_fromHolder as MAOrderBoardUI).Building;
            }

            if (returnBuilding != null && returnBuilding.CardHolder != null)
            {
                foreach (var seg in returnBuilding.CardHolder.m_segments)
                {
                    foreach (var slot in seg.m_slots)
                    {
                        if (m_ownerSlot != null && slot.SlotInfo == m_ownerSlot.SlotInfo)
                            return slot;
                        if(orderTile != null && slot.SlotInfo.m_order == orderTile.Order)
                            return slot;
                    }
                }
            }
            return m_ownerSlot;
        }
    }
    
    public GameObject m_invalidDisplay;
    public TMP_Text m_invalidText;
    public BuildingCardHolderSegmentSlot m_ownerSlot;
    public TMP_Text m_title;
    public TMP_Text m_price;
    public Image m_image;
    public Color m_activeColour;
    public Color m_inactiveColour;
    public Color m_positiveColour;
    public Transform m_newAlert;
    public Image m_progressBar;
    public GameObject m_objectToEnableWhenNoValidTarget;
    protected NGBusinessGift m_gift;
    protected MAParserSection m_maParserSection;

    [Header("3D Drag options")]
    public Vector3 m_3DDragOffsetPos = Vector3.zero;
    public float m_3DDragOverrideScale = 1;
    [Header("3D Drag options")]
    public NGBusinessGift Gift { get { return m_gift; } }
    public 
    MAParserSection MaParserSection { get { return m_maParserSection; } }
    public virtual bool ScaleCardDownOnDrag { get { return false; } }

    public static NGPickupUpgradeCard s_cardInFlight = null;
    private static TooPoorIndicator s_currentTooPoorIndicator;
    public static NGDirectionCardBase DraggingCard { get; set; }
    public NGMovingObject DragActivatedObject { get { return m_dragActivatedObject; } }
    
    protected INGDecisionCardHolder m_fromHolder;

    private DragCard m_dragCard;
    protected NGMovingObject m_dragActivatedObject;
    public NGMovingObject PickupCard => gameObject.GetComponentInChildren<NGPickupUpgradeCard>();

    [NonSerialized]
    public NGCommanderBase m_destFactory;
    
    protected Vector3 m_originalPosition;
    
    virtual public string DragAudioSwitch => null;
    virtual public string DragAudio => "PlaySound_HolderCard_Take";
    virtual public string DragCancelAudio => "PlaySound_HolderCard_Return";
    virtual public string DragAssignedAudio => "PlaySound_HolderCard_Release";

    public float Cost => (m_gift == null ? 0 : m_gift.CardPrice);

    public bool m_disableInteraction = false;
    public RectTransform m_rectTransform;
    public bool m_lockUntilEndDrag = false;

    protected bool m_cardActivated;
    private Vector3 m_dragOffset;
    private Vector3 m_dragPreviousPos;
    public Transform m_origionalParent;
    private bool m_lockHolder = false;
    private List<GameObject> m_disabled = new List<GameObject>();
    protected bool m_dragging = false;
    protected Vector3 m_originalScale;
    private Transform m_startParent;
    private bool m_dragBlocked = false;
    public bool m_isInScrollView = false;
    private int m_returnToHolderState = -1;
    public bool CanReturnToHolder => m_returnToHolderState > 0 || (m_fromHolder as MAOrderBoardUI) != null || (m_fromHolder as MAOrderCardDisplay) != null;

    protected virtual void Awake()
    {
        m_dragCard = GetComponent<DragCard>();
        m_rectTransform = GetComponent<RectTransform>();
        var popup = GetComponentInParent<INGDecisionCardHolder>();
        if(popup != null)
            m_origionalParent = popup.GetParentHolder(this);
        else
            m_origionalParent = NGBusinessGiftsPanel.Me?.GetParentHolder(this);
    }
    
    protected virtual void Start()
    {
        m_originalPosition = m_rectTransform.anchoredPosition;
        m_originalScale = m_rectTransform.localScale;
        
        UpdateGhosting();
    }
    
    public void GiftReceived()
    {
        m_fromHolder?.GiftReceived(this);
    }
    
    private static string GetUnavailableText(CardUnavailableReason _reason)
    {
        switch(_reason)
        {
            case CardUnavailableReason.NotEnoughMoney: return "Not Enough Money";
            case CardUnavailableReason.NotEnoughBedroomSlots: return "No Bedroom Slots Available";
            case CardUnavailableReason.NotEnoughJobSlots: return "No Jobs Available";
        }
        return null;
    }
    
    public static bool CreateGenericDialogue(NGDirectionCardBase _card, CardUnavailableReason _reason)
    {
        string reason = GetUnavailableText(_reason);
        
        if (s_currentTooPoorIndicator == null)
        {
            var indicator = Instantiate(GlobalData.Me.m_tooPoorDialogPrefab, GlobalData.Me.m_centreGUI);
            
            switch(_reason)
            {
                case CardUnavailableReason.NotEnoughMoney:
                    indicator.Activate(reason, _card.Cost);
                    break;
                case CardUnavailableReason.NotEnoughBedroomSlots:
                    indicator.Activate(reason);
                    break;
                case CardUnavailableReason.NotEnoughJobSlots:
                    indicator.Activate(reason);
                    break;
            }
            s_currentTooPoorIndicator = indicator;
            AudioClipManager.Me.PlaySound("PlaySound_CardHolderClose", GameManager.Me.gameObject);
            return true;
        }
        
        // Hold the dialogue on screen
        if(s_currentTooPoorIndicator.m_title.text == reason)
        {
            s_currentTooPoorIndicator.RefreshDieTime();
        }
        return false;
    }

    public Ray? GetScreenRay()
    {
        var anchor = GetComponentInChildren<BezierAnchor>();
        if(anchor == null) return null;
        return RectTransformUtility.ScreenPointToRay(Camera.main, anchor.transform.position);        
    }

    private CanvasGroup m_canvasGroup;
    protected void UpdateGhosting()
    {
        var reason = GetCardUnavailableReason(); 
        
        if(m_invalidDisplay)
        {
            if(reason == CardUnavailableReason.None)
            {
                if(m_invalidDisplay.gameObject.activeSelf) 
                    m_invalidDisplay.gameObject.SetActive(false);
            }
            else
            {
                if(!m_invalidDisplay.gameObject.activeSelf)
                    m_invalidDisplay.gameObject.SetActive(true);
            }
                
            if(m_invalidText != null)
            {
                switch(reason)
                {
                    case CardUnavailableReason.NotEnoughMoney:
                        m_invalidText.text = $"<size=60>{MAMessageManager.GetTMPString("Money")}{Cost:F0}</size>\nRequired";
                    break;
                    case CardUnavailableReason.NotEnoughBedroomSlots:
                        m_invalidText.text = $"<size=70>{MAMessageManager.GetTMPString("Bedroom", MAMessageManager.ESlotState.Unoccupied)}</size>\nBedroom Required";
                    break;
                    case CardUnavailableReason.NotEnoughJobSlots:
                        m_invalidText.text = $"<size=70>{MAMessageManager.GetTMPString("Worker", MAMessageManager.ESlotState.Unoccupied)}</size>\nJob Required";
                        break;
                }
            }
        }
        
        if(reason == CardUnavailableReason.None)
        {
            if(m_canvasGroup != null)
            {
                Destroy(m_canvasGroup);
                m_canvasGroup = null;
            }
        }
        else if(m_canvasGroup == null)
        {
            m_canvasGroup = gameObject.AddComponent<CanvasGroup>();
            m_canvasGroup.alpha = 0.4f;
            m_canvasGroup.interactable = false;
        }
    }

    protected virtual void Update()
    {
        if (m_price != null)
        {
            if(Cost == 0)
                m_price.gameObject.SetActive(false);
            m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
        }
           
        UpdateGhosting();

        UpdateDebug();
    }
    
    virtual public void ShowDetailsCard(NGCommanderBase _building, NGMovingObject _object) { }
    virtual public void ShowBasicCard() { }
    virtual protected void DragDeactivate() { }
    
    virtual public void DestroyMe()
    {
        Destroy(gameObject);
    }
    
    virtual protected NGMovingObject DragActivate(PointerEventData _eventData)
    {
        var uc = gameObject.GetComponentInChildren<NGPickupUpgradeCard>();
        if(uc == null)
            uc = gameObject.AddComponent<NGPickupUpgradeCard>();
            
        uc.ActivateExternal(this, m_gift);

        return null;
    }

    public void DisableInteraction(bool _disable)
    {
        m_disableInteraction = _disable;
    }
    
    virtual public void DestroyHeldObject()
    {
        m_dragActivatedObject?.DestroyMe();
        m_dragActivatedObject = null;
    }

    virtual public NGCardInfoHolder GetDescriptionText()
    {
        return new NGCardInfoHolder(m_gift.m_giftTitle, m_gift.m_description, Cost, 1, "Block");
    }

	public void DragCardConfirmed(GameObject _context) {
		var ped = new PointerEventData(null); ped.position = Input.mousePosition;
		DragActivate(ped);
		GiveReward(); 
	}

    public virtual void OnClickInternal()
    {
        
    }
    
    public void OnClick(bool _long)
    {
        if (!_long)
        {
            m_fromHolder.OnCardClick();
            
            if(m_fromHolder == null || !m_fromHolder.IsHidden)
                CreateCardInfoGUI();
            else
            {
                m_fromHolder.ToggleHiding(false, true);
            }
        }
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.CardTap);
    }

    protected virtual bool CreateCardInfoGUI()
    {
        NGCardInfoGUI.Create(this);
        return true;
    }
    
    public bool DraggedFromVault()
    {
        return false;
    }

    public bool IsOverVault(float _buffer = 0)
    {        
        return false;/*
        if(!m_gift.m_canBePutInVault || !NGUnlocks.Vault || NGValutGUI.Me == null || NGValutGUI.Me.IsFull() || m_rectTransform == null)
            return false;
            
        var r1 = TransformRectIntoCanvasSpace(NGValutGUI.Me.m_rt, NGValutGUI.Me.m_rt.GetComponentInParent<Canvas>());
        var r2 = TransformRectIntoCanvasSpace(m_rectTransform, GetComponentInParent<Canvas>());
        
        return r2.Overlaps(r1, true);*/
    }

    public bool BlockScreenDrag()
    {
        if(IsCardOverHolder(out INGDecisionCardHolder holder, 100))
            return true;

        if(IsOverVault(100))
            return true;

        return false;
    }
    
    protected Rect TransformRectIntoCanvasSpace(RectTransform _fromRect, Canvas _toCanvas) 
    {
        return _fromRect.TransformRectIntoCanvasSpace(_toCanvas);
    }
    
    /*public bool IsOverMAOrderBoardCard()
    {
        var orderBoard = MAOrderDataManager.Me.m_orderBoardUIInstance;
        
        if(orderBoard == null) return false;

        var r1 = TransformRectIntoCanvasSpace(orderBoard.GetComponentInChildren<MAOrderCardInfoUI>(true).GetComponent<RectTransform>(), GetComponentInParent<Canvas>());
        var r2 = TransformRectIntoCanvasSpace(m_rectTransform, GetComponentInParent<Canvas>());
        return r2.Overlaps(r1, true);
    }*/

    public virtual bool IsCardOverHolder(out INGDecisionCardHolder _optionalholderOut, float _buffer = 0)
    {
        _optionalholderOut = null;
        if(m_fromHolder == null || m_fromHolder.DragIn3D)
            return false;
        var canvas = GameManager.Me.m_fullScreenCanvas.GetComponentInParent<Canvas>();
        var r1 = TransformRectIntoCanvasSpace(m_fromHolder.Root.GetComponent<RectTransform>(), canvas);
        var r2 = TransformRectIntoCanvasSpace(m_rectTransform, canvas);

        if(m_showDebugOverlapRects || PlayerHandManager.Me.m_showDebugWorldSpaceCardToScreen)
        {
            m_debugRect1 = r1;
            m_debugRect2 = r2;
        }
        
        if(r2.Overlaps(r1, true)) { _optionalholderOut = m_fromHolder; return true; }
        return false;
    }
    
    public bool IsIn3D => m_fromHolder != null && m_fromHolder.DragIn3D;
    
    protected virtual void OnDragWhenActivated() { }

    Vector3 m_cardHolderPosition;
    Vector3? m_startDragCardPos = null;
    Vector3 m_lastDragCardPos;
    
    void IBeginDragHandler.OnBeginDrag(PointerEventData _eventData)
    {
        DragAttemptStarted();
        
        // Clear any rotation
        transform.localRotation = Quaternion.identity;
        
        CardHolderScrollRect.s_scrolling = false;

        m_dragMode = DragMode.None;
        m_returnToHolderState = -1;
        
        if (m_rectTransform == null ||GameManager.TownStandardInteractionsActive == false || m_disableInteraction || MAResearchManagerUI.Me != null ||
            GameManager.Me.IsDesignTable || m_fromHolder == null || m_fromHolder.ParentForDraggingCard(this) == null || 
            GameManager.Me.AreMouseInteractionsBlocked) //The card hasn't been initialised, it's probably still being displayed
        {
            m_dragBlocked = true;
            return; 
        }
        Vector2 p0;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(transform.parent.GetComponent<RectTransform>(), _eventData.position,
            GetComponentInParent<Canvas>().renderMode == RenderMode.WorldSpace ? Camera.main : null, out p0);

        m_dragOffset = m_rectTransform.anchoredPosition.ToVector3Screen() - p0.ToVector3Screen();

        m_dragPreviousPos = p0.ToVector3Screen();
        m_lockHolder = m_fromHolder!=null?m_fromHolder.IsHidden : false;
        m_dragBlocked = false;
        m_cardHolderPosition = transform.position;
        m_startDragCardPos = null;
        m_startParent = transform.parent;
        m_startDragPos = p0;
        m_hasLeftCardHolder = false;
        m_isInScrollView = (GetComponentInParent<CardHolderScrollRect>() != null);

        
        if(m_fromHolder != null && m_fromHolder.DragIn3D)
        {
            //m_rectTransform.parent.localPosition = Vector3.zero;
            m_rectTransform.localScale = Vector3.one * m_3DDragOverrideScale;
        }
    }
    
    private Vector3 m_backupScaleFor3D = new Vector3(0.000075f, 0.000075f, 0.000075f);

    public virtual void DragAttemptStarted() {}
    public virtual void OnBeginDragging() {}

    static void PlayGeneralAudio(string _audio, string _faction)
    {
        var playOnObject = GameManager.Me.gameObject;
        if (!string.IsNullOrEmpty(_faction))
            AudioClipManager.Me.SetSoundSwitch("FactionGroup", _faction, playOnObject);
        if (!string.IsNullOrEmpty(_audio))
            AudioClipManager.Me.PlaySound(_audio, playOnObject);
    }

    void PlayCardAudio()
    {
        PlayGeneralAudio(DragAudio, DragAudioSwitch);
    }

    public enum CardUnavailableReason
    {
        None,
        NotEnoughMoney,
        NotEnoughBedroomSlots,
        NotEnoughJobSlots,
    }
    
    virtual protected CardUnavailableReason GetCardUnavailableReason()
    {
        var cost = Cost;
        if(cost <= 0) return CardUnavailableReason.None;
        if(cost <= NGPlayer.Me.m_cash.Balance) return CardUnavailableReason.None;
        return CardUnavailableReason.NotEnoughMoney;
    }
    
    public void PayGiftCost()
    {
        if(Cost > 0)
            NGPlayer.Me.m_cash.Spend(Cost, $"Upgrade card: {m_gift.m_giftTitle}", m_gift.m_giftTitle, "Upgrade card");
    }
    private enum DragMode
    {
        None,
        Scrolling,
        Dragging,
    }
    DragMode m_dragMode;
    
    Vector2 m_startDragPos;
    bool m_hasLeftCardHolder = false;
    public RectTransform m_newParent = null;

    private void OnDrawGizmos()
    {
        if(m_startDragCardPos == null) return;
        
        Gizmos.DrawSphere(GetCardHolderPos(), 0.5f);
        Gizmos.DrawSphere(m_lastDragCardPos, 0.5f);
    }

    Vector3 GetCardHolderPos()
    {
        var plane = new Plane(-Camera.main.transform.forward, transform.position);
        var camPos = Camera.main.transform.position;
        Ray r = new Ray(camPos, (m_cardHolderPosition-camPos).normalized);
        plane.Raycast(r, out var dist);
        return r.GetPoint(dist);
    }
    
    void IDragHandler.OnDrag(PointerEventData _eventData)
    {
        if ((!m_dragging && m_disableInteraction) || m_lockUntilEndDrag || s_cardInFlight != null || m_dragBlocked)
            return;
        
        Vector2 p0;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(transform.parent.GetComponent<RectTransform>(), _eventData.position,
            GetComponentInParent<Canvas>().renderMode == RenderMode.WorldSpace ? Camera.main : null, out p0);

        if (m_dragMode == DragMode.Dragging)
        {
            var holderPos = GetCardHolderPos();
            if (m_startDragCardPos == null)
            {
                m_startDragCardPos = transform.position;
                m_lastDragCardPos = transform.position;
            }
            
            if(m_returnToHolderState == -1)
            {
                var toStart = (transform.position - m_startDragCardPos.Value);    
                var heading = (transform.position - m_lastDragCardPos);
                var dir = Vector3.Dot(toStart.normalized, heading.normalized);
                if (dir < 0 && toStart.magnitude > 1)
                {
                    m_returnToHolderState = 0;
                }
            }
            else
            {
                var toHolder = (transform.position - holderPos);
                m_returnToHolderState = toHolder.magnitude < 5 ? 1 : 0; 
            }

            m_lastDragCardPos = transform.position;
        }  
        
        if(m_dragMode == DragMode.None)
        {
            var dragDelta = m_startDragPos.ToVector3Screen() - p0.ToVector3Screen();
            Debug.Log($"delta diff d1{dragDelta} vs d2{_eventData.delta}");
            if(Mathf.Abs(dragDelta.y) > 10 || !m_isInScrollView)
            {
                m_dragMode = DragMode.Dragging;

                PlayCardAudio();

                RectTransform tr = transform as RectTransform;
                if(m_fromHolder != null && m_fromHolder.DragIn3D)
                {
                    m_newParent = PlayerHandManager.Me.CursorHeld3DObjectContainer.AttachCard(this);
                }
                else
                { 
                    var dragRoot = m_fromHolder?.ParentForDraggingCard(this);
                    tr.parent = dragRoot;
                    m_newParent = tr.parent as RectTransform;
                }
                                
                tr.localRotation = Quaternion.identity;
                
                RectTransform parentRT = tr.parent.GetComponent<RectTransform>();
                Canvas parentCv = m_newParent.GetComponentInParent<Canvas>();
                RectTransformUtility.ScreenPointToLocalPointInRectangle(parentRT, _eventData.position,
                    parentCv.renderMode == RenderMode.WorldSpace ? Camera.main : null, out p0);
                
                m_startDragPos = p0;

                BuildingCardHolderSegment cardholderSeg = m_startParent.GetComponentInParent<BuildingCardHolderSegment>();
                if(cardholderSeg != null) //TODO: TS - need to manually invoke layoutGroup rebuild. may not be necessary anymore once new cardholder stuff (dimitris?) has its layour group components sorted out (parent doesnt need one etc)
                {
                    LayoutGroup lg = cardholderSeg.GetComponent<LayoutGroup>();
                    if(lg != null)
                    {
                        LayoutRebuilder.MarkLayoutForRebuild(lg.GetComponent<RectTransform>());
                    }
                }
                
                OnBeginDragging();
                m_fromHolder?.OnStartCardDrag();
            }
            else if(Mathf.Abs(dragDelta.x) > 20)
            {
                m_dragMode = DragMode.Scrolling;
                CardHolderScrollRect.s_scrolling = true;
                return;
            }
            else
                return;
        }
        else if(m_dragMode == DragMode.Scrolling)
            return;

        if(ScaleCardDownOnDrag)
        {          
            m_rectTransform.localScale = m_originalScale;
            //float shrinkFactor = (m_startDragPos - p0).magnitude / 200f;
            //m_rectTransform.localScale = m_originalScale*Mathf.Lerp(0.5f, 1f, shrinkFactor); // Changed to scale card up
        }
        
        Cursor.visible = false;
        DraggingCard = this;
        m_dragging = true;
        
        m_rectTransform.anchoredPosition = p0.ToVector3Screen() + m_dragOffset;
        

        bool isDraggingUp = (m_dragPreviousPos.y <= p0.ToVector3Screen().y);
        bool isAboveOrigionalPosition = (m_rectTransform.anchoredPosition.y < (m_originalPosition.y+100));
        bool enableOnUpwardDrag = m_fromHolder == null ? true : m_fromHolder.EnableCardOnUpwardDrag();
        bool isCardOverHolder = IsCardOverHolder(out INGDecisionCardHolder outHolder) && (!enableOnUpwardDrag || !isDraggingUp || isAboveOrigionalPosition);
        bool isCardOverHolderAnd = isCardOverHolder && (!enableOnUpwardDrag || !isDraggingUp || isAboveOrigionalPosition);

        m_dragPreviousPos = p0.ToVector3Screen();

        bool isCardOverVault = IsOverVault();
        bool draggedFromVault = DraggedFromVault();        
        bool isCardOverOriginHolder = m_fromHolder != null && m_fromHolder == outHolder;
        if(outHolder == null) m_hasLeftCardHolder = true;
        
        if(!draggedFromVault)
        {
            // Toggle the vault if we are dragging from the business descision dialogue
            /*if(NGValutGUI.Me != null)
                NGValutGUI.Me.ToggleHiding(!isCardOverVault);*/
        }
        else if(!isCardOverHolderAnd)
        {
            // Hide the business descision dialogue if it exists and we are dragging from the vault
            if(NGBusinessDecisionDialog.Me != null)
            {
                NGBusinessDecisionDialog.Me.ToggleHiding(true);
            }
        }

        if (!m_lockHolder)
        {
            m_fromHolder?.ToggleHiding(!isCardOverHolderAnd);
        }
        else if (!isCardOverHolder)
        {
            m_lockHolder = false;
        }
        
        bool dragIn3d = m_fromHolder == null ? false : m_fromHolder.DragIn3D;
        m_dragCard.UpdateBezierTarget((int)_eventData.button, outHolder != null || isCardOverVault, dragIn3d);
        if(m_objectToEnableWhenNoValidTarget != null) m_objectToEnableWhenNoValidTarget.SetActive(isCardOverOriginHolder);

        if (!isCardOverHolderAnd && !isCardOverVault)
        {
            if(!m_cardActivated)
            {
                if (m_fromHolder == null)
                {
                    DragActivate(_eventData);
                    m_cardActivated = true;
                    HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Drag);
                }
                else if(GetCardUnavailableReason() != CardUnavailableReason.None)
                {
                    if(CreateGenericDialogue(this, GetCardUnavailableReason()))
                        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Drag);
                }
                else
                {
                    ++_eventData.clickCount; // clickCount is imposting as number of activates this drag 
                    m_dragActivatedObject = DragActivate(_eventData);
                    m_cardActivated = true;
                    HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.Drag);
                }
            }
            else
            {
                OnDragWhenActivated();
            }
        }
        else if(m_cardActivated)
        {
            OnCardDraggedBackOverHolder();
            SetCardHidden(false);
            DestroyHeldObject();
            m_cardActivated = false;
        }
        
        if(m_fromHolder != null && m_fromHolder.DragIn3D)
        {
            m_rectTransform.localPosition = Vector3.zero;
            m_rectTransform.localScale = Vector3.one * m_3DDragOverrideScale;
        }
    }

    protected virtual void OnCardDraggedBackOverHolder()
    {
    }

    public void SetCardHidden(bool _hide)
    {
        if (this == null) 
            return;

        float alpha = 0;
        if(_hide)
        {
            if(m_disabled.Count > 0) return;

            foreach(Transform t in transform)
            {
                if(!t.gameObject.activeSelf)
                    continue;
                m_disabled.Add(t.gameObject);
                t.gameObject.SetActive(false);
            }
        }
        else
        {
            foreach(var d in m_disabled)
            {
                d.SetActive(true);
            }
            m_disabled.Clear();
            alpha = 1;
            
        }
        var image = GetComponent<Image>();
        if(image != null)
        {
            var c = image.color;
            c.a = alpha;
            image.color = c;
        }
    }

    private void PlayAudioCancel()
    {
        PlayGeneralAudio(DragCancelAudio, DragAudioSwitch);
    }
    private void PlayAudioAssigned()
    {
        PlayGeneralAudio(DragAssignedAudio, DragAudioSwitch);
    }

    public virtual void ActionCancelled(bool _lockCardUntilEndDragEvent = false)
    {
        PlayAudioCancel();

        m_lockUntilEndDrag = _lockCardUntilEndDragEvent;

        Destroy(GetComponent<NGPickupUpgradeCard>());
        DestroyHeldObject();
        SetCardHidden(false);
        
        m_fromHolder?.ToggleHiding(false);
        
        if (this != null && transform != null)
            m_rectTransform.anchoredPosition = m_originalPosition;

        m_cardActivated = false;

        m_rectTransform.localScale = m_originalScale;
        OnCardDraggedBackOverHolder();
    }

    protected virtual bool IsPendingUserAction()
    {
        return false;
    }

    private IPickupBehaviour GetPickupBehaviour()
    {
        return GetComponent<IPickupBehaviour>();
    }
    
    private void Create3DCardAndDropOnTarget(Vector3 _worldPos, NGCommanderBase _target)
    {
        enabled = false;    // Disable this script
        
        // Create 3d card
        
        var card = NGPickupUpgradeCard.Create(this, m_gift, _worldPos);
        // Drop on the target
        var pickup = card.GetComponent<ReactPickup>();
        pickup.m_intendedDestination = _target;
        card.DroppedByPlayerAt(_target);
        _target.DroppedFromHand(card, Vector3.zero);
        s_cardInFlight = card;
    }
    
    void IEndDragHandler.OnEndDrag(PointerEventData _eventData)
    {
        CardHolderScrollRect.s_scrolling = false;
        OnEndDragCustom(_eventData, false);
    }

    virtual protected bool IsValidDrag(PointerEventData _eventData)
    {
        return true;
    }
    
    public void OnEndDragCustom(PointerEventData _eventData, bool _undo)
    {
        if(m_objectToEnableWhenNoValidTarget != null) m_objectToEnableWhenNoValidTarget.SetActive(false);
        m_fromHolder?.OnEndCardDrag();

        Cursor.visible = true;
        if ((!m_dragging && m_disableInteraction) || m_dragBlocked)
            return;
        
        bool isCardOverHolder = IsCardOverHolder(out INGDecisionCardHolder outHolder);
        bool isCardOverVault = IsOverVault();
        bool isCardOverOriginHolder = m_fromHolder == outHolder;

        Vector3 cardWorldPos = transform.position;
        if(m_newParent != null)
        {
            Canvas parentCv = m_newParent.GetComponentInParent<Canvas>();
            if(parentCv.renderMode != RenderMode.WorldSpace)
            {
                cardWorldPos = Camera.main.ScreenPointToRay(transform.position).GetPoint(4);
            }
        }
        
        Transform tr = transform;
        tr.SetParent(m_startParent, false);
        BuildingCardHolderSegment cardholderSeg = m_startParent.GetComponentInParent<BuildingCardHolderSegment>();
        if(cardholderSeg != null) //TODO: TS - need to manually invoke layoutGroup rebuild. may not be necessary anymore once new cardholder stuff (dimitris?) has its layour group components sorted out (parent doesnt need one etc)
        {
            LayoutGroup lg = cardholderSeg.GetComponent<LayoutGroup>();
            if(lg != null)
            {
                LayoutRebuilder.MarkLayoutForRebuild(lg.GetComponent<RectTransform>());
            }
        }
        
        if(m_fromHolder != null && m_fromHolder.DragIn3D)
        {
            PlayerHandManager.Me.CursorHeld3DObjectContainer.ResetCardHolder();
        }
        
        m_newParent = null;
        tr.localPosition = Vector3.zero;
        tr.localRotation = Quaternion.identity;
        tr.localScale = Vector3.one;

        m_dragging = false;
        m_lockUntilEndDrag = false;
        m_cardActivated = false;
        var pickupCard = GetPickupBehaviour();
        if(_undo || !IsValidDrag(_eventData))
        {
            ActionCancelled();
        }
        else if(IsPendingUserAction())
        {
            SetCardHidden(true); // Do nothing
        }
        /*else if(isCardOverVault && NGValutGUI.Me.TryAddToVault(this))
        {
            OnCardDraggedBackOverHolder();
            DestroyHeldObject();
            m_fromHolder?.GiftReceived(this);
        }*/
        else if((isCardOverHolder && isCardOverOriginHolder) || GetCardUnavailableReason() != CardUnavailableReason.None)
        {
            NGDemoManager.Me?.DestroyUpgradeLevelDialogue();
            ActionCancelled();
        }
        else if(isCardOverOriginHolder == false && isCardOverHolder && TryReleaseOverCustomHolder(outHolder))
        {
            Debug.Log($"{GetType().Name} - OnEndDragCustom - Releasing Card {name} over holder {outHolder}");
        }
        else if(pickupCard != null)
        {
            if(m_dragCard.BestTaget != null)
            {
                MABuilding building = m_dragCard.BestTaget.GetComponent<MABuilding>();
                NGCommanderBase target = building ? building : null;
                bool canDropOnTarget = TryDropOnTarget(building); 
                if(canDropOnTarget && target)
                {
                    if(building != null && building.CardHolder == m_fromHolder)
                        PlayAudioCancel();
                    else
                        PlayAudioAssigned();
                    Create3DCardAndDropOnTarget(cardWorldPos, target);
                    Destroy(GetComponent<NGPickupUpgradeCard>());//TODO: TS - this seems to happen in actioncancelled, maybe needs to be within if(target) scope
                }
                else
                {
                    ActionCancelled();
                }
            }
            else
            {
                if (m_gift != null && m_gift.m_componentsToUpgrade.IsNullOrWhiteSpace())
                {
                    PlayAudioAssigned();
                    GiveReward();
                }
            }
        }
        else if(m_fromHolder != null)
        {
            if (m_gift.Type == NGBusinessGift.GiftType.Building)
            { 
                ActionCancelled();//building card dropped at place without acceptable world tile
            }
            else
            {
                PlayAudioAssigned();
                GiveReward();
            }
        }

        DraggingCard = null;
    }
    
    protected virtual bool TryDropOnTarget(MABuilding _building)
    {
        return true;
    }
    
    private void OnDestroy()
    {
        if(DraggingCard == this) DraggingCard = null;

        /*if(NGValutGUI.Me != null && NGValutGUI.Me.IsEmpty())
            NGValutGUI.Me.ToggleHiding(true);*/
    }

    virtual public bool TryReleaseOverCustomHolder(INGDecisionCardHolder _holder)
    {
        // NGBusinessGiftsPanel giftsPanel = _holder as NGBusinessGiftsPanel;
        // if(giftsPanel != null)
        // {
        //     //TODO: TS - there is no design to allow dragging from any card holder to the singular card panel.
        //     //implement here if needed
        // }
        return false;
    }
    
    virtual public void GiveReward()
    {
        if (m_gift.m_rewardFunction.IsNullOrWhiteSpace() == false)
        {
            var trigger = m_gift.m_rewardFunction; 
            if (m_gift.m_rewardFunction.Contains("(") == false)
            {
                trigger = $"Dialog({m_gift.m_rewardFunction})";
            }

            if (MAParserSupport.TryParse(trigger, out var result) == false)
            {
                Debug.LogError($"GiveReward:{m_gift.m_rewardFunction} Error");                
            }
        }
        GiftReceived();
    }

    void OnPointerClick(PointerEventData eventData)
    {

    }

    virtual protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        m_gift = _gift;
        m_fromHolder = _fromHolder;
        m_maParserSection = _maParserSection;
        if (m_price != null)
        {
            if (Cost == 0)
                m_price.gameObject.SetActive(false);
            m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
        }
        if(m_title != null)
            m_title.text = m_gift.m_giftTitle.Replace("[Value]", $"{m_gift.m_quantity}");
    }

    public static NGDirectionCardBase Create(GameObject _prefab, NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder, Transform _holder)
    {
        var go = Instantiate(_prefab, _holder);
        var bd = go.GetComponent<NGDirectionCardBase>();
        bd.Activate(_gift, _maParserSection, _fromHolder);
        return bd;
    }
    
    [Header("Debug")]
    public bool m_showDebugOverlapRects = false;
    private Rect m_debugRect1;
    private Rect m_debugRect2;
    private void UpdateDebug()
    {
        if(m_dragging && (m_showDebugOverlapRects || PlayerHandManager.Me.m_showDebugWorldSpaceCardToScreen))
        {
            Image debugImage1 = null;
            Color col;
            if(Mathf.Approximately(m_debugRect1.width, 0) == false &&
               Mathf.Approximately(m_debugRect1.height, 0) == false)
            {
                debugImage1 = PlayerHandManager.Me.ShowDebugRect(name + " 1", true);
                debugImage1.rectTransform.anchoredPosition = m_debugRect1.center;
                debugImage1.rectTransform.sizeDelta = new Vector2(m_debugRect1.width, m_debugRect1.height);
            }

            Image debugImage2 = null;
            if(Mathf.Approximately(m_debugRect2.width, 0) == false &&
               Mathf.Approximately(m_debugRect2.height, 0) == false)
            {
                debugImage2 = PlayerHandManager.Me.ShowDebugRect(name + " 2", true);
                debugImage2.rectTransform.anchoredPosition = m_debugRect2.center;
                debugImage2.rectTransform.sizeDelta = new Vector2(m_debugRect2.width, m_debugRect2.height);
            }
            
            if(m_debugRect2.Overlaps(m_debugRect1, true))
            {
                if(debugImage1 != null && debugImage2 != null)
                {        
                    debugImage2.color = debugImage1.color;
                }
            }
        }
        else
        {
            PlayerHandManager.Me.ShowDebugRect(name + " 1", false);
            PlayerHandManager.Me.ShowDebugRect(name + " 2", false);
        }
    }
}
