using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class NGColliderPasser : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerClick<PERSON>andler, IStandardClickHandler, IBeginDragHandler, IEndDragHandler, IDragHandler, IBeginClickHoldHandler, IEndClickHoldHandler
{
	NGCommanderBase m_parentCommander;

	public System.Action<PointerEventData> m_onClick = null; //optional, e.g. if you want to listen for clicks but don't have a parent commander.
	
    // Start is called before the first frame update
    void Start()
    {
        m_parentCommander = GetComponentInParent<NGCommanderBase>();
    }

	void OnCollisionEnter(Collision collision)
	{
		if (m_parentCommander && collision.gameObject.activeSelf)
		{
//			Debug.LogError($"#####{m_parentCommander.name} NGColliderParser::OnCollisionEnter with {collision.gameObject.name}");
			m_parentCommander.OnCollisionEnter(collision);
		}
	}

	public void OnPointerClick(PointerEventData _eventData)
    {
        //Output to console the clicked GameObject's name and the following message. You can replace this with your own actions for when clicking the GameObject.
//		if(m_parentCommander) m_parentCommander.OnStandardClick(_eventData);
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
	//	Debug.LogError($"#####{name} OnPointerClick {name}");
		m_onClick?.Invoke(_eventData);
    }

	public void OnStandardClick(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} OnStandardClick {name}");

		if(m_parentCommander) m_parentCommander.OnStandardClick(_eventData);
	}
	
	public void OnBeginDrag(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} OnBeginDrag {name}");
		if(m_parentCommander) m_parentCommander.OnBeginDrag(_eventData);
	}

	public void OnEndClickHold(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} OnEndClickHold {name}");
		if(m_parentCommander) m_parentCommander.OnEndClickHold(_eventData);
	}

	public void OnDrag(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} OnDrag {name}");
		if(m_parentCommander) m_parentCommander.OnDrag(_eventData);
	}

	public void OnEndDrag(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} OnEndDrag {name}");
		if(m_parentCommander) m_parentCommander.OnEndDrag(_eventData);
	}

	public void BeginDragInternal(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} BeginDragInternal {name}");
		if(m_parentCommander) m_parentCommander.BeginDragInternal(_eventData);
	}

	public void EndDragInternal(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} EndDragInternal {name}");
		if(m_parentCommander) m_parentCommander.EndDragInternal(_eventData);
	}

	public void OnBeginClickHold(PointerEventData _eventData)
	{
		var name = (m_parentCommander == null) ? "NULL": m_parentCommander.name;
//		Debug.LogError($"####{name} OnBeginClickHold {name}");
		if(m_parentCommander) m_parentCommander.OnBeginClickHold(_eventData);
	}
}

public class ClickListener : MonoBehaviour, IPointerClickHandler
{
	private System.Action m_action;
	public void SetListener(System.Action _action)
	{
		m_action = _action;
	}
	public void OnPointerClick(PointerEventData eventData)
	{
		m_action?.Invoke();
	}
}
