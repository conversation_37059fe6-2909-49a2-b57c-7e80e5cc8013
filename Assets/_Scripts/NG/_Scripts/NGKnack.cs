using System.Globalization;
using System;
using System.Collections.Generic;
using System.Net;
using System.IO;
using System.IO.Compression;
using UnityEngine;
using TMPro;
using System.Text;
using System.Text.RegularExpressions;
using System.Linq;
using static UnityEngine.EventSystems.EventTrigger;


#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.Build.Pipeline.Utilities;
using UnityEditor.PackageManager;
using UnityEditor.SceneManagement;

[CustomEditor(typeof(NGKnack))]
public class NGKnackInspector : MonoEditorDebug.MonoBehaviourEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var myScript = (NGKnack)target;
        NGKnack.SetEditorMe(myScript);
        if (GUILayout.Button($"Cache Knack To Json"))
        {
            NGKnack.CacheAllKnacks();
        }
        if (GUILayout.But<PERSON>($"Test Knack Import"))
        {
            var li = NGKnack.ImportKnackInto<TestKnackImport>();
        }
        if (GUILayout.Button($"Test Knack Write Record"))
        {
            var li = NGKnack.ImportKnackInto<TestKnackImport>();
            li[0].m_index = 987654321;

            object lip = li[0];
            
            NGKnack.GetKnackPutWholeRecord(lip);
        }

        if (GUILayout.Button($"Create CSV from Json:{myScript.m_csvToJsonJsonName}"))
        {
            myScript.CreateCsvFromJson();
        }    
        
        if (GUILayout.Button($"Cache Just '{myScript.m_cacheJustWhatKnack}' Knack"))
        {
            NGKnack.CacheJustKnacks(myScript.m_cacheJustWhatKnack);
        }    
        

    }
}


#endif
public class KnackField : PropertyAttribute
{
    public KnackField()
    {
    }
}

public class NGKnack : MonoSingleton<NGKnack>
{
#if UNITY_EDITOR
    [System.Serializable]
    public class KnackVersion
    {
        public KnackVersion(string _name, string _appId, string _appKey)
        {
            m_name = _name;
            m_appId = _appId;
            m_appKey = _appKey;
        }
        public string m_name;
        public string m_appId;
        public string m_appKey;
    }

    public List<KnackVersion> m_knackVersions = new List<KnackVersion>()
    {
        new KnackVersion("Main Dev", c_knackAppId, c_kanckApiKey),
    };

    const string c_knackAppId = "610289f5551f68001e7c4f5b";
    const string c_kanckApiKey = "f02db07e-8de6-4797-95ad-84858daa01fb";
    public static string AppID => (UseKnack == null) ? c_knackAppId : UseKnack.m_appId;
    public static string AppKey => (UseKnack == null) ? c_kanckApiKey : UseKnack.m_appKey;
    public static KnackVersion UseKnack
    {
        get
        {
            if (s_Me == null) return null;
            s_Me.m_selectedKnack = EditorPrefs.GetString("UseKnack");
            return s_Me.m_knackVersions.Find(o => o.m_name.Equals(s_Me.m_selectedKnack, StringComparison.OrdinalIgnoreCase));
        }
    }
#else
    const string c_knackAppId = "";
    const string c_kanckApiKey = "";
    public static string AppID => c_knackAppId;
    public static string AppKey => c_kanckApiKey;
#endif
    public bool m_readFieldsFromKnack = true;

    [System.Serializable]
    public class KnackImport
    {
        public string m_jsonName;
        public string m_class;
        public int m_knackTable;
        public int m_knackPagesScene;
        public int m_knackPagesView;
        public bool m_isView = true;
        public string m_jsonFileLocation; //REACTXLS/NGBalance/NGKnack
        public string m_csvFileLocation;
        public bool m_debugWindowSelected;


        public KnackObjectFields m_fields;
        public Type GetClassType => Type.GetType(m_class);
        public string FileNameJson => $"{m_jsonFileLocation}/{m_jsonName}";
        public string FileNameCsv => $"{m_csvFileLocation}/{m_jsonName}";
        public string JsonFileName => $"Assets/Resources/{FileNameJson}.json";
        public string CSVFileName => $"Assets/Resources/{FileNameCsv}.csv";

    }

    public List<KnackImport> m_importKnacks = new List<KnackImport>();
    [System.Serializable]
    public class KnackObjectFields
    {
        public string m_className;
        [System.Serializable]
        public class KnackObjectFieldsInfo
        {
            public string label;
            public string key;
            public bool required;
            public string type;
            public string FieldName { get
                {
                    return $"m_{label.FirstCharToLower()}";
                } }
        }
        public KnackObjectFieldsInfo[] fields;
        public KnackObjectFieldsInfo GetField(string _name) { return fields.Find(o => o.key == _name); }
        public bool IsNull => m_className.IsNullOrWhiteSpace();
    }

    /* // 5/24/22 scastro: now using MiniJSON to parse header with records
    [System.Serializable]
    public class KnackHeader
    {

        public long total_pages;
        public long current_page;
        public long total_records;
        public byte[] records;
    }
	*/
#if UNITY_EDITOR
    public string m_selectedKnack;
    public string m_cacheJustWhatKnack;
    [MenuItem("22Cans/Knack/CacheAllKnacks")]
    public static void CacheAllKnacks()
    {
        SetEditorMe();
        var oldReadFields = s_Me.m_readFieldsFromKnack;
        s_Me.m_readFieldsFromKnack = true;
        float count = 0;
        try
        {
            foreach (var k in s_Me.m_importKnacks)
            {
                if (EditorUtility.DisplayCancelableProgressBar("Generating Knack Cache", $"File '{k.m_jsonName}'", count / (float)s_Me.m_importKnacks.Count))
                    break;
                CacheKnack(k);
                count++;
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        s_Me.m_readFieldsFromKnack = oldReadFields;
    }
    [MenuItem("22Cans/Knack/CacheFlowKnacks")]
    public static void CacheFlowKnacks()
    {
        SetEditorMe();
        var oldReadFields = s_Me.m_readFieldsFromKnack;
        s_Me.m_readFieldsFromKnack = true;
        float count = 0;
        var flowKnacks = s_Me.m_importKnacks.FindAll(o => o.m_jsonName.Contains("Flow") || 
                                                                                    o.m_jsonName.Contains("Gift") || 
                                                                                    o.m_jsonName.Contains("Tutorial") || 
                                                                                    o.m_jsonName.Contains("Decision"));
        try
        {
            foreach (var k in flowKnacks)
            {
                if (EditorUtility.DisplayCancelableProgressBar("Generating Knack Cache", $"File '{k.m_jsonName}'", count / (float)flowKnacks.Count))
                    break;
                CacheKnack(k);
                count++;
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        s_Me.m_readFieldsFromKnack = oldReadFields;
    }

    public static void CacheJustKnacks(string _justTheseKnacks)
    {
        SetEditorMe();
        var oldReadFields = s_Me.m_readFieldsFromKnack;
        s_Me.m_readFieldsFromKnack = true;
        float count = 0;
        try
        {
            foreach (var k in s_Me.m_importKnacks)
            {
                if (_justTheseKnacks.IsNullOrWhiteSpace() || _justTheseKnacks.Contains(k.m_class))
                {
                    if (EditorUtility.DisplayCancelableProgressBar("Generating Knack Cache", $"File '{k.m_jsonName}'", count / (float)s_Me.m_importKnacks.Count))
                        break;
                    CacheKnack(k);
                    count++;
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        s_Me.m_readFieldsFromKnack = oldReadFields;
    }

    [MenuItem("22Cans/Knack/TranslateAndCacheAllKnacks")]
    public static void TranslateAllKnacks()
    {
        SetEditorMe();
        var oldReadFields = s_Me.m_readFieldsFromKnack;
        s_Me.m_readFieldsFromKnack = true;
        LocalizeKnack.TranslateKnackData();
        Me.m_readFieldsFromKnack = oldReadFields;
    }

    [MenuItem("22Cans/Knack/AddLocalizationComponentsToAllPrefabs")]
    public static void AddLocalizationComponentsToAllPrefabs()
    {
        LocalizeKnack.AddComponentToAllPrefabs();
    }

    public static string KnackCheckForEscChars(string _in)
    {
        Regex rgx = new Regex("[^a-zA-Z0-9._]");
        string result = rgx.Replace(_in, "");

        if (result != _in)
            Debug.LogWarning("Unwanted character detected in: " + _in);

        return result;
    }

    public static string CacheKnack(Type _knack)
    {
        var otype = _knack.ToString();
        var knack = NGKnack.Me.m_importKnacks.Find(o => o.m_class.Equals(otype));
        if (knack == null)
            return null;
        return CacheKnack(knack);
    }

    public static string CacheKnack(KnackImport _knack)
    {
        SetEditorMe();
        var kFields = GetFields(_knack);

        // get the records from knack.com
        int totalPages = 1;
        List<object> allRecords = new List<object>();
        for (int i = 1; i <= totalPages; i++)
        {
            KnackGetData(_knack, out string recordsString, i);
            //            recordsString = KnackCheckForEscChars(recordsString);
            var kData = LMiniJSON.Json.Deserialize(recordsString) as Dictionary<string, object>;
            if (kData == null)
            {
                Debug.LogError($"kData is null on {_knack.m_class}");
                return null;
            }

            if (i == 1)
                totalPages = Convert.ToInt32(kData["total_pages"]);

            var recordDicts = kData["records"] as List<object>;
            allRecords.AddRange(recordDicts);
        }

        // identify scan fields and string fields for preprocessing
        // Is it really necessary to use reflection here? Couldn't we just check all string values to see if they have "<span" or "<br" and do replacements on them if they do?
        Type recordType = _knack.GetClassType;
        List<string> scanFields = new List<string>();
        List<string> stringFields = new List<string>();
        foreach (var f in kFields.fields)
        {
            if (f == null || recordType == null)
                continue;
            System.Reflection.FieldInfo fieldInfo = recordType.GetField(f.FieldName);
            if (fieldInfo == null)
                continue;
            if (fieldInfo.CustomAttributes.Any(a => a.AttributeType == typeof(ScanField)))
                scanFields.Add(f.FieldName);
            if (fieldInfo.FieldType == typeof(string))
                stringFields.Add(f.FieldName);
        }

        // preprocess the records
        foreach (Dictionary<string, object> record in allRecords)
        {
            foreach (var f in kFields.fields)
            {
                // replace field names
                if (record.TryGetValue(f.key, out object value))
                {
                    record.Remove(f.key);
                    record[f.FieldName] = value;
                }

                //Convert ScanFields
                if (scanFields.Contains(f.FieldName)
                && record.TryGetValue(f.FieldName, out value) && value is string && value != null)
                {
                    var scan = RemoveScanMakeString(value as string, $"{_knack.JsonFileName}::{_knack.m_class}{f.FieldName}");
                    record[f.FieldName] = scan;
                }
                // Prepare string values.
                if (stringFields.Contains(f.FieldName))
                {
                    record.TryGetValue(f.FieldName, out value);
                    // If the record didn't have this field, it will be populated with empty string.
                    // This could make the cached JSON a little larger than necessary, but will ensure no null strings to preserve existing behavior.
                    record[f.FieldName] = (value as string)?.Replace("<br />", "\n") ?? "";
                }
            }
        }

        string allRecordsString = LMiniJSON.Json.Serialize(allRecords);
        File.WriteAllText(_knack.JsonFileName, allRecordsString);
        return allRecordsString;
    }
#else
    public static string CacheKnack(KnackImport _knack) => string.Empty;
#endif
    private static ZipArchive s_zipData;
    public static string CacheFile => Application.persistentDataPath + "/knack.cache";
    private static byte[] s_cachedContent = null;
    public static byte[] CacheFileContent {
        get
        {
            if (s_cachedContent == null)
            {
                if (!File.Exists(CacheFile)) s_cachedContent = Array.Empty<Byte>();
                else s_cachedContent = File.ReadAllBytes(CacheFile);
            }
            return s_cachedContent;
        }
    }

    public static void SetCachedData(byte[] _data)
    {
        System.IO.File.WriteAllBytes(CacheFile, _data);
        s_cachedContent = _data;
    }

    public static string GetCachedDataMD5()
    {
        var bytes = CacheFileContent;
        if (bytes.Length == 0) return "0";
        string res = "";
        using (var md5 = System.Security.Cryptography.MD5.Create())
        {
            byte[] hash = md5.ComputeHash(bytes);
            res = BitConverter.ToString(hash).Replace("-", string.Empty).ToLowerInvariant();
        }
        return res;
    }
    public static string ReadCachedData(string _name)
    {
        if (s_zipData == null)
        {
            var zipStream = new MemoryStream(CacheFileContent);
            s_zipData = new ZipArchive(zipStream, ZipArchiveMode.Read);
        }
        for (int i = 0; i < s_zipData.Entries.Count; ++i)
        {
            var e = s_zipData.Entries[i];
            if (e.Name == _name)
            {
                var len = e.Length;
                var bytes = new byte[len];
                var readStream = e.Open();
                var countRead = readStream.Read(bytes, 0, (int)len);
                if (countRead < len)
                {
                }
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
        }
        return null;
    }

    public static List<T> ImportKnackInto<T>(Func<T, bool> _callbackOnEachElement = null, string _andCSVName = null)
    {

        //Find Correct Knack
        var findKnack = typeof(T).ToString();
        if (s_Me == null)
        {
            SetEditorMe();
        }
        var knack = s_Me.m_importKnacks.Find(o =>
            o.m_class.Equals(findKnack) && (_andCSVName == null || o.m_jsonName.Equals(_andCSVName)));
        if (knack == null)
        {
            Debug.LogError($"can't find {findKnack} in m_importKnacks");
            return new List<T>();
        }

        return ImportKnackInto<T>(knack, _callbackOnEachElement);
    }

    public static List<T> ImportKnackInto<T>(KnackImport _knack, Func<T, bool> _callbackOnEachElement = null)
    {
        string records = null;
        var results = new List<T>();
        // By default, we want to load the JSON that's provided in the server's blueprints.zip
        bool loadFromServer = true;

        // If in Editor mode, check the editor prefs for loading from either cache or from server
#if UNITY_EDITOR
        loadFromServer = NGKnack.ReadFromServer;
#endif

        if (false && loadFromServer)
        {
            records = ReadCachedData(_knack.m_jsonName + ".json");
        }
        else
        {
#if UNITY_EDITOR
            //Read Fields and Records
            if (ReadFromCache == false)
            {
                records = CacheKnack(_knack);
            }
            else
#endif            
            {
#if true
                var res = Resources.Load<TextAsset>(_knack.FileNameJson);
                if (res == null)
                {
                    Debug.LogError($"ImportKnackInfo: {_knack.JsonFileName} not found for class {_knack.m_class}");
                    return results;
                }
                records = res.text;
#else
            if(File.Exists(knack.JsonFileName) == false)
            {
                Debug.LogError($"ImportKnackInfo: {knack.JsonFileName} not found for class {knack.m_class}");
                return results;
            }
			records = File.ReadAllText(knack.JsonFileName);
#endif
            }
        }

        //Process Records
        {
            var fromJson = JsonHelper.FromJson<T>(records);
            if (fromJson == null)
            {
                Debug.LogError($"ImportKnackInto::json return null on {typeof(T)} have you Serializable the class");
                return null;
            }
            foreach (var fj in fromJson)
            {
                if (fj == null) continue;

                // 5/24/22 scastro: ScanFields and string fields are now pre-processed when JSON files are cached.
                // The JSON files no longer have fields info (KnackObjectFields).
                // This should speed up runtime loading.

                if (_callbackOnEachElement != null)
                {
                    if (_callbackOnEachElement(fj) == false) continue;
                }
                results.Add(fj);
            }
        }
        return results;
    }
    public static KnackObjectFields GetFields(KnackImport _knack, bool _force = false)
    {
        SetEditorMe();
        var kFields = _knack.m_fields;
        var fieldsString = "";
        if (kFields.fields == null)
        {
            Debug.LogError($"Unable to GetFields on {_knack.m_class} - kFields.fields is null");
            return null;
        }
        if (s_Me.m_readFieldsFromKnack || ReadFromCache == false || kFields.fields.Length == 0 || _force)
        {
            KnackGetFields(_knack, out fieldsString);
            if (fieldsString.IsNullOrWhiteSpace() == false)
                kFields = JsonUtility.FromJson<KnackObjectFields>(fieldsString);
            else
                kFields = null;
        }
        /* // 5/24/22 scastro: fields are no longer cached in JSON files, and must be cached in KnackObjectFields if needed.
        else
        {
            if(File.Exists(_knack.JsonFileName) == false)
                { Debug.LogError($"GetFields: {_knack.JsonFileName} Not Found"); return null; }
            var res = Resources.Load<TextAsset>(_knack.FileName);
            if(res == null)
                { Debug.LogError($"GetFields: {_knack.JsonFileName} Resources.Load == null"); return null; }

            var lines = res.text.Split('\n');
            kFields = JsonUtility.FromJson<KnackObjectFields>(lines[0]);
        }
		*/
        return kFields;
    }

    public static string RemoveScanMakeString(string _scan, string _debug = "")
    {
        var result = "";
        _scan = _scan.Trim('"');
        var lines = _scan.Split(new string[] { "<br />" }, System.StringSplitOptions.RemoveEmptyEntries);
        var regex = new Regex("<span class=\"[a-zA-Z0-9]+\"[^>]*>(?<result>[\\sa-zA-Z0-9_()-:&]+)</span>", RegexOptions.IgnoreCase);
        foreach (var l in lines)
        {
            var match = regex.Match(l);
            if (match.Groups.Count >= 2)
            {
                var m = match.Groups;
                result += $"{m[1].Value}|";
            }
            else
            {
                Debug.LogError($"Scan Field {_scan} has no contents Field '{_debug}'.");
            }
        }
        result = result.TrimEnd('|');
        return result;
    }
#if UNITY_EDITOR
    //*********** Read from Server Enabled Menu Toggle
    private const string ReadFromServerName = "ReadFromServer";
    private const string ReadFromServerMenuName = "22Cans/Knack/Read From Server";
    public static bool ReadFromServer { get => false && EditorPrefs.GetBool(ReadFromServerName, true); private set => EditorPrefs.SetBool(ReadFromServerName, value); }

    /*[MenuItem(ReadFromServerMenuName)]
    private static void ToggleReadFromServer()
    {
        ReadFromServer = !ReadFromServer;
    }

    [MenuItem(ReadFromServerMenuName, true)]
    private static bool ToggleAndValidateReadFromServer()
    {
        Menu.SetChecked(ReadFromServerMenuName, ReadFromServer);
        return true;
    }*/

    //*********** Cache Enabled Menu Toggle
    private const string ReadFromCacheName = "ReadFromCache";
    private const string ReadFromCacheMenuName = "22Cans/Knack/Read From Local Cache";
    public static bool ReadFromCache { get => EditorPrefs.GetBool(ReadFromCacheName, true); private set => EditorPrefs.SetBool(ReadFromCacheName, value); }

    [MenuItem(ReadFromCacheMenuName)]
    private static void ToggleCache()
    {
        ReadFromCache = !ReadFromCache;
    }

    [MenuItem(ReadFromCacheMenuName, true)]
    private static bool ToggleCacheValidate()
    {
        Menu.SetChecked(ReadFromCacheMenuName, ReadFromCache);
        return true;
    }
    //*********** Alpha Business Flow Toggle
    public static bool AlphaBusinessFlow { get => EditorPrefs.GetBool(AlphaBusinessFlowName, false); private set => EditorPrefs.SetBool(AlphaBusinessFlowName, value); }
    private const string AlphaBusinessFlowName = "AlphaBusinessFlow";
    private const string AlphaBusinessFlowMenuName = "22Cans/Knack/Use Alpha Business Flow";

    [MenuItem(AlphaBusinessFlowMenuName, false, 100)]
    private static void ToggleShoBalanceMenu()
    {
        AlphaBusinessFlow = !AlphaBusinessFlow;
    }

    [MenuItem(AlphaBusinessFlowMenuName, true)]
    private static bool ToggleAlphaBusinessFlow()
    {
        if (AlphaBusinessFlow)
        {
            PDMFlow = false;
            IainFlow = false;
        }
        Menu.SetChecked(AlphaBusinessFlowMenuName, AlphaBusinessFlow);
        return true;
    }
    //*********** Show ShoBuildInfo Menu Toggle
    /*   public static bool ShoBuildInfo { get => EditorPrefs.GetBool(ShoBuildInfoName, false); private set => EditorPrefs.SetBool(ShoBuildInfoName, value);}
       private const string ShoBuildInfoName = "ShoBuildInfoSetting";
       private const string ShoBuildInfoBalance = "22Cans/Knack/Use Sho BuildInfo";

       [MenuItem(ShoBuildInfoBalance)]
       private static void ToggleShoBuildInfoBalance()
       {
           ShoBuildInfo = !ShoBuildInfo;
       }

       [MenuItem(ShoBuildInfoBalance, true)]
       private static bool ToggleShoBuildInfoBalanceValidate()
       {
           Menu.SetChecked(ShoBuildInfoBalance, ShoBuildInfo);
           return true;
       }*/
    //*********** Iain Flow Menu Toggle
    public static bool IainFlow { get => EditorPrefs.GetBool(IainFlowName, false); private set => EditorPrefs.SetBool(IainFlowName, value); }
    private const string IainFlowName = "IainFlowSetting";
    private const string IainFlowMenu = "22Cans/Knack/Use Iain Flow";

    [MenuItem(IainFlowMenu, false, 101)]
    private static void ToggleMiloFlowMenu()
    {
        IainFlow = !IainFlow;
    }

    [MenuItem(IainFlowMenu, true)]
    private static bool ToggleIainFlowMenuValidate()
    {
        if (IainFlow)
        {
            AlphaBusinessFlow = false;
            PDMFlow = false;
        }
        Menu.SetChecked(IainFlowMenu, IainFlow);
        return true;
    }
    //*********** PDM Flow Menu Toggle
    public static bool PDMFlow { get => EditorPrefs.GetBool(PDMFlowName, false); private set => EditorPrefs.SetBool(PDMFlowName, value); }
    private const string PDMFlowName = "PDMFlowSetting";
    private const string PDMFlowMenu = "22Cans/Knack/Use PDM Flow";

    [MenuItem(PDMFlowMenu, false, 102)]
    private static void TogglePDMFlowMenu()
    {
        PDMFlow = !PDMFlow;
        if (PDMFlow)
        {
            IainFlow = false;
            AlphaBusinessFlow = false;
        }
    }

    [MenuItem(PDMFlowMenu, true)]
    private static bool TogglePDMFlowMenuValidate()
    {
        Menu.SetChecked(PDMFlowMenu, PDMFlow);
        return true;
    }

    //*********** PDM Master Tutorial Menu Toggle
    public static bool PDMMasterTutorialInfo { get => EditorPrefs.GetBool(PDMMasterTutorialInfoName, false); private set => EditorPrefs.SetBool(PDMMasterTutorialInfoName, value); }
    private const string PDMMasterTutorialInfoName = "PDMMasterTutorialInfoSetting";
    private const string PDMMasterTutorialInfoMenu = "22Cans/Knack/Use PDM Master Tutorial";

    [MenuItem(PDMMasterTutorialInfoMenu)]
    private static void TogglePDMMasterTutorialInfoMenu()
    {
        PDMMasterTutorialInfo = !PDMMasterTutorialInfo;
    }

    [MenuItem(PDMMasterTutorialInfoMenu, true)]
    private static bool TogglePDMMasterTutorialInfoMenuValidate()
    {
        Menu.SetChecked(PDMMasterTutorialInfoMenu, PDMMasterTutorialInfo);
        return true;
    }
    //*********** PDM BusinessFlow & Decision Tutorial Menu Toggle
    public static bool PDMFlowDecisionInfo { get => EditorPrefs.GetBool(PDMFlowDecisionInfoName, false); private set => EditorPrefs.SetBool(PDMFlowDecisionInfoName, value); }
    private const string PDMFlowDecisionInfoName = "PDMFlowDecisionInfoSetting";
    private const string PDMFlowDecisionInfoMenu = "22Cans/Knack/Use PDM FlowDecisionInfo";

    [MenuItem(PDMFlowDecisionInfoMenu)]
    private static void TogglePDMFlowDecisionInfoInfoMenu()
    {
        PDMFlowDecisionInfo = !PDMFlowDecisionInfo;
    }

    [MenuItem(PDMFlowDecisionInfoMenu, true)]
    private static bool TogglePDMFlowDecisionInfoInfoMenuValidate()
    {
        Menu.SetChecked(PDMFlowDecisionInfoMenu, PDMFlowDecisionInfo);
        return true;
    }


    //*********** PDM BuildInfo Menu Toggle
    public static bool PDMBuildInfo { get => EditorPrefs.GetBool(PDMBuildInfoName, false); private set => EditorPrefs.SetBool(PDMBuildInfoName, value); }
    private const string PDMBuildInfoName = "PDMBuildInfoSetting";
    private const string PDMBuildInfoMenu = "22Cans/Knack/Use PDM BuildInfo";

    [MenuItem(PDMBuildInfoMenu)]
    private static void TogglePDMBuildInfoMenu()
    {
        PDMBuildInfo = !PDMBuildInfo;
    }

    [MenuItem(PDMBuildInfoMenu, true)]
    private static bool TogglePDMBuildInfoMenuValidate()
    {
        Menu.SetChecked(PDMBuildInfoMenu, PDMBuildInfo);
        return true;
    }

    //*********** Test Research Info Menu Toggle
    public static bool TestReasearch { get => EditorPrefs.GetBool(TestResearchName, false); private set => EditorPrefs.SetBool(TestResearchName, value); }
    private const string TestResearchName = "TestResearchSetting";
    private const string TestResearchMenu = "22Cans/Knack/Use Test Research";

    [MenuItem(TestResearchMenu)]
    private static void ToggleTestResearchMenu()
    {
        TestReasearch = !TestReasearch;
    }

    [MenuItem(TestResearchMenu, true)]
    private static bool ToggleTestResearchMenuValidate()
    {
        Menu.SetChecked(TestResearchMenu, TestReasearch);
        return true;
    }
    //*********** End Test Research Info Menu Toggle

    public static bool ShoBalances => BuildDetails.BalanceSet == "Sho";

#else
    public static bool ReadFromCache => true;
    public static bool ShoBalance => BuildDetails.BalanceSet == "Sho";
    public static bool ShowBalance => BuildDetails.BalanceSet == "Show";
    public static bool IainFlow => BuildDetails.BalanceSet == "Iain";
    public static bool PDMFlow => BuildDetails.BalanceSet == "PDM";
    public static bool AlphaBusinessFlow => BuildDetails.BalanceSet == "Alpha";
    public static bool PDMMasterTutorialInfo => false;
    public static bool ShoBuildInfo => false;
    public static bool PDMBuildInfo => false;
    public static bool TestReasearch => false;
    public static bool PDMFlowDecisionInfo => false;
#endif
    public static string BalanceSet => AlphaBusinessFlow ? "<color=#ff8080>Sho</color>" : (IainFlow ? "<color=#ff8080>Iain</color>" : "Normal");

    //Knack Read functions
    public static bool KnackGetFields(KnackImport _knack, out string _fields)
    {
        _fields = "";
        GetKnackGetFields(_knack.m_knackTable, -1, out _fields);
        return true;
    }
    public static bool KnackGetData(KnackImport _knack, out string _records, long _pageNumber = -1)
    {
        _records = "";
        if (_knack.m_isView)
            GetKnackGetRecords(_knack.m_knackPagesView, _knack.m_knackPagesScene, true, out _records, _pageNumber);
        else
            GetKnackGetRecords(_knack.m_knackTable, -1, false, out _records, _pageNumber);
        return true;
    }
    public static bool GetKnackGetFields(int _objectID, int _sceneID, out string _fields)
    {
        var request = $"https://api.knack.com/v1/objects/object_{_objectID}/fields";
        _fields = KnackRequest(request, false);

        return true;
    }
    public static bool GetKnackGetRecords(int _objectID, int _sceneID, bool _isScene, out string _records, long _pageNumber = -1)
    {
        bool viewRequest = false;
        var request = $"https://api.knack.com/v1/objects/object_{_objectID}/records?format=html&rows_per_page=1000";
        if (_isScene)
        {
            if (_objectID > 0)
                request = $"https://api.knack.com/v1/pages/scene_{_sceneID}/views/view_{_objectID}/records?format=html&rows_per_page=1000";
            else
                request = $"https://api.knack.com/v1/pages/scene_{_sceneID}?format=html&rows_per_page=1000";
            viewRequest = true;
        }
        if (_pageNumber >= 0)
            request += $"&page={_pageNumber}";
        _records = KnackRequest(request, viewRequest);
        return true;
    }

    public static string KnackRequest(string _request, bool _viewRequest)
    {
        try
        {
            HttpWebRequest recordRequest = (HttpWebRequest)WebRequest.Create(_request);
            var appID = AppID;
            var appKey = AppKey;
            //recordRequest.Headers.Add("X-Knack-Application-Id", c_knackAppId);
            recordRequest.Headers.Add("X-Knack-Application-Id", appID);
            if (_viewRequest)
                recordRequest.Headers.Add("X-Knack-REST-API-Key", "knack");
            else
                recordRequest.Headers.Add("X-Knack-REST-API-Key", appKey);
            //recordRequest.Headers.Add("X-Knack-REST-API-Key", c_kanckApiKey);

            using (var recordResponse = (HttpWebResponse)recordRequest.GetResponse())
            {
                switch (recordResponse.StatusCode)
                {
                    case HttpStatusCode.OK:
                        break;
                    case HttpStatusCode.BadRequest:
                        Debug.LogError($"KnackRequest:Malformed App ID");
                        return "";
                    case HttpStatusCode.Unauthorized:
                        Debug.LogError($"KnackRequest:Invalid API key/request");
                        return "";
                    case HttpStatusCode.Forbidden:
                        Debug.LogError($"KnackRequest:Invalid or Expired Token");
                        return "";
                    case (HttpStatusCode)429:
                        Debug.LogError($"KnackRequest:Rate limit exceeded or Plan Limit Exceeded");
                        return "";
                    default:
                        Debug.LogError($"Web Response of {recordResponse.StatusCode} not expected.");
                        break;
                }
                StreamReader recordReader = new StreamReader(recordResponse.GetResponseStream());
                var records = recordReader.ReadToEnd();
                return records;
            }
        }
        catch (WebException e)
        {
            Debug.LogError($"Knack Exception {e} Request {_request}");
            return null;
        }
    }

    public static bool GetKnackPutRecord(object _record, string _field)
    {
        string id = "";
        var otype = _record.GetType().ToString();
        var knack = Me.m_importKnacks.Find(o => o.m_class.Equals(otype));
        if (knack == null)
        {
            Debug.LogError($"No such Knack as {otype}");
            return false;
        }

        var kFields = GetFields(knack);
        var field = kFields.fields.Find(o => o.FieldName.Equals(_field));

        var fi = _record.GetType().GetField(_field);
        var idField = _record.GetType().GetField("id");

        var value = fi.GetValue(_record);
        var json = "{\"" + field.key + "\":\"" + value.ToString() + "\"}";
        var request = $"https://api.knack.com/v1/objects/object_{knack.m_knackTable}/records/{idField.GetValue(_record)}?format=html&rows_per_page=1000";
        var records = KnackPutRequest(request, json);
        return true;
    }

    public static bool KnackDeleteWholeRecord(Type _type, string _id, string _debugMessage = "")
    {
        var otype = _type.ToString();
        var knack = Me.m_importKnacks.Find(o => o.m_class.Equals(otype));
        if (knack == null)
        {
            Debug.LogError($"No such Knack as {otype}");
            return false;
        }
        var request = $"https://api.knack.com/v1/objects/object_{knack.m_knackTable}/records/{_id}?format=html&rows_per_page=1000";
        KnackDeleteRequest(request, _debugMessage);
        return true;
    }


    public static bool GetKnackPutWholeRecord(object _record, string _debugMessage = "")
    {
        SetEditorMe();

        string id = "";
        var otype = _record.GetType().ToString();
        var knack = s_Me.m_importKnacks.Find(o => o.m_class.Equals(otype));
        if (knack == null)
        {
            Debug.LogError($"No such Knack as {otype}");
            return false;
        }

        var kFields = GetFields(knack);
        var allJasons = "";
        foreach (var f in _record.GetType().GetFields())
        {
            var name = f.Name;
            if (name.Equals("id"))
            {
                id = f.GetValue(_record) as string;
                continue;
            }

            var field = kFields.fields.Find(o => o.FieldName.Equals(name));
            if (field == null) continue;
            var value = f.GetValue(_record);
            if (value is string)
            {
                value = ((string)value).Replace("\n", "<br />");
            }
            var json = "\"" + field.key + "\":\"" + value.ToString() + "\",";
            allJasons += json;

        }
        allJasons = "{" + allJasons.TrimEnd(',') + "}";
        var request = $"https://api.knack.com/v1/objects/object_{knack.m_knackTable}/records/{id}?format=html&rows_per_page=1000";
        var records = "";
        if (id.IsNullOrWhiteSpace())
        {
            request = $"https://api.knack.com/v1/objects/object_{knack.m_knackTable}/records?format=html&rows_per_page=1000";
            records = KnackPostRequest(request, allJasons, $"{_debugMessage}");
        }
        else
        {
            records = KnackPutRequest(request, allJasons, $"{_debugMessage}");
        }
        return true;
    }


    public static string KnackPutRequest(string _request, string _put, string _debugMessage = "")
    {
        return KnackMethodRequest("PUT", _request, _put, _debugMessage);
    }
    public static string KnackPostRequest(string _request, string _put, string _debugMessage = "")
    {
        return KnackMethodRequest("POST", _request, _put, _debugMessage);
    }
    public static string KnackDeleteRequest(string _request, string _debugMessage = "")
    {
        return KnackMethodRequest("DELETE", _request, "", _debugMessage);
    }

    public static string KnackMethodRequest(string _method, string _request, string _put, string _debugMessage = "")
    {
        try
        {
            var appID = AppID;
            var appKey = AppKey;

            HttpWebRequest recordRequest = (HttpWebRequest)WebRequest.Create(_request);
            //recordRequest.Headers.Add("X-Knack-Application-Id", c_knackAppId);
            //recordRequest.Headers.Add("X-Knack-REST-API-Key", c_kanckApiKey);
            recordRequest.Headers.Add("X-Knack-Application-Id", appID);
            recordRequest.Headers.Add("X-Knack-REST-API-Key", appKey);
            recordRequest.Method = _method;
            recordRequest.ContentType = "application/json";
            var bytes = System.Text.Encoding.UTF8.GetBytes(_put);
            using (var requestStream = recordRequest.GetRequestStream()) requestStream.Write(bytes, 0, bytes.Length);

            using (var recordResponse = (HttpWebResponse)recordRequest.GetResponse())
            {
                switch (recordResponse.StatusCode)
                {
                    case HttpStatusCode.OK:
                        break;
                    case HttpStatusCode.BadRequest:
                        Debug.LogError($"KnackRequest:Malformed App ID");
                        return "";
                    case HttpStatusCode.Unauthorized:
                        Debug.LogError($"KnackRequest:Invalid API key/request");
                        return "";
                    case HttpStatusCode.Forbidden:
                        Debug.LogError($"KnackRequest:Invalid or Expired Token");
                        return "";
                    case (HttpStatusCode)429:
                        Debug.LogError($"KnackRequest:Rate limit exceeded or Plan Limit Exceeded");
                        return "";
                    default:
                        Debug.LogError($"Web Response of {recordResponse.StatusCode} not expected.");
                        break;
                }
                StreamReader recordReader = new StreamReader(recordResponse.GetResponseStream());
                var records = recordReader.ReadToEnd();
                return records;
            }
        }
        catch (WebException e)
        {
            Debug.LogError($"Knack Exception {e} Request {_request} {_debugMessage}");
            return null;
        }
    }

    public static KnackImport GetKnack(object _object)
    {
        var otype = _object.ToString();
        var knack = Me.m_importKnacks.Find(o => o.m_class.Equals(otype));
        return knack;
    }

#if UNITY_EDITOR
    [MenuItem("22Cans/Knack/Reload BlockInfo")]
    public static void ReloadBlockInfo()
    {
        var oldCache = ReadFromCache;
        ReadFromCache = false;
        NGBlockInfo.LoadInfo();
        NGProductInfo.LoadInfo();
        DesignTableManager.Me.RefreshDesignInfo();
        ReadFromCache = oldCache;
    }

    private const string UseKnackPath = "22Cans/Knack/UseKnack/";
    [MenuItem(UseKnackPath + "Development", priority = 1)] public static void UseDevelopment() => FindAndUseKnackVersion("Development");
    [MenuItem(UseKnackPath + "Development", true)] public static bool UseDevelopmentValidate() => IsUseKnack("Development");
    [MenuItem(UseKnackPath + "Alpha", priority = 11)] public static void UseAlpha() => FindAndUseKnackVersion("Alpha");
    [MenuItem(UseKnackPath + "Alpha", true)] public static bool UseAlphaValidate() => IsUseKnack("Alpha");
    [MenuItem(UseKnackPath + "Christians Dev", priority = 11)] public static void UseChristians() => FindAndUseKnackVersion("Christians Dev");
    [MenuItem(UseKnackPath + "Christians Dev", true)] public static bool UseChristiansValidate() => IsUseKnack("Christians Dev");
    [MenuItem(UseKnackPath + "22cans 0.101", priority = 12)] public static void Use102() => FindAndUseKnackVersion("22cans 0.101");
    [MenuItem(UseKnackPath + "22cans 0.101", true)] public static bool Use102Validate() => IsUseKnack("22cans 0.101");
    [MenuItem(UseKnackPath + "22cans Alpha V1.0", priority = 13)] public static void UseAlphaV1() => FindAndUseKnackVersion("22cans Alpha V1.0");
    [MenuItem(UseKnackPath + "22cans Alpha V1.0", true)] public static bool UseAlphaV1Validate() => IsUseKnack("22cans Alpha V1.0");
    [MenuItem(UseKnackPath + "MOAT", priority = 14)] public static void UseMoat() => FindAndUseKnackVersion("MOAT");
    [MenuItem(UseKnackPath + "MOAT", true)] public static bool UseMOATValidate() => IsUseKnack("MOAT");

    public static void FindAndUseKnackVersion(string _name)
    {
        SetEditorMe();
        var version = Me.m_knackVersions.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
        if (version != null)
        {
            Me.m_selectedKnack = version.m_name;
            EditorPrefs.SetString("UseKnack", Me.m_selectedKnack);
        }
        else
        {
            Me.m_selectedKnack = $"ERROR {_name} not found in Me.m_knackVersions";
        }
    }

    public static bool IsUseKnack(string _name)
    {
        var selectedKnack = "Development";
        if (EditorPrefs.HasKey("UseKnack"))
            selectedKnack = EditorPrefs.GetString("UseKnack");
        else
            EditorPrefs.SetString("UseKnack", selectedKnack);
        var ret = selectedKnack.Equals(_name, StringComparison.OrdinalIgnoreCase);
        //var ret = UseKnack != null && UseKnack.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase);
        Menu.SetChecked(UseKnackPath + _name, ret);
        return true;
    }
    public static List<T> ReflectImportKnackInto<T>(KnackImport _knack)
    {
        return ImportKnackInto<T>(_knack);
    }

    public string m_csvToJsonJsonName = "All";
    [MenuItem("22Cans/Knack/CreateCsvsFromJson")]
    public static void MenuItemCreateCsvFromJson()
    {
        SetEditorMe();
        Me.CreateCsvFromJson();
    }
    static async void GetLastWriteTime(KnackImport _knack)
    {
        /*string appId = "YOUR_APP_ID";
        string apiKey = "YOUR_API_KEY";
        string tableId = "YOUR_TABLE_ID"; // Replace with the actual table ID

        using (HttpClient client = new HttpClient())
        {
            client.DefaultRequestHeaders.Add("X-Knack-Application-Id", appId);
            client.DefaultRequestHeaders.Add("X-Knack-REST-API-Key", apiKey);

            string url = $"https://api.knack.com/v1/objects/{tableId}/records";

            HttpResponseMessage response = await client.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                // Deserialize the response and extract the last write date and time
                // based on your table structure.
                // Example: var records = await response.Content.ReadAsAsync<YourRecordType>();
                // var lastWriteDateTime = records[0].LastWriteDateTime;

                Console.WriteLine("Last write date and time: " + lastWriteDateTime);
            }
            else
            {
                Console.WriteLine("Error fetching data from Knack API.");
            }
        }*/
    }
    public void CreateCsvFromJson()
    {
        SetEditorMe();
        foreach (var knack in Me.m_importKnacks)
        {
            if (m_csvToJsonJsonName.Equals("all", StringComparison.OrdinalIgnoreCase) || m_csvToJsonJsonName.IsNullOrWhiteSpace() || knack.m_jsonName.Equals(m_csvToJsonJsonName, StringComparison.OrdinalIgnoreCase))
            {
                //Write out header
                if (File.Exists(knack.JsonFileName) == false)
                {
                    Debug.LogError($"{knack.m_jsonName} not found");
                    continue;
                }
                KnackObjectFields kFields = GetFields(knack, true);
                if (kFields is null)
                {
                    Debug.LogError($"Knack has no reference to {knack.m_jsonName}");
                    continue;
                }
                var output = "";
                foreach (var f in kFields.fields)
                {
                    output += $"{f.label},";
                }
                output = $"{output.TrimEnd(',')}\n";
                //Decode data
                var type = Type.GetType(knack.m_class);
                var method = typeof(NGKnack).GetMethod("ReflectImportKnackInto");
                var generic = method.MakeGenericMethod(type);
                var genericData = generic.Invoke(null, new object[] { knack });
                if (genericData == null) continue;
                //convert data to list
                var lType = typeof(List<>);
                var glType = lType.MakeGenericType(type);
                var listData = Convert.ChangeType(genericData, glType);

                //call decode data to fields
                method = typeof(NGKnack).GetMethod("GetFieldData");
                generic = method.MakeGenericMethod(type);
                output += (string)generic.Invoke(null, new object[] { listData, kFields });
                File.WriteAllText(knack.CSVFileName, output);
            }
        }
    }

    public static string GetFieldData<T>(List<T> _data, KnackObjectFields _kFields)
    {
        var output = "";
        foreach (var line in _data)
        {
            foreach (var f in _kFields.fields)
            {
                var info = typeof(T).GetField(f.FieldName);
                if (info == null)
                {
                    info = typeof(T).GetField(f.label);
                    if (info == null)
                    {
                        output += $"{f.FieldName} not found,";
                        continue;
                    }
                }
                var value = info.GetValue(line);
                if (value == null)
                {
                    output += $"{f.FieldName} Null on GetValue,";
                    continue;
                }
                output += $"\"{value.ToString()}\",";
            }
            output = $"{output.TrimEnd(',')}\n";
        }

        return output.TrimEnd('\n');
    }
#endif
}
