using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class NGDecoration : NGLegacyBase, IOnClick, IDamageReceiver, IStrikePoint
{
    public string m_resourceType = NGCarriableResource.c_timber;
    public float m_resourceQuantity = 10f;
    public bool m_canPickupAndThrow => m_info?.m_canPickupAndThrow ?? false;
    public float m_throwExplosionSpeedBase => m_info.m_throwExplosionSpeedBase;
    public float m_throwExplosionRadiusBase => m_info.m_throwExplosionRadiusBase;
    public float m_throwExplosionPowerBase => m_info.m_throwExplosionPowerBase;
    public float m_throwExplosionDamageBase => m_info.m_throwExplosionDamageBase;
    public AkEventHolder m_throwExplosionImpactSound;
    
    public AkEventHolder m_impactAudio;
    
    public GameObject m_onExplodeVFX;
    public bool m_isSingleUse = false;
    
    public float m_dragRaise = 0;

    public bool m_isNonInteractive = false;
    public bool m_isSpecialCaseEnterCrypt = false;
    
    private NGDecorationInfoManager.NGDecorationInfo m_info;
    
    protected GameState_Deccoration m_state;

    private Transform m_transform = null;
    
    #region IDamageReceiver
    public virtual int TargetPriority { get; set; } = 1;
    public virtual bool CanBeTargeted { get { return true; } }
    public virtual MAMovingInfoBase GetMovingInfo() { return null; }
    public Transform Transform => m_transform;
    public HashSet<IDamager> TargettedBy => new();
    #endregion

    Vector3 IStrikePoint.StrikePoint => m_transform.position + Vector3.up * (m_dragRaise * .5f);
    
    public static NGDecoration FindDecorationByName(string _name)
    {
        foreach (var state in GameManager.Me.m_state.m_decorations)
            if (state.m_name == _name)
                return state.Decoration;
        return null;
    }

    private static DebugConsole.Command s_damageTargetCmd = new("damagetarget", _s =>
    {
        var bits = _s.Split(",");
        if (bits.Length == 1) bits = new[] {"Decoration[MA_Tardis_Crypt]", bits[0]};
        if (bits.Length != 2) return;
        var receiver = MACharacterBase.GetObjectiveTarget(bits[0]);
        if (receiver == null) return;
        var damage = floatinv.Parse(bits[1]);
        var totalDamage = damage;
        receiver.ApplyDamageEffect(IDamageReceiver.DamageSource.Debug, damage, Vector3.zero);
        Debug.LogError($"Damaged {bits[0]} by {totalDamage} - {totalDamage - damage} applied");
    });
    
    private static DebugConsole.Command s_damageDecorationCmd = new ("damagedecoration", _s => {
        var bits = _s.Split(",");
        if (bits.Length == 1) bits = new [] {"MA_Tardis_Crypt", bits[0]};
        if (bits.Length != 2) return;
        var dec = FindDecorationByName(bits[0]);
        if (dec == null) return;
        var damage = floatinv.Parse(bits[1]);
        var totalDamage = damage;
        dec.ApplyDamageEffect(IDamageReceiver.DamageSource.Debug, damage, Vector3.zero);
        Debug.LogError($"Damaged {bits[0]} by {totalDamage} to {dec.m_state.m_health} - {totalDamage - damage} applied");
    });

    public virtual void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null)
    {
        var damageAbsorbed = Mathf.Min(_damageDone, m_state.m_health);
        m_state.m_health -= damageAbsorbed;
        _damageDone -= damageAbsorbed;
    }

    public void CheckExplosionEffect()
    {
        if (m_onExplodeVFX != null) Instantiate(m_onExplodeVFX, transform.position, Quaternion.identity, transform.parent);
        if (m_isSingleUse) Destroy(gameObject);

        if (m_state != null)
        {
            ++m_state.m_impactCount;
            if (m_info.m_maxImpactCount > 0 && m_state.m_impactCount > m_info.m_maxImpactCount) Destroy(gameObject);
        }
    }

    public void NewDay()
    {
        if (m_state != null)
            m_state.m_health = 1;
    }

    public bool WasEverInOwnedDistrict => m_state?.m_wasEverInOwnedDistrict ?? false;
    virtual protected void Awake()
    {
        m_transform = transform;
        if (GetComponentInChildren<Collider>() == null)
            AddBoxCollider(1, out _);
    }

    virtual protected void Start()
    {
        m_info = NGDecorationInfoManager.GetInfo(Name);
        gameObject.AddComponent<NGDecorationInputHandler>();
        if (m_canPickupAndThrow)
        {
            gameObject.AddComponent<PickupAndThrowBehaviour>();
            var rb = GetComponent<Rigidbody>(); 
            if (rb == null) rb = gameObject.AddComponent<Rigidbody>();
            const float c_defaultThrowableMass = 200;
            var mass = m_info?.m_throwableMass ?? 0;
            if (mass == 0) mass = c_defaultThrowableMass;
            rb.mass = mass;
            PhysicsAudio.Create(gameObject, string.IsNullOrEmpty(m_impactAudio?.Name) ? "PlaySound_BarrelCollision" : m_impactAudio.Name, "ThrowableObjectImpactSpeed");
            GlobalData.Me.RegisterTransformChangeTracker(transform, UpdateState);

            if (gameObject.GetComponentInChildren<BuildingNavBlocker>() == null)
            {
                var bnbgo = new GameObject("NavBlocker");
                bnbgo.transform.SetParent(transform, false);

                var sc = bnbgo.AddComponent<SphereCollider>();
                sc.isTrigger = true;

                var mesh = gameObject.GetComponentInChildren<MeshFilter>().sharedMesh;
                var ext = mesh.bounds.extents;
                float max = Mathf.Max(Mathf.Max(ext.x, ext.y), ext.z);
                sc.radius = max;

                var bnb = bnbgo.AddComponent<BuildingNavBlocker>();
                bnb.m_addNavMargin = true;
            }
        }
        else
        {
            transform.Reseat(0, Utility.ReseatType.PhysicsPositionExcludeRidigbodies);
            gameObject.SetStaticRecursively(true);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            DayNight.Me.RegisterNewDayCallback(NewDay);
#endif
        }
    }

    protected BoxCollider AddBoxCollider(float _scale, out Bounds _bounds)
    {
        var oldRot = transform.rotation;
        transform.rotation = Quaternion.identity;
        var bounds = ManagedBlock.GetTotalVisualBounds(gameObject, null, true);
        transform.rotation = oldRot;
        var cll = gameObject.AddComponent<BoxCollider>();
        bounds.size *= _scale;
        cll.center = bounds.center - transform.position; cll.size = bounds.size;
        cll.isTrigger = true;
        _bounds = bounds;
        return cll;
    }

    protected virtual void UpdateState()
    {
        if (m_state == null) return;
        m_state.m_position = transform.position;
        m_state.m_rotation = transform.eulerAngles;
    }

    public void OnClick(int _inputId, bool _long)
    {
        // bool inDecEditMode = PDMDebugDecoration.Me != null;
        // if (PISSManager.UsePISS)
        // {
        //     if(PISSManager.PISSActive)
        //         ContextMenuManager.Me.ShowContextMenu(GetContextMenuData(), transform.position, this);
        // }
        // else 
 //       if (_long && m_state != null && !GameManager.IsVisitingInProgress && !inDecEditMode)
        if ((m_isSpecialCaseEnterCrypt || m_isNonInteractive == false) && _long && m_state != null && !GameManager.IsVisitingInProgress && DistrictManager.Me.IsWithinDistrictBounds(transform.position, true))
        {
            ContextMenuManager.Me.ShowContextMenu(GetContextMenuData(), transform.position, this);
        }
    }
    public void Register()
    {
        //var rp = gameObject.GetComponentInChildren<NGReactionPoint>();
        //if (rp != null) rp.Activate();
    }

    private ContextMenuData GetContextMenuData()
    {
        ContextMenuData data = new ContextMenuData();
        data.m_title = m_state.m_displayName;
        data.m_buttonDataList = GetContextMenuButtonData();
        return data;
    }

    virtual protected List<ContextMenuData.ButtonData> GetContextMenuButtonData()
    {
        List<ContextMenuData.ButtonData> buttonDatas;
        if (m_isSpecialCaseEnterCrypt)
        {
            buttonDatas = new List<ContextMenuData.ButtonData>
            {
                new ContextMenuData.ButtonData
                {
                    m_label = "Enter",
                    m_onClick = () => IntroControl.Me.ReEnterCrypt(),
                },
            };
            return buttonDatas;
        }
        buttonDatas = new List<ContextMenuData.ButtonData>
        {
                new ContextMenuData.ButtonData{
                    m_label = Localizer.Get(TERM.GUI_INFO),
                    m_onClick = ShowInfoPlaque
                },
                /*new ContextMenuData.ButtonData{
                    m_label = "Salvage Decoration",
                    m_onClick = ConfirmSalvage
                },*/
                new ContextMenuData.ButtonData{
                    m_label = "Move Decoration",
                    m_onClick = PickupDecoration
                }
        };

        return buttonDatas;
    }
    
    protected void ShowInfoPlaque()
    {
        NGDecorationInfoGUI.Create(this);
        return;
    }

    private void ConfirmSalvage()
    {
        List<ContextMenuData.ButtonData> buttonDatas;
        buttonDatas = new List<ContextMenuData.ButtonData>
        {
                new ContextMenuData.ButtonData{
                    m_label = "Confirm",
                    m_onClick = Salvage
                },
                new ContextMenuData.ButtonData{
                    m_label = "Cancel",
                    m_onClick = Cancel
                }
        };

        ContextMenuData data = new ContextMenuData();
        data.m_title = "Salvage";
        data.m_buttonDataList = buttonDatas;

        ContextMenuManager.Me.ShowContextMenu(data, transform.position, this);
    }

    private void Cancel()
    {
        AudioClipManager.Me.PlayUISound("PlaySound_SalvageDecorationCancel");
    }

    private void Salvage()
    {
        AudioClipManager.Me.PlayUISound("PlaySound_SalvageDecorationConfirm");
        SpawnReward();
        DestroyMe();
    }

    private void SpawnReward()
    {
        if (m_resourceQuantity > 0)
        {
            var o = ReactPickupPersistent.Create(null, NGCarriableResource.GetInfo(m_resourceType), m_resourceQuantity, GlobalData.Me.m_pickupsHolder, false, (_o) => {
                _o.GetComponent<NGMovingObject>().SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
                _o.AddComponent<Pickup>();
            });
            var source = transform.position + Vector3.up * 3f;
            var randomDirection = Random.Range(-180.0f, 180.0f) * Mathf.Deg2Rad;
            var direction = new Vector3(Mathf.Sin(randomDirection), 0, Mathf.Cos(randomDirection));
            const float c_throwDistance = 3f, c_throwTime = .4f;
            var target = source + direction * c_throwDistance;
            var velocity = Global3D.GetVelocityRequiredForPointToPointOverTime(source, target, c_throwTime);
            o.GetComponent<Rigidbody>().linearVelocity = velocity;
            o.transform.position = source;
        }
    }

    protected void PickupDecoration()
    {
        NGDecorationInfoManager.Me.SetPlaceDeccoration(m_state.m_name, true, m_state);
        DestroyMe();
    }

    void OnDestroy()
    {
        DestroyMe();
    }
    public override void DestroyMe()
    {
        if (GameManager.Me != null)
        {
            GameManager.Me.m_state.m_decorations.Remove(m_state);
        }

        Destroy(gameObject);
    }
    public virtual NGDecoration SetDecorationData(GameState_Deccoration _data) {
        m_state = _data;
        m_state.m_wasEverInOwnedDistrict |= DistrictManager.Me.IsWithinDistrictBounds(transform.position, true);
        if (m_state.m_health <= 0) m_state.m_health = 1;
        if (m_state.m_wasEverInOwnedDistrict) gameObject.IgnoreDistrictFilter();
        Name = m_state?.m_name;
        _data.Decoration = this;
        return this;
    }

    public NGDecoration SetDrop(string _type, float _count) {
        m_resourceType = _type;
        m_resourceQuantity = _count;
        return this;
    }
}
