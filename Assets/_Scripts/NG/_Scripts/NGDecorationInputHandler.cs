using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class NGDecorationInputHandler : Pickup, IImpactDataSupplier
{
	public static NGDecorationInputHandler s_draggingDecoration;

	public override string m_handAnimationEvent => "HoldGrab";
	
	NGDecoration m_decoration;

	private bool canCreateExplosion = false;
	public bool CanCreateExplosion { get => canCreateExplosion; set => canCreateExplosion = value; }

	protected override float DragRaise => m_decoration.m_dragRaise; 
	
	void Start()
	{
		m_decoration = GetComponent<NGDecoration>();
	}

	override public bool AcceptsClicks => true;
	override public bool UpdatesDuringClick => true;

	/*override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		if (CouldBeClick && TimeSinceClick > LongClickTime) {
				EndDrag();
		}
		base.OnDragUpdate(_dragPoint, _totalScreenDrag);
	}*/
	private float LongClickTime => (NGManager.Me != null) ? NGManager.Me.m_longClickTime : .75f;
	override public void OnClick() {
		if (m_decoration == null || m_decoration.m_canPickupAndThrow) return;
		var onClick = GetComponent<IOnClick>();
		Utility.LastClickLength = TimeSinceClick;
		onClick?.OnClick((int)InputId, IsRightButton);//TimeSinceClick > LongClickTime);
	}
	
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	private bool CanDrag => IsLeftButton && (m_decoration.WasEverInOwnedDistrict || DistrictManager.Me.IsWithinDistrictBounds(transform.position, true));
#else
	private bool CanDrag => true;
#endif

	public override void OnDragStart()
	{
		if (CanDrag == false)
		{
			EndDrag();
			return;
		}
		s_draggingDecoration = this;
		canCreateExplosion = true;
		m_overrideIsInDistrict = m_decoration.WasEverInOwnedDistrict && NGManager.Me.m_canPickUpNonCharacterOutOfDistrict;
		base.OnDragStart();
	}

	public override void OnDragEnd(bool _undo)
	{
		if (s_draggingDecoration == this) s_draggingDecoration = null;
		base.OnDragEnd(_undo);
	}
	
	void OnCollisionEnter(Collision _collision)
	{
		if (m_decoration == null) return;
		if (m_decoration.m_canPickupAndThrow == false) return;

		int layer = _collision.gameObject.layer;
		if (!canCreateExplosion) return;
		if (layer == LayerMask.NameToLayer("RagDollMovingObject")) return;
		
		if ((layer == LayerMask.NameToLayer("Terrain")) || (layer == LayerMask.NameToLayer("Roads")))
			canCreateExplosion = false;

		var maxSpeedSqrd = 0f;
		var maxSpeedPoint = Vector3.zero;
		foreach (var contact in _collision.contacts)
		{
			var thisBody = contact.thisCollider.attachedRigidbody;
			if (thisBody == null) continue;
			var thisSpeedSqrd = thisBody.linearVelocity.sqrMagnitude;
			if (thisSpeedSqrd > maxSpeedSqrd)
			{
				maxSpeedSqrd = thisSpeedSqrd;
				maxSpeedPoint = contact.point;
			}
		}
		const string c_debugLabel = null;//"DecorationExplosion";
		float explosionBaseSpeed = m_decoration.m_throwExplosionSpeedBase;
		float explosionMinSpeed = explosionBaseSpeed * .2f;
		float explosionRadius = m_decoration.m_throwExplosionRadiusBase;
		float explosionForceAtBase = m_decoration.m_throwExplosionPowerBase;
		if (maxSpeedSqrd > explosionMinSpeed * explosionMinSpeed)
		{
			var explosionMultiplier = Mathf.Sqrt(maxSpeedSqrd) / explosionBaseSpeed;
			var explosionForce = explosionForceAtBase * explosionMultiplier;
			explosionRadius *= Mathf.Sqrt(explosionMultiplier);//.5f + .5f * explosionMultiplier; 
			MAPowerEffectBase.CreateExplosionAtPoint(IDamageReceiver.DamageSource.ThrownObject, transform.position/*maxSpeedPoint*/, explosionRadius, explosionForce, this, Vector3.up, gameObject, c_debugLabel);
			m_decoration.CheckExplosionEffect();
		}
		else if (c_debugLabel != null)
		{
			GameManager.Me.ClearGizmos(c_debugLabel);
			GameManager.Me.AddGizmoPoint(c_debugLabel, maxSpeedPoint, explosionRadius * .5f, new Color(1, .5f, .25f, .25f));
			GameManager.Me.AddGizmoLabel(c_debugLabel, maxSpeedPoint, $"{Mathf.Sqrt(maxSpeedSqrd):n1} / {explosionBaseSpeed}", new Color(1, 1, 1, 1));
		}
#if UNITY_EDITOR
		if (c_debugLabel != null) UnityEditor.EditorApplication.isPaused = true;
#endif
	}

	public void PlayImpactAudioOnObject(NGMovingObject _obj)
	{
		_obj.m_hitByDecorationEvent?.Play(_obj.gameObject);
		m_decoration.m_throwExplosionImpactSound.Play(_obj.gameObject);
	}

	public void ApplyDamage(IDamageReceiver.DamageSource _source, NGMovingObject _obj, bool _isContinuous, float _multiplier = 1)
	{
		var damage = m_decoration.m_throwExplosionDamageBase * _multiplier;
		_obj.ApplyDamageEffect(_source, damage, _obj.transform.position + Vector3.up * 1.5f);
	}
}
