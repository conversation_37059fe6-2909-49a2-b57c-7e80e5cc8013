using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[System.Serializable]
public class NGStock
{
    [System.Serializable]
    public class NGStockItem
    {
        public NGStockItem(NGCarriableResource _resource, float _neededToProduce, int _stock = 0)
        {
            if(_resource.m_name.IsNullOrWhiteSpace())
                Debug.LogError($"NGStockItem resource name is null or whitespace: {_resource.m_name}");
            m_resource = _resource;
            m_carriableResourceName = _resource.Name;
            Stock = _stock;
            m_neededToProduce = _neededToProduce;
        }
        public NGStockItem() { }
        public NGStockItem(string _import)
        {
            var split = _import.Split(',');
            if (split.Length < 3)
                return;
            var i = 0;
            m_resource = NGCarriableResource.GetInfo(split[i++]);
            m_carriableResourceName = m_resource.Name;
            
            int.TryParse(split[i++], out m_stock);
            split[i++].TryFloatInv(out m_neededToProduce);
        }
        
        public string m_carriableResourceName;
        public int m_stock;
        public float m_neededToProduce;
        public bool CanMake => Stock >= m_neededToProduce;
        public int Needed => Mathf.Max(0, (int)m_neededToProduce - Stock); 
        public string Export => $"{m_resource?.Name},{Stock.ToStringInv()},{m_neededToProduce.ToStringInv("F3")}";
        public int Stock
        {
            get { return m_stock; }
            set { m_stock = value; }
        }
        private NGCarriableResource m_resource;
        public NGCarriableResource Resource => m_resource ??= NGCarriableResource.GetInfo(m_carriableResourceName);
    }

    public static NGStock operator +(NGStock _a) => _a;

    public static NGStock operator +(NGStock _a, NGStock _b)
    {
        var stock = new NGStock(_a);

        foreach(var i in _b.m_items)
            stock.AddOrCreateStock(i.Resource, i.m_stock, i.m_neededToProduce);
        return stock;
    }

    public NGStock() { }
    public NGStock(List<NGCarriableResource> _resources, int _stock, int _needToProduce = 1)
    {
        foreach (var r in _resources)
        {
            AddOrCreateStock(r, _stock, _needToProduce);
        }
    }
    public NGStock(NGCarriableResource _resource, int _stock = 1, int _needToProduce = 1)
    {
        AddOrCreateStock(_resource, _stock, _needToProduce);
    }

    public NGStock(NGStock _stock)
    {
        foreach (var i in _stock.Items)
            AddOrCreateStock(i.Resource, i.m_stock, i.m_neededToProduce);
    }
    [SerializeField] private List<NGStockItem> m_items = new List<NGStockItem>();
    public List<NGStockItem> Items
    {
        get { return m_items; }
        set { m_items = value;  }
    }

    public bool IsNeededToProduceZero()
    {
        foreach(var i in Items)
            if (i.m_neededToProduce != 0)
                return false;
        return true;
    }

    public bool IsStockZero()
    {
        foreach(var i in Items)
            if (i.m_stock != 0)
                return false;
        return true;
    }
    public bool HasResources => Items.Count > 0;
    public NGCarriableResource GetPrimaryStock => HasResources ? Items[0].Resource : null;
    public int GetPrimaryStockCount => HasResources ? Items[0].m_stock : 0;
    public float GetPrimaryNeededToProduceCount => HasResources ? Items[0].m_neededToProduce : 0;
    
    public NGStockItem Find(NGCarriableResource _what)
    {
        return Items.Find(obj => obj.Resource == _what);
    }
    
    public bool IsCompatible(BCBase _base, NGCarriableResource _what, MABuilding _target, bool _isTest = false)
    {
        if(_what.IsProduct)
        {
            var order = _what.GetProduct()?.GetLinkedOrder();
            if(order.IsNullOrEmpty() || order.CanDeliverTo(_target) == false)
                return false;
                
             foreach(var item in Items)
             {
                if(item.Resource.IsAnyProduct)
                {
                    return item.m_neededToProduce > 0;
                }
             }
        }
        /*else if(_isTest == false && _base is BCStockIn && _target != null && _target.AcceptsTopLevelResources && _what.IsTopLevelResource)
        {
            return true;
        }*/
        else
        {
            var item = Find(_what);
            if(item != null)
            {
                return item.m_neededToProduce > 0;
            }
        }
        return false;
    }

    public void MakeStockFromNeeded()
    {
        foreach (var i in m_items)
        {
            i.m_stock = (int)i.m_neededToProduce;
        }
    }

    public bool IsEnoughStock(NGStock _stock)
    {
        foreach (var i in Items)
        {
            var si = _stock.Find(i.Resource);
            if (si == null)
                return false;
            if (si.m_stock < i.m_neededToProduce)
                return false;
        }

        return true;
    }

    public void CapacityFraction(NGCarriableResource _what, float _factoryCapacity, out int _totalSlotsToAssign, out int _totalSlotsAvailable)
    {
         _totalSlotsToAssign = 0;
        _totalSlotsAvailable = 0;

        float totalToProduce = 0, totalThisResourceToProduce = 0;
        int currentInventory = 0;
        foreach (var r in m_items)
        {
            totalToProduce += r.m_neededToProduce;
            if (r.Resource.Name == _what.Name)
            {
                currentInventory += r.m_stock;
                totalThisResourceToProduce += r.m_neededToProduce;
            }
        }
        if (totalThisResourceToProduce < 1) return; // don't need this resource, never deliver it here
        int maxProductInputsInFactory = (int)_factoryCapacity / (int)totalToProduce;
        _totalSlotsAvailable = (int)totalThisResourceToProduce * maxProductInputsInFactory;
        int totalSlotsRemaining = _totalSlotsAvailable - currentInventory;
        int excessSlots = (int)_factoryCapacity - (int)totalToProduce * maxProductInputsInFactory;
        _totalSlotsToAssign = totalSlotsRemaining + excessSlots; // any excess (rounding) slots are allowed for any resource
    }

    public float CapacityFraction(NGCarriableResource _what, float _factoryCapacity)
    {
        int totalSlotsToAssign;
        int totalSlotsAvailable;

        CapacityFraction(_what, _factoryCapacity, out totalSlotsToAssign, out totalSlotsAvailable);
        
        float score = (float)totalSlotsToAssign / (float)totalSlotsAvailable;
        return Mathf.Clamp01(score);
    }
    
    public string DebugCapacityFractions(float _factoryCapacity)
    {
        string s = "";
        float totalToProduce = 0;
        var current = new Dictionary<NGCarriableResource, float>();
        var totalResToProduce = new Dictionary<NGCarriableResource, float>();
        foreach (var r in m_items)
        {
            totalToProduce += r.m_neededToProduce;
            current[r.Resource] = r.m_stock;
            totalResToProduce[r.Resource] = r.m_neededToProduce;
        }
        if (totalToProduce < .001f) return $"No product";
        int maxProductInputsInFactory = (int)_factoryCapacity / (int)totalToProduce;
        int excessSlots = (int)_factoryCapacity - (int)totalToProduce * maxProductInputsInFactory;
        s += $"Max Products: {maxProductInputsInFactory} Excess: {excessSlots}\n";
        foreach (var kvp in current)
        {
            var res = kvp.Key;
            var currentInventory = (int)kvp.Value;
            var toProduce = totalResToProduce[res];
            
            int totalSlotsAvailable = (int)toProduce * maxProductInputsInFactory;
            int totalSlotsRemaining = totalSlotsAvailable - currentInventory;
            int totalSlotsToAssign = totalSlotsRemaining + excessSlots; // any excess (rounding) slots are allowed for any resource
            float score = (float)totalSlotsToAssign / (float)totalSlotsAvailable;
            score = Mathf.Clamp01(score);
            if (toProduce < 1) score = 0;
            s += $"{res.Name} stock:{currentInventory} perPrd:{toProduce} tsa:{totalSlotsAvailable} tsr:{totalSlotsRemaining} tsta:{totalSlotsToAssign} score:{score}\n";
        }
        return s;
    }

    public string ExportString()
    {
        string result = "";
        foreach (var item in Items)
        {
            result += item.Export + "|";
        }
        result = result.TrimEnd('|');
        return result;
    }

    public void Clear() => Items.Clear();

    public void ClearStock()
    {
        foreach(var s in Items)
            s.Stock = 0;
    }
    public void Import(string _import)
    {
        m_items.Clear();
        if (_import.IsNullOrWhiteSpace()) return;
        foreach (var s in _import.Split('|'))
        {
            var item = new NGStockItem(s);
            if (item.Resource != null)
                Items.Add(item);
        }
        
    }
    public int GetStock(NGCarriableResource _what)
    {
        int result = 0;
        var found = Find(_what);
        if (found != null) result = found.Stock;
        return result;
    }

    public bool AddRequirements(NGCarriableResource _what, int _amount = 0)
    {
        var found = Find(_what);
        if (found == null)
            m_items.Add(new NGStockItem(_what, _amount, 0));
        else
            found.m_neededToProduce += _amount;
        return true;
    }
    
    public bool AddStock(NGCarriableResource _what, int _amount = 0)
    {
        var found = Find(_what);
        if (found == null)
            return false;
        found.Stock += _amount;
        return true;
    }
    
    public bool AddOrCreateStock(NGCarriableResource _what, int _stock, float _neededToProduce = 0)
    {
        var found = Find(_what);
        if (found == null)
            Items.Add(new NGStockItem(_what, _neededToProduce, _stock));
        else  
        {      
            found.Stock += _stock;
            found.m_neededToProduce += _neededToProduce; 
        }
        return true;
    }

    public void ClearNeededToProduce()
    {
        foreach (var i in Items) i.m_neededToProduce = 0;
    }
    public bool CreateOrUpdate(NGCarriableResource _what, float _neededToProduce)
    {
        var found = Find(_what);
        if (found == null)
            Items.Add(new NGStockItem(_what, _neededToProduce, 0));
        else
            found.m_neededToProduce = _neededToProduce;
        return true;
    }
    
    public void RemoveResources(int _amount)
    {
        foreach (var stockItem in Items)
        {
            stockItem.Stock -= Mathf.CeilToInt(stockItem.m_neededToProduce * _amount);
        }
    }
    public void Set(NGCarriableResource _what, float _neededToProduce, int _amount)
    {
        var found = Find(_what);
        if (found == null)
        {
            Items.Add(new NGStockItem(_what, _neededToProduce, _amount));
        }
        else
        {
            found.Stock = _amount;
            found.m_neededToProduce = _neededToProduce;
        }
    }
    public int GetTotalStock()
    {
        var total = 0;
        foreach (var s in Items)
            total += s.Stock;
        return total;
    }

    public int GetTotalStock(NGCarriableResource _res)
    {
        var stockItem = Find(_res);
        if (stockItem == null) return 0;
        return stockItem.Stock;
    }
    
    public int GetNeededToProduce(NGCarriableResource _res)
    {
        var stockItem = Find(_res);
        if (stockItem == null) return 0;
        return (int)stockItem.m_neededToProduce;
    }

    public void Empty()
    {
        foreach (var s in Items)
            s.Stock  = 0;
    }

    public int GetLowestStock()
    {
        int lowest = int.MaxValue;
        foreach(var i in Items)
            if (i.m_neededToProduce > 0 && i.Stock < lowest)
                lowest = i.Stock;
        return (lowest == int.MaxValue) ? 0 : lowest;
    }

    public NGCarriableResource GetLowestStockItem()
    {
        int lowest = int.MaxValue, lowestNotNeeded = int.MaxValue;
        NGCarriableResource result = null;
        NGCarriableResource resultNotNeeded = null;
        foreach (var i in Items)
        {
            if (i.m_neededToProduce > 0 && i.Stock < lowest)
            {
                lowest = i.Stock;
                result = i.Resource;
            }
            else if (i.Stock < lowestNotNeeded)
            {
                lowestNotNeeded = i.Stock;
                resultNotNeeded = i.Resource;
            }
        }
        if (result == null) return resultNotNeeded;
        return result;
    }
    
    public int GetHighestStock()
    {
        int highest = int.MinValue;
        foreach(var i in Items)
            if (i.Stock > highest)
                highest = i.Stock;
        return (highest == int.MinValue) ? 0 : highest;
    }

    public NGCarriableResource GetHighestStockItem()
    {
        int highest = int.MinValue;
        NGCarriableResource result = null;
        foreach (var i in Items)
        {
            if (i.Stock > highest)
            {
                highest = i.Stock;
                result = i.Resource;
            } 
        }
        return result;
    }
    
    public string GetResourcesString()
    {
        string text = "";
        
        foreach(var item in Items)
        {
            if(item.Stock <= 0) continue;

            text += $"{item.Stock}{item.Resource.TextSprite}";
        }
        
        return text;
    }
    
    public void RemoveEmptyResources()
    {
        for(int i = m_items.Count-1; i >= 0; i--)
        {
            if(m_items[i].m_neededToProduce > 0) continue;
            
            if(m_items[i].Stock <= 0)
                m_items.RemoveAt(i);
        }
    }

    public void RemoveEmptyStockAndClearNeededToProduce(NGCarriableResource _removeAllButThisType = null)
    {
        for(int i = m_items.Count-1; i >= 0; i--)
        {
            if(m_items[i].Stock <= 0 || (_removeAllButThisType != null && _removeAllButThisType != m_items[i].Resource))
            {
                m_items.RemoveAt(i);
                continue;
            }
            m_items[i].m_neededToProduce = 0;
        }
    }
    
    public float GetHighestNeededToProduce()
    {
        float highest = 0;
        foreach(var i in Items)
            if (i.m_neededToProduce > highest)
            {
                highest = i.m_neededToProduce;
            }
        return highest;
    }
    
    public NGCarriableResource GetOrderProducts(BCActionOrderBase _orderDestination)
    {
        foreach(var item in Items)
        {
            if(item.Stock <= 0) continue;
            
            var resource = item.Resource;
            if(resource.IsProduct == false) continue;
            if(resource.IsAnyProduct)
            {
                item.Stock--;
                Debug.LogError("Any product should never be stocked");
                continue;
            }
            
            if(_orderDestination == null || _orderDestination.CanAcceptResource(resource) == false)
                continue;
            
            return resource;
        }
        return null;
    }
    
    public bool Consume(NGCarriableResource _res)
    {
        var stockItem = Find(_res);
        if(stockItem == null || stockItem.Stock <= 0) return false;
        stockItem.Stock--;
        return true;
    }
    
    public bool HasStock(NGCarriableResource _res)
    {
        var stockItem = Find(_res);
        if(stockItem == null) return false;
        return stockItem.Stock > 0;
    }
    
    public bool IsNeeded(NGCarriableResource _res)
    {
        var stockItem = Find(_res);
        if(stockItem == null) return false;
        return stockItem.Needed > 0;
    }
    
    public bool HasAnyCapacity(NGCarriableResource _res = null)
    {
        return GetRemainingNeededToProduce(_res) > 0;
    }

    public int GetRemainingNeededToProduce(NGCarriableResource _toExclude = null)
    {
        int total = 0;
        foreach (var i in Items)
        {
            if (i.Resource == _toExclude)
                continue;
            total += Mathf.Max(0, Mathf.CeilToInt(i.m_neededToProduce) - i.m_stock);
        }
        return total;
    }

    public string GetRequirementsMessage()
    {
        var ret = "Needs: ";
        int numNeeded = 0;
        foreach(var i in Items)
            if (i.CanMake == false)
                ++numNeeded;
        int numCounted = 0;
        foreach(var i in Items)
        {
            if (i.CanMake == false)
            {
                string prefix = "";
                if (numCounted > 0)
                {
                    if (numCounted == numNeeded-1) prefix = " and ";
                    else prefix = " ";
                }
                ret += $"{prefix}{Mathf.CeilToInt(i.m_neededToProduce) - i.Stock} {i.Resource.m_title}";
                ++ numCounted;
            }
        }
        return ret;
    }
    
    public bool CanMake()
    {
        if(GetTotalStock() <= 0)
            return false;
            
        foreach(var i in Items)
            if (i.CanMake == false)
                return false;
        return true;
    }

    public float TotalRequiredToMakeOne()
    {
        float count = 0;
        foreach (var i in Items)
        {
            count += RoundPerProduct(i.m_neededToProduce);
        }
        return count == 0 ? 1 : count;
    }
    public float CanMakeHighestNeeded()
    {
        float best = 0;
        foreach (var i in Items)
        {
            best = Mathf.Max(i.m_neededToProduce, best);
        }
        return RoundPerProduct(best);
    }
    public int GetDispatchStock()
    {
        float worst = 9999;
        foreach (var i in Items)
        {
            var amount = i.Stock;
            if (amount < worst)
                worst = amount;
        }

        return worst < 9999 ? Mathf.FloorToInt(worst) : 0;
    }
    public int CanMakeQuantity()
    {
        float worst = 9999;
        foreach (var i in Items)
        {
            var amount = i.Stock / RoundPerProduct(i.m_neededToProduce);
            if (amount < worst)
                worst = amount;
        }

        return worst < 9999 ? Mathf.FloorToInt(worst) : 0;
    }

    
    static float RoundPerProduct(float _f) {
        // if a product uses a whole number of each material regardless of how small a fraction is required
        //return Mathf.Ceil(_f);
        // if a product can consume fractions of a material
        return _f;
    }

    public List<float> StockSplit()
    {
        var ret = new List<float>();
        float total = GetTotalNeededToProduce();
        if (total > 0f)
        {
            foreach (var i in Items)
                ret.Add(i.m_neededToProduce/total);
        }
        return ret;
    }

    public float GetTotalNeededToProduce()
    {
        var total = 0f;
        foreach (var i in Items)
            total += i.m_neededToProduce;
        return total;
    }

    public int GetTotalPossibleToProduce()
    {
        if (Items.Count == 0)
            return 0;
        var produce = new int[Items.Count];
        for(int i = 0; i < Items.Count; i++)
        {
            var item = Items[i];
            if(item.m_neededToProduce > 0f)
                produce[i] = (int)(item.m_stock / item.m_neededToProduce);
            else
                produce[i] = -1;
        }

        var maxProduce = int.MaxValue;
        foreach(var s in produce)
            if (s >= 0 && s < maxProduce)
                maxProduce = s;
        return maxProduce;
    }
}
