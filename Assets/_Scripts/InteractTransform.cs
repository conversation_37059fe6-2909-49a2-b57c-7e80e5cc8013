using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class InteractTransform : MonoBehaviour {
	public enum Type {
		Door,
		DoorExit,
		DropoffPoint, // note that these are where -workers- would go to drop off and pick up, opposite of ActionInteract which considers where the AI itself would drop or pick
		PickupPoint,
		MillingArea, // area for workers to mill around in, defined by a box collider on this gameobject
		SecondaryDoor, // lower priority door, ignored if a Door exists
		DoorFront,  // specifically the Outer/Exterior position to walk in from
		DamageArea,  // an area any attacker has to be in to damage this object
	}
	public Type m_type;

	private void Awake()
	{
		Init();
	}
	
	private void Start()
	{
		Init();
	}

	private void Init()
	{
		Collider damageAreaCollider = GetDamageArea();
		if (damageAreaCollider != null)
		{
			MADamageArea damageArea =GetComponent<MADamageArea>();
			if (damageArea == null)
			{
				gameObject.AddComponent<MADamageArea>();
			}

			damageAreaCollider.isTrigger = true;
		}
	}
	
	public BoxCollider GetMillingArea() {
		if (m_type == Type.MillingArea) return GetComponent<BoxCollider>();
		return null;
	}


	public static BoxCollider FindMillingArea(GameObject _o) {
		return Find(_o, Type.MillingArea)?.GetMillingArea();
	}
	
	public Collider GetDamageArea() {
		if (m_type == Type.DamageArea) return GetComponent<Collider>();
		return null;
	}
	
	public static Collider FindDamageArea(GameObject _o) {
		return Find(_o, Type.DamageArea)?.GetDamageArea();
	}
	
	public static List<Collider> FindDamageAreas(GameObject _o) {
		List<Collider> areas = new();if(_o == null) return areas;
		List<InteractTransform> outList = new();
		_o.GetComponentsInChildren(outList);
		foreach (var interactTransform in outList)
		{
			if (interactTransform.m_type == Type.DamageArea)
			{
				Collider col = interactTransform.GetComponent<Collider>();
				areas.Add(col);
			}
		}
		return areas;
	}
	
	private static void SanitiseDoors(GameObject _root)
	{
		var baseBlock = _root.GetComponentInChildren<BaseBlock>();
		if (baseBlock == null) return;
		var bld = _root.GetComponent<MABuilding>();
		if (bld == null) return;
		foreach (var it in _root.GetComponentsInChildren<InteractTransform>(true))
		{
			// door interacts are only valid if on the ground floor, facing outwards
			if (it.m_type != Type.Door && it.m_type != Type.SecondaryDoor) continue;
			var block = it.GetComponentInParent<Block>();
			if (block == null) continue;
			var blockTransform = block.transform;
			var valid = true;
			if (blockTransform.localPosition.y > bld.m_buildingRaise + 2f) valid = false;
			else if (baseBlock.IsValidDoorDirection(blockTransform.localPosition, blockTransform.forward) == false) valid = false;
			else if (block.GetComponent<DTDragBlock>() is {} db && db.IsDragging) valid = false;
			it.enabled = valid;
		}
	}

	public static InteractTransform Find(GameObject _root, Type _type, Vector3? _forward = null) {
		if (_type == Type.Door)
			SanitiseDoors(_root);
		var ints = _root.GetComponentsInChildren<InteractTransform>();
		foreach (var i in ints)
			if (i.m_type == _type && i.enabled)
				if (_forward == null || Vector3.Dot(i.transform.forward, _forward.Value) > 0.9f)
					return i;
		if (_forward != null) return Find(_root, _type);
		return null;
	}
}
