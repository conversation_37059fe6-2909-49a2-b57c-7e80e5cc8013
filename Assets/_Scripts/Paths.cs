//#define DEBUG_CLIPS
using System;
using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;

public class PathHandle : MonoBehaviour { }

public static partial class PathManager
{
	public static bool s_blockDataDirty = true;

	static void RestoreSplatArea(Vector3 _min, Vector3 _max)
	{
		int sx = GlobalData.TerrainX(_min.x), ex = GlobalData.TerrainX(_max.x);
		int sz = GlobalData.TerrainZ(_min.z), ez = GlobalData.TerrainZ(_max.z);
		CameraRenderSettings.Me.ResetSplat(sx, sz, ex - sx, ez - sz);
	}

	public static void FillSplatRectangle(Vector3 _corner, float _rot, int _width, int _depth, int _splatIndex, float _margin = .5f)
	{
		var fwd = _rot.EulerY();
		var side = new Vector3(fwd.z, fwd.y, -fwd.x);
		var pos = _corner;
		if (true)
			pos -= fwd * (RoadManager.c_buildingBlockSize * .5f) + side * (RoadManager.c_buildingBlockSize * .5f);
		else
			pos -= fwd * (_depth * .5f) + side * (_width * .5f);
		pos.y = 0;
		Vector3 c1 = pos, c2 = c1 + fwd * _depth, c3 = c2 + side * _width, c4 = c1 + side * _width;
		
		var min = Vector3.Min(Vector3.Min(c1, c2), Vector3.Min(c3, c4));
		var max = Vector3.Max(Vector3.Max(c1, c2), Vector3.Max(c3, c4));
		int sx = Mathf.FloorToInt(min.x) - 1, sz = Mathf.FloorToInt(min.z) - 1;
		int ex = Mathf.CeilToInt(max.x) + 1, ez = Mathf.CeilToInt(max.z) + 1;
		float step = 1f / GlobalData.c_terrainXZScale;
		int splatChannels = CameraRenderSettings.c_SplatChannels;
		for (float z = sz; z <= ez; z += step)
		{
			for (float x = sx; x <= ex; x += step)
			{
				var p = new Vector3(x, 0, z);
				var posToP = p - pos;
				var dx = Vector3.Dot(posToP, side);
				var dz = Vector3.Dot(posToP, fwd);
				if (dx >= -_margin && dx < _width + _margin && dz >= -_margin && dz < _depth + _margin)
				{
					int fx = GlobalData.TerrainX(p.x), fz = GlobalData.TerrainZ(p.z);
					for (int i = 0; i < splatChannels; ++i)
						CameraRenderSettings.Me.SetSplatPoint(fx, fz, i, i == _splatIndex ? 1 : 0);
				}
			}
		}
	}
	
	public static Action m_onGlobalNavGridChange = null;
	
	public static (float3, float3) FillHollowNavRectangle(Vector3 _corner, float _rot, float _width, float _depth, float _outerWidth, byte _valueOuter, byte _valueInner, bool _cornerOffset, Vector3 _dirtyMin, Vector3 _dirtyMax, float _margin = 0, bool _ignorePriorities = false)
	{
		GlobalData.Me.SetNavGenerationFrameToCurrent();
		
		bool changed = false;
		var fwd = _rot.EulerY();

		var job = new Path.FillHollowNavRectangleJob(_corner, fwd, _width, _depth, _outerWidth, _valueOuter, _valueInner, _cornerOffset, _margin, _dirtyMin, _dirtyMax, _ignorePriorities);
		job.Schedule().Complete();
		var minMax = job.GetMinMax();
		m_onGlobalNavGridChange?.Invoke();
		return minMax;
	}

	public static void FillNavRectangle(Vector3 _corner, float _rot, int _width, int _depth, int _inset, byte _value, bool _cornerOffset)
	{
		GlobalData.Me.SetNavGenerationFrameToCurrent();
		var grid = GlobalData.Me.NavGrid;
		var gridStride = GlobalData.Me.NavW;
		var fwd = _rot.EulerY();
		var side = new Vector3(fwd.z, fwd.y, -fwd.x);
		var pos = _corner;
		if (_cornerOffset)
			pos -= fwd * (RoadManager.c_buildingBlockSize * .5f) + side * (RoadManager.c_buildingBlockSize * .5f);
		float step = 1f / GlobalData.c_terrainXZScale;
		for (float y = _inset; y < _depth-_inset; y += step)
		{
			for (float x = _inset; x < _width-_inset; x += step)
			{
				var p = pos + side * x + fwd * y;
				var i = GlobalData.Me.V2I(ref p);
				grid[i] = _value;
				//grid[i+1] = _value;
				//grid[i+gridStride] = _value;
				////grid[i+1+gridStride] = _value;
				//grid[i-1] = _value;
				//grid[i-gridStride] = _value;
			}
		}
	}
	
    public static Vector3 RayIntersectsQuadrClipped(Vector3 _corner, Vector3 _edge1, Vector3 _edge2, Vector3 _point, Vector3 _direction)
    {
        var A = new Vector2(_corner.x, _corner.z);
        var e1 = new Vector2(_edge1.x, _edge1.z);
        var e2 = new Vector2(_edge2.x, _edge2.z);
        var P = new Vector2(_point.x, _point.z);
        var D = new Vector2(_direction.x, _direction.z).normalized;
        
        float det = e1.x * e2.y - e1.y * e2.x;
        if (Mathf.Approximately(det, 0f))
            return _corner;
        
        Vector2 Q = P - A;
        float u0 = (Q.x * e2.y - Q.y * e2.x) / det;
        float v0 = (-Q.x * e1.y + Q.y * e1.x) / det;
        
        float dU = (D.x * e2.y - D.y * e2.x) / det;
        float dV = (-D.x * e1.y + D.y * e1.x) / det;
        
        float tCandidate = 1e23f;
        bool foundCandidate = false;
        
        void TestCandidate(float t, float newU, float newV)
        {
            if (t >= 0f && newU >= 0f && newU <= 1f && newV >= 0f && newV <= 1f && t < tCandidate)
            {
	            tCandidate = t;
                foundCandidate = true;
            }
        }
        
        const float c_epsilon = .001f;
        if (dU * dU > c_epsilon * c_epsilon)
        {
            float t = (0 - u0) / dU;
            float vAtT = v0 + t * dV;
            TestCandidate(t, 0, vAtT);
            
            t = (1 - u0) / dU;
            vAtT = v0 + t * dV;
            TestCandidate(t, 1, vAtT);
        }
        
        if (dV * dV > c_epsilon * c_epsilon)
        {
            float t = (0 - v0) / dV;
            float uAtT = u0 + t * dU;
            TestCandidate(t, uAtT, 0);
            
            t = (1 - v0) / dV;
            uAtT = u0 + t * dU;
            TestCandidate(t, uAtT, 1);
        }
        
        if (foundCandidate)
        {
            Vector2 intersect2D = P + tCandidate * D;
            return new Vector3(intersect2D.x, _point.y, intersect2D.y);
        }
        return _corner;
    }

	public const float c_buildingMargin = 3;
	public static (Vector3, Vector3) FillNavBuilding(Vector3 _pos, float _rot, int _width, int _depth,  int _value, bool _fillDoorRoute, Vector3 _doorCenter, Vector3 _dirtyMin, Vector3 _dirtyMax, bool _ignorePriorities = false)
	{
		const int c_buildingBlockSize = 4;
		float finalWidth = _width * c_buildingBlockSize, finalDepth = _depth * c_buildingBlockSize;
		byte innerValue = _fillDoorRoute ? (byte) (int) GlobalData.NavCostTypes.BuildingInner : (byte)_value;
		var (min, max) = FillHollowNavRectangle(_pos, _rot, finalWidth, finalDepth, 1.5f / GlobalData.c_terrainXZScale, (byte)_value, innerValue, true, _dirtyMin, _dirtyMax, c_buildingMargin, _ignorePriorities);
		if (_fillDoorRoute)
		{
			var (minPath, maxPath) = FillHollowNavRectangle(_doorCenter, _rot, 1, 1, 0, (byte) (int) GlobalData.NavCostTypes.EnterBuilding, (byte) (int) GlobalData.NavCostTypes.EnterBuilding, false, _dirtyMin, _dirtyMax);
			min = math.min(min, minPath);
			max = math.max(max, maxPath);
		}
		return (min, max);
	}
	
	
	[System.Serializable]
	public partial class Path
	{
		const float c_pathWidth = 3;
		const float c_MinLength = 3;
		const float c_PathHeightOffset = 0.3f;
		const float c_BlockAvoidanceRadiusSqr = 2 * BuildingPlacementManager.c_buildingBlockSize * BuildingPlacementManager.c_buildingBlockSize;
		const float c_BreakBlockAvoidanceRadiusSqr = 3 * 3;
		static float[] c_PerTypeBlockAvoidanceRadiusSqr = {
			0,
			c_BlockAvoidanceRadiusSqr,
			c_BreakBlockAvoidanceRadiusSqr,
			c_BreakBlockAvoidanceRadiusSqr, // non-nav-blocking, same radius as nav-blocking
		};

		public int m_roadType = 0;
		public int FinishedType => m_roadType < 0 ? (-m_roadType - 1) : m_roadType;
		public List<float3> m_path = new();
		private float3 m_sharedStart = -1f;
		private float3 m_sharedEnd = -1f;
		private NativeishList<float3> m_pathSmoothed = new(256);
		private NativeishList<float> m_bridgeBoundaries = new(256);
		private bool m_smoothedDirty = true;
		public bool m_isPlayerPath = false;
		public bool m_isConstructionValid = false;

		[SerializeField]
		private float m_finishedDistance = 0;

		private GameObject m_handles;
		private GameObject m_visuals;
		private List<Mesh> m_ownedMeshes = new();
		private Vector3 m_minExtent, m_maxExtent;
		public Vector3 MinExtent => m_minExtent;
		public Vector3 MaxExtent => m_maxExtent;

		public void CopyFrom(Path _from, bool _keepCurrentFinishedDistance = false)
		{
			m_roadType = _from.m_roadType;
			m_path = new();
			for (int i = 0; i < _from.m_path.Count; ++i) m_path.Add(_from.m_path[i]);
			m_isPlayerPath = _from.m_isPlayerPath;
			if (_keepCurrentFinishedDistance == false)
				m_finishedDistance = _from.m_finishedDistance;
			m_smoothedDirty = true;
			m_visuals = _from.m_visuals;
		}
		
		public int Count => m_path.Count;
		private Vector3 m_splatChangeMin = Vector3.one * 1e23f, m_splatChangeMax = Vector3.one * -1e23f;
		private Vector3 m_startSplatMin, m_startSplatMax;

		public Path OnDestroy()
		{
			DestroyVisuals();
			m_pathSmoothed.Dispose();
			m_bridgeBoundaries.Dispose();
			if (m_locToT.IsCreated && !m_locToTDisposed)
				m_locToT.Dispose();
			m_locToTDisposed = true;
			m_smoothedDirty = false;
			return this;
		}

		public void DestroyVisuals()
		{
			GameObject.Destroy(m_handles);	
			m_handles = null;
			GameObject.Destroy(m_visuals);
			m_visuals = null;
		}

		public void RemoveAt(int _index)
		{
			m_path.RemoveAt(_index);
			m_smoothedDirty = true;
		}

		public void Add(float3 _add)
		{
			m_path.Add(_add);
			m_smoothedDirty = true;
		}

		public static float SanitiseT(float _t)
		{
			// don't let _t go too far below 0 or above 1
			// use log to shrink, offset such that the curves meet around .2
			const float c_alpha = .2f;
			const float c_tAtAlpha = .18154f;
			if (_t < -c_tAtAlpha) return -math.log10(-_t + c_alpha) - (.8f - c_alpha);
			if (_t > 1 + c_tAtAlpha) return math.log10(_t - 1 + c_alpha) + 1 + (.8f - c_alpha);
			return _t;
		}

		static float3 CatmullRom(NativeishList<float3> _list, float _t)
		{
			_t = SanitiseT(_t);
			_t *= (_list.Count - 4);
			int index = (int) _t;
			index = Mathf.Clamp(index, 0, (_list.Count - 4));
			_t -= index;
			return CatmullRom(_list, index + 1, _t);
		}

		static float3 CatmullRom(NativeishList<float3> _list, int _index0, float _t)
		{
			int countM1 = _list.Length - 1;
			if (_index0 < 0)
			{
				_index0 = 0;
				_t = 0;
			}
			if (_index0 > countM1)
			{
				_index0 = countM1;
			}
			float t1 = _t;
			float t2 = t1 * t1;
			float t3 = t2 * t1;
			int indexM1 = _index0 - 1;
			if (indexM1 < 0) indexM1 = 0;
			int indexP1 = _index0 + 1;
			if (indexP1 > countM1) indexP1 = countM1;
			int indexP2 = _index0 + 2;
			if (indexP2 > countM1) indexP2 = countM1;
			const float Tau = 0.5f;//PathSet.s_tension;
			float kTt1 = Tau * t1, kTt2 = Tau * t2, kTt3 = Tau * t3;
			float k3mTt2 = t2 + t2 + t2 - kTt2, k2mTt3 = t3 + t3 - kTt3; 
			float mM1 = kTt2 + kTt2 - kTt1 - kTt3;//(-t1 + 2 * t2 - t3) * Tau;
			float m0 = 1 - k3mTt2 + k2mTt3;//1 + (Tau - 3) * t2 + (2 - Tau) * t3;
			float mP1 = kTt1 + k3mTt2 - kTt2 - k2mTt3;//Tau * t1 + (3 - Tau * 2) * t2 + (Tau - 2) * t3;
			float mP2 = kTt3 - kTt2;//(-t2 + t3) * Tau;
			//return _list[indexM1] * mM1 + _list[_index0] * m0 + _list[indexP1] * mP1 + _list[indexP2] * mP2;
			var lM1 = _list[indexM1];
			var l0 = _list[_index0];
			var lP1 = _list[indexP1];
			var lP2 = _list[indexP2];
			var cr = new float3(lM1.x * mM1 + l0.x * m0 + lP1.x * mP1 + lP2.x * mP2,
								lM1.y * mM1 + l0.y * m0 + lP1.y * mP1 + lP2.y * mP2,
								lM1.z * mM1 + l0.z * m0 + lP1.z * mP1 + lP2.z * mP2);
			return cr;
		}

		static float CatmullRomLength(NativeishList<float3> _list)
		{
			var job = new CatmullLengthJob(ref _list);
			job.Schedule().Complete();
			return job.GetOutput();
		}

		const float c_stepFwd = .0001f;
		static (float3, float3, float3) CatmullRomPosFwdSide(NativeishList<float3> _list, float _t)
		{
			var thisPos = CatmullRom(_list, _t);
			var thisFwd = CatmullRom(_list, _t + c_stepFwd) - thisPos;
			thisFwd.y = 0;
			var thisSide = new float3(-thisFwd.z, 0, thisFwd.x);
			return (thisPos, math.normalize(thisFwd), math.normalize(thisSide));
		}

		public (float3, float3, float3) GetPosFwdSide(float _t)
		{
			UpdateSmoothed();
			return CatmullRomPosFwdSide(m_pathSmoothed, _t);
		}

		public float3 GetPoint(float _t)
		{
			UpdateSmoothed();
			return CatmullRom(m_pathSmoothed, _t);
		}

		public static bool NormaliseIntersection(ref float3 _fwd1, float3 _side2)
		{
			if (math.dot(_fwd1, _side2) < 0)
			{
				_fwd1 = -_fwd1;
				return true;
			}
			return false;
		}
		
		private static void DropPathPoint(float3 _pos, float _scale, int _state, Transform _parent, Vector3 _forward)
		{
			var go = GameObject.Instantiate(GlobalData.Me.m_roadSegmentEndpointEditPrefab);
			Transform tr = go.transform;
			tr.position = _pos;
			tr.LookAt((Vector3)_pos + _forward, Vector3.up);
			tr.localScale = Vector3.one * _scale;
			tr.SetParent(_parent);
			go.AddComponent<PathHandle>();
			SetHandleState(go, _state);
		}
		private static void SetHandleState(GameObject go, int _state)
		{
			if (_state == 1)
				go.GetComponentInChildren<MeshRenderer>().material = GlobalData.Me.m_roadSegmentEndpointEditMaterialSelected;
			else if (_state == 2)
				go.GetComponentInChildren<MeshRenderer>().material = GlobalData.Me.m_negativeSelectionMaterial;
		}

		const float c_snapDistance = 2;
		public bool IsCycle => m_path.Count > 4 && math.lengthsq((m_path[0] - m_path[^1]).xz) < c_snapDistance * c_snapDistance;

		static NativeishList<float3> s_temp = new(256);

		public void Reverse()
		{
			m_smoothedDirty = true;
			m_path.Reverse();
			UpdateSmoothed();
		}

		private void CheckDegenerateEnd()
		{
			if (m_path.Count < 2) return;
			var last = m_path[^1];
			var lastButOne = m_path[^2];
			if (math.lengthsq((last - lastButOne).xz) < .01f * .01f)
				m_path.RemoveAt(m_path.Count - 1);
		}

		public void UpdateSmoothed()
		{
			if (m_smoothedDirty == false) return;
			
			CheckDegenerateEnd();
			
			m_renderMesh = null;
			m_smoothedDirty = false;
			m_cachedLength = 0;
			m_pathSmoothed.Reset(m_path.Count + 4);
			if (m_path.Count < 2)
			{
				if (m_path.Count > 0)
					m_minExtent = m_maxExtent = m_path[0];
				else
					m_maxExtent = -(m_minExtent = Vector3.one * 1e23f);
				return;
			}

			var cleanedPath = new NativeishList<float3>(m_path.Count);
			bool sharedStart = m_sharedStart.y != -1;
			bool sharedEnd = m_sharedEnd.y != -1;

			if (m_path.Count == 2)
			{
				if (math.lengthsq((m_path[0] - m_path[1]).xz) < .1f * .1f)
					return;

				var first = m_path[0];
				var fwd = m_path[1] - first;
				if (math.lengthsq(fwd) < c_MinLength * c_MinLength)
					fwd = math.normalize(fwd) * c_MinLength;
				cleanedPath.Add(first);
				cleanedPath.Add(first + fwd);
			}
			else
			{
				s_temp.Reset(m_path.Count + 3);

				if (IsCycle)
				{
					s_temp.Add(m_path[^2]);
					for (int i = 0; i < m_path.Count; ++i)
						s_temp.Add(m_path[i]);
					s_temp[^1] = m_path[0];
					s_temp.Add(m_path[1]);
					s_temp.Add(m_path[2]);
				}
				else
				{
					var last = m_path[^1];
					var fwd = math.normalize(last - m_path[^2]) * Set.m_stepSize;
					var first = m_path[0];
					var back = math.normalize(first - m_path[1]) * Set.m_stepSize;

					s_temp.Add(first + back);
					for (int i = 0; i < m_path.Count; ++i)
						s_temp.Add(m_path[i]);
					s_temp.Add(last + fwd);
					s_temp.Add(last + 2 * fwd);
				}
				if (sharedStart)
					s_temp[0] = m_sharedStart;
				if (sharedEnd)
					s_temp[^1] = m_sharedEnd;
				float tStep = .5f * LocStep;
				float length = CatmullRomLength(s_temp);
				int numSegs = Mathf.CeilToInt(length / Set.m_stepSize);
				float segLength = length / numSegs;
				float3 prev = CatmullRom(s_temp, 0);
				float distCounter = segLength;
				cleanedPath.Add(prev);
				for (float t = tStep; t < 1 - 4 * tStep; t += tStep)
				{
					float3 current = CatmullRom(s_temp, t);
					distCounter -= math.length((prev - current).xz);
					if (distCounter <= 0)
					{
						distCounter += segLength;
						cleanedPath.Add(prev);
					}
					prev = current;
				}
				cleanedPath.Add(CatmullRom(s_temp, 1));
			}

			float3 minExtent;
			float3 maxExtent;
			minExtent = maxExtent = cleanedPath[0];
			for (int i = 1; i < cleanedPath.Count; i++)
			{
				ref var pos = ref cleanedPath.RefAt(i);
				minExtent = math.min(minExtent, pos);
				maxExtent = math.max(maxExtent, pos);
			}
			float maxRadius = Set.m_heightWidthOuter * (1.0f + Set.m_splatOuterRandomness);
			m_minExtent = new Vector3(math.floor(minExtent.x - maxRadius), math.floor(minExtent.y), math.floor(minExtent.z - maxRadius));
			m_maxExtent = new Vector3(math.ceil(maxExtent.x + maxRadius), math.ceil(maxExtent.y), math.ceil(maxExtent.z + maxRadius));

			if (IsCycle)
			{
				m_pathSmoothed.Add(cleanedPath[^2]);
				m_pathSmoothed.AddRange(cleanedPath);
				m_pathSmoothed[^1] = cleanedPath[0];
				m_pathSmoothed.Add(cleanedPath[1]);
				m_pathSmoothed.Add(cleanedPath[2]);
			}
			else
			{
				var last = cleanedPath[^1];
				var fwd = last - cleanedPath[^2];
				m_pathSmoothed.Add(cleanedPath[0] + (cleanedPath[0] - cleanedPath[1]));
				m_pathSmoothed.AddRange(cleanedPath);
				m_pathSmoothed.Add(last + fwd);
				m_pathSmoothed.Add(last + fwd * 2);
			}
			if (sharedStart)
				m_pathSmoothed[0] = m_sharedStart;
			if (sharedEnd)
				m_pathSmoothed[^1] = m_sharedEnd;

			s_temp.Dispose();
			cleanedPath.Dispose();
			
			if (m_isConstructionValid == false)
			{
				if (m_isPlayerPath == false)
					m_finishedDistance = CatmullRomLength(m_pathSmoothed);
				m_isConstructionValid = true;
			}
			
			var len = TotalLength();
			if (math.isnan(m_finishedDistance))
				m_finishedDistance = len;
			else if (RoadManager.Me.m_pathSet.IsDragging(this) == false)
				if (m_finishedDistance > len * .98f)
					m_finishedDistance = len;
		}

		public bool BoundsOverlap(Path _path)
		{
			return Overlaps(_path.m_minExtent, _path.m_maxExtent);
		}

		public bool Overlaps(Vector3 _min, Vector3 _max)
		{
			var d = (m_maxExtent + m_minExtent) - (_max + _min);
			var e = (m_maxExtent - m_minExtent) + (_max - _min);
			return d.x * d.x <= e.x * e.x && /*d.y * d.y <= e.y * e.y && */d.z * d.z <= e.z * e.z;
		}

		public static int LocAtT(float _t, float3 _min, NativeishList<float3> _path)
		{
			var pos = (CatmullRom(_path, _t) - _min) * 1;
			int locX = (int)(pos.x + 1024), locZ = (int)(pos.z + 1024);
			return locX + locZ * 65536;
		}

		public bool IsNonCut => Set.m_isNonCutter;
		public bool IsNavCutOnly => Set.m_splatOnly;
		public bool NoNavBlockInterrupt => Set.m_noNavBlockerInterrupt;
		public bool IsNavBlocker => Set.m_blocksNavigation;
		public bool IsNavBlockerNoPush => Set.m_blocksNavigation && Set.m_pushThroughable == false;
		public RoadSet Set => RoadManager.Me.m_roadSets[FinishedType];

		public static int NextPowerOfTwo(int _n)
		{
			--_n;
			_n |= _n >> 1;
			_n |= _n >> 2;
			_n |= _n >> 4;
			_n |= _n >> 8;
			return ++_n;
		}

		public float LocStep => .125f / NextPowerOfTwo(Count);

		const int c_intersectionType_RemoveBlocker = 0;
		const int c_intersectionType_Left = 1;
		const int c_intersectionType_Right = 2;
		const int c_intersectionType_IsPrimary = 4;
		const int c_intersectionType_IsSameType = 8;
		const int c_intersectionType_IsInLine = 16;
		const int c_intersectionType_IsOtherLineEnd = 32;
		const int c_intersectionType_IsCorrupted = 64;
		const int c_intersectionType_IsBridge = 128;
		const int c_intersectionType_NavBlockOther = 256;
		const int c_intersectionType_OtherIndexShift = 10;
		const int c_intersectionType_FlagsMask = (1 << c_intersectionType_OtherIndexShift) - 1; 
		
		private Vector4 GenerateIntersectionData(float _tThis, float _tOther, float _step, float _stepOther, int _primaryBit, int _sameTypeBit, bool _reversed, bool _navBlockThis, bool _navBlockOther, bool _thisNonCut, bool _otherNonCut, bool _getsCorrupted, bool _isPrimaryNonCutter, Vector3 _fwdOther, int _otherIndex)
		{
			int dirs = c_intersectionType_Left | c_intersectionType_Right;
			var eitherNonCut = _thisNonCut || _otherNonCut;
			if (_tThis >= _step * 8 && _tThis <= 1 - _step * 8) dirs |= _primaryBit;
			if (_navBlockThis && _isPrimaryNonCutter == false && _otherNonCut == false && _navBlockOther == false) dirs = c_intersectionType_RemoveBlocker; // get rid of this entirely around this point (something removed a hedge, for example)
			else if (_getsCorrupted) dirs |= c_intersectionType_IsCorrupted;
			else if (_navBlockOther) dirs = c_intersectionType_NavBlockOther;
			else if (eitherNonCut) dirs = c_intersectionType_NavBlockOther;
			else if (_fwdOther.y > 0) dirs |= (_tOther > 0.5f) ? c_intersectionType_IsInLine | c_intersectionType_IsOtherLineEnd : c_intersectionType_IsInLine;
			else if (_tOther < _stepOther * 8) dirs = _reversed ? c_intersectionType_Right | c_intersectionType_IsPrimary : c_intersectionType_Left | c_intersectionType_IsPrimary;
			else if (_tOther > 1 - _stepOther * 8) dirs = _reversed ? c_intersectionType_Left | c_intersectionType_IsPrimary : c_intersectionType_Right | c_intersectionType_IsPrimary;
			return new Vector4(_fwdOther.x, dirs | _sameTypeBit | (_otherIndex << c_intersectionType_OtherIndexShift), _fwdOther.z, _tThis);
		}		
		private void AddIntersection(List<Vector4> _intersections, float _tThis, float _tOther, float _step, float _stepOther, int _primaryBit, int _sameTypeBit, bool _reversed, bool _navBlockThis, bool _navBlockOther, bool _thisNonCut, bool _otherNonCut, bool _getsCorrupted, bool _isPrimaryNonCutter, Vector3 _fwdOther, int _otherIndex)
		{
			_intersections.Add(GenerateIntersectionData(_tThis, _tOther, _step, _stepOther, _primaryBit, _sameTypeBit, _reversed, _navBlockThis, _navBlockOther, _thisNonCut, _otherNonCut, _getsCorrupted, _isPrimaryNonCutter, _fwdOther, _otherIndex));
		}

		private NativeParallelHashMap<int, float> m_locToT;
		private bool m_locToTDisposed;
		private List<Vector4> m_selfIntersections = null; public List<Vector4> SelfIntersections => m_selfIntersections;
		public void PrepareIntersectionData(bool _isFinal)
		{
			const float hashMapSpace = 1.5f;
			UpdateSmoothed();
			m_selfIntersections = new();
			if (IsValid == false) return;
			var step = LocStep;

			var numSteps = (int)(1 / step);
			var capacity = (int)(hashMapSpace * 4 * numSteps);
			if (m_locToT.IsCreated && !m_locToTDisposed)
			{
				m_locToT.Dispose();
				Debug.LogError("m_locToT was not disposed before PrepareIntersectionData");
			}
			m_locToTDisposed = false;
			m_locToT = new NativeParallelHashMap<int, float>(capacity, Allocator.Persistent);

#if true
			var job = new CalcPathSelfIntersectionsJob(_isFinal, step, m_minExtent, ref m_locToT, ref m_pathSmoothed);
			job.Schedule().Complete();
			int count = job.GetOutput(out var ts, out var fwds, out var sideRevs);
			for (int i = 0; i < count; ++i)
			{
				AddIntersection(m_selfIntersections, ts[i].x, ts[i].y, step, step, 4, 8, sideRevs[i], false, false, false, false, false, false, fwds[i], 0);
			}
			job.Clean();
#else
			for (float t = 0; t <= 1; t += step)
			{
				int loc = LocAtT(t, m_minExtent, m_pathSmoothed);
				
				if (m_locToT.TryGetValue(loc, out var tPrevious))
				{
					if (t - tPrevious > step * 16 && (tPrevious > step * 8 || t < 1 - step * 8))
					{
						(var tFirst, var tSecond) = PolishIntersectionNear(m_pathSmoothed, tPrevious, m_pathSmoothed, t);
						(var otherPos, var otherFwd, var otherSide) = CatmullRomPosFwdSide(m_pathSmoothed, tFirst);
						(var thisPos, var thisFwd, var thisSide) = CatmullRomPosFwdSide(m_pathSmoothed, tSecond);
						bool sideReversedOther = NormaliseIntersection(ref otherFwd, thisSide);
						bool sideReversedThis = NormaliseIntersection(ref thisFwd, otherSide);
							
						AddIntersection(m_selfIntersections, tSecond, tFirst, step, step, 4, 8, sideReversedOther, false,false,false, false, false, false, otherFwd);
						//Debug.LogError($"AddIntSelf {GameManager.Me.m_state.m_paths.IndexOf(this)} {tSecond} {tFirst} step {step} - {(int)_intersections[_intersections.Count-1].y}");
						if (_isFinal)
							AddIntersection(m_selfIntersections, tFirst, tSecond, step, step, 4, 8, sideReversedThis, false,false,false, false, false, false, thisFwd);
						//Debug.LogError($"AddIntSelf {GameManager.Me.m_state.m_paths.IndexOf(this)} {tFirst} {tSecond} step {step} - {(int)_intersections[_intersections.Count-1].y}");

						t += step * 8; // don't catch multiple intersections at the same point
					}
				}
				else
				{
					m_locToT[loc] = t;
					if (!m_locToT.ContainsKey(loc + 1))
						m_locToT[loc + 1] = t;
					if (!m_locToT.ContainsKey(loc + 65536))
						m_locToT[loc + 65536] = t;
					if (!m_locToT.ContainsKey(loc + 65536 + 1))
						m_locToT[loc + 65536 + 1] = t;
				}
			}
#endif
		}
		
		void DebugLocToT(Path _other = null)
		{
			int minX = 10000000, maxX = -10000000, minZ = 10000000, maxZ = -10000000;
			foreach (var t in m_locToT)
			{
				int x = t.Key & 0xFFFF, z = t.Key >> 16;
				minX = math.min(x, minX);
				minZ = math.min(z, minZ);
				maxX = math.max(x, maxX);
				maxZ = math.max(z, maxZ);
			}
			int w = maxX + 1 - minX, h = maxZ + 1 - minZ;
			var ss = new char[h,w];
			for (int z = 0; z < h; ++z)
				for (int x = 0; x < w; ++x)
					ss[z,x] = '-';
			foreach (var t in m_locToT)
			{
				int x = t.Key & 0xFFFF, z = t.Key >> 16;
				x -= minX; z -= minZ;
				char v = (char) ((int) (t.Value * 9.999f) + '0');
				ss[z,x] = v;
			}

			if (_other != null)
			{
				foreach (var t in _other.m_locToT)
				{
					int x = t.Key & 0xFFFF, z = t.Key >> 16;
					x += (int)(_other.m_minExtent.x - m_minExtent.x);
					z += (int)(_other.m_minExtent.z - m_minExtent.z);
					x -= minX; z -= minZ;
					if (x < 0 || x >= w || z < 0 || z >= h) continue;
					char v = (char) ((int) (t.Value * 9.999f) + 'A');
					if (ss[z, x] == '-')
						ss[z, x] = v;
					else
						ss[z, x] = (char)(ss[z, x] + 'Z' - '9');
				}
			}

			var s = "";
			for (int z = 0; z < h; ++z)
			{
				for (int x = 0; x < w; ++x)
				{
					s += ss[z, x];
				}
				s += "\n";
			}
			Debug.LogError(s);
		}

		void DebugLocToT2(Path _other = null)
		{
			int minX = 10000000, maxX = -10000000, minZ = 10000000, maxZ = -10000000;
			foreach (var t in _other.m_locToT)
			{
				int x = t.Key & 0xFFFF, z = t.Key >> 16;
				minX = math.min(x, minX);
				minZ = math.min(z, minZ);
				maxX = math.max(x, maxX);
				maxZ = math.max(z, maxZ);
			}
			int w = maxX + 1 - minX, h = maxZ + 1 - minZ;
			var ss = new char[h, w];
			for (int z = 0; z < h; ++z)
			for (int x = 0; x < w; ++x)
				ss[z, x] = '-';
			foreach (var t in _other.m_locToT)
			{
				int x = t.Key & 0xFFFF, z = t.Key >> 16;
				x -= minX;
				z -= minZ;
				char v = (char) ((int) (t.Value * 9.999f) + '0');
				ss[z, x] = v;
			}

			var step = LocStep;
			for (float t = 0; t <= 1; t += step)
			{
				int loc = LocAtT(t, m_minExtent, m_pathSmoothed);
				int x = loc & 0xFFFF, z = loc >> 16;
				x += (int) (m_minExtent.x - _other.m_minExtent.x);
				z += (int) (m_minExtent.z - _other.m_minExtent.z);
				x -= minX;
				z -= minZ;
				if (x < 0 || x >= w || z < 0 || z >= h) continue;
				char v = (char) ((int) (t * 9.999f) + 'A');
				if (ss[z, x] == '-')
					ss[z, x] = v;
				else if (ss[z, x] >= '0' && ss[z, x] <= '9')
					ss[z, x] = (char) (v + 'Q' - 'A');
			}
			var s = "";
			for (int z = 0; z < h; ++z)
			{
				for (int x = 0; x < w; ++x)
				{
					s += ss[z, x];
				}
				s += "\n";
			}
			Debug.LogError(s);
		}

		public void FinishIntersectionData()
		{
			if (IsValid == false) return;
			if (m_locToTDisposed)
				Debug.LogError("m_locToT was disposed before FinishIntersectionData");
			else
				m_locToT.Dispose();
			m_locToTDisposed = true;
			m_selfIntersections = null;
		}

		public static bool LineSegmentAABBIntersection2D(Vector3 _segmentStart, Vector3 _segmentEnd, Vector3 _aabbMin, Vector3 _aabbMax)
		{
			float minX = _segmentStart.x < _segmentEnd.x ? _segmentStart.x : _segmentEnd.x;
			float maxX = _segmentStart.x + _segmentEnd.x - minX;
			if (maxX > _aabbMax.x) maxX = _aabbMax.x;
			if (minX < _aabbMin.x) minX = _aabbMin.x;
			if (minX > maxX) return false;
			float minY = _segmentStart.z;
			float maxY = _segmentEnd.z;
			float dx = _segmentEnd.x - _segmentStart.x;
			if (dx * dx > .001f * .001f) 
			{
				float grad = (_segmentEnd.z - _segmentStart.z) / dx;
				float inter = _segmentStart.z - grad * _segmentStart.x;
				minY = grad * minX + inter;
				maxY = grad * maxX + inter;
			}
			if (minY > maxY) (minY, maxY) = (maxY, minY);
			if (maxY > _aabbMax.z) maxY = _aabbMax.z;
			if (minY < _aabbMin.z) minY = _aabbMin.z;
			if (minY > maxY) return false;
			return true;
		}
		
		public (float, float) FindIntersection(Vector3 _origin, Vector3 _forward, float maxT)
		{
			UpdateSmoothed();
			if (IsValid == false) return (-1, -1);
			if (LineSegmentAABBIntersection2D(_origin, _origin + _forward * maxT, m_minExtent, m_maxExtent) == false) return (-1, -1);
			float dt = LocStep;
			var job = new CalcPathRayIntersectionsJob(_origin, _forward, dt, maxT, ref m_pathSmoothed);
			job.Schedule().Complete();
			var res = job.GetOutput();
			return (res.x, res.y);
			/*
			float bestT = maxT, bestU = -1;
			var prevPos = GetPoint(0);
			for (float t = dt; t <= 1; t += dt)
			{
				var nextPos = GetPoint(t);
				Utility.LineSegmentIntersectXZ(_origin, _origin + _forward, prevPos, nextPos, out var tRay, out var tPath, true);
				if (tRay > 0 && tRay < bestT && tPath >= 0 && tPath <= 1)
				{
					bestT = tRay;
					bestU = t - dt * (1 - tPath);
				}
				prevPos = nextPos;
			}
			return (bestT, bestU);*/
		}

		public List<Vector4> FindAllIntersections(List<Path> _paths, bool _isFinal, ref NativeishList<float2> _buildingTs, bool _navOnly = false)
		{
			m_sharedStart = m_sharedEnd = -1f;
			PrepareIntersectionData(_isFinal);
			List<Vector4> intersections = SelfIntersections;
			if (IsValid == false) return intersections;
			List<Path> relevantPaths = new();
			for (int i = 0; i < _paths.Count; ++i)
			{
				var path = _paths[i];
				if (path.BoundsOverlap(this) && path != this)
				{
					relevantPaths.Add(path);
					SnapEnds(path, intersections);
					path.PrepareIntersectionData(_isFinal);
				}
			}
			for (int i = 0; i < relevantPaths.Count; ++i)
			{
				var path = relevantPaths[i];
				Intersections(path, intersections);
			}
			if (Set.m_blocksNavigation)
			{
				var job = new CalcBuildingIntersectionsJob(LocStep, c_PerTypeBlockAvoidanceRadiusSqr, m_pathSmoothed, RoadManager.Me.m_pathSet.m_blockPositions, _buildingTs.GetBackingArray(), _navOnly);
				job.Schedule().Complete();
				int count = job.GetOutput();
				_buildingTs.SetPointer(count);
			}
			for (int i = 0; i < relevantPaths.Count; ++i)
				relevantPaths[i].FinishIntersectionData();
			FinishIntersectionData();
			return intersections;
		}

		public bool Intersections(Path _other, List<Vector4> _intersections)
		{
			UpdateSmoothed();
			if (IsValid == false) return false;
			if (this == _other) return false;
			//if ((_other.IsNavCutOnly && IsNavBlocker == false) || (IsNavCutOnly && _other.IsNavBlocker == false)) return false;
			if ((_other.NoNavBlockInterrupt && IsNavBlockerNoPush) || (NoNavBlockInterrupt && _other.IsNavBlockerNoPush)) return false;
			if (IsNavBlocker && _other.IsNavBlocker) return false;
			_other.UpdateSmoothed();
			if (_other.IsValid == false) return false;
			if (BoundsOverlap(_other) == false) return false;

			var otherIndex = OtherIndex(_other);
			int primaryBit = RoadManager.Me.m_pathSet.IsPrimary(this, _other) ? c_intersectionType_IsPrimary : 0;
			int sameTypeBit = FinishedType == _other.FinishedType ? c_intersectionType_IsSameType : 0;
			var step = LocStep;
			var stepOther = _other.LocStep;
			var otherMin = _other.m_minExtent;
			var otherLocToT = _other.m_locToT;
			var job = new CalcPathIntersectionsJob(step, otherMin, ref otherLocToT, ref m_pathSmoothed, ref _other.m_pathSmoothed);
			job.Schedule().Complete();
			int numIntersections = job.GetOutput(out var ts, out var otherFwds, out var sideRevs);

			Vector4 addToEnd = Vector4.zero;
			for (int i = 0; i < numIntersections; i++)
			{
				float tThis = ts[i].x;
				float tOther = ts[i].y;
				var otherFwd = otherFwds[i];
				float tol = 4 * step;
				float tolOther = 4 * stepOther;
				bool sideReversed = sideRevs[i];
				if ((tOther < tolOther || tOther > 1 - tolOther) && (tThis < tol || tThis > 1 - tol))
				{
					continue;
				}
				var isBridge = Set.m_blocksNavigation == false && _other.Set.m_blocksNavigation && _other.Set.m_createSurfaceMaterial != null; 
				var intData = GenerateIntersectionData(tThis, tOther, step, stepOther, primaryBit, sameTypeBit, sideReversed, IsNavBlocker, _other.IsNavBlocker, IsNonCut, _other.IsNonCut, Set.m_isCorruptable && _other.Set.m_corruptOthers, Set.m_createSurfaceMaterial != null, otherFwd, otherIndex);
				if (isBridge) intData.y += c_intersectionType_IsBridge;
				_intersections.Add(intData);
				if (IsCycle)
				{
					if (tThis < step * 4)
						addToEnd = intData + new Vector4(0,0,0,1);
					else if (tThis > 1 - step * 4)
						_intersections.Insert(0, intData + new Vector4(0,0,0,-1));
				}
			}
			if (addToEnd.sqrMagnitude > 0) _intersections.Add(addToEnd);
			ts.Dispose();
			otherFwds.Dispose();
			sideRevs.Dispose();
			return numIntersections > 0;
		}

		public static int OtherIndex(Path _other) => RoadManager.Me.m_pathSet.CurrentPaths.IndexOf(_other);
		public bool SnapEnds(Path _other, List<Vector4> _intersections, bool _hasTriggered = false)
		{
			if (_other.IsValid == false) return false;
			int otherIndex = OtherIndex(_other);
			const float endSnapThreshhold = 1f;
			var (thisStart, thisEnd) = (m_path[0], m_path[^1]);
			var (otherStart, otherEnd) = (_other.m_path[0], _other.m_path[^1]);
			float tThis = 0, tOther = 0;
			bool snapped = false;
			if (math.lengthsq(thisStart - otherStart) < endSnapThreshhold * endSnapThreshhold)
			{
				m_path[0] = otherStart;
				m_sharedStart = _other.m_path[1];
				
				snapped = true;
			}
			else if (math.lengthsq(thisStart - otherEnd) < endSnapThreshhold * endSnapThreshhold)
			{
				m_path[0] = otherEnd;
				m_sharedStart = _other.m_path[^2];
				tOther = 1;
				snapped = true;
			}
			if (math.lengthsq(thisEnd - otherStart) < endSnapThreshhold * endSnapThreshhold)
			{
				m_path[^1] = otherStart;
				m_sharedEnd = _other.m_path[1];
				tThis = 1;
				snapped = true;
			}
			else if (math.lengthsq(thisEnd - otherEnd) < endSnapThreshhold * endSnapThreshhold)
			{
				m_path[^1] = otherEnd;
				m_sharedEnd = _other.m_path[^2];
				tThis = tOther = 1;
				snapped = true;
			}
			if (snapped)
			{
				m_smoothedDirty = true;
				if (!_hasTriggered)
				{
					var (_, fwd, _) = CatmullRomPosFwdSide(_other.m_pathSmoothed, tOther);
					fwd.y = 1;
					AddIntersection(_intersections, tThis, tOther, LocStep, _other.LocStep, RoadManager.Me.m_pathSet.IsPrimary(this, _other) ? 4 : 0, 
									FinishedType == _other.FinishedType ? 8 : 0, false, IsNavBlocker, _other.IsNavBlocker, IsNonCut, _other.IsNonCut, Set.m_isCorruptable && _other.Set.m_corruptOthers, Set.m_createSurfaceMaterial != null, fwd, otherIndex);
					_other.SnapEnds(this, _intersections, true);
				}
			}
			return snapped;
		}

		public static (float, float) PolishIntersectionNear(NativeishList<float3> _pathA, float _posA,	NativeishList<float3> _pathB, float _posB)
		{
			var tThis = _posA;
			var tOther = _posB;
			const int c_maxPolishPasses = 10;
			for (int polishPass = 0; polishPass < c_maxPolishPasses; ++polishPass)
			{
				var thisPos = CatmullRom(_pathA, tThis);
				var otherPos = CatmullRom(_pathB, tOther);
				if (math.lengthsq((thisPos - otherPos).xz) < .001f * .001f) break;
				
				thisPos[1] = 0;
				// polish t/tThis
				var distance = math.length((thisPos - otherPos).xz);
				var tForDistanceThis = 3 * distance / (8 * _pathA.Count);
				var tForDistanceOther = 3 * distance / (8 * _pathB.Count);
				var tThisPrev = tThis - tForDistanceThis;
				var tThisNext = tThis + tForDistanceThis;
				var tOtherPrev = tOther - tForDistanceOther;
				var tOtherNext = tOther + tForDistanceOther;
				var thisPrev = CatmullRom(_pathA, tThisPrev);
				var thisNext = CatmullRom(_pathA, tThisNext);
				var otherPrev = CatmullRom(_pathB, tOtherPrev);
				var otherNext = CatmullRom(_pathB, tOtherNext);
				Utility.LineSegmentIntersectXZ(thisPrev, thisNext, otherPrev, otherNext, out float tFrac, out float uFrac);
				tThis = Mathf.Lerp(tThisPrev, tThisNext, tFrac);
				tOther = Mathf.Lerp(tOtherPrev, tOtherNext, uFrac);
			}
			return (tThis, tOther);
		}


		public float GetClosestPointToPoint(float3 _point, bool _wantNavBlocker, float _minDistanceSqrd, ref float3 _onPath, ref float _pathT, bool _ignoreNavBlocker = false)
		{
			if (IsValid == false) return _minDistanceSqrd;
			if (_ignoreNavBlocker == false && IsNavBlocker != _wantNavBlocker) return _minDistanceSqrd;
			if (IsInRange(_point, Mathf.Sqrt(_minDistanceSqrd)) == false) return _minDistanceSqrd;
			
			var job = new ClosestPointJob(ref m_pathSmoothed, _point, _minDistanceSqrd);
			job.Schedule().Complete();
			var res = job.GetOutput();
			if (res.Item1 < _minDistanceSqrd)
			{
				_onPath = res.Item2;
				_pathT = res.Item3;
				return res.Item1;
			}
			return _minDistanceSqrd;

			var step = LocStep;
			for (float t = 0; t < 1; t += step)
			{
				var pos = CatmullRom(m_pathSmoothed, t);
				var distSqrd = math.lengthsq((pos - _point).xz);
				if (distSqrd < _minDistanceSqrd)
				{
					_minDistanceSqrd = distSqrd;
					_onPath = pos;
				}
			}
			return _minDistanceSqrd;
		}

		public (float, float, float3) GetClosestPointToPoint(float3 _point, bool _wantNavBlocker, float _minDistanceSqrd, bool _applyPolish = false)
		{
			if (IsValid == false) return (_minDistanceSqrd, 0, float3.zero);
			if (IsNavBlocker != _wantNavBlocker) return (_minDistanceSqrd, 0, float3.zero);
			if (IsInRange(_point, 8) == false) return (_minDistanceSqrd, 0, float3.zero);
			var step = LocStep;
			float3 onPath = float3.zero;
			float bestT = 0;
			float startT = 0, endT = 1;
			for (int pass = _applyPolish ? 2 : 1; pass >= 0; --pass)
			{
				for (float t = startT; t < endT; t += step)
				{
					var pos = CatmullRom(m_pathSmoothed, t);
					var distSqrd = math.lengthsq((pos - _point).xz);
					if (distSqrd < _minDistanceSqrd)
					{
						_minDistanceSqrd = distSqrd;
						onPath = pos;
						bestT = t;
					}
				}
				step /= 16;
				startT = bestT - step * 8;
				endT = bestT + step * 8;
			}
			return (_minDistanceSqrd, bestT, onPath);
		}

		public bool IsInRange(Vector3 _pos, float _margin)
		{
			if (_pos.x < m_minExtent.x - _margin) return false;
			if (_pos.x > m_maxExtent.x + _margin) return false;
			if (_pos.z < m_minExtent.z - _margin) return false;
			if (_pos.z > m_maxExtent.z + _margin) return false;
			return true;
		}

		public bool IsValid => Count >= 2 && !m_pathSmoothed.IsEmpty();

		public bool IsDegenerate => m_isPlayerPath == false && (m_path.Count == 1 || (m_path.Count == 2 && math.lengthsq((m_path[0] - m_path[1]).xz) < c_MinLength * c_MinLength));

		public void RemoveHandles()
		{
			if (m_handles != null) m_handles.transform.DestroyChildren();
		}

		public GameObject GetHandle(bool _start)
		{
			if (m_handles == null) return null;
			if (m_handles.transform.childCount < 3) return null;
			return m_handles.transform.GetChild(_start ? 1 : 2).gameObject;
		}

		public void UpdateHandles(bool _highlight, int _index, List<Vector4> _intersections = null)
		{
			UpdateSmoothed();
			if (IsValid == false) return;
			if (m_handles == null)
			{
				m_handles = new GameObject($"Handles{_index}");
				m_handles.transform.SetParent(RoadManager.Me.m_pathHolder);
			}

			if (IsValid == false) return;

			var set = Set;
			
			var step = LocStep;
			GameObject lineObj;
			if (m_handles.transform.childCount == 0)
				lineObj = GameObject.Instantiate(GlobalData.Me.m_roadSegmentEditPrefab, m_handles.transform);
			else
				lineObj = m_handles.transform.GetChild(0).gameObject;
			var line = lineObj.GetComponent<LineRenderer>();
			line.material = _highlight ? GlobalData.Me.m_roadSegmentEndpointEditMaterialSelected : 
				(m_isPlayerPath ? GlobalData.Me.m_roadSegmentEndpointEditMaterialPlayerPath : GlobalData.Me.m_roadSegmentEndpointEditMaterialUnselected);
			line.widthMultiplier = .3f;
			var points = Mathf.CeilToInt(1 / step);
			line.positionCount = points;
			UpdateEditLine(_highlight, _intersections);
		}

		public void UpdateEditLine(bool _highlight, List<Vector4> _intersections = null)
		{
			if (IsValid == false) return;
			
			var set = Set;
			var step = LocStep;
			if (m_handles == null)
				return;
			var line = m_handles.GetComponentInChildren<LineRenderer>();
			if (line == null)
				return;
			int index = 0;
			if (m_isPlayerPath == false)
			{
				for (float t = 0; t < 1; t += step)
				{
					var p = CatmullRom(m_pathSmoothed, t);
					line.SetPosition(index++, p.GroundPosition(set.m_handleRaise + .5f));
				}
			}

			int highlightType = _highlight ? (IsDegenerate ? 2 : 1) : 0;

			const bool c_showInnerPoints = false, c_showIntersects = false;
			if (c_showInnerPoints)
			{
				for (int i = 0; i < Count - 1; ++i)
				{
					DropPathPoint(((Vector3)m_pathSmoothed[i + 1]).GroundPosition(set.m_handleRaise), .45f, highlightType, m_handles.transform, Vector3.forward);
					DropPathPoint(CatmullRom(m_pathSmoothed, i + 1, 0.25f).GroundPosition(set.m_handleRaise), .25f, highlightType, m_handles.transform, Vector3.forward);
					DropPathPoint(CatmullRom(m_pathSmoothed, i + 1, 0.5f).GroundPosition(set.m_handleRaise), .25f, highlightType, m_handles.transform, Vector3.forward);
					DropPathPoint(CatmullRom(m_pathSmoothed, i + 1, 0.75f).GroundPosition(set.m_handleRaise), .25f, highlightType, m_handles.transform, Vector3.forward);
				}
			}
			var startDirection = ((Vector3)(CatmullRom(m_pathSmoothed, 0.01f) - CatmullRom(m_pathSmoothed, 0))).GetXZNorm();
			var endDirection = ((Vector3)(CatmullRom(m_pathSmoothed, 1) - CatmullRom(m_pathSmoothed, 0.99f))).GetXZNorm();
			var startPos = ((Vector3) m_path[0]).GroundPosition(set.m_handleRaise + .5f);
			var endPos = ((Vector3)m_path[^1]).GroundPosition(set.m_handleRaise + .5f);
			var canEditStartPoint = IsFullyConstructed;
			if (GetHandle(true) == null)
			{
				DropPathPoint(startPos, 1, canEditStartPoint ? 1 : 2, m_handles.transform, -startDirection);
				DropPathPoint(endPos, 1, 1, m_handles.transform, endDirection);
			}
			else
			{
				var start = GetHandle(true).transform;
				start.position = startPos;
				start.LookAt(startPos - startDirection, Vector3.up);
				SetHandleState(start.gameObject, canEditStartPoint ? 1 : 2);
				var end = GetHandle(false).transform;
				end.position = endPos;
				end.LookAt(endPos + endDirection, Vector3.up);
				SetHandleState(end.gameObject, 1);
			}

			GetHandle(true).SetActive(RoadManager.Me.IsHandleActive(this, true));
			GetHandle(false).SetActive(RoadManager.Me.IsHandleActive(this, false));
			//if (m_isPlayerPath) GetHandle(true).SetActive(false);
			
			if (c_showIntersects)
			{
				if (_intersections != null)
				{
					for (int i = 0; i < _intersections.Count; ++i)
					{
						var pos = CatmullRom(m_pathSmoothed, _intersections[i].w);
						DropPathPoint(pos, .4f, 2, m_handles.transform, Vector3.forward);
					}
				}
			}
		}

		public void UpdateSplat(bool _inner)
		{
			UpdateSmoothed();
			if (IsValid == false) return;

			var set = Set;
			int splatOuter = set.m_splatOuterChannel;
			if (splatOuter > -1)
			{
				if (!_inner)
				{
					var radius = set.m_splatRadiusOuter;
					CameraRenderSettings.Me.SetSplatPath(m_pathSmoothed, radius, LocStep * radius * .25f*4, splatOuter, .85f, 0x1234, set.m_splatOuterRandomness);
				}
				else
				{
					int splatInner = set.m_splatInnerChannel;
					if (splatInner > -1)
					{
						float currWidth = set.SplatRadiusInner;

						var randomness = set.m_splatInnerRandomness;
						randomness = math.min(randomness, (1f / set.m_splatInnerFrac));
						var randomRate = set.m_splatInnerRandomRate;
						float maxWidth = currWidth * (1 + randomness);
						float minWidth = currWidth / (1 + randomness);
						CameraRenderSettings.Me.SetSplatPath(m_pathSmoothed, currWidth, LocStep * currWidth * .25f*4, splatInner, .6f, 0x1234, set.m_splatInnerRandomness);
						//currWidth = math.clamp(currWidth + randomRate * Random(p, -1, 1), minWidth, maxWidth);
					}
				}
			}	
		}

		public float Random(float3 _pos, float _min, float _max)
		{
			float range = _max - _min;
			var x = GlobalData.TerrainX(_pos.x);
			var z = GlobalData.TerrainZ(_pos.z);
			var seed = x << 16 + z;
			var rand = new System.Random(seed);			
			return _min + range * (float)rand.NextDouble();
		}

		public void UpdateVisuals(bool _isFinal)
		{
			var previousSplatMin = m_splatChangeMin;
			var previousSplatMax = m_splatChangeMax;
			m_splatChangeMin = m_minExtent - Vector3.one * 4;
			m_splatChangeMax = m_maxExtent + Vector3.one * 4;
			var repaintMin = m_splatChangeMin;
			var repaintMax = m_splatChangeMax;
			if (previousSplatMin.x < previousSplatMax.x)
			{
				repaintMin = Vector3.Min(repaintMin, previousSplatMin);
				repaintMax = Vector3.Max(repaintMax, previousSplatMax);
			}
			if (_isFinal)
			{
				repaintMin = Vector3.Min(repaintMin, m_startSplatMin);
				repaintMax = Vector3.Max(repaintMax, m_startSplatMax);
			}
			RoadManager.Me.m_pathSet.DirtyRegion(repaintMin, repaintMax, _isFinal ? null : this);
		}

		public float UnclampedConstructedPathDistance { get => m_finishedDistance; set => m_finishedDistance = value; }
		float ConstructedPathDistance() => Mathf.Clamp(m_finishedDistance, 0.1f, TotalLength());
		public void SetCompletedPercent(float _f) => m_finishedDistance = Mathf.Clamp(_f * TotalLength(), 0.1f, TotalLength());
		public float GetCompletedPercent() => (TotalLength()> 0) ?ConstructedPathDistance() / TotalLength() : 0f;
		public float m_cachedLength, m_cachedLocStep;
		public float TotalLength()
		{
			if (m_smoothedDirty || m_cachedLength == 0)
			{
				UpdateSmoothed();
				if (IsValid == false)
				{
					m_cachedLength = 0;
					m_cachedLocStep = 1;
				}
				else
				{
					m_cachedLength = CatmullRomLength(m_pathSmoothed);
					m_cachedLocStep = .8f / m_cachedLength;
				}
			}
			return m_cachedLength;
		}

		public float AccurateLocStep {
			get {
				TotalLength();
				return m_cachedLocStep;
			}
		}

		public void ClearVisuals()
		{
			if (m_visuals == null) return;
			if (m_fakeContructionBuilding != null)
			{
				m_fakeContructionBuilding.DestroyMe();
				m_fakeContructionBuilding = null;
			}
			m_visuals.transform.DestroyChildren();
			foreach (var mesh in m_ownedMeshes)
				GameObject.Destroy(mesh);
			m_ownedMeshes.Clear();
		}

		public bool ContainsAnyOpenGates
		{
			get
			{
				if (m_visuals == null) return false;
				GateOpener[] gates = m_visuals.GetComponentsInChildren<GateOpener>(true);
				foreach(var gateOpener in gates)
				{
					if(gateOpener.IsGateOpen)
					{
						return true;
					}
				}
				return false;
			}
		}

		public GateOpener[] Gates => m_visuals.GetComponentsInChildren<GateOpener>(true);
		
		public bool ContainsOpenPathBreaks
		{
			get
			{
				return PathBreak.s_allBreaks.FindIndex(x => x.IsOpen && x.Path == this) > -1;
			}
		}
		
		public List<PathBreak> ReturnDamagedPathBreaks
        {
        	get
        	{
        		return PathBreak.s_allBreaks.FindAll(x => x.IsDamaged && x.IsOpen == false);
        	}
        }


		public bool IsOpen => ContainsAnyOpenGates || ContainsOpenPathBreaks;

		static NativeRiverDepthDrop s_nullDepthDrop = NativeRiverDepthDrop.Null();
		
		const float c_surfaceWidthFactor = 1.2f;

		static float s_debugEndElevate = 0;
		static DebugConsole.Command s_endElevate = new ("endelevate", _s => s_debugEndElevate = float.Parse(_s));
		private static DebugConsole.Command s_fixAllWalls = new DebugConsole.Command("fixwalls", _s =>
		{
			PathBreak.s_allBreaks.ForEach(x => x.DoRepair(1));
		});
		List<(String, int)> m_meshInfo = new(); public List<(String, int)> MeshInfo => m_meshInfo;

		public void PlayConstructAudio(GameObject _obj)
		{
			var audio = Set.m_pathConstructAudio;
			if (audio == null || audio.IsValid() == false)
				audio = NGManager.Me.m_constructWallDefaultAudio;
			if (audio != null)
				audio.Play(_obj);
		}

		private void PlayConstructAudio()
		{
			var obj = m_visuals;
			var transformOffset = obj.GetComponent<AudioTransformOverride>();
			if (transformOffset == null) transformOffset = obj.AddComponent<AudioTransformOverride>();
			transformOffset.m_positionAdjust = (Vector3)CatmullRom(m_pathSmoothed, GetCompletedPercent()).GroundPosition(.5f) - obj.transform.position;
			transformOffset.m_rotationAdjust = new Vector3(0, .1f, 0); // give a slight orientation adjust to make sure we keep a real AudioTransformOverride
			PlayConstructAudio(obj);
		}

		public void DoConstruct(float _amount)
		{
			var totalLength = TotalLength();
			var details = RoadDetails.GetInfo(Set.name);
			float metersPerDeliveryUnit = details.m_constructionMultiplier;
			m_finishedDistance = Mathf.Min(m_finishedDistance + _amount * metersPerDeliveryUnit, totalLength);
			Vector3 min = m_minExtent, max = m_maxExtent;
			PlayConstructAudio();
			RoadManager.Me.m_pathSet.CreateVisuals(min, max, null, true);
			RoadManager.Me.m_pathSet.UpdateNav(min, max);
		}
		
		private List<Vector3> m_fakeConstructionBuildingPreviousPositions = new();
		private MABuilding m_fakeContructionBuilding;
		public List<GameObject> GenerateVisuals(int _index, List<Vector4> _intersections, NativeishList<float2> _buildingIntersections, bool _isFinal)
		{
			m_meshInfo.Clear();
			
			UpdateSmoothed();
			List<GameObject> gates = null;
			if (IsValid == false) return gates;
			
			var step = LocStep;
			var set = Set;

			bool clearGameObjects = Application.unityVersion.StartsWith("6000.0.");
			//if (set.m_splatOnly == false || set.m_createSurfaceMaterial != null) // always add visuals in case there are bridges or gates
			{
				if (m_visuals == null)
				{
					m_visuals = new GameObject($"Path{_index}__{FinishedType}_{set.name}");
					m_visuals.transform.SetParent(RoadManager.Me.m_pathHolder);
					if (set.m_isConveyor)
						m_visuals.AddComponent<Conveyor>().SetPath(this);
				}
				if (clearGameObjects)
					ClearVisuals();

				var fms = new Dictionary<Material, MeshUtils.FlexibleMesh>();
				MeshUtils.FlexibleMesh fm;

				var extrudeDirection = Vector3.right;
				var extrudeSide = Vector3.forward;
				var totalDistance = TotalLength();
				var finishedDistance = ConstructedPathDistance();
				var constructionDistance = totalDistance - finishedDistance;
				
				Vector3 fmScale = Vector3.one, fmScaleEnd1, fmScaleEnd2;
				var fmOffsStart = extrudeDirection * 0;
				var fmOffsEnd = extrudeDirection * totalDistance;

				var extrudeDistance = finishedDistance;
				
				var hasBridge = false;
				
				if (set.m_createSurfaceMaterial != null)
				{
					const float c_surfaceStep = 1f;
					const int c_crossVerts = 8;
					float maxRadius = set.m_heightWidthOuter + set.m_splatOuterRandomness;
					int endCapSteps = Mathf.CeilToInt(maxRadius / c_surfaceStep);
					int verts = Mathf.CeilToInt(extrudeDistance / c_surfaceStep) + endCapSteps * 2;
					if (verts >= 2)
					{
						fm = new MeshUtils.FlexibleMesh(true);
						fms[set.m_createSurfaceMaterial] = fm;
						float halfWidth = maxRadius * c_surfaceWidthFactor;
						for (int i = 0; i < verts; ++i)
						{
							float x = (i - endCapSteps) * c_surfaceStep;
							float thisWidth = halfWidth;
							if (i < endCapSteps) thisWidth *= math.sqrt((float)(i + 1) / (float)(endCapSteps + 1));
							else if (i >= verts - endCapSteps) thisWidth *= math.sqrt((float)(verts - 1 - i + 1) / (float)(endCapSteps + 1));
							float thisV = .5f * thisWidth;// / halfWidth;
							for (int j = 0; j < c_crossVerts; ++j)
							{
								float cross = (float)j / (float)(c_crossVerts - 1);
								fm.m_verts.Add(new MeshUtils.ClipVert() {m_pos = new float3(x, 0, -thisWidth + thisWidth * 2 * cross), m_nrm = Vector3.up, m_uv = new float2(x, .5f + thisV * (cross * 2 - 1))});
							}
						}
						for (int i = 0; i < verts - 1; ++i)
						{
							for (int j = 0; j < c_crossVerts - 1; ++j)
							{
								fm.m_inds.Add(i * c_crossVerts + j + 0);
								fm.m_inds.Add(i * c_crossVerts + j + 1);
								fm.m_inds.Add(i * c_crossVerts + j + c_crossVerts);
								fm.m_inds.Add(i * c_crossVerts + j + c_crossVerts);
								fm.m_inds.Add(i * c_crossVerts + j + 1);
								fm.m_inds.Add(i * c_crossVerts + j + c_crossVerts + 1);
							}
						}
					}
				}

				var boundaryTs = new NativeishList<float>(_intersections.Count * 2 + _buildingIntersections.Count);
				var bridgeEndHeights = new NativeishList<float>(256);
				Material collisionObjectMaterial = null;
				var minExtrude = 0f;
				if (!set.m_splatOnly)
				{

					(List<MeshUtils.FlexibleMesh>[], List<Material>[], float) PrepareTemporaryFMs(GameObject[] straights)
					{
						var straightFMs = new List<MeshUtils.FlexibleMesh>[straights.Length];
						var straightMats = new List<Material>[straights.Length];
						float straightSize = -1;
						for (int s = 0; s < straights.Length; ++s)
						{
							var prefab = straights[s];
							var mesh = prefab.GetComponentInChildren<MeshFilter>().sharedMesh;
							straightFMs[s] = new List<MeshUtils.FlexibleMesh>();
							straightMats[s] = new List<Material>();
							var fmScaleBase = prefab.GetComponentInChildren<MeshFilter>().transform.lossyScale;
							float minX = 1e23f;
							for (int i = 0; i < mesh.subMeshCount; ++i)
							{
								var mat = prefab.GetComponentInChildren<MeshRenderer>().sharedMaterials[i];
								if (mat == null)
								{
									Utility.ShowErrorOnce($"Prefab {prefab.name} renderer {prefab.GetComponentInChildren<MeshRenderer>().name} material {i} is null, skipping");
									continue;
								}
								var ifm = new MeshUtils.FlexibleMesh(true);
								ifm.AddMesh(mesh, Vector3.zero, Vector3.zero, fmScaleBase, i);
								ifm.RefreshDetails();
								straightFMs[s].Add(ifm);
								straightMats[s].Add(mat);
								minX = Mathf.Min(minX, ifm.Min.x);
							}
							var min = new float3(minX, 0, 0);
							for (int i = 0; i < mesh.subMeshCount; ++i)
							{
								var ifm = straightFMs[s][i];
								var job = new OffsetVertJob(-min, ifm.m_verts.GetBackingArray());
								job.Schedule(ifm.VertexCount, 64).Complete();
								ifm.RefreshDetails();
								straightSize = Vector3.Dot(extrudeDirection, ifm.Extents);
							}
						}
						return (straightFMs, straightMats, straightSize);
					}

					void RoundExtrudeDistance(float straightSize)
					{
						extrudeDistance = Mathf.Round(extrudeDistance / straightSize) * straightSize;
						extrudeDistance = Mathf.Max(.1f, extrudeDistance); // don't allow zero length
						var diff = extrudeDistance - finishedDistance;
						if (diff > 0)
						{
							constructionDistance -= diff;
							if (constructionDistance < 0)
							{
								totalDistance -= constructionDistance;
								constructionDistance = 0;
							}
						}
						else if (diff < 0)
						{
							if (constructionDistance > 0) constructionDistance -= diff;
							else totalDistance += diff;
						}
					}					
					
				var straights = set.m_prefabStraights;
				if (straights == null || straights.Length <= 1)
				{
					var prefab = straights != null && straights.Length > 0 ? straights[0] : set.m_prefabStraight;
					
					var mesh = prefab.GetComponentInChildren<MeshFilter>().sharedMesh;
					var fmOffs = Vector3.zero;
					fmScale = prefab.GetComponentInChildren<MeshFilter>().transform.lossyScale;
					float straightSize = -1;
					for (int i = 0; i < mesh.subMeshCount; ++i)
					{
						var mat = prefab.GetComponentInChildren<MeshRenderer>().sharedMaterials[i];
						if (mat == null)
							Debug.LogError($"Prefab {prefab.name} renderer {prefab.GetComponentInChildren<MeshRenderer>().name} material {i} is null, gonna crash!");
						if (!fms.TryGetValue(mat, out fm))
							fm = new MeshUtils.FlexibleMesh(true);
						fms[mat] = fm;
						fm.AddMesh(mesh, fmOffs, Vector3.zero, fmScale, i);
						fm.RefreshDetails();
						var min = fm.Min;
						min.y = min.z = 0;
						var job = new OffsetVertJob(-min, fm.m_verts.GetBackingArray());
						job.Schedule(fm.VertexCount, 64).Complete();
						fm.RefreshDetails();
						straightSize = Vector3.Dot(extrudeDirection, fm.Extents);
					}
					if (set.m_onlyWholeSections) RoundExtrudeDistance(straightSize);
					foreach (var kvp in fms)
						if (kvp.Key != set.m_createSurfaceMaterial)
							kvp.Value.SetLength(extrudeDirection, extrudeDistance, set.m_extrudeInset, false);
				}
				else
				{
					var (straightFMs, straightMats, straightSize) = PrepareTemporaryFMs(straights);

					fmScale = straights[0].GetComponentInChildren<MeshFilter>().transform.lossyScale;

					if (set.m_onlyWholeSections) RoundExtrudeDistance(straightSize);

					float totalLen = 0;
					var prng = new Unity.Mathematics.Random((uint) (12345678 + _index));
					while (totalLen < extrudeDistance)
					{
						int index = (prng.NextInt() & 0x7FFFFFFF) % straightFMs.Length;
						var fmList = straightFMs[index];
						var matList = straightMats[index];
						float maxLen = 0;
						for (int i = 0; i < fmList.Count; ++i)
						{
							var fmFrom = fmList[i];
							var mat = matList[i];
							if (!fms.TryGetValue(mat, out fm))
							{
								fm = new MeshUtils.FlexibleMesh(true);
								fms[mat] = fm;
							}
							fm.AddMesh(fmFrom, extrudeDirection * totalLen);
							fm.RefreshDetails();
							maxLen = math.max(maxLen, Vector3.Dot(fmFrom.Extents, extrudeDirection));
						}
						totalLen += maxLen - set.m_extrudeInset;
					}
					foreach (var kvp in fms)
						if (kvp.Key != set.m_createSurfaceMaterial)
							kvp.Value.Clip(new Plane(extrudeDirection, extrudeDirection * extrudeDistance), false);
				}

				finishedDistance = extrudeDistance; // in case we changed extrudeDistance
				extrudeDistance = totalDistance;
				if (constructionDistance > 0)
				{
					var nextConstructionAtT = constructionDistance / totalDistance;
					var nextConstructionAt = CatmullRom(m_pathSmoothed, 1f - nextConstructionAtT).GroundPosition(0);
					// drop a construction object here
					GlobalData.Me.DoPostLoad(() =>
					{
						if (m_fakeContructionBuilding != null)
						{
							// delete and recreate, we need to set the m_fakeConstructionBuildingPreviousPositions set of colliders (could just add/update them but that would be quite involved)
							GameObject.Destroy(m_fakeContructionBuilding.gameObject);
							m_fakeContructionBuilding = null;
						}
						if (m_fakeContructionBuilding == null)
						{
							m_fakeContructionBuilding = PathBreak.CreateFakeBuildingWithRepair(nextConstructionAt, m_visuals.transform, NGCarriableResource.GetInfo(set.m_breakRepairMaterial), out var repair, m_fakeConstructionBuildingPreviousPositions);
							repair.SetPath(this);
						}
						else
							m_fakeContructionBuilding.transform.position = nextConstructionAt;
						m_fakeConstructionBuildingPreviousPositions.Add(nextConstructionAt);
						if (m_fakeConstructionBuildingPreviousPositions.Count > 6) m_fakeConstructionBuildingPreviousPositions.RemoveAt(0);
						var buildingNav = m_fakeContructionBuilding.gameObject.GetComponent<BuildingNav>();
						buildingNav.MoveBuilding();
					});

					var prefabConst = set.m_prefabConstruction;
					GameObject prefabConst2 = null;
					var yOffs = 0.0f;
					if (prefabConst == null)
					{
						prefabConst = set.m_prefabBreakRight;
						prefabConst2 = set.m_prefabBreakLeft;
						if (prefabConst == null)
						{
							prefabConst = (set.m_prefabStraights != null && set.m_prefabStraights.Length > 0) ? set.m_prefabStraights[0] : set.m_prefabStraight;
							prefabConst2 = null;
							yOffs = -2;
						}
					}
					var prefabs = prefabConst2 == null ? new GameObject[] {prefabConst} : new GameObject[] {prefabConst, prefabConst2};
					var (straightFMs, straightMats, straightSize) = PrepareTemporaryFMs(prefabs);

					fmScale = prefabs[0].GetComponentInChildren<MeshFilter>().transform.lossyScale;

					foreach (var kvp in fms)
						kvp.Value.SwapInds();
					
					var baseLength = Vector3.Dot(straightFMs[0][0].Extents, extrudeDirection);
					float totalLen = Mathf.Floor(finishedDistance / baseLength) * baseLength;
					int indexNext = 0, indexMask = prefabs.Length == 2 ? 1 : 0;
					while (totalLen < extrudeDistance)
					{
						int index = indexNext ++;
						var fmList = straightFMs[index & indexMask];
						var matList = straightMats[index & indexMask];
						float maxLen = 0;
						for (int i = 0; i < fmList.Count; ++i)
						{
							var fmFrom = fmList[i];
							var mat = matList[i];
							if (!fms.TryGetValue(mat, out fm))
							{
								fm = new MeshUtils.FlexibleMesh(true);
								fms[mat] = fm;
							}
							fm.AddMesh(fmFrom, extrudeDirection * totalLen + Vector3.up * yOffs);
							fm.RefreshDetails();
							maxLen = math.max(maxLen, Vector3.Dot(fmFrom.Extents, extrudeDirection));
						}
						totalLen += maxLen - set.m_extrudeInset;
					}
					foreach (var kvp in fms)
					{
						if (kvp.Key != set.m_createSurfaceMaterial)
						{
							kvp.Value.Clip(new Plane(extrudeDirection, extrudeDirection * extrudeDistance), false);
							kvp.Value.Clip(new Plane(-extrudeDirection, extrudeDirection * finishedDistance), false);
						}
					}
					foreach (var kvp in fms)
						kvp.Value.Unsplit();
				}
				else if (m_fakeContructionBuilding != null)
				{
					m_fakeContructionBuilding.DestroyMe();
					m_fakeContructionBuilding = null;
				}

				if (set.m_prefabStraightCollision != null)
				{
					var prefab = set.m_prefabStraightCollision;
					var mesh = prefab.GetComponentInChildren<MeshFilter>().sharedMesh;
					var fmOffs = Vector3.zero;
					fmScale = prefab.GetComponentInChildren<MeshFilter>().transform.lossyScale;
					var mat = prefab.GetComponentInChildren<MeshRenderer>().sharedMaterials[0];
					collisionObjectMaterial = mat;
					if (!fms.TryGetValue(mat, out fm))
						fm = new MeshUtils.FlexibleMesh(true);
					fms[mat] = fm;
					fm.AddMesh(mesh, fmOffs, Vector3.zero, fmScale, 0);
					fm.RefreshDetails();
					var min = fm.Min;
					min.y = min.z = 0;
					var job = new OffsetVertJob(-min, fm.m_verts.GetBackingArray());
					job.Schedule(fm.VertexCount, 64).Complete();
					fm.RefreshDetails();
					fm.SetLength(extrudeDirection, finishedDistance, set.m_extrudeInset, false);
				}

				fmScaleEnd1 = fmScale;
				fmScaleEnd1.x *= -1;
				fmScaleEnd2 = fmScale;
				
				fmOffsStart.y += s_debugEndElevate;
				fmOffsEnd.y += s_debugEndElevate;

				bool addStart = true, addEnd = true;
				if (IsCycle) addStart = addEnd = false;
				for (int i = 0; i < _intersections.Count; ++i)
				{
					if (_intersections[i].w > 1 - step * 8)
						addEnd = false;
					if (_intersections[i].w < step * 8)
						addStart = false;
				}
				if (addStart || addEnd)
				{
					void AddCapMeshes(GameObject prefabEnd, GameObject prefabEndAlt)
					{
						if (prefabEnd != null)
						{
							var meshEnd = prefabEnd.GetComponentInChildren<MeshFilter>().sharedMesh;
							if (meshEnd.isReadable == false)
							{
								Utility.ShowErrorOnce($"Prefab {prefabEnd.name} mesh {meshEnd.name} is not readable, skipping");
								return;
							}
							for (int i = 0; i < meshEnd.subMeshCount; ++i)
							{
								var mat = prefabEnd.GetComponentInChildren<MeshRenderer>().sharedMaterials[i];
								if (mat == null)
								{
									Debug.LogError($"Shared material {i} of mesh renderer on {prefabEnd.name} is null, make sure number of submeshes on the prefab is equal to the number of materials");
									continue;
								}
								if (!fms.TryGetValue(mat, out fm))
									fm = new MeshUtils.FlexibleMesh(true);
								fms[mat] = fm;
								if (addStart)
									fm.AddMesh(meshEnd, fmOffsStart, Vector3.zero, fmScaleEnd1, i);
								if (addEnd)
								{
									if (prefabEndAlt == null)
										fm.AddMesh(meshEnd, fmOffsEnd, Vector3.zero, fmScaleEnd2, i);
									else
										fm.AddMesh(prefabEndAlt.GetComponentInChildren<MeshFilter>().sharedMesh, fmOffsEnd, Vector3.zero, fmScaleEnd1, i);
								}
							}
						}
					}
					AddCapMeshes(set.m_prefabCap, set.m_prefabCapEnd);
					if (set.m_prefabCapCollider != null) AddCapMeshes(set.m_prefabCapCollider, null);
				}
				}
				
				HashSet<MeshUtils.FlexibleMesh> bridgeFMs = new();
				var tStep = LocStep;
				var radius = Mathf.Min(set.m_splatRadiusOuter, 3);
				var prevBlocked = false;
				m_bridgeBoundaries.Reset();
				const float yScale = 0.5f;
				const float zScale = 1f;
				const int altHowOften = 2;

				float Bridge(float _startT, float _endT, bool _isCollider)
				{
					MeshUtils.FlexibleMesh fm = null;

					var prefabBridgeMid = _isCollider ? set.m_prefabBridgeColliderMid : set.m_prefabBridgeMid;
					var prefabBridgeAlt = _isCollider ? set.m_prefabBridgeColliderAlt : set.m_prefabBridgeAlt;
					var prefabBridgeEnd = _isCollider ? set.m_prefabBridgeColliderEnd : set.m_prefabBridgeEnd;
					if (_isCollider && collisionObjectMaterial == null)
						collisionObjectMaterial = prefabBridgeMid.GetComponentInChildren<MeshRenderer>().sharedMaterials[0];

					var bridgeMidMesh = prefabBridgeMid.GetComponentInChildren<MeshFilter>().sharedMesh;
					var bridgeAltMesh = prefabBridgeAlt.GetComponentInChildren<MeshFilter>().sharedMesh;
					var bridgeEndMesh = prefabBridgeEnd.GetComponentInChildren<MeshFilter>().sharedMesh;

					var midBounds = bridgeMidMesh.bounds;
					var altBounds = bridgeAltMesh.bounds;
					var endBounds = bridgeEndMesh.bounds;
					var midScale = prefabBridgeMid.transform.lossyScale;
					var altScale = prefabBridgeAlt.transform.lossyScale;
					var endScale = prefabBridgeEnd.transform.lossyScale;
					var midSize = Vector3.Scale(midScale, midBounds.size);
					var altSize = Vector3.Scale(altScale, altBounds.size);
					var endSize = Vector3.Scale(endScale, endBounds.size);
					var midMin = Vector3.Scale(midScale, midBounds.min);
					var altMin = Vector3.Scale(altScale, altBounds.min);
					var endMin = Vector3.Scale(endScale, endBounds.min);
					var midMax = Vector3.Scale(midScale, midBounds.max);
					var altMax = Vector3.Scale(altScale, altBounds.max);
					var endMax = Vector3.Scale(endScale, endBounds.max);

					var start = _startT * extrudeDistance;
					var end = _endT * extrudeDistance;
					
					hasBridge = true;

					if (_isCollider == false)
					{
						foreach (var kvp in fms)
						{
							var toClip = kvp.Value;
							for (int i = 0; i < m_bridgeBoundaries.Count; i += 2)
							{
								ApplySplit(toClip, extrudeDirection, extrudeDirection * end);
								ApplyClip(toClip, extrudeDirection, extrudeDirection * start);
								toClip.Unsplit();
							}
						}
					}
					end += set.m_bridgeOverlap;
					start -= set.m_bridgeOverlap;
					var dist = end - start;
					var cycleLength = altSize.x + (altHowOften - 1) * midSize.x;
					var numCycles = Mathf.Max(0, (dist - 2 * endSize.x) / (2 * cycleLength));
					int fullCycles = (int)numCycles;
					var remaining = (numCycles - fullCycles) * 2 * cycleLength;
					float totLength = 2 * (fullCycles * cycleLength + endSize.x);
					int extraPieces = (int)(remaining / midSize.x);
					totLength += midSize.x * extraPieces;
					if (extraPieces > 2 * altHowOften - 1)
						totLength += 2 * (altSize.x - midSize.x);
					else if (extraPieces == 2 * altHowOften - 1)
						totLength += altSize.x - midSize.x;

					int numPieces = fullCycles * 2 * altHowOften + extraPieces;
					var adjust = (totLength - dist) * .5f;
					start -= adjust; end += adjust;
					dist += adjust * 2;
					var scale = dist / totLength;
					var bridgeScale1 = yScale * scale * Vector3.up + scale * extrudeDirection + zScale * extrudeSide;
					var bridgeScale2 = yScale * scale * Vector3.up - scale * extrudeDirection + zScale * extrudeSide;
					var nextPos = endSize.x * scale;
					for (int i = 0; i < numPieces; ++i)
					{
						int fromEnd = math.min(i, numPieces - 1 - i);
						var isAlt = (fromEnd % altHowOften) == (altHowOften >> 1);
						var (currentPref, currentMesh, currentMin, currentMax, currentScale, currentLength) 
							= isAlt ? (prefabBridgeAlt, bridgeAltMesh, altMin, altMax, altScale, altSize.x) : (prefabBridgeMid, bridgeMidMesh, midMin, midMax, midScale, midSize.x);

						var offset = scale * currentMin.x * extrudeDirection + (currentMin.z + currentMax.z) * 0.5f * extrudeSide;
						var pos = (start + nextPos) * extrudeDirection - offset;
						for (int j = 0; j < currentMesh.subMeshCount; ++j)
						{
							var bridgeMat = currentPref.GetComponentInChildren<MeshRenderer>().sharedMaterials[j];
							if (!fms.TryGetValue(bridgeMat, out fm))
								fm = new MeshUtils.FlexibleMesh(true);
							fms[bridgeMat] = fm;
							bridgeFMs.Add(fm);
							fm.AddMesh(currentMesh, pos, Vector3.zero, Vector3.Scale(bridgeScale1, currentScale), j);
						}
						nextPos += currentLength * scale;
					}

					var offset1 = scale * endMin.x * extrudeDirection + (endMin.z + endMax.z) * 0.5f * extrudeSide;
					var offset2 = offset1 - 2 * scale * endMin.x * extrudeDirection;

					var pos1 = (end - endSize.x * scale) * extrudeDirection - offset1;
					var pos2 = (start + endSize.x * scale) * extrudeDirection - offset2;
					for (int i = 0; i < bridgeEndMesh.subMeshCount; ++i)
					{
						var bridgeMat = prefabBridgeEnd.GetComponentInChildren<MeshRenderer>().sharedMaterials[i];
						if (!fms.TryGetValue(bridgeMat, out fm))
							fm = new MeshUtils.FlexibleMesh(true);
						fms[bridgeMat] = fm;
						bridgeFMs.Add(fm);

						fm.AddMesh(bridgeEndMesh, pos1, Vector3.zero, Vector3.Scale(bridgeScale1, endScale), i);
						fm.AddMesh(bridgeEndMesh, pos2, Vector3.zero, Vector3.Scale(bridgeScale2, endScale), i);
					}
					return midSize.x;
				}

				bool hasSeparateColliders = set.m_prefabBridgeColliderMid != null;

				void BridgeAndCollider(float _startT, float _endT)
				{
					var size = Bridge(_startT, _endT, false);
					if (hasSeparateColliders)
					{
						var cllSize = Bridge(_startT, _endT, true);
						if (cllSize < .001f) Utility.ShowErrorOnce($"Bridge collider size is zero, check prefab {set.m_prefabBridgeColliderMid.name} (main section size is {size:n2})");
						else
						{
							var ratio = size / cllSize;
							if (ratio < .9f || ratio > 1.1f) Utility.ShowErrorOnce($"Bridge collider size {cllSize:n2} is not close to the bridge size {size:n2}, check prefab {set.m_prefabBridgeColliderMid.name}");
						}
					}
				}

				if (set.m_prefabBridgeMid != null && set.m_createSurfaceMaterial == null)
				{
					Vector3 pos = new();
					for (float t = 0; t <= 1; t += tStep)
					{
						pos = CatmullRom(m_pathSmoothed, t);
						var blocked = CheckTerrain(pos.GetXZVector2(), radius);
						if (blocked != prevBlocked)
						{
							if (!blocked)
								BridgeAndCollider(m_bridgeBoundaries[^1], t);
							prevBlocked = blocked;
							m_bridgeBoundaries.Add(t);
							bridgeEndHeights.Add(GlobalData.Me.GetRealHeight(pos));
						}
					}
					if (prevBlocked)
					{
						BridgeAndCollider(m_bridgeBoundaries[^1], 1);
						m_bridgeBoundaries.Add(1);
						bridgeEndHeights.Add(GlobalData.Me.GetRealHeight(pos));
					}
				}
				minExtrude = float.PositiveInfinity;
				foreach (var kvp in fms)
				{
					var extrude = Vector3.Dot(kvp.Value.Min, extrudeDirection);
					minExtrude = math.min(minExtrude, extrude);
				}
				if (!_isFinal)
				{
					List<Vector2> unsortedBoundaries = new();
					for (int i = 0; i < _intersections.Count; ++i)
					{
						(var _, var fwd, var _) = CatmullRomPosFwdSide(m_pathSmoothed, _intersections[i].w);
						Vector3 otherFwd = _intersections[i];
						int sides = (int)otherFwd.y;
						if (sides == -1) continue; // this intersects a nav-blocker
						otherFwd.y = 0;
						var otherSide = new Vector3(-otherFwd.z, otherFwd.y, otherFwd.x);

						var clipMultiplier = Mathf.Min(1.6f, 1 / Mathf.Abs(Vector3.Dot(fwd, otherSide)));

						float tMin = Mathf.Max(0, _intersections[i].w - LocStep * 8 * clipMultiplier);
						float tMax = Mathf.Min(1, _intersections[i].w + LocStep * 8 * clipMultiplier);
						unsortedBoundaries.Add(new Vector2(tMin, tMax));
					}
					unsortedBoundaries.Sort((a, b) => (int)math.sign(a.x - b.x));
					for (int i = 0; i < unsortedBoundaries.Count; ++i)
					{
						var boundaries = unsortedBoundaries[i];
						if (i == 0 || boundaries.x > boundaryTs[^1])
						{
							boundaryTs.Add(boundaries.x);
							boundaryTs.Add(boundaries.y);
						}
						else
						{
							var prevHigh = boundaryTs[^1];
							var highest = math.max(prevHigh, boundaries.y);
							boundaryTs[^1] = highest;
						}
					}
				}
				foreach (var kvp in fms)
				{
					fm = kvp.Value;
					for (int i = 0; i < _buildingIntersections.Count - 1; i += 2)
					{
						ApplySplit(fm, extrudeDirection, _buildingIntersections[i + 1].x * extrudeDistance * extrudeDirection);
						ApplyClip(fm, extrudeDirection, _buildingIntersections[i].x * extrudeDistance * extrudeDirection);
						fm.Unsplit();
					}
				}

				var prefabEndMinor = set.Prefabs[7];
				var prefabEndBrokenL = set.m_prefabBreakLeft;
				var prefabEndBrokenR = set.m_prefabBreakRight;
				var meshEndMinor = prefabEndMinor != null ? prefabEndMinor.GetComponentInChildren<MeshFilter>().sharedMesh : null;
				var meshEndBrokenL = prefabEndBrokenL != null ? prefabEndBrokenL.GetComponentInChildren<MeshFilter>().sharedMesh : null;
				var meshEndBrokenR = prefabEndBrokenR != null ? prefabEndBrokenR.GetComponentInChildren<MeshFilter>().sharedMesh : null;
				
				GameObject prefabEndUsed = prefabEndMinor;
				fmScaleEnd1 = fmScale;
				fmScaleEnd2 = fmScale; fmScaleEnd2.x *= -1;
				for (int i = 0; i < _buildingIntersections.Count - 1; i += 2)
				{
					var startT = _buildingIntersections[i].x;
					var endT = _buildingIntersections[i + 1].x;
					var type = (int) _buildingIntersections[i].y;
					Mesh meshL = null, meshR = null;
					switch (type)
					{
						case PathSet.c_blockTypeBuilding:
							meshL = meshR = meshEndMinor;
							prefabEndUsed = prefabEndMinor;
							break;
						case PathSet.c_blockTypeBroken:
						case PathSet.c_blockTypeBrokenPartial:
							meshL = meshEndBrokenL;
							meshR = meshEndBrokenR;
							prefabEndUsed = prefabEndBrokenL;
							break;
					}
					if (meshL != null || meshR != null)
					{
						fmOffsStart = extrudeDistance * startT * extrudeDirection;
						fmOffsEnd = extrudeDistance * endT * extrudeDirection;
						for (int j = 0; j < meshL.subMeshCount; ++j)
						{
							var mat = prefabEndUsed.GetComponentInChildren<MeshRenderer>().sharedMaterials[j];
							if (!fms.TryGetValue(mat, out fm))
								fm = new MeshUtils.FlexibleMesh(true);
							fms[mat] = fm;
							if (startT > 0 && startT < 1)
								fm.AddMesh(meshL, fmOffsStart, Vector3.zero, fmScaleEnd1, j);
							if (endT < 1 && endT > 0)
								fm.AddMesh(meshR, fmOffsEnd, Vector3.zero, fmScaleEnd2, j);
						}
					}
				}

				// check for clip-caps and make sure their FMs are present
				for (int i = 0; i < _intersections.Count; ++i)
				{
					Vector3 otherFwd = _intersections[i];
					int sides = (int)otherFwd.y;
					if (sides == 0)
					{
						var prefabEnd = set.m_prefabCapIntersection == null ? set.m_prefabCap : set.m_prefabCapIntersection;
						if (prefabEnd != null)
						{
							var meshEnd = prefabEnd.GetComponentInChildren<MeshFilter>().sharedMesh;
							for (int m = 0; m < meshEnd.subMeshCount; ++m)
							{
								var mat = prefabEnd.GetComponentInChildren<MeshRenderer>().sharedMaterials[m];
								if (fms.ContainsKey(mat) == false)
									fms[mat] = new MeshUtils.FlexibleMesh();
							}
						}
					}
				}

				var unusedObjects = new HashSet<GameObject>();
				foreach (Transform t in m_visuals.transform)
					if (t.gameObject.GetComponent<MABuilding>() == null)
						unusedObjects.Add(t.gameObject);
				
				bool isFirst = true;
				foreach (var kvp in fms)
				{
					var mat = kvp.Key;
					float heightAdjust = set.m_heightAdjust + c_PathHeightOffset;
					bool isSurface = mat == set.m_createSurfaceMaterial;
					float surfaceHeightStart = 0, surfaceHeightEnd = 0;
					if (isSurface)
					{
						heightAdjust = set.m_createSurfaceLevel;
						if (m_terrainSmoothValues.IsCreated)
						{
							surfaceHeightStart = m_terrainSmoothValues[0] + set.m_terrainAdjust;
							surfaceHeightEnd = m_terrainSmoothValues[^1] + set.m_terrainAdjust;
						}
					}
					fm = kvp.Value;
					var xform = m_visuals.transform;
					var offset = new Vector3(m_pathSmoothed[0].x, 0, m_pathSmoothed[0].z);
					bool isCycle = IsCycle;
					var startEndT = 1f / Count;
					var gd = GlobalData.Me;

					var thisExtrude = mat == set.m_createSurfaceMaterial ? 0 : minExtrude; 
					var depthDrop = m_riverDepthDrop?.ToNative(set.m_terrainAdjust) ?? s_nullDepthDrop;
					var job = new CatmullVertJob(extrudeDirection, extrudeSide, offset, thisExtrude, totalDistance/*extrudeDistance*/, heightAdjust, GlobalData.c_heightmapW, _isFinal, isSurface, surfaceHeightStart, surfaceHeightEnd, ref m_pathSmoothed,
						gd.Heights, ref m_bridgeBoundaries, ref bridgeEndHeights, ref boundaryTs, ref fm.m_verts, depthDrop);
					job.Schedule(fm.VertexCount, 8).Complete();
					if (m_riverDepthDrop != null) depthDrop.Dispose();
					fm.RefreshDetails();

					List<GameObject> addGates = null;
					if (_isFinal)
						addGates = ApplyIntersections(fm, _intersections, offset, mat, isFirst);
					if (m_riverDepthDrop != null)
						m_riverDepthDrop.AddDecorations(this, set.m_createSurfaceDecorationsTop, set.m_createSurfaceDecorationsBottom, ref addGates);

					var meshName = $"Roads_{mat.name}";
					var go = clearGameObjects ? null : m_visuals.transform.Find(meshName)?.gameObject;
					if (go != null)
					{
						if (go.activeSelf == false) go = null; // being destroyed
						else go.transform.DestroyChildren();
					}
					if (fm.VertexCount > 0 && fm.m_inds.Count > 0)
					{
						m_meshInfo.Add(new (mat.name, fm.VertexCount));
						MeshFilter mf = null;
						MeshCollider mc = null;
						if (go == null)
						{
							go = new GameObject(meshName);
							go.SetActive(false);
							go.isStatic = true;
							go.layer = GameManager.c_layerRoads;
							go.transform.SetParent(m_visuals.transform);
							var mesh = new Mesh();
							mesh.name = meshName;
							m_ownedMeshes.Add(mesh);
							if (collisionObjectMaterial == null || collisionObjectMaterial != mat)
							{
								var mr = go.AddComponent<MeshRenderer>();
								mf = go.AddComponent<MeshFilter>();
								mr.sharedMaterial = mat;
								mr.shadowCastingMode = set.m_meshCastsShadows ? UnityEngine.Rendering.ShadowCastingMode.On : UnityEngine.Rendering.ShadowCastingMode.Off;
								mf.mesh = mesh;
							}
							if ((collisionObjectMaterial == null || collisionObjectMaterial == mat) && set.m_noMeshColliders == false)
							{
								if (RoadManager.Me.m_omitCollidersForMaterials.FindIndex(x => !string.IsNullOrEmpty(x) && x.Equals(mat.name)) == -1)
								{
									mc = go.AddComponent<MeshCollider>();
									mc.sharedMesh = mesh;
									go.AddComponent<RoadSetLink>().Set(set);
									go.AddComponent<SurfaceMaterial>().m_surfaceType = hasBridge ? set.m_bridgeSurfaceType : set.m_surfaceType;
									if (set.m_isConveyor)
										mc.sharedMaterial = new PhysicsMaterial() {dynamicFriction = 0, staticFriction = 0, frictionCombine = PhysicsMaterialCombine.Minimum};
								}
							}
						}
						else
						{
							mf = go.GetComponent<MeshFilter>();
							mc = go.GetComponent<MeshCollider>();
						}
						go.transform.position = offset;
						fm.UpdateMesh(mf, mc);

						if (addGates != null && addGates.Count > 0)
						{
							gates = addGates;
							foreach (var gate in addGates)
								gate.transform.SetParent(go.transform, true);
						}
						go.SetActive(true);
					}
					else
					{
						if (addGates != null && addGates.Count > 0)
						{
							foreach (var gate in addGates)
								GameObject.Destroy(gate);
						}
						if (go != null)
							go.SetActive(false);
					}

					if (go != null)
						unusedObjects.Remove(go);
					
					isFirst = false;
				}
				foreach (var go in unusedObjects)
					go.SetActive(false);
				
				boundaryTs.Dispose();
				bridgeEndHeights.Dispose();
			}

			if (m_terrainSmoothValues.IsCreated)
				m_terrainSmoothValues.Dispose();
			
			return gates;
		}

		public static int NumBefore(NativeArray<float> _vals, int _length, float _toFind)
		{
			if (_length < 1)
				return 0;

			int above = _length - 1;
			int below = 0;

			if (_toFind > _vals[above])
				return above + 1;
			if (_toFind < _vals[below])
				return below;

			while (above - below > 1)
			{
				int mid = (above + below) >> 1;
				if (_vals[mid] > _toFind)
					above = mid;
				else
					below = mid;
			}
			return below + 1;
		}

		private bool CheckTerrain(Vector2 _pos, float _radius)
		{
			return GlobalData.Me.CheckTerrainBlocker(_pos.x - _radius, _pos.y - _radius, _pos.x + _radius, _pos.y + _radius);
		}

		private bool DoesIntersectionCut(Vector4 _intersection)
		{
			int sides = (int)_intersection.y;
			if ((sides & c_intersectionType_NavBlockOther) != 0) return false; // this intersects a nav-blocker, it'll get out of our way
			if ((sides & c_intersectionType_IsBridge) != 0) return false; // this is a bridge, it doesn't cut
			//bool isPrimary = (sides & c_intersectionType_IsPrimary) != 0;
			//bool isSameType = (sides & c_intersectionType_IsSameType) != 0;
			//bool isInLine = (sides & c_intersectionType_IsInLine) != 0;
			//bool isOtherLineEnd = (sides & c_intersectionType_IsOtherLineEnd) != 0;
			bool isCorrupted = (sides & c_intersectionType_IsCorrupted) != 0;
			//bool isThisLineEnd = (_intersection.w > 0.5f);
			if (isCorrupted) return false; // doesn't cut, just corrupts
			sides &= c_intersectionType_FlagsMask;
			if (sides == 0) return true;
			return false;
		}

		private List<GameObject> ApplyIntersections(MeshUtils.FlexibleMesh _fm, List<Vector4> _intersections, Vector3 _vertexOffset, Material _matMatch, bool _addGates)
		{
			var gates = new List<GameObject>();
			
			SetClipOffset(_vertexOffset);
			
			float endRange = LocStep * 8;
			for (int i = 0; i < _intersections.Count; ++i)
			{
				Vector3 otherFwd = _intersections[i];
				int sides = (int) otherFwd.y;
				if ((sides & c_intersectionType_NavBlockOther) != 0) continue; // this intersects a nav-blocker, it'll get out of our way
				if ((sides & c_intersectionType_IsBridge) != 0) continue; // this is a bridge, it doesn't cut
				bool hasLeft = (sides & c_intersectionType_Left) != 0;
				bool hasRight = (sides & c_intersectionType_Right) != 0;
				bool isPrimary = (sides & c_intersectionType_IsPrimary) != 0;
				bool isSameType = (sides & c_intersectionType_IsSameType) != 0;
				bool isInLine = (sides & c_intersectionType_IsInLine) != 0;
				bool isOtherLineEnd = (sides & c_intersectionType_IsOtherLineEnd) != 0;
				bool isCorrupted = (sides & c_intersectionType_IsCorrupted) != 0;
				int otherIndex = sides >> c_intersectionType_OtherIndexShift;
				var otherPath = RoadManager.Me.m_pathSet.CurrentPaths[otherIndex];
				var t = _intersections[i].w;
				sides &= c_intersectionType_FlagsMask;
				bool isThisLineEnd = (_intersections[i].w > 0.5f);
				otherFwd.y = 0;
				var otherSide = new Vector3(-otherFwd.z, otherFwd.y, otherFwd.x);
				(Vector3 pos, Vector3 fwd, Vector3 side) = CatmullRomPosFwdSide(m_pathSmoothed, _intersections[i].w);
				pos -= _vertexOffset;

				if (isCorrupted)
				{
					const float c_corruptHeight = -0.25f;
					float corruptionFalloff = 1.0f / (LocStep * 6);
					_fm.AdjustHeightsWithContext(_intersections[i].w, corruptionFalloff, c_corruptHeight);
					/*_fm.ApplyToPointsWithContext(_v4 =>
					{
						var dContext = (_intersections[i].w - _v4.w) * corruptionFalloff;
						var factor = 1 - math.min(dContext * dContext, 1);
						_v4.y += c_corruptHeight * factor;
						return _v4;
					});*/
					continue;
				}
				
				var clipMultiplier = Mathf.Min(1.6f, 1 / Mathf.Abs(Vector3.Dot(fwd, otherSide)));
				var otherPathWidth = otherPath.Set.m_navPathWidth;
				var basePathExtent = otherPathWidth * 5;
				var baseClipExtent = AccurateLocStep * basePathExtent * clipMultiplier;
				_fm.SetClipContext(_intersections[i].w, baseClipExtent);
				_fm.ApplyClipContext();

				if (sides == 0)
				{
					// remove a section of nav blocker
					(Vector3 clip1, Vector3 clipFwd1, _) = CatmullRomPosFwdSide(m_pathSmoothed, _intersections[i].w - baseClipExtent * .6f);
					clip1 -= _vertexOffset;
					(Vector3 clip2, Vector3 clipFwd2, _) = CatmullRomPosFwdSide(m_pathSmoothed, _intersections[i].w + baseClipExtent * .6f);
					clip2 -= _vertexOffset;
					ApplySplit(_fm, -clipFwd1, clip1);
					ApplyClip(_fm, -clipFwd2, clip2);
					_fm.Unsplit();
					_fm.UnapplyClipContext();
					_fm.ClearClipContext();
					
					float angle1 = Mathf.Atan2(clipFwd1.z, clipFwd1.x) * -Mathf.Rad2Deg;
					float angle2 = Mathf.Atan2(clipFwd2.z, clipFwd2.x) * -Mathf.Rad2Deg;
					var set = Set;
					var prefabEnd = set.m_prefabCapIntersection == null ? set.m_prefabCap : set.m_prefabCapIntersection;
					var prefabEndCollider = set.m_prefabCapIntersectionCollider == null ? set.m_prefabCapCollider : set.m_prefabCapIntersectionCollider;
					bool addedGatePost = false;
					if (prefabEnd != null)
					{
						if (prefabEndCollider != null)
						{
							var rndCll = prefabEndCollider.GetComponentInChildren<MeshRenderer>();
							if (rndCll != null)
								if (rndCll.sharedMaterials[0] == _matMatch)
									prefabEnd = prefabEndCollider;
						}
						var meshEnd = prefabEnd.GetComponentInChildren<MeshFilter>().sharedMesh;
						var fmScale = set.Prefabs[1].GetComponentInChildren<MeshFilter>().transform.lossyScale;
						var fmScaleEnd1 = fmScale;
						var fmScaleEnd2 = fmScale;
						fmScaleEnd2.x *= -1;
						Vector3 startPos = (clip1 + _vertexOffset).GroundPosition(set.m_heightAdjust);
						Vector3 endPos = (clip2 + _vertexOffset).GroundPosition(set.m_heightAdjust);
						for (int m = 0; m < meshEnd.subMeshCount; ++m)
						{
							var mat = prefabEnd.GetComponentInChildren<MeshRenderer>().sharedMaterials[m];
							if (mat != _matMatch) continue;
							bool hasStart = true, hasEnd = true;
							float2 f2sp = new(startPos.x, startPos.z);
							float2 f2ep = new(endPos.x, endPos.z);
							foreach (var blockPos in RoadManager.Me.m_pathSet.m_blockPositions)
							{
								int type = (int)blockPos.z;
								if (math.lengthsq(blockPos.xy - f2sp) < c_PerTypeBlockAvoidanceRadiusSqr[type])
									hasStart = false;
								if (math.lengthsq(blockPos.xy - f2ep) < c_PerTypeBlockAvoidanceRadiusSqr[type])
									hasEnd = false;
							}
							if (hasStart)
								_fm.AddMesh(meshEnd, startPos - _vertexOffset, Vector3.up * angle1, fmScaleEnd1, m);
							if (hasEnd)
								_fm.AddMesh(meshEnd, endPos - _vertexOffset, Vector3.up * angle2, fmScaleEnd2, m);
						}
						if (_addGates && set.m_prefabCapFurniture != null)
						{
							var furnitureL = GameObject.Instantiate(set.m_prefabCapFurniture);
							furnitureL.transform.position = startPos;
							furnitureL.transform.eulerAngles = Vector3.up * angle1;
							furnitureL.SetStaticRecursively(true);
							gates.Add(furnitureL);
							var furnitureR = GameObject.Instantiate(set.m_prefabCapFurniture);
							furnitureR.transform.position = endPos;
							furnitureR.transform.eulerAngles = Vector3.up * (angle2 + 180);
							furnitureR.SetStaticRecursively(true);
							gates.Add(furnitureR);
						}
						if (_addGates && set.m_prefabGate != null)
						{
							var lToRnrm = endPos - startPos;
							lToRnrm.y = 0;
							lToRnrm.Normalize();
							var gatePosL = startPos + lToRnrm * set.m_insetGateDistance;
							var gatePosR = endPos - lToRnrm * set.m_insetGateDistance;
							gatePosL.y = gatePosR.y = (gatePosL.y + gatePosR.y) * .5f;
							var lToR = gatePosR - gatePosL;
							var forward = new Vector3(-lToR.z, 0, lToR.x);

							var gateHolder = new GameObject("Gate");
							gateHolder.transform.position = (gatePosL + gatePosR) * .5f;
							gateHolder.transform.LookAt(gateHolder.transform.position + forward, Vector3.up);
							
							var gateL = GameObject.Instantiate(set.m_prefabGate, gateHolder.transform);
							var gateR = GameObject.Instantiate(set.m_prefabGate, gateHolder.transform);
							gateL.transform.position = gatePosL;
							gateR.transform.position = gatePosR;
							var postToPostDistance = lToR.xzMagnitude();
							var gateBounds = ManagedBlock.GetTotalVisualBounds(set.m_prefabGate, _allowRoots:true);
							var scale = postToPostDistance * .5f / gateBounds.size.x;
							gateL.transform.localScale = new Vector3(scale, 1, 1);
							gateR.transform.localScale = new Vector3(scale, 1, 1);

							gateHolder.AddComponent<GateOpener>().SetPaths(this, RoadManager.Me.m_pathSet.CurrentPaths[otherIndex]);

							gates.Add(gateHolder);
						}
					}
				}
				else
				{
					if (isInLine)
					{
						if (!isThisLineEnd)
							fwd = -fwd;
						if (!isOtherLineEnd)
							otherFwd = -otherFwd;
						float3 clip = (fwd - otherFwd).normalized;
						ApplyClip(_fm, clip, pos);
					}
					else
					{
						var clip1Fwd = (fwd - otherFwd).normalized;
						var clip2Fwd = (fwd + otherFwd).normalized;

						if (_intersections[i].w < endRange)
							ApplyClip(_fm, -fwd, pos);
						else if (_intersections[i].w > 1 - endRange)
							ApplyClip(_fm, fwd, pos);

						if (isSameType == false)
						{
							const float c_forwardInset = 2.5f;
							if (isPrimary)
							{
								var ext1 = clip1Fwd * (c_forwardInset / Vector3.Dot(clip1Fwd, side));
								var ext2 = clip2Fwd * (c_forwardInset / Vector3.Dot(clip2Fwd, side));
								var clipBack = pos + ext1;
								var clipFwd = pos + ext2;

								var clipLeft = pos - side * c_forwardInset;
								var clipRight = pos + side * c_forwardInset;

								ApplySplit(_fm, -otherSide, clipBack);
								if (hasRight)
									ApplyClip(_fm, clip1Fwd, pos);
								if (hasLeft)
									ApplyClip(_fm, clip2Fwd, pos);
								_fm.SwapInds();
								_fm.SwapInds2Temp();
								ApplySplit(_fm, otherSide, clipFwd);
								if (hasLeft)
									ApplyClip(_fm, -clip1Fwd, pos);
								if (hasRight)
									ApplyClip(_fm, -clip2Fwd, pos);
								_fm.SwapInds2Temp();
								_fm.Unsplit();
								_fm.SwapInds2Temp();
								_fm.SwapInds();
								if (hasRight)
									ApplyClip(_fm, -side, clipLeft);
								if (hasLeft)
									ApplyClip(_fm, side, clipRight);
								_fm.Unsplit();
							}
							else
							{
								var clipFwd = -otherSide;
								ApplySplit(_fm, clipFwd, pos - clipFwd * c_forwardInset);
								if (hasLeft)
									ApplyClip(_fm, clip1Fwd, pos);
								if (hasRight)
									ApplyClip(_fm, clip2Fwd, pos);
								_fm.SwapInds();
								ApplyClip(_fm, -clipFwd, pos + clipFwd * c_forwardInset);
								ApplyClip(_fm, -clip1Fwd, pos);
								ApplyClip(_fm, -clip2Fwd, pos);
								_fm.Unsplit();
							}
						}
						else
						{
							ApplySplit(_fm, clip1Fwd, pos);
							if (hasLeft)
								ApplyClip(_fm, clip2Fwd, pos);
							_fm.SwapInds();
							if (hasRight)
								ApplyClip(_fm, -clip2Fwd, pos);
							_fm.Unsplit();
						}
					}
				}
				_fm.UnapplyClipContext();
			}
			return gates;
		}

		private void SetClipOffset(Vector3 _offset)
		{
			m_gizmoClipOffset = _offset;
		}
		private void ApplySplit(MeshUtils.FlexibleMesh _fm, Vector3 _normal, Vector3 _origin)
		{
			_fm.Split(new Plane(_normal, _origin));
#if DEBUG_CLIPS
			GizmoClip(_origin + m_gizmoClipOffset, _normal, true);
#endif
		}
		private void ApplyClip(MeshUtils.FlexibleMesh _fm, Vector3 _normal, Vector3 _origin)
		{
			_fm.Clip(new Plane(_normal, _origin), false);
#if DEBUG_CLIPS
			GizmoClip(_origin + m_gizmoClipOffset, _normal, false);
#endif
		}

		private static Vector3 m_gizmoClipOffset;
		private static List<Vector3> m_gizmoClips = new();
		private void GizmoClip(Vector3 _origin, Vector3 _normal, bool _doubleSided)
		{
			m_gizmoClips.Add(_origin);
			_normal.y = _doubleSided ? 1 : 0;
			m_gizmoClips.Add(_normal);
		}

		public static void ClearGizmoClips()
		{
			m_gizmoClips.Clear();
		}
		public static void DrawGizmoClips()
		{
			Gizmos.color = Color.magenta;
			for (int i = 0; i < m_gizmoClips.Count; i += 2)
			{
				var origin = m_gizmoClips[i + 0];
				var fwd = m_gizmoClips[i + 1];
				var side = new Vector3(-fwd.z, 0, fwd.x);
				Gizmos.DrawLine(origin - side * 2, origin + side * 2);
			}
			Gizmos.color = Color.red;
			for (int i = 0; i < m_gizmoClips.Count; i += 2)
			{
				var origin = m_gizmoClips[i + 0];
				var fwd = m_gizmoClips[i + 1];
				bool doubleSided = fwd.y > 0;
				fwd.y = 0;
				Gizmos.DrawLine(origin, origin + fwd * 1.5f);
				if (doubleSided) Gizmos.DrawLine(origin, origin - fwd * 1.0f);
			}
		}

		Vector3 m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace;
		NativeArray<float> m_terrainSmoothValues;
		NativeArray<Vector3> m_jitteredTerrainSmoothPositions;
		NativeArray<Vector3> m_unjitteredTerrainSmoothPositions;
		float m_terrainSmoothStart, m_terrainSmoothEnd;
		RiverDepthDrop m_riverDepthDrop;
		float m_ravineStartFraction, m_ravineEndFraction;

		public void PrepareEarlyTerrainSmoothing(Vector3 _dirtyMin, Vector3 _dirtyMax)
		{
			UpdateSmoothed();
			if (IsValid == false) return;
			var set = Set;
			var gd = GlobalData.Me;
			var radius = set.m_splatRadiusOuter;
			var step = LocStep * radius * .25f;
			float smoothWidthInner = set.m_heightWidthInner;
			float smoothWidthOuter = set.m_heightWidthOuter;

			m_dirtyMinTerrainSpace = new Vector3(GlobalData.TerrainXf(_dirtyMin.x), 0, GlobalData.TerrainZf(_dirtyMin.z));
			m_dirtyMaxTerrainSpace = new Vector3(GlobalData.TerrainXf(_dirtyMax.x), 0, GlobalData.TerrainZf(_dirtyMax.z));
			var dirtyMid = (_dirtyMax + _dirtyMin) * .5f;
			var maxDX = (_dirtyMax.x - _dirtyMin.x) * .5f + smoothWidthOuter + 2;
			var maxDZ = (_dirtyMax.z - _dirtyMin.z) * .5f + smoothWidthOuter + 2;

			bool isWaterSurface = set.m_createSurfaceMaterial != null;
			if (isWaterSurface)
			{
				m_terrainSmoothStart = gd.GetRealOriginalHeight(GetPoint(0));
				m_terrainSmoothEnd = gd.GetRealOriginalHeight(GetPoint(1));
				if (set.m_createSurfaceAlwaysFlat) m_terrainSmoothStart = m_terrainSmoothEnd = Mathf.Min(m_terrainSmoothStart, m_terrainSmoothEnd);
				m_riverDepthDrop = new RiverDepthDrop(m_path[0], m_terrainSmoothStart, m_terrainSmoothEnd, TotalLength(), set.m_createSurfaceAverageLengthBetweenDrops, set.m_createSurfaceMaxDropHeight, set.m_createSurfaceMaxSlope);
			}
			
			bool isRavine = set.m_isRavine;
			m_ravineStartFraction = set.m_ravineStartFraction;
			m_ravineEndFraction = set.m_ravineEndFraction;

			var posListUnjittered = new List<Vector3>();
			if (isRavine)
			{
				var minStep = step * .1f;
				for (float t = 0; t < 1;)
				{
					var p = CatmullRom(m_pathSmoothed, t);
					p.y = t;
					posListUnjittered.Add(p);
					float mul = 1;
					if (t < m_ravineStartFraction) mul = math.max(minStep, (t + step * .1f) / m_ravineStartFraction);
					else if (t > 1 - m_ravineEndFraction) mul = math.max(minStep, (1 - t + step * .1f) / m_ravineEndFraction);
					t += step * mul;
				}
			}
			else
			{
				for (float t = 0; t < 1; t += step)
				{
					var p = CatmullRom(m_pathSmoothed, t);
					p.y = t;
					posListUnjittered.Add(p);
				}
			}
			m_unjitteredTerrainSmoothPositions = posListUnjittered.ToNativeArray(Allocator.TempJob);
		}

		public struct NativeRiverDepthDrop
		{
			public NativeArray<float> m_dropTs;
			public NativeArray<float> m_dropHeights;
			public float m_slopeBlend;

			public bool IsFlat
			{
				get
				{
					var dh = m_dropHeights[^1] - m_dropHeights[0];
					return dh * dh < .001f * .001f;
				}
			}

			public float HeightAtT(float _t)
			{
				var hStep = HeightAtTstep(_t);
				var hSlope = HeightAtTslope(_t);
				return hStep + (hSlope - hStep) * m_slopeBlend;
			}

			float HeightAtTstep(float _t)
			{
				for (int i = 1; i < m_dropTs.Length; ++i)
					if (_t < m_dropTs[i])
						return m_dropHeights[i - 1];
				return m_dropHeights[^1];
			}

			float HeightAtTslope(float _t)
			{
				var s = m_dropHeights[0];
				var e = m_dropHeights[^1];
				return s + (e - s) * _t;
			}

			public void Dispose()
			{
				m_dropTs.Dispose();
				m_dropHeights.Dispose();
			}
			
			public static NativeRiverDepthDrop Null()
			{
				var drop = new NativeRiverDepthDrop()
				{
					m_dropTs = new NativeArray<float>(2, Allocator.Persistent),
					m_dropHeights = new NativeArray<float>(2, Allocator.Persistent)
				};
				drop.m_dropTs[0] = 0;
				drop.m_dropTs[1] = 1;
				drop.m_dropHeights[0] = 0;
				drop.m_dropHeights[1] = 0;
				return drop;
			}
		}
		public class RiverDepthDrop
		{
			float[] m_dropTs;
			float[] m_dropHeights;
			uint m_seed;
			float m_slopeBlend;

			public RiverDepthDrop(Vector3 _pos, float _heightAtStart, float _heightAtEnd, float _totalLength, float _averageLengthBetweenDrops, float _maxDropHeight, float _maxSlopeGradient)
			{
				m_seed = (uint)(math.asint(_pos.x) ^ math.asint(_pos.z) ^ math.asint(_totalLength) ^ 0x71771);
				var heightChange = _heightAtStart - _heightAtEnd;
				var riverLength = _totalLength;
				if (riverLength < .001f) riverLength = .001f;
				int drops = Mathf.CeilToInt(riverLength / _averageLengthBetweenDrops);
				var heightChangePerDrop = heightChange / drops;
				if (heightChangePerDrop * heightChangePerDrop > _maxDropHeight * _maxDropHeight)
				{
					drops = Mathf.CeilToInt(Mathf.Abs(heightChange) / _maxDropHeight);
					heightChangePerDrop = heightChange / drops;
				}
				float segmentDistance = riverLength / drops;
				m_dropTs = new float[1+drops+1];
				m_dropHeights = new float[1+drops+1];
				m_dropTs[0] = 0;
				m_dropHeights[0] = _heightAtStart;
				m_dropTs[^1] = 1;
				m_dropHeights[^1] = _heightAtEnd;
				for (int i = 0; i < drops; ++i)
				{
					m_dropTs[i + 1] = (i + .5f) * segmentDistance / riverLength;
					m_dropHeights[i + 1] = _heightAtStart - heightChangePerDrop * (i + 1);
				}
				for (int i = 0; i < drops; ++i)
				{
					var rt = Utility.XorShift01(ref m_seed);
					var rh = Utility.XorShift01(ref m_seed);
					m_dropTs[i + 1] = Mathf.Lerp(m_dropTs[i], m_dropTs[i + 2], rt);
					m_dropHeights[i + 1] = Mathf.Lerp(m_dropHeights[i], m_dropHeights[i + 2], rh);
				}
				if (_maxSlopeGradient < .01f)
					m_slopeBlend = 0;
				else
				{
					var gradient = math.abs(heightChangePerDrop) / segmentDistance;
					m_slopeBlend = Mathf.Clamp01(2 - gradient / _maxSlopeGradient);
				}
			}
			
			public NativeRiverDepthDrop ToNative(float _adjustHeight)
			{
				var drop = new NativeRiverDepthDrop()
				{
					m_dropTs = new NativeArray<float>(m_dropTs, Allocator.TempJob),
					m_dropHeights = new NativeArray<float>(m_dropHeights, Allocator.TempJob),
					m_slopeBlend = m_slopeBlend,
				};
				for (int i = 0; i < drop.m_dropHeights.Length; ++i) drop.m_dropHeights[i] += _adjustHeight;
				return drop;
			}

			public float HeightAtT(float _t)
			{
				var hStep = HeightAtTstep(_t);
				var hSlope = HeightAtTslope(_t);
				return hStep + (hSlope - hStep) * m_slopeBlend;
			}

			float HeightAtTstep(float _t)
			{
				for (int i = 1; i < m_dropTs.Length; ++i)
					if (_t < m_dropTs[i])
						return m_dropHeights[i - 1];
				return m_dropHeights[^1];
			}

			float HeightAtTslope(float _t)
			{
				var s = m_dropHeights[0];
				var e = m_dropHeights[^1];
				return s + (e - s) * _t;
			}

			public void AddDecorations(Path _path, GameObject[] _decorationsTop, GameObject[] _decorationsBottom, ref List<GameObject> _instances)
			{
				if ((_decorationsTop == null || _decorationsTop.Length == 0) && (_decorationsBottom == null || _decorationsBottom.Length == 0))
					return;
				if (_instances == null) _instances = new();
				var step = _path.LocStep;
				int countTop = _decorationsTop?.Length ?? 0;
				int countBottom = _decorationsBottom?.Length ?? 0;
				for (int i = 1; i < m_dropTs.Length - 1; ++i)
				{
					if (countTop > 0)
					{
						int r = (((int) Utility.XorShift(ref m_seed) & 0xFFFF) * countTop) >> 16;
						var prefab = _decorationsTop[r];
						var pos = _path.GetPoint(m_dropTs[i] - step);
						pos.y = m_dropHeights[i - 1] - .5f;
						var orientation = Quaternion.Euler(0, Utility.XorShift01(ref m_seed) * 360, 0);
						var go = GameObject.Instantiate(prefab, pos, orientation);
						go.SetStaticRecursively(true);
						_instances.Add(go);
					}
					if (countBottom > 0)
					{
						int r = (((int) Utility.XorShift(ref m_seed) & 0xFFFF) * countBottom) >> 16;
						var prefab = _decorationsBottom[r];
						var pos = _path.GetPoint(m_dropTs[i] + step);
						pos.y = m_dropHeights[i] - .5f;
						var orientation = Quaternion.Euler(0, Utility.XorShift01(ref m_seed) * 360, 0);
						var go = GameObject.Instantiate(prefab, pos, orientation);
						go.SetStaticRecursively(true);
						_instances.Add(go);
					}
				}
			}
		}

		public void PrepareTerrainSmoothing()
		{
			UpdateSmoothed();
			if (IsValid == false) return;
			//if (IsNavBlocker) return;
			
			var set = Set;
			var gd = GlobalData.Me;

			var radius = set.m_splatRadiusOuter;
			var step = LocStep * radius * .25f;
			int numSteps = m_unjitteredTerrainSmoothPositions.Length;//Mathf.CeilToInt(1 / step);
			var heights = new float[numSteps];
			int index = 0;
			var heightAdjust = set.m_terrainAdjust;
			float smoothWidthInner = set.m_heightWidthInner;
			float smoothWidthOuter = set.m_heightWidthOuter;
			uint seedBase = 0x1234;
			float randomness = set.m_splatOuterRandomness;
			float maxRadiusOuter = (smoothWidthOuter + randomness) * c_surfaceWidthFactor;
			var posList = new List<Vector3>();
			
			bool isWaterSurface = set.m_createSurfaceMaterial != null;
			float heightAtStart = 0, heightAtEnd = 0;
			if (isWaterSurface)
			{
				heightAtStart = gd.GetRealHeight(GetPoint(0));
				heightAtEnd = gd.GetRealHeight(GetPoint(1));
			}
			m_jitteredTerrainSmoothPositions = GlobalData.Me.CheckTerrainBlockerBatch(m_unjitteredTerrainSmoothPositions, smoothWidthInner, isWaterSurface, randomness);
			for (int i = 0; i < m_unjitteredTerrainSmoothPositions.Length; ++i)
			{
				float3 p = m_unjitteredTerrainSmoothPositions[i];
				var t = p.y;
				float h;
				if (m_riverDepthDrop != null)
					h = m_riverDepthDrop.HeightAtT(t);
				else if (isWaterSurface)
					h = math.lerp(heightAtStart, heightAtEnd, t);
				else
					h = gd.GetRealHeight(m_jitteredTerrainSmoothPositions[i]);// GetOriginalHeight(p);
				heights[index++] = h;
			}
			m_terrainSmoothValues = new NativeArray<float>(Smooth(heights, index), Allocator.TempJob);
		}

		NativeArray<Vector3> m_nullV3NativeArray = new NativeArray<Vector3>(0, Allocator.Persistent);
		public void ExecuteEarlyTerrainSmoothing()
		{
			if (IsValid == false) return;
			var gd = GlobalData.Me;
			var set = Set;
			bool isWaterSurface = set.m_createSurfaceMaterial != null;
			int count = m_unjitteredTerrainSmoothPositions.Length;
			float smoothWidthOuter = set.m_heightWidthOuter;
			float randomness = set.m_splatOuterRandomness;
			float maxRadiusOuter = (smoothWidthOuter + randomness) * c_surfaceWidthFactor;
			if (isWaterSurface)
			{
				float heightAtStart = m_terrainSmoothStart;
				float heightAtEnd = m_terrainSmoothEnd;
				NativeArray<float> heights = new NativeArray<float>(count, Allocator.TempJob);
				for (int i = 0; i < count; ++i)
				{
					var p = m_unjitteredTerrainSmoothPositions[i];
					var t = p.y;
					heights[i] = m_riverDepthDrop?.HeightAtT(t) ?? math.lerp(heightAtStart, heightAtEnd, t);
				}
				gd.SetManyHeightsRadial(m_nullV3NativeArray, m_unjitteredTerrainSmoothPositions, maxRadiusOuter, maxRadiusOuter * set.m_createSurfaceHeightSmoothRadiusMultiplier, heights, 0, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace, GlobalData.RadialHeightsBatchJobMode.WATERSURFACE, true, true);
				heights.Dispose();
			}
		}

		public void ExecuteTerrainSmoothing()
		{
			if (IsValid == false) return;
			var gd = GlobalData.Me;
			var set = Set;
			int count = m_jitteredTerrainSmoothPositions.Length;
			var ignoreWater = set.m_ignoreWater;
			var heightAdjust = set.m_terrainAdjust;
			float smoothWidthInner = set.m_heightWidthInner;
			float smoothWidthOuter = set.m_heightWidthOuter;
			var islandWidth = set.m_islandWidth;
			var islandHeightMin = set.m_islandHeightMin;
			var islandHeightMax = set.m_islandHeightMax;
			var islandRandom = set.m_islandRandom;
			var islandWidthInner = islandWidth * set.m_islandSteepness;
			var radius = set.m_splatRadiusOuter;
			var step = LocStep * radius * .25f;
			var inset = Mathf.FloorToInt(set.m_islandStartEnd / (TotalLength() * step));
			var isRavine = set.m_isRavine;
			var smoothProfile = isRavine ? 0 : 1;
			var smoothEdge = set.m_ravineEdgeSmooth;

			if (isRavine)
			{
				var ravineData = new GlobalData.RavineData();
				ravineData.startFraction = m_ravineStartFraction;
				ravineData.endFraction = m_ravineEndFraction;
				ravineData.step = step;

				gd.SetManyHeightsRadial(m_jitteredTerrainSmoothPositions, m_unjitteredTerrainSmoothPositions, smoothWidthInner, smoothWidthOuter, m_terrainSmoothValues, heightAdjust, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace, GlobalData.RadialHeightsBatchJobMode.RAVINE, _ignoreWaterPresence: ignoreWater, _smoothProfile: smoothProfile, _ravineData: ravineData);
				if (smoothEdge > 0)
				{
					gd.SmoothHeightsRadial(m_jitteredTerrainSmoothPositions, smoothWidthOuter, smoothWidthOuter + smoothEdge, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace);
				}
			}
			else
			{
				gd.SetManyHeightsRadial(m_jitteredTerrainSmoothPositions, m_unjitteredTerrainSmoothPositions, smoothWidthInner, smoothWidthOuter, m_terrainSmoothValues, heightAdjust, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace, GlobalData.RadialHeightsBatchJobMode.NORMAL, _ignoreWaterPresence: ignoreWater, _smoothProfile: smoothProfile);
				gd.SmoothHeightsRadial(m_jitteredTerrainSmoothPositions, smoothWidthInner, smoothWidthOuter, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace);
			}
			if (islandWidth > 0)
			{
				var islandData = new GlobalData.IslandData();
				islandData.m_heightMin = islandHeightMin;
				islandData.m_heightMax = islandHeightMax;
				islandData.m_random = islandRandom;
				islandData.m_inset = inset;

				gd.SetManyHeightsRadial(m_jitteredTerrainSmoothPositions, m_unjitteredTerrainSmoothPositions, islandWidthInner, islandWidth, m_terrainSmoothValues, heightAdjust, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace, GlobalData.RadialHeightsBatchJobMode.ISLAND, _islandData: islandData);
			}
			if (set.m_fillWaterPresence)
			{
				float waterPresenceRadius = smoothWidthOuter * 1.1f;
				for (int i = 0; i < count; ++i)
				{
					var t = m_unjitteredTerrainSmoothPositions[i].y;
					const float c_heightMargin = .25f;
					var h = m_riverDepthDrop?.HeightAtT(t) - c_heightMargin ?? 1e23f;
					var p = m_jitteredTerrainSmoothPositions[i];
					gd.SetWaterPresenceRadial(p, waterPresenceRadius, m_dirtyMinTerrainSpace, m_dirtyMaxTerrainSpace, h);
				}
			}

			m_unjitteredTerrainSmoothPositions.Dispose();
			m_jitteredTerrainSmoothPositions.Dispose();
		}

		static float[] s_smoothKernel = { .2f, .2f, .2f, .2f, .2f };
		public static float[] Smooth(float[] _values, int _count)
		{
			if (_count < s_smoothKernel.Length)
				return _values;
			var working = new float[_count];
			int kernelSize = s_smoothKernel.Length;
			int kernelMid = kernelSize / 2;
			for (int i = 0; i < kernelMid; ++i)
			{
				working[i] = _values[i];
				working[_count - 1 - i] = _values[_count - 1 - i];
			}
			for (int i = kernelMid; i < _count - kernelMid; ++i)
			{
				float total = 0;
				for (int j = 0; j < kernelSize; ++j)
				{
					int index = i + j - kernelMid;
					total += _values[index] * s_smoothKernel[j];
				}
				working[i] = total;
			}
			return working;
		}
		
		float NewPathPointThreshold => Set.m_stepSize;
		float NewPathPointThresholdSqrd => Set.m_stepSize * Set.m_stepSize;
		
		public static Vector3 FindValidPoint(Vector3 _p)
		{
			if (RoadManager.Me.IsInDebugEditMode == false)
			{
				_p = DistrictManager.Me.NearestPointInOwnedDistrict(_p);
				if (GlobalData.Me.IsWallDraggableAtPoint(_p) == false) _p = Vector3.zero;
			}
			return _p;
		}

		private List<Vector3> m_validPointsTo = new();
		private List<Vector3> GetValidPointsTo(Vector3 _to)
		{
			m_validPointsTo.Clear();

			_to = FindValidPoint(_to);
			if (_to.sqrMagnitude < .001f * .001f) return m_validPointsTo;
			
			var dir = _to - (Vector3) m_path[^2];
			var dist = dir.xzMagnitude();
			int count = (int) (dist / NewPathPointThreshold);
			if (count > 0)
			{
				float3 offset = NewPathPointThreshold / dist * dir.GetXZ();
				float3 nextPos = m_path[^2];
				for (int i = 0; i < count; i++)
				{
					nextPos += offset;
					var validPos = FindValidPoint(nextPos);
					if (validPos.sqrMagnitude < .001f * .001f)
					{
						m_validPointsTo.Clear();
						return m_validPointsTo;
					}
					//Debug.LogError($"Added Point {i}/{count} to {nextPos} valid {validPos} on {GlobalData.Me.GetNavAtPoint(validPos)} (was {GlobalData.Me.GetNavAtPoint(nextPos)})");
					m_validPointsTo.Add(validPos);
				}
			}

			m_validPointsTo.Add(_to);
			return m_validPointsTo;
		}

		public void ConsiderNewPoint(Vector3 _pos)
		{
			// offset ray based on m_handleRaise
			if (m_path.Count > 0)
			{
				var lastPoint = m_path[^1];
				var set = Set;
				var raise = set.m_handleRaise;
				var lastPointRaised = lastPoint;
				lastPointRaised.y += raise;
				var screenPosBase = Camera.main.WorldToScreenPoint(lastPoint);
				var screenPosRaise = Camera.main.WorldToScreenPoint(lastPointRaised);
				var screenPosDelta = screenPosBase - screenPosRaise;
				_pos += screenPosDelta;
			}

			var ray = Camera.main.RayAtScreenPosition(_pos);
			if (GameManager.Me.Raycast(ray.origin, ray.direction * 1000, out var hit, GameManager.c_layerTerrainBit))
			{
				hit.point = PathQuant(hit.point);
				hit.point = SnapToOtherPaths(hit.point);

				if (Count == 0) Add(hit.point);
				if (Count == 1) Add(hit.point);

				var points = GetValidPointsTo(hit.point);
				if (points.Count == 0) return;
				m_path[^1] = points[0];
				for (int i = 1; i < points.Count; ++i) m_path.Add(points[i]);
				
				m_smoothedDirty = true;
				if (Count >= 3)
				{
					int lastIndexCheck = Mathf.Max(0, Count - 4);
					for (int i = Count - 3; i >= lastIndexCheck; --i)
					{
						if (((Vector3)m_path[i] - hit.point).xzSqrMagnitude() < NewPathPointThresholdSqrd)
						{
							for (int j = Count - 2; j > i; --j)
								RemoveAt(j);
							break;
						}
					}
				}
				UpdateSmoothed();
				m_pathBreakInfo.Update();
			}
		}

		public Vector3 SnapToOtherPaths(Vector3 _point)
		{
			return RoadManager.Me.m_pathSet.GetClosestPathToPoint(this, IsNavBlocker, _point, c_snapDistance);
		}

		public static Vector3 PathQuant(Vector3 _point)
		{
			const float c_quant = 2;//8;
			const float c_invQuant = 1 / c_quant;
			_point.x = Mathf.Round(_point.x * c_quant) * c_invQuant;
			_point.y = Mathf.Round(_point.y * c_quant) * c_invQuant;
			_point.z = Mathf.Round(_point.z * c_quant) * c_invQuant;
			return _point;
		}

		private static bool RayHitsPoint(Ray _ray, Vector3 _point, ref float _minSquareDistance)
		{
			var originToPoint = _point - _ray.origin;
			var k = Vector3.Dot(originToPoint, _ray.direction);
			var closestPointOnRay = _ray.GetPoint(k);
			var distanceSqrdToRay = (closestPointOnRay - _point).sqrMagnitude;
			if (distanceSqrdToRay < _minSquareDistance)
			{
				_minSquareDistance = distanceSqrdToRay;
				return true;
			}
			return false;
		}

		private static bool CanEditPoint(bool _isEditMode, Vector3 _point)
		{
			if (_isEditMode) return true;
			return DistrictManager.Me.IsWithinDistrictBounds(_point);
		}
		
		public int RayHitsEnd(Ray _ray, ref float _minSquareDistance, bool _isEditMode)
		{
			if (Count < 1) return 0;
			var set = Set;
			int rc = 0;
			if (RoadManager.Me.IsHandleActive(this, true) && RayHitsPoint(_ray, ((Vector3) m_path[0]).GroundPosition(set.m_handleRaise + .5f), ref _minSquareDistance) && CanEditPoint(_isEditMode, m_path[0])) rc = 1;
			if (RoadManager.Me.IsHandleActive(this, false) && RayHitsPoint(_ray, ((Vector3) m_path[^1]).GroundPosition(set.m_handleRaise + .5f), ref _minSquareDistance) && CanEditPoint(_isEditMode, m_path[^1])) rc = 2;
			return rc;
		}
		
		public bool IsFullyConstructed => ConstructedPathDistance() >= TotalLength() - .001f;

		public class PathBreakInfo
		{
			public class PathBreakItem
			{
				public float m_tStart, m_tEnd;
				public PathBreak m_pathBreak;
			}
			private List<PathBreakItem> m_pathBreaks = new ();
			private Path m_path;
			private float m_initialLength, m_initialConstructedLength;

			public void Add(float _tStart, float _tEnd)
			{
				var startPos = m_path.GetPoint(_tStart);
				var endPos = m_path.GetPoint(_tEnd);
				var centerPos = (startPos + endPos) * .5f;
				var pathBreak = PathBreak.FindAt(centerPos, 4);
				//Debug.LogError($"Path {RoadManager.Me.PathIndex(m_path)} has break from {m_initialLength * _tStart:n0} to {m_initialLength * _tEnd:n0} [{_tStart} to {_tEnd}] - total {(_tEnd - _tStart) * m_initialLength} construction removed");
				m_pathBreaks.Add(new PathBreakItem()
				{
					m_tStart = _tStart,
					m_tEnd = _tEnd,
					m_pathBreak = pathBreak
				});
			}

			public void Clear()
			{
				m_pathBreaks.Clear();
			}

			public void Init(Path _path)
			{
				m_path = _path;
				m_initialLength = _path.TotalLength();
				m_initialConstructedLength = _path.ConstructedPathDistance();
				m_pathBreaks.Clear();

				NativeishList<float2> buildingTs = new(RoadManager.Me.m_pathSet.m_blockPositions.Count * 2);
				_path.FindAllIntersections(new List<Path>(), false, ref buildingTs);

				//Debug.LogError($"Path {RoadManager.Me.PathIndex(m_path)} has constructed {m_initialConstructedLength:n0} out of {m_initialLength:n0} total length, with {buildingTs.Count / 2} building breaks");

				for (int i = 0; i < buildingTs.Count - 1; i += 2)
				{
					var startT = buildingTs[i].x;
					var endT = buildingTs[i + 1].x;
					var type = (int)buildingTs[i].y;
					switch (type)
					{
						case PathSet.c_blockTypeBuilding:
							break;
						case PathSet.c_blockTypeBroken:
						case PathSet.c_blockTypeBrokenPartial:
							Add(startT, endT);
							break;
					}
				}
				buildingTs.Dispose();
			}

			public void Update()
			{
				// check current length against all path breaks
				var currentLength = m_path.TotalLength();
				for (int i = m_pathBreaks.Count - 1; i >= 0; --i)
				{
					var item = m_pathBreaks[i];
					var itemStartDistance = item.m_tStart * m_initialLength;
					if (currentLength < itemStartDistance)
					{
						// path break is no longer valid, remove it
						var materialRemoved = (item.m_tEnd - item.m_tStart) * m_initialLength;
						//Debug.LogError($"Path {RoadManager.Me.PathIndex(m_path)} has removed break from {m_initialLength * item.m_tStart:n0} to {m_initialLength * item.m_tEnd:n0} [{item.m_tStart} to {item.m_tEnd}] - total {materialRemoved:n0} construction removed");
						GameObject.Destroy(item.m_pathBreak.gameObject);
						m_pathBreaks.RemoveAt(i);
						// also subtract the removed material from the constructed length so the constructed section when drawn back out will be that much shorter
						m_path.m_finishedDistance = Mathf.Max(0, m_path.m_finishedDistance - materialRemoved);
					}
				}
			}
		}
		
		private PathBreakInfo m_pathBreakInfo = new();

		public void StartEdit(int _end, int _type)
		{
			m_pathBreakInfo.Init(this);
			
			if (_end == 1)
			{
				// picked up start of path, reverse the path so we're moving the end instead
				m_path.Reverse();
			}
			UpdateSmoothed();
			if (_end == 0) // new path
				m_roadType = _type;

			m_startSplatMin = m_minExtent - Vector3.one * 4;
			m_startSplatMax = m_maxExtent + Vector3.one * 4;
		}

		public bool Finish()
		{
			m_pathBreakInfo.Clear();
			
			if (IsDegenerate)
			{
				m_path.Clear();
				ClearVisuals();
			}
			UpdateHandles(false, -1);
			m_splatChangeMin = Vector3.one * 1e23f;
			m_splatChangeMax = Vector3.one * -1e23f;
			m_pathSmoothed.Dispose();
			m_smoothedDirty = true;
			return m_path.Count == 0;
		}

		public void FillNavAndDetail(Vector3 _min, Vector3 _max, List<Vector4> _intersections, NativeishList<float2> _buildingIntersections, List<GameObject> _gates, bool _navOnly = false)
		{
			int x = GlobalData.TerrainX(_min.x);
			int z = GlobalData.TerrainZ(_min.z);
			int w = GlobalData.TerrainXc(_max.x) - x;
			int h = GlobalData.TerrainZc(_max.z) - z;
			FillNavAndDetail(x, z, w, h, _intersections, _navOnly, _buildingIntersections, _gates);
		}
		public void FillNavAndDetail(int _x, int _z, int _w, int _h, List<Vector4> _intersections, bool _navOnly, NativeishList<float2> _buildingIntersections, List<GameObject> _gates)
		{
			if (Count == 0) return;
			UpdateSmoothed();
			if (m_pathSmoothed.Count == 0) return;
			var set = Set;
			var navWidth = set.m_navPathWidth;
			var innerNavWidth = navWidth - set.m_navPavementWidth;
			var treeRemoveWidth = set.m_treeRemoveWidth > 0 ? set.m_treeRemoveWidth : set.m_detailWidth + 1;
			var treeRemoveExtent = new float3(treeRemoveWidth, treeRemoveWidth, treeRemoveWidth);
			var step = LocStep;
			int stepIndex = 0;
			GlobalData.NavCostTypes innerNavCost = GlobalData.NavCostTypes.Road;
			GlobalData.NavCostTypes outerNavCost = GlobalData.NavCostTypes.Pavement;
			if (set.m_isPavement)
			{
				innerNavCost = GlobalData.NavCostTypes.Pavement;
				innerNavWidth = 0;
			}
			List<float> intersectionTs = new List<float>();
			int intersectionsPassed = 0;
			const float c_locStepPathWidth = 8; 
			if (set.m_blocksNavigation)
			{
				outerNavCost = innerNavCost = set.m_pushThroughable ? GlobalData.NavCostTypes.PushThrough : (set.m_creatureBreakable ? GlobalData.NavCostTypes.NoNavCanBreak : GlobalData.NavCostTypes.NoNav);
				if (_intersections.Count + _buildingIntersections.Count > 0)
				{
					int pathCount = 0, buildCount = 0;
					Vector4 pathIntersect;
					float pathIntMid;
					float3 pathIntFwd;
					float clipMult;
					float pathIntWidth;
					float pathIntMin = 2, pathIntMax = 2, buildIntMin = 2, buildIntMax = 2;

					void FillNextIntersectionDetail()
					{
						if (pathCount < _intersections.Count)
						{
							pathIntersect = _intersections[pathCount];
							if (DoesIntersectionCut(pathIntersect) == false)
							{
								pathCount++;
								FillNextIntersectionDetail();
							}
							else
							{
								pathIntMid = pathIntersect.w;
								(_, pathIntFwd, _) = CatmullRomPosFwdSide(m_pathSmoothed, _intersections[pathCount].w);
								clipMult = Mathf.Min(1.6f, 1 / Mathf.Abs(Vector3.Dot(pathIntFwd, new Vector3(-pathIntersect.z, 0, pathIntersect.x))));
								pathIntWidth = LocStep * c_locStepPathWidth * clipMult;
								pathIntMin = pathIntMid - pathIntWidth;
								pathIntMax = pathIntMid + pathIntWidth;
								++pathCount;
							}
						}
						else
							pathIntMin = pathIntMax = 2;
					}
					void FillNextBuildingIntersectionDetail()
					{
						if (2 * buildCount + 1 < _buildingIntersections.Count)
						{
							if ((int) _buildingIntersections[2 * buildCount + 0].y == (int) PathManager.PathSet.c_blockTypeBrokenPartial)
							{
								++buildCount;
								FillNextBuildingIntersectionDetail();
								return;
							}
							buildIntMin = _buildingIntersections[2 * buildCount + 0].x;
							buildIntMax = _buildingIntersections[2 * buildCount + 1].x;
							++buildCount;
						}
						else
							buildIntMin = buildIntMax = 2;
					}

					FillNextIntersectionDetail();
					FillNextBuildingIntersectionDetail();
					
					while (pathIntMin < 2 || buildIntMin < 2)
					{
						float nextAddMin, nextAddMax;
						if (pathIntMin < buildIntMin)
						{
							(nextAddMin, nextAddMax) = (pathIntMin, pathIntMax);
							FillNextIntersectionDetail();
						}
						else
						{
							(nextAddMin, nextAddMax) = (buildIntMin, buildIntMax);
							FillNextBuildingIntersectionDetail();
						}
						if (intersectionTs.Count > 0 && nextAddMin <= intersectionTs[^1])
							intersectionTs[^1] = math.max(nextAddMin, intersectionTs[^1]);
						else
						{
							intersectionTs.Add(nextAddMin);
							intersectionTs.Add(nextAddMax);
						}
					}
				}
			}
			else if (m_bridgeBoundaries.Count > 0)
			{
				var offsetRadius = LocStep * c_locStepPathWidth * set.m_navPathWidth * (1.0f / 16f); // offset from center to counter the radial-forward splatting approach
				var insetT = LocStep * -4f;
				for (int i = 0; i < m_bridgeBoundaries.Count; i += 2)
				{
					intersectionTs.Add(m_bridgeBoundaries[i + 0] + offsetRadius + insetT);
					intersectionTs.Add(m_bridgeBoundaries[i + 1] + offsetRadius - insetT);
				}
			}
			intersectionTs.Add(1);
			var totalLength = TotalLength();
			var fractionComplete = Mathf.Min(1f, ConstructedPathDistance() / totalLength);
			float tStart = 0, tEnd = fractionComplete;
			if (IsCycle == false)
			{
				// take into account the length of end caps
				var capLength = Mathf.Max(0, set.EndCapLength() - set.m_navPathWidth * .25f);
				var overshoot = capLength / totalLength;
				tStart -= overshoot;
				if (tEnd >=  .999f)
					tEnd += overshoot;
			}
			
			GlobalData.Me.FillNavPathBatch(tStart, tEnd, step, m_pathSmoothed, intersectionTs, innerNavWidth, navWidth, innerNavCost, outerNavCost, _x, _z, _w, _h, set.m_blocksNavigation == false);
			if (_navOnly == false)
			{
				for (float t = tStart; t < tEnd; t += step)
				{	
					var p = CatmullRom(m_pathSmoothed, t);
					//CameraRenderSettings.Me.FillDetailRadial(GlobalData.TerrainXr(p.x), GlobalData.TerrainZr(p.z), (int)(set.m_detailWidth * GlobalData.c_terrainXZScale), 0);
					if ((stepIndex++ & 3) == 0)
					{
						var h = 1e23f;
						if (m_riverDepthDrop != null)
							h = m_riverDepthDrop.HeightAtT(t);
						TerrainPopulation.Me.DisableInstancesInRange(p - treeRemoveExtent, p + treeRemoveExtent, h);
					}
				}	
			}
		}

		public void GeneratePlots(List<Vector4> _list, int _width, int _depth, RoadSet.BuildingsAccepted _acceptedTypes, Vector3 _zoneCenter, float _zoneRadius)
		{
			var set = Set;
			if (set.m_blocksNavigation) return;
			if ((set.m_buildingsAccepted & _acceptedTypes) == 0) return;

			UpdateSmoothed();
			if (!IsValid) return;

			const int c_plotMargin = 2;
			var lastPos = new float3[] { float3.zero, float3.zero };
			var step = LocStep * .125f;
			float roadHalfWidth = Set.m_navPathWidth + Set.m_pathToBuildingDistance;
			int plotDepth = _depth + c_plotMargin * 2, plotWidth = _width + c_plotMargin * 2;
			var offset = new float3(2, 2, 2);
			for (float t = 0; t <= 1; t += step)
			{
				var pos = CatmullRom(m_pathSmoothed, t);
				var posFwd = CatmullRom(m_pathSmoothed, t + step * .5f);
				var fwd = math.normalize(new float3(posFwd.x - pos.x, 0, posFwd.z - pos.z));
				var side = new float3(-fwd.z, 0, fwd.x);
				var degrees = Mathf.Floor(-Mathf.Atan2(side.z, side.x) * Mathf.Rad2Deg);
				for (int s = -1; s <= 1; s += 2)
				{
					int sIndex = (s + 1) / 2;
					if (math.lengthsq(pos - lastPos[sIndex]) < 2 * 2) continue;
					var plotFwd = side * s;
					var plotSide = fwd * s;
					var plotFrontPos = pos + plotFwd * roadHalfWidth;
					var plotCenter = plotFrontPos + plotFwd * (plotDepth / 2);
					var pp = plotFrontPos + offset + plotFwd * 2;
					var plot = new Vector4(pp.x, 0, pp.z, degrees - s * 90);
					float dx = pp.x - _zoneCenter.x, dz = pp.z - _zoneCenter.z;
					bool isInZone = dx * dx + dz * dz < _zoneRadius * _zoneRadius;
					bool isValid = isInZone && IsPlotValid(plotCenter, plotFwd, plotSide, plotWidth, plotDepth);
#if false
					if (isValid == false)
					{
						plot.w += 3600;
						isValid = true;
					}
#endif
					if (isValid)
					{
						lastPos[sIndex] = pos;
						_list.Add(plot);
					}
				}
			}

			if (IsCycle == false)
			{
				// take into account the length of end caps
				var capLength = Mathf.Max(0, set.EndCapLength() - set.m_navPathWidth * .25f);
				float tStart = -capLength / TotalLength();
				float tEnd = 1 - tStart;
				for (int tt = 0; tt <= 1; ++tt)
				{
					int alongDir = tt * 2 - 1;
					var t = tStart + (tEnd - tStart) * tt;
					var pos = CatmullRom(m_pathSmoothed, t);
					var posFwd = CatmullRom(m_pathSmoothed, t + step * .5f * alongDir);
					var side = math.normalize(new float3(posFwd.x - pos.x, 0, posFwd.z - pos.z));
					var fwd = new float3(side.z, 0, -side.x);
					var degrees = Mathf.Floor(-Mathf.Atan2(side.z, side.x) * Mathf.Rad2Deg);
					var plotFwd = side;
					var plotSide = fwd;
					var plotFrontPos = pos + plotFwd * roadHalfWidth;
					var plotCenter = plotFrontPos + plotFwd * (plotDepth / 2);
					var pp = plotFrontPos + offset + plotFwd * 2;
					var plot = new Vector4(pp.x, 0, pp.z, degrees - 90);
					bool isValid = IsPlotValid(plotCenter, plotFwd, plotSide, plotWidth, plotDepth);
					if (isValid)
					{
						_list.Add(plot);
					}
				}
			}
		}

		public static bool IsPlotValid(float2 _pos, float2 _fwd, float2 _side, int _width, int _depth)
		{
			var pos3 = new float3(_pos.x, 0, _pos.y);
			var fwd3 = new float3(_fwd.x, 0, _fwd.y);
			var side3 = new float3(_side.x, 0, _side.y);
			return IsPlotValid(pos3, fwd3, side3, _width, _depth);
		}

		public static bool IsPlotValid(float3 _pos, float3 _fwd, float3 _side, int _width, int _depth, bool _navOnly = false, GlobalData.AllowNavType _allowNavType = GlobalData.AllowNavType.None)
		{
			_pos -= _fwd * (_depth * .5f) + _side * (_width * .5f);
			float step = 1f / GlobalData.c_terrainXZScale;
			var blockers = GlobalData.Me.CheckNavTerrainBlockerAndDistrictAligned(_pos, _side, _fwd, _width, _depth, step, _navOnly, _navOnly, _allowNavType);
			return blockers.Length == 0;
		}

		public static long[] GetPlotBlockers(float3 _pos, float3 _fwd, float3 _side, int _width, int _depth, bool _navOnly = false, GlobalData.AllowNavType _allowNavType = GlobalData.AllowNavType.None, int _maxBlockers = 64)
		{
			_pos -= _fwd * (_depth * .5f) + _side * (_width * .5f);
			float step = 1f / GlobalData.c_terrainXZScale;
			return GlobalData.Me.CheckNavTerrainBlockerAndDistrictAligned(_pos, _side, _fwd, _width, _depth, step, _navOnly, _navOnly, _allowNavType, _maxBlockers, true);
		}

		public static bool IsInsideZone(Vector3 _pos, Vector3 _zoneCenter, Vector3 _zoneFwd, Vector3 _zoneSide)
		{
			var d = _pos - _zoneCenter;
			return Mathf.Abs(Vector3.Dot(d, _zoneFwd)) < _zoneFwd.sqrMagnitude && Mathf.Abs(Vector3.Dot(d, _zoneSide)) < _zoneSide.sqrMagnitude;
		}

		public void DrawGizmos()
		{
#if UNITY_EDITOR
			if (Count < 2) return;
			if (m_pathSmoothed.IsEmpty()) UpdateSmoothed();
			if (m_pathSmoothed.Count == 0) return;
			float step = .1f / Count;
			var prev = CatmullRom(m_pathSmoothed, 0);
			Gizmos.color = Color.white;
			for (float t = step; t <= 1; t += step)
			{
				var next = CatmullRom(m_pathSmoothed, t);
				Gizmos.DrawLine(prev, next);
				prev = next;
			}
			for (int i = 0; i < m_pathSmoothed.Count; ++i)
			{
				float size = 1;
				if (i == 0)
				{
					Gizmos.color = new Color(0, 1, 0, .5f);
					size = .75f;
				}
				else if (i == 1 || i == m_pathSmoothed.Count - 2)
				{
					Gizmos.color = Color.black;
					size = .5f;
				}
				else if (i == m_pathSmoothed.Count - 1)
				{
					Gizmos.color = new Color(1, 0, 0, .5f);
					size = 1.25f;
				}
				else
					Gizmos.color = new Color(1, 1, 0, .5f);
				Gizmos.DrawSphere(m_pathSmoothed[i], .3f * size);
			}
#endif
		}
	}

	public class PathSet
	{
		private Path m_currentPath;
		private int m_currentPathIndex;
		private int m_pathEditDrag = -1;
		private bool m_currentPathIsNew = false;
		private List<Path> RealPaths => GameManager.Me.m_state.m_paths;
		private List<Path> Paths => m_inEditMode ? m_editModePaths : GameManager.Me.m_state.m_paths;
		private bool m_inEditMode = false;
		private List<Path> m_editModePaths = null;
		
		public void EnterEditMode()
		{
			//DestroyAllVisuals();
			m_inEditMode = true;
			m_editModePaths = new List<Path>();
			CopyPaths(RealPaths, Paths);
			//CreateVisuals();
			ShowHandles(true);
		}

		public void LeaveEditMode(bool _acceptChanges)
		{
			HideHandles();
			if (_acceptChanges == false)
				DestroyAllVisuals();
			else
				CopyPaths(Paths, RealPaths);
			m_inEditMode = false;
			m_editModePaths = null;
			if (_acceptChanges == false)
				CreateVisuals();
			ShowHandles(false);
			Utility.ClearTooltip();
		}
		
		public bool IsDragging(Path _path) => m_currentPath == _path;

		public int UndoHead { get => GameManager.Me.m_state.m_pathUndo.m_head; set => GameManager.Me.m_state.m_pathUndo.m_head = value; }
		public int UndoCount => GameManager.Me.m_state.m_pathUndo.Count;
		public void UndoRemoveEnd() => GameManager.Me.m_state.m_pathUndo.RemoveEnd();
		public void UndoClear() => GameManager.Me.m_state.m_pathUndo.Clear();

		public void UndoPushEntry(int _index, int _type)
		{
			var path = new Path();
			path.CopyFrom(CurrentPaths[_index]);
			GameManager.Me.m_state.m_pathUndo.PushEntry(_index, path, _type);
		}

		public void UndoApplyEntry(int _index, bool _reverse)
		{
			void UndoApplyAddEntry(GameState_PathUndoEntry _entry, bool _isNew)
			{
				if (_isNew) CurrentPaths.Insert(_entry.m_roadId, new Path());
				else CurrentPaths[_entry.m_roadId].ClearVisuals();
				CurrentPaths[_entry.m_roadId].CopyFrom(_entry.m_path, true);
				CurrentPaths[_entry.m_roadId].UpdateVisuals(true);
				UpdateDirtyRegions();
			}
			
			var entry = GameManager.Me.m_state.m_pathUndo.m_entries[_index];
			if (entry.m_changeType == 0)
			{
				UndoApplyAddEntry(entry, false);
			}
			else
			{
				var type = entry.m_changeType;
				if (_reverse) type = 3 - type;
				if (type == 2) // add
				{
					UndoApplyAddEntry(entry, true);
				}
				else // remove
				{
					CurrentPaths[entry.m_roadId].ClearVisuals();
					CurrentPaths.RemoveAt(entry.m_roadId);
				}
			}
		}


		public void PushUndo(int _index, int _type)
		{
			while (UndoHead < UndoCount)
				UndoRemoveEnd();
			if (_type > 0) { UndoClear(); return; } // TODO - fix add/delete undo/redo, until then just clear undo stack
			UndoPushEntry(_index, _type);
			UndoHead = UndoCount;
		}

		public bool CanUndo => UndoHead > 1;
		public bool CanRedo => UndoHead < UndoCount;
		public bool Undo()
		{
			if (CanUndo)
			{
				--UndoHead;
				UndoApplyEntry(UndoHead - 1, false);
				return true;
			}
			return false;
		}
		
		public Path GetPlayerPathNearPoint(Vector3 _point, bool _onlyUnlocked = false, float _maxDistance = 10000)
		{
			var paths = Paths;
			Path bestPath = null;
			float bestDistanceSqrd = _maxDistance * _maxDistance;
			for (int i = 0; i < paths.Count; ++i)
			{
				var path = paths[i];
				if (path.m_isPlayerPath == false) continue;
				if (path.IsValid == false) continue;
				Vector3 startPos = path.m_path[0];
				Vector3 endPos = path.m_path[^1];
				if (_onlyUnlocked && DistrictManager.Me.IsWithinDistrictBounds(startPos, true) == false) continue;
				var distSqrd = (startPos - _point).xzSqrMagnitude();
				if (distSqrd < bestDistanceSqrd)
				{
					bestDistanceSqrd = distSqrd;
					bestPath = path;
				}
				distSqrd = (endPos - _point).xzSqrMagnitude();
				if (distSqrd < bestDistanceSqrd)
				{
					bestDistanceSqrd = distSqrd;
					bestPath = path;
				}
			}
			return bestPath;
		}

		public bool Redo()
		{
			if (CanRedo)
			{
				++UndoHead;
				UndoApplyEntry(UndoHead - 1, true);
				return true;
			}
			return false;
		}

		private void DestroyAllVisuals()
		{
			var paths = Paths;
			for (int i = 0; i < paths.Count; ++i)
				paths[i].DestroyVisuals();
		}
		private void UpdatePathsFrom(List<Path> _paths)
		{
			var (minOld, maxOld) = GetTotalExtents();
			DestroyAllVisuals();
			CopyPaths(_paths, Paths);
			CreateVisualsAll(minOld, maxOld);
			ShowHandles(true);
		}
		public static void CopyPaths(List<Path> _from, List<Path> _to)
		{
			_to.Clear();
			for (int i = 0; i < _from.Count; ++i)
			{
				var path = new Path();
				path.CopyFrom(_from[i]);
				_to.Add(path);
			}
		}

		public bool IsPrimary(Path _this, Path _other)
		{
			int thisType = _this.FinishedType, otherType = _other.FinishedType;
			int thisPriority = _this.Set.m_priority, otherPriority = _other.Set.m_priority;
			if (thisType == otherType || thisPriority == otherPriority)
			{
				var paths = Paths;
				return paths.IndexOf(_this) < paths.IndexOf(_other);
			}
			return thisPriority < otherPriority;
		}

		public (Path, float) FindPathIntersection(Vector3 _origin, Vector3 _forward, float _maxDistance)
		{
			var paths = Paths;
			Path bestPath = null;
			float bestTPath = 1e23f, bestTRay = _maxDistance;
			for (int i = 0; i < paths.Count; ++i)
			{
				(var tRay, var tPath) = paths[i].FindIntersection(_origin, _forward, bestTRay);
				if (tRay >= 0 && tRay < bestTRay)
				{
					bestTRay = tRay;
					bestTPath = tPath;
					bestPath = paths[i];
				}
			}
			return (bestPath, bestTPath);
		}

		private int m_audioHandle = -1;
		private float m_dragSpeed = 0;
		private float3 m_dragLastEnd;
		private GameObject m_audioObject;
		private void StartEditAudio(Path _path)
		{
			if (_path.IsValid == false) return;
			if (m_audioObject == null)
			{
				m_audioObject = new GameObject("Path Drag Audio");
				m_audioObject.transform.SetParent(RoadManager.Me.transform);
			}
			var set = _path.Set;
			var audioEvent = (set.m_pathDragAudio != null && set.m_pathDragAudio.IsValid()) ? set.m_pathDragAudio : NGManager.Me.m_pathDragAudio;
			m_audioHandle = audioEvent.Play(m_audioObject);
			m_dragSpeed = 0;
			m_dragLastEnd = _path.m_path[^1];
			UpdateEditAudio(_path);
		}
		private void EndEditAudio(Path _path)
		{
			if (m_audioHandle != -1)
			{
				NGManager.Me.m_pathDragRTPC.SetValue(m_audioObject, 0);
				AudioClipManager.Me.StopSound(m_audioHandle, m_audioObject);
				m_audioHandle = -1;
			}
		}
		private void UpdateEditAudio(Path _path)
		{
			if (m_audioObject == null)
			{
				StartEditAudio(_path);
				return;
			}
			var pos = _path.m_path[^1];
			m_audioObject.transform.position = pos.GroundPosition(1);
			var xzDist = math.length((pos - m_dragLastEnd).xz);
			m_dragLastEnd = pos;
			var thisSpeed = xzDist / Time.deltaTime;
			m_dragSpeed = thisSpeed;//math.lerp(m_dragSpeed, thisSpeed, .05f);
			NGManager.Me.m_pathDragRTPC.SetValue(m_audioObject, m_dragSpeed);
			//GameManager.SetConsoleDisplay(() => $"Drag Speed {m_dragSpeed}");
		}

		public void UpdateTooltip()
		{
			var mousePos = GameManager.InputPosition(0);
			GameManager.Me.RaycastAtPoint(mousePos, out var hit, -1);
			var pos = GetClosestPathToPoint(null, false, hit.point, 10, out var path, out var t, null, true);
			if (path != null)
			{
				var set = path.Set;
				var tooltipText = $"<size=50%>{RoadManager.Me.m_pathSet.Paths.IndexOf(path)} - {set.name} ({path.m_roadType}) {(path.m_isPlayerPath ? "[P]" : "")} [{t:n2}]\nLength {path.TotalLength():n1} Constructed {path.GetCompletedPercent() * path.TotalLength():n1}</size>";
				Utility.ShowTooltip(tooltipText, mousePos - Vector3.up * (Screen.height * .1f));
			}
			else
				Utility.ClearTooltip();
		}

		private float m_nextTooltipErrorAt = 0;
		public void Update(bool _isEditMode)
		{
			if (_isEditMode == false && CanEditPlayerPaths == false) return;
			var paths = Paths;
			if (GameManager.GetPrimaryDragStarted())
			{
				m_pathEditDrag = GameManager.CancelDrag();
				var ray = Camera.main.RayAtScreenPosition(GameManager.InputPosition(m_pathEditDrag));
				int hitEnd = 0;
				float bestDistSqrd = 3 * 3;
				for (int i = 0; i < paths.Count; ++i)
				{
					if (_isEditMode == false && paths[i].m_isPlayerPath == false) continue;
					var hit = paths[i].RayHitsEnd(ray, ref bestDistSqrd, _isEditMode);
					if (hit == 1 && paths[i].IsFullyConstructed == false)
					{
						hit = 0;
						if (Time.time > m_nextTooltipErrorAt)
						{
							MAParser.ShowHelper(paths[i].GetHandle(true), "DragWall", "Complete the other end first", 0, 2, 1, "");
							m_nextTooltipErrorAt = Time.time + 5;
						}
					}
					//if (_isEditMode == false && hit == 1 && paths[i].m_isPlayerPath) continue;
					if (hit != 0)
					{
						m_currentPath = paths[i];
						m_currentPathIndex = i;
						hitEnd = hit;
						AudioClipManager.Me.PlayUISound("PlaySound_WallbuildingPost_GRAB");
					}
				}
				m_currentPathIsNew = false;
				if (m_currentPath == null)
				{
					if (_isEditMode)
					{
						paths.Add(new());
						m_currentPath = paths[paths.Count - 1];
						m_currentPathIndex = paths.Count - 1;
						m_currentPath.m_isConstructionValid = true;
						m_currentPathIsNew = true;
					}
					else
					{
						m_pathEditDrag = -1;
						return;
					}
				}
				m_currentPath.StartEdit(hitEnd, RoadManager.Me.m_currentRoadSet);
				StartEditAudio(m_currentPath);
			}
			else if (m_pathEditDrag != -1)
			{
				if (_isEditMode)
				{
					if (Input.GetKeyDown(KeyCode.C))
					{
						paths.Remove(m_currentPath.OnDestroy());
						m_currentPath = null;
						m_currentPathIndex = -1;
						m_pathEditDrag = -1;
					}
					if (Input.GetKeyDown(KeyCode.U))
					{
						m_currentPath.m_isPlayerPath = !m_currentPath.m_isPlayerPath;
						m_currentPath.UpdateHandles(true, m_currentPathIndex);
					}
				}
				if (GameManager.GetMouseButton(m_pathEditDrag) == false)
				{
					EndEditAudio(m_currentPath);
					int type = m_currentPathIsNew ? 1 : 0;
					if (m_currentPath.Finish())
					{
						paths.Remove(m_currentPath.OnDestroy());
						type = 2; // remove
					}
					else
						Conveyor.SearchAllFactories();

					PushUndo(m_currentPathIndex, type);
					m_currentPath.UpdateHandles(false, m_currentPathIndex);
					m_currentPath.UpdateVisuals(true);
					m_currentPath = null;
					m_currentPathIndex = -1;
					m_pathEditDrag = -1;
				}
				else
				{
					m_currentPath.ConsiderNewPoint(GameManager.InputPosition(m_pathEditDrag));
					UpdateEditAudio(m_currentPath);

					if (m_currentPath.m_path.Count > 0 && ((Vector3)m_currentPath.m_path[^1] - m_lastPathEndPoint).xzSqrMagnitude() > .1f * .1f)
					{
						m_lastPathEndPoint = m_currentPath.m_path[^1];
						m_currentPath.UpdateHandles(true, m_currentPathIndex);
						m_currentPath.UpdateVisuals(false);
					}
				}

				UpdateDirtyRegions();

			}
		}
		private Vector3 m_lastPathEndPoint;

		public void GeneratePlots(List<Vector4> _list, int _width, int _depth, RoadSet.BuildingsAccepted _acceptedTypes, Vector3 _zoneCenter, float _zoneRadius)
		{
			GameManager.Me.ClearGizmos("plots");
			
			var paths = Paths;
			for (int i = 0; i < paths.Count; ++i)
			{
				var path = paths[i];
				path.GeneratePlots(_list, _width, _depth, _acceptedTypes, _zoneCenter, _zoneRadius);
			}
		}

		public int Count => Paths.Count;
		public List<Path> CurrentPaths => Paths;

		public NativeishList<float3> m_blockPositions;
		public void CacheBlockData(int _x, int _z, int _w, int _h)
		{
			float2 minDirty = new(_x - _w / 2, _z - _h / 2);
			float2 maxDirty = new(_x + _w / 2, _z + _h / 2);
			CacheBlockData(minDirty, maxDirty);
		}

		public const int c_blockTypeBuilding = 1;
		public const int c_blockTypeBroken = 2;
		public const int c_blockTypeBrokenPartial = 3;
		
		private const bool c_buildingsInterruptPaths = false;
		
		public void CacheBlockData(float2 _minDirty, float2 _maxDirty)
		{
			if (PathManager.s_blockDataDirty)
			{
				const float buildExtent = 1.414213562f * BuildingPlacementManager.c_buildingBlockSize;
				var buildings = NGManager.Me.m_maBuildings;
				int numComponents = 0;
				if (c_buildingsInterruptPaths)
					for (int i = 0; i < buildings.Count; ++i)
						numComponents += buildings[i].Visuals?.GetComponentsInChildren<Block>()?.Length ?? 0;
				m_blockPositions.Dispose();
				m_blockPositions = new(numComponents + PathBreak.s_allBreaks.Count);
				if (c_buildingsInterruptPaths)
				{
					for (int i = 0; i < buildings.Count; ++i)
					{
						var currentBuilding = buildings[i];
						var baseBlock = currentBuilding.Visuals.GetComponentInChildren<BaseBlock>();
						if (baseBlock != null)
							foreach (var t in baseBlock.GetHinges())
								m_blockPositions.Add(new float3(t.position.x, t.position.z, c_blockTypeBuilding));
					}
				}

				for (int i = 0; i < PathBreak.s_allBreaks.Count; ++i)
				{
					var brk = PathBreak.s_allBreaks[i];
					if (brk.IsDestroyed) continue;
					m_blockPositions.Add(new float3(brk.transform.position.x, brk.transform.position.z, brk.IsOpen ? c_blockTypeBroken : c_blockTypeBrokenPartial));
				}
				PathManager.s_blockDataDirty = false;
			}
		}

		public void OnDestroy()
		{
			if (GameManager.Me == null) return;
			var paths = Paths;
			for (int i = paths.Count - 1; i >= 0; --i)
			{
				paths[i].OnDestroy();
			}
			paths.Clear();
		}

		private void ResetTerrainData()
		{
			GlobalData.Me.RestoreTerrainHeights(m_terrainOperationsMin, m_terrainOperationsMax);
			GlobalData.Me.RestoreWaterPresence(m_terrainOperationsMin, m_terrainOperationsMax);
			CameraRenderSettings.Me.ResetDetailArea(m_terrainOperationsMin, m_terrainOperationsMax);
			GlobalData.Me.ClearNavRect(m_terrainOperationsMin, m_terrainOperationsMax);
		}

		private Vector3 m_terrainOperationsMin = Vector3.one * 1e23f, m_terrainOperationsMax = Vector3.one * -1e23f;
		private void BeginTerrainOperations(Vector3 _min, Vector3 _max)
		{
			m_terrainOperationsMin = _min; m_terrainOperationsMax = _max;
			GlobalData.Me.BeginBatchTerrainOperations();
			CameraRenderSettings.Me.BeginSplatOperations(_min, _max);
			CameraRenderSettings.Me.BeginDetailOps();
		}

		private void EndTerrainHeightOperations()
		{
			GlobalData.Me.EndBatchTerrainOperations();
		}
		private void EndTerrainOtherOperations()
		{
			CameraRenderSettings.Me.EndDetailOps();
			CameraRenderSettings.Me.EndSplatOperations();
			m_terrainOperationsMin = Vector3.one * 1e23f; m_terrainOperationsMax = Vector3.one * -1e23f;
		}

		public static void AccrueSetLengths(List<Path> _paths, float[] _lengths, float _multiplier)
		{
			for (int i = 0; i < _paths.Count; ++i)
			{
				var path = _paths[i];
				int type = path.FinishedType;
				float length = path.TotalLength();
				_lengths[type] += length * _multiplier;
			}
		}

		public float[] GetTotalAdjustments()
		{
			var lengths = new float[64];
			AccrueSetLengths(Paths, lengths, 1);
			AccrueSetLengths(RealPaths, lengths, -1);
			return lengths;
		}
		
		public void CreateVisuals()
		{
			CreateVisuals(GlobalData.c_terrainOrigin, GlobalData.c_terrainOrigin + Vector3.one * GlobalData.c_heightmapW / GlobalData.c_terrainXZScale, null);
			GlobalData.Me.BeginBatchTerrainOperations();
			foreach (var ngc in NGManager.Me.m_NGCommanderList)
				BuildingPlacementManager.ReapplyFlatten(ngc, true);
			GlobalData.Me.EndBatchTerrainOperations();
			foreach (var pathbreak in PathBreak.s_allBreaks)
				pathbreak.FlagUpdated(); // generated all visuals, all path breaks have therefore been updated
		}

		public void RerunSplats()
		{
			FillAllSplats(Paths, GlobalData.c_terrainOrigin, GlobalData.c_terrainOrigin + Vector3.one * GlobalData.c_heightmapW / GlobalData.c_terrainXZScale);
		}

		(Vector3, Vector3) GetTotalExtents()
		{
			Vector3 min = Vector3.one * 1e23f, max = Vector3.one * -1e23f;
			var paths = Paths;
			for (int i = 0; i < paths.Count; ++i)
			{
				paths[i].UpdateSmoothed();
				min = Vector3.Min(paths[i].MinExtent, min);
				max = Vector3.Max(paths[i].MaxExtent, max);
			}
			return (min, max);
		}
		public void CreateVisualsAll(Vector3 _previousMin, Vector3 _previousMax)
		{
			var (min, max) = GetTotalExtents();
			min = Vector3.Min(min, _previousMin);
			max = Vector3.Max(max, _previousMax);
			min = Vector3.Max(min, GlobalData.c_terrainOrigin);
			max = Vector3.Min(max, GlobalData.c_terrainOrigin + GlobalData.c_terrainExtent);
			CreateVisuals(min, max, null);
		}
		
		public void CreateVisuals(Vector3 _dirtyMin, Vector3 _dirtyMax, Path _primaryPath, bool _visualsOnly = false)
		{
			CacheBlockData(new float2(_dirtyMin.x, _dirtyMin.z), new float2(_dirtyMax.x, _dirtyMax.z));
			var paths = Paths;
			List<Path> relevantPaths = new();
			if (_primaryPath != null)
			{
				List<Vector4> intersections = new();
				NativeishList<float2> buildingTs = new(RoadManager.Me.m_pathSet.m_blockPositions.Count * 2);
				if (!_primaryPath.Set.m_splatOnly)
					intersections = _primaryPath.FindAllIntersections(paths, !false, ref buildingTs);
				int index = -1;
				relevantPaths.Add(_primaryPath);
				for (int i = 0; i < paths.Count; ++i)
				{
					var path = paths[i];
					if (path == _primaryPath)
						index = i;
					else if (path.Overlaps(_dirtyMin, _dirtyMax))
						relevantPaths.Add(path);
				}
				FillAllSplats(relevantPaths, _dirtyMin, _dirtyMax);
				_primaryPath.GenerateVisuals(index, intersections, buildingTs, !false);
				buildingTs.Dispose();
				return;
			}
			
			Path.ClearGizmoClips();

			for (int i = 0; i < paths.Count; ++i)
			{
				var path = paths[i];
				path.UpdateSmoothed();
				if (path.Overlaps(_dirtyMin, _dirtyMax))
				{
					relevantPaths.Add(path);
				}
			}
			
			if (_visualsOnly == false)
			{
				BeginTerrainOperations(_dirtyMin, _dirtyMax);
				ResetTerrainData();
				for (int i = 0; i < relevantPaths.Count; ++i)
					relevantPaths[i].PrepareEarlyTerrainSmoothing(_dirtyMin, _dirtyMax);
				for (int i = 0; i < relevantPaths.Count; ++i)
					relevantPaths[i].ExecuteEarlyTerrainSmoothing();
				for (int i = 0; i < relevantPaths.Count; ++i)
					relevantPaths[i].PrepareTerrainSmoothing();
				for (int i = 0; i < relevantPaths.Count; ++i)
					if (relevantPaths[i].Set.m_createSurfaceMaterial != null)
						relevantPaths[i].ExecuteTerrainSmoothing();
				for (int i = 0; i < relevantPaths.Count; ++i)
					if (relevantPaths[i].Set.m_createSurfaceMaterial == null)
						relevantPaths[i].ExecuteTerrainSmoothing();
				EndTerrainHeightOperations();
				FillAllSplats(relevantPaths, _dirtyMin, _dirtyMax);
			}
			String debugInfo = "\n";
			for (int i = 0; i < relevantPaths.Count; ++i)
			{
				var path = relevantPaths[i];
				NativeishList<float2> buildingTs = new(RoadManager.Me.m_pathSet.m_blockPositions.Count * 2);
				var intersections = path.FindAllIntersections(paths, true, ref buildingTs);
				var gateList = path.GenerateVisuals(paths.IndexOf(path), intersections, buildingTs, true);
				if (_visualsOnly == false)
					path.FillNavAndDetail(_dirtyMin, _dirtyMax, intersections, buildingTs, gateList, _visualsOnly);
				path.UpdateEditLine(true, intersections);
				buildingTs.Dispose();
#if UNITY_EDITOR
				var meshInfo = path.MeshInfo;
				foreach (var info in meshInfo)
					debugInfo += $"{path.Set.name} {info.Item1} verts:{info.Item2}\n";
#endif
			}
#if UNITY_EDITOR
			Debug.Log("Path info:\n" + debugInfo);
#endif
			if (_visualsOnly == false)
				EndTerrainOtherOperations();
			//m_blockPositions.Dispose();
		}

		public void FillBuildingNavData(Vector3 _dirtyMin, Vector3 _dirtyMax, bool _withVisuals)
		{
			if (_withVisuals && GlobalData.Me.m_splatPathToDoor != -1)
			{
				foreach (var cmd in NGManager.Me.m_NGCommanderList)
				{
					if (cmd.m_ignoreDoorPaths) continue;
					var inner = cmd.DoorPosInner;
					var outer = cmd.DoorPosOuter;
					if (RegionContainsPoint(_dirtyMin, _dirtyMax, inner) || RegionContainsPoint(_dirtyMin, _dirtyMax, outer))
					{
						FillPathToDoor(cmd);
					}
				}
			}
			
			foreach (var b in GlobalData.Me.m_buildings)
			{
				b.FillNavigationData((int)GlobalData.NavCostTypes.Building, _dirtyMin, _dirtyMax, b.IsFakeBuilding == false && b.m_innerToOuterDistance > 0);
				if (_withVisuals)
					b.FillSplatData(false);
			}
		}

		private List<Vector3> m_dirtyNavRegions = new();

		private bool OverlapRegions(ref Vector3 _thisMin, ref Vector3 _thisMax, Vector3 _otherMin, Vector3 _otherMax)
		{
			if (_thisMax.x < _otherMin.x || _thisMin.x > _otherMax.x || _thisMax.z < _otherMin.z || _thisMin.z > _otherMax.z) return false;
			_thisMin = Vector3.Min(_thisMin, _otherMin);
			_thisMax = Vector3.Max(_thisMax, _otherMax);
			return true;
		}

		public void UpdateNavDeferred(Vector3 _dirtyMin, Vector3 _dirtyMax)
		{
			for (int i = 0; i < m_dirtyNavRegions.Count; i += 2)
			{
				if (OverlapRegions(ref _dirtyMin, ref _dirtyMax, m_dirtyNavRegions[i], m_dirtyNavRegions[i + 1]))
				{
					m_dirtyNavRegions.FastUnorderedRemoveAt(i + 1);
					m_dirtyNavRegions.FastUnorderedRemoveAt(i);
					i = -2;
				}
			}
			m_dirtyNavRegions.Add(_dirtyMin);
			m_dirtyNavRegions.Add(_dirtyMax);
		}

		public void ExecuteDirtyNavRegions()
		{
			for (int i = 0; i < m_dirtyNavRegions.Count; i += 2) UpdateNav(m_dirtyNavRegions[i], m_dirtyNavRegions[i + 1]);
			m_dirtyNavRegions.Clear();
		}

		public void UpdateNav(Vector3 _dirtyMin, Vector3 _dirtyMax)
		{
			_dirtyMin = GlobalData.TerrainQuantize(_dirtyMin, .5f); // make sure dirty region encompasses the final rounded integer area used by ClearNavRect
			_dirtyMax = GlobalData.TerrainQuantize(_dirtyMax, .5f);
			
			CacheBlockData(new float2(_dirtyMin.x, _dirtyMin.z), new float2(_dirtyMax.x, _dirtyMax.z));
			var paths = Paths;
			List<Path> relevantPaths = new();

			GlobalData.Me.ClearNavRect(_dirtyMin, _dirtyMax);

			for (int i = 0; i < paths.Count; ++i)
			{
				var path = paths[i];
				path.UpdateSmoothed();
				if (path.Overlaps(_dirtyMin, _dirtyMax))
				{
					relevantPaths.Add(path);
				}
			}
			for (int i = 0; i < relevantPaths.Count; ++i)
			{
				var path = relevantPaths[i];
				NativeishList<float2> buildingTs = new(RoadManager.Me.m_pathSet.m_blockPositions.Count * 2);
				var intersections = path.FindAllIntersections(paths, true, ref buildingTs, true);
				path.FillNavAndDetail(_dirtyMin, _dirtyMax, intersections, buildingTs, null, true);
				buildingTs.Dispose();
			}
			//m_blockPositions.Dispose();
			FillBuildingNavData(_dirtyMin, _dirtyMax, false);
			PathBlock.FillNavData(_dirtyMin, _dirtyMax);
		}
		
		public static bool RegionContainsPoint(Vector3 _regionMin, Vector3 _regionMax, Vector3 _point)
		{
			return _point.x >= _regionMin.x && _point.x <= _regionMax.x && _point.z >= _regionMin.z && _point.z <= _regionMax.z;
		}

		public static void FillPathToDoor(NGCommanderBase _cmd, bool _controlSplatOps = false)
		{
			if (GlobalData.Me.m_splatPathToDoor != -1)
			{
				var inner = _cmd.DoorPosInner;
				var outer = _cmd.DoorPosOuter;
				const float c_minStep = -.8f, c_maxStep = 1.2f;
				if (_controlSplatOps)
				{
					var start = Vector3.LerpUnclamped(inner, outer, c_minStep);
					var end = Vector3.LerpUnclamped(inner, outer, c_maxStep);
					var min = Vector3.Min(start, end) - Vector3.one * 2;
					var max = Vector3.Max(start, end) + Vector3.one * 2;
					CameraRenderSettings.Me.BeginSplatOperations(min, max);
				}
				for (float t = c_minStep; t <= c_maxStep; t += .1f)
				{
					var p = Vector3.LerpUnclamped(inner, outer, t);
					CameraRenderSettings.Me.SetSplatCircle(p + UnityEngine.Random.insideUnitCircle.V3XZ() * .3f, UnityEngine.Random.Range(.8f, 1.2f), GlobalData.Me.m_splatPathToDoor, Mathf.Lerp(.8f, .5f, t));
				}
				if (_controlSplatOps)
					CameraRenderSettings.Me.EndSplatOperations();
			}
		}

		public void FillAllSplats(List<Path> _relevantPaths, Vector3 _dirtyMin, Vector3 _dirtyMax)
		{
			if (GlobalData.Me.m_buildings.Count == 0) return; // still loading, do splats after we load buildings
			CameraRenderSettings.Me.BeginSplatOperations(_dirtyMin, _dirtyMax);
			RestoreSplatArea(_dirtyMin, _dirtyMax);

			FillBuildingNavData(_dirtyMin, _dirtyMax, true);

			foreach (var path in _relevantPaths)
				path.UpdateSplat(false);
			foreach (var b in GlobalData.Me.m_buildings)
				b.FillSplatData(true);
			foreach (var path in _relevantPaths)
				path.UpdateSplat(true);
			
			PathBlock.FillNavData(_dirtyMin, _dirtyMax);

			CameraRenderSettings.Me.EndSplatOperations();
		}

		public static bool CanEditPlayerPaths => RoadManager.Me.RoadBuildMode && GameManager.IsDesignTableActively == false;
		public void ShowHandles(bool _showAll)
		{
			if (_showAll == false && CanEditPlayerPaths == false)
			{
				HideHandles();
				return;
			}
			var paths = Paths;
			for (int i = 0; i < paths.Count; ++i)
				if (_showAll || paths[i].m_isPlayerPath)
					paths[i].UpdateHandles(false, i);
		}

		public void HideHandles()
		{
			var paths = Paths;
			for (int i = 0; i < paths.Count; ++i) paths[i].RemoveHandles();
		}

		Vector3 m_dirtyRegionMin = Vector3.one * 1e23f, m_dirtyRegionMax = Vector3.one * -1e23f;
		Path m_dirtyRegionPrimaryPath = null;
		public void DirtyRegion(Vector3 _min, Vector3 _max, Path _primaryPath)
		{
			m_dirtyRegionMin = _min; m_dirtyRegionMax = _max;
			m_dirtyRegionPrimaryPath = _primaryPath;
		}

		public void UpdateDirtyRegions()
		{
			if (m_dirtyRegionMin.x < m_dirtyRegionMax.x)
			{
				CreateVisuals(m_dirtyRegionMin, m_dirtyRegionMax, m_dirtyRegionPrimaryPath);
				m_dirtyRegionMin = Vector3.one * 1e23f;
				m_dirtyRegionMax = Vector3.one * -1e23f;
				m_dirtyRegionPrimaryPath = null;
			}
		}

		public Vector3 GetClosestPathToPoint(Path _exclude, bool _wantNavBlocker, Vector3 _pos, float _minDistance)
		{
			return GetClosestPathToPoint(_exclude, _wantNavBlocker, _pos, _minDistance, out _, out _);
		}
		
		public Vector3 GetClosestPathToPoint(Path _exclude, bool _wantNavBlocker, Vector3 _pos, float _minDistance, out Path _path)
		{
			return GetClosestPathToPoint(_exclude, _wantNavBlocker, _pos, _minDistance, out _path, out _);
		}
		
		public Vector3 GetClosestPathToPoint(Path _exclude, bool _wantNavBlocker, Vector3 _pos, float _minDistance, out Path _path, out float _pathT, Predicate<Path> _customPathFilter = null, bool _ignoreNavBlocker = false)
		{
			var paths = Paths;
			_path = null;
			_pathT = 0;
			float3 bestPos = _pos;
			float minDistanceSqrd = _minDistance * _minDistance;
			for (int i = 0; i < paths.Count; ++i)
			{
				var path = paths[i];
				if (path == _exclude) continue;
				if(_customPathFilter?.Invoke(path) == false)
					continue;
				path.UpdateSmoothed();
				float bestT = 0;
				float thisMinDistanceSqrd = path.GetClosestPointToPoint(_pos, _wantNavBlocker, minDistanceSqrd, ref bestPos, ref bestT, _ignoreNavBlocker);
				if (thisMinDistanceSqrd < minDistanceSqrd)
				{
					minDistanceSqrd = thisMinDistanceSqrd;
					_pathT = bestT;
					_path = path;
				}
			}
			return bestPos;
		}


		public static float s_smoothness = 1;
		public static float s_tension = .5f;

		public void SetSmoothingLevel(float _smoothness)
		{
			if (s_smoothness.Nearly(_smoothness) == false)
			{
				s_smoothness = _smoothness;
				CreateVisuals();
				if (RoadManager.Me.InPathEdit) ShowHandles(true);
			}
		}

		public void SetTensionLevel(float _tension)
		{
			if (s_tension.Nearly(_tension) == false)
			{
				s_tension = _tension;
				CreateVisuals();
				if (RoadManager.Me.InPathEdit) ShowHandles(true);
			}
		}

		public void DrawGizmos()
		{
			if (GameManager.Me?.m_state == null) return;
			var paths = Paths;
			for (int i = 0; i < paths.Count; ++i) paths[i].DrawGizmos();
			Path.DrawGizmoClips();
		}
	}
}

public class RoadSetLink : MonoBehaviour
{
	public RoadSet m_set;
	public void Set(RoadSet _set)
	{
		m_set = _set;
	}
}
