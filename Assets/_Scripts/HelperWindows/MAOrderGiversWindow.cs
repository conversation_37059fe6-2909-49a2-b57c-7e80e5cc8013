#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class MAOrderGiversWindow : EditorWindow
{
    private Vector2 m_scrollPosition;
    
    [MenuItem("22Cans/Debug Windows/Order Givers")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MAOrderGiversWindow>("OrderGivers");
        
        window.titleContent = new GUIContent("Order Givers");
        window.Show();
    }

    private void OnInspectorUpdate()
    {
        Repaint();
    }
    
    void OnGUI()
    {
        if (GlobalData.Me == null || NGManager.Me == null || GameManager.Me == null || GameManager.Me.LoadComplete == false)
        {
            EditorGUILayout.LabelField("Game Not Loaded", EditorStyles.boldLabel);
            return;
        }
        
        GUIStyle style = new GUIStyle ();
        
        style.normal.textColor = Color.white;
        style.richText = true;
        
        EditorGUILayout.LabelField("<b>Order Givers</b>", style);
        
        if (GUILayout.Button("Clear"))
        {
            GameManager.Me.m_state.m_orderGivers.Clear();
        }
        
        foreach(var giver in GameManager.Me.m_state.m_orderGivers.m_values)
        {
            GUILayout.BeginHorizontal();
            GUILayout.Label($"Giver: {giver.m_name} ReputationScore: {giver.m_reputationScore}");
            GUILayout.EndHorizontal();
        }
    }
}
#endif
