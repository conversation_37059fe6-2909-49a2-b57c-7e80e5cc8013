using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Mathematics;

public static class UnsafeUtilities
{
    public static unsafe ref T RefAt<T>(this NativeArray<T> _this, int _index) where T : struct
    {
        return ref UnsafeUtility.ArrayElementAsRef<T>(_this.GetUnsafeReadOnlyPtr(), _index);
    }

    public static unsafe ref T MutableRefAt<T>(this NativeArray<T> _this, int _index) where T : struct
    {
        return ref UnsafeUtility.ArrayElementAsRef<T>(_this.GetUnsafePtr(), _index);
    }
    
    public static unsafe void Copy<T>(System.Array _source, NativeArray<T> _dest, int _size) where T : struct
    {
        var src = UnsafeUtility.PinGCArrayAndGetDataAddress(_source, out var handle);
        var dst = _dest.GetUnsafePtr();
        UnsafeUtility.MemCpy(dst, src, _size * UnsafeUtility.SizeOf<T>());
        UnsafeUtility.ReleaseGCObject(handle);
    }
    
    public static unsafe void Copy<T>(System.Array _source, NativeList<T> _dest, int _size) where T : unmanaged
    {
        var src = UnsafeUtility.PinGCArrayAndGetDataAddress(_source, out var handle);
        var dst = _dest.GetUnsafePtr();
        UnsafeUtility.MemCpy(dst, src, _size * UnsafeUtility.SizeOf<T>());
        UnsafeUtility.ReleaseGCObject(handle);
    }
    
    public static unsafe uint2 xxHashList<T>(NativeList<T> _list) where T : unmanaged
    {
        if (_list.Length == 0)
            return uint2.zero;

        var ptr = _list.GetUnsafeReadOnlyPtr();
        return xxHash3.Hash64(ptr, (uint)(_list.Length * UnsafeUtility.SizeOf<T>()));
    }
}
