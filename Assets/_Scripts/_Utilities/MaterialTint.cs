using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MaterialTint : MonoBehaviour, IBatchWhenSame
{
    public string m_type = "Wood";
    public bool m_applyToChildren = true;
#if UNITY_EDITOR
    void Awake() => Refresh();

    void OnValidate()
    {
        if (Application.isPlaying) Refresh();
    }

    void Refresh()
    {
        var ng = GetComponentInParent<NGCommanderBase>();
        if (ng == null) return;
        MaterialSetColour.RandomiseAllMaterials(ng.Visuals.gameObject);
    }
#endif
    public Component Component() => this;

    public bool IsApplicable(int _smi) => true;

    public int BatchHash() => 0;

    public void OnBatch(Component _new)
    {
        DestroyImmediate(_new);
    }
}
