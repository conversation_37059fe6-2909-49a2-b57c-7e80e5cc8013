using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Starburst : MonoBehaviour
{
    public static Dictionary<string, List<Starburst>> s_starbursts = new();

    private static void Register(Starburst _sb)
    {
        if (s_starbursts.TryGetValue(_sb.m_label, out var list) == false)
        {
            list = new List<Starburst>();
            s_starbursts[_sb.m_label] = list;
        }
        list.Add(_sb);
    }

    private static void Unregister(Starburst _sb, string _overrideLabel = null)
    {
        if (s_starbursts.TryGetValue(_overrideLabel ?? _sb.m_label, out var list) == false) return;
        list.Remove(_sb);
    }

    [SerializeField] private RawImage m_starburstImage; 
    [SerializeField] private string m_label = "Label";
    [SerializeField] private float m_durationIn = .4f;
    [SerializeField] private float m_durationHold = .1f;
    [SerializeField] private float m_durationOut = .5f;
    [SerializeField] private float m_starburstScale = 3f;
    [SerializeField] private float m_starburstTotalRotation = 1440;
    private CanvasGroup[] m_canvasGroups;
    private string m_previousLabel = null;

    void Start()
    {
        CheckAndRegister();
        m_canvasGroups = GetComponentsInChildren<CanvasGroup>();
        EnableCanvasGroups(false);
    }

    void OnDestroy() => Unregister(this);

    void CheckAndRegister()
    {
        if (string.IsNullOrEmpty(m_previousLabel) == false) Unregister(this, m_previousLabel);
        Register(this);
        m_previousLabel = m_label;
    }
    
#if UNITY_EDITOR
    void OnValidate()
    {
        if (string.IsNullOrEmpty(m_label) == false && m_previousLabel != m_label)
            CheckAndRegister();
    }
#endif
    
    private static DebugConsole.Command s_showStarburstCmd = new ("starburst", _s => ShowStarburst(_s, null), "Show a starburst with the specified label", "<string>");
    
    public void Show()
    {
        StartCoroutine(Co_Show());
    }
    
    private IEnumerator Co_Show()
    {
        EnableCanvasGroups(true);
        ResetStar();
        for (float f = 0; f < m_durationIn; f += Time.deltaTime)
        {
            float t = Mathf.Clamp01(f / m_durationIn);
            SetCanvasGroupsAlpha(t);
            TurnAndExpandStar(Time.deltaTime);
            yield return null;
        }
        SetCanvasGroupsAlpha(1);
        for (float f = 0; f < m_durationHold; f += Time.deltaTime)
        {
            TurnAndExpandStar(Time.deltaTime);
            yield return null;
        }
        for (float f = 0; f < m_durationOut; f += Time.deltaTime)
        {
            float t = Mathf.Clamp01(f / m_durationOut);
            SetCanvasGroupsAlpha(1 - t);
            TurnAndExpandStar(Time.deltaTime);
            yield return null;
        }
        EnableCanvasGroups(false);
    }

    private float m_starTransformState = 0;
    private void ResetStar()
    {
        m_starTransformState = 0;
        SetStarState();
    }

    private void TurnAndExpandStar(float _dt)
    {
        m_starTransformState += _dt;
        SetStarState();
    }

    private void SetStarState()
    {
        var fullDuration = m_durationIn + m_durationHold + m_durationOut;
        var t = Mathf.Clamp01(m_starTransformState / fullDuration);
        m_starburstImage.transform.localScale = Vector3.one * (t * m_starburstScale);
        m_starburstImage.transform.localRotation = Quaternion.Euler(0, 0, t * m_starburstTotalRotation);
    }

    private void EnableCanvasGroups(bool _enable)
    {
        foreach (var canvasGroup in m_canvasGroups)
            if (canvasGroup != null)
                canvasGroup.gameObject.SetActive(_enable);
    }

    private void SetCanvasGroupsAlpha(float _alpha)
    {
        foreach (var canvasGroup in m_canvasGroups)
            if (canvasGroup != null)
                canvasGroup.alpha = _alpha;
    }
    
    public static void ShowStarburst(string _label, Transform _parent)
    {
        if (s_starbursts.TryGetValue(_label, out var list) && list.Count > 0)
        {
            foreach (var sb in list)
                if (_parent == null || sb.transform.IsChildOf(_parent))
                    sb.Show();
        }
        else
            Debug.LogError($"Starburst with label '{_label}' not found.");
    }
}
