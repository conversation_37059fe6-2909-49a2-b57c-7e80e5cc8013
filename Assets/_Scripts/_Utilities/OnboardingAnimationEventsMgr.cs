#if CHOICES
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class OnboardingAnimationEventsMgr : MonoBehaviour
{
    public void OnboardingStartEnd()
    {
        NGTutorialManager.Me.AnimationClipEnd(NGTutorialManager.AnimClip.Onboarding_Start);
    }

    public void OnboardingAnimationEnd()
    {
        NGTutorialManager.Me.AnimationEnds();
    }

    public void OnboardingAnimationEvent()
    {
    }

    public void PlayOnboardAudioStartABusiness()
    {
        NGTutorialManager.Me.PlayAudioClip("start_a_business");
    }

    public void PlayOnboardAudioIComeIn()
    {
        NGTutorialManager.Me.PlayAudioClip("i_come_in");
    }

    public void PlayOnboardAudioLaugh()
    {
        NGTutorialManager.Me.PlayAudioClip("business_laugh");
    }

    public void PlayOnboardAudioLikeYourPitch()
    {
        NGTutorialManager.Me.PlayAudioClip("like_your_pitch");
    }

    public void PlayOnboardAudioTellMe()
    {
        NGTutorialManager.Me.PlayAudioClip("tell_me");
    }

    public void PlayOnboardAudioWhyInvest()
    {
        NGTutorialManager.Me.PlayAudioClip("why_invest");
    }

    public void PlayOnboardAudioLikeNickname()
    {
        NGTutorialManager.Me.PlayAudioClip("like_nickname");
    }

    public void PlayOnboardAudioBusinessPun()
    {
        NGTutorialManager.Me.PlayAudioClip("business_pun");
    }

    public void PlayOnboardAudioSignHere()
    {
        NGTutorialManager.Me.PlayAudioClip("sign_here");
    }



    public void PlayVicsChallengeAudioFanWork()
    {
        NGTutorialManager.Me.PlayAudioClip("fan_work_nickname");
    }

    public void PlayVicsChallengeAudioNailed()
    {
        NGTutorialManager.Me.PlayAudioClip("nailed_challenges");
    }

    public void PlayVicsChallengeAudioCigar()
    {
        NGTutorialManager.Me.PlayAudioClip("cigar_go_far");
    }

    public void PlayVicsChallengeAudioRoadRich()
    {
        NGTutorialManager.Me.PlayAudioClip("on_road_rich");
    }

    public void PlayVicsChallengeAudioFlex()
    {
        NGTutorialManager.Me.PlayAudioClip("flex_yer_muscles");
    }

    public void PlayVicsChallengeAudioMuckIn()
    {
        NGTutorialManager.Me.PlayAudioClip("muck_in_plebs");
    }

    public void PlayVicsChallengeAudioKeepUp()
    {
        NGTutorialManager.Me.PlayAudioClip("keep_up_nickname");
    }



    public void PlayVicsChallengeAudioYoureHere()
    {
        NGTutorialManager.Me.PlayAudioClip("ex_youre_here");
    }

    public void PlayVicsChallengeAudioFalcone()
    {
        NGTutorialManager.Me.PlayAudioClip("falcone_tells_me");
    }

    public void PlayVicsChallengeAudioWorkTogether()
    {
        NGTutorialManager.Me.PlayAudioClip("we_work_together");
    }

    public void PlayVicsChallengeAudioTeam()
    {
        NGTutorialManager.Me.PlayAudioClip("as_a_team");
    }

    public void PlayVicsChallengeAudioBatman()
    {
        NGTutorialManager.Me.PlayAudioClip("commander_bat_man");
    }

    public void PlayVicsChallengeAudioCaptain()
    {
        NGTutorialManager.Me.PlayAudioClip("captain_industry");
    }

    public void PlayVicsChallengeAudioSoup()
    {
        NGTutorialManager.Me.PlayAudioClip("climb_out_soup_nickname");
    }

    public void PlayVicsChallengeAudioEvolve()
    {
        NGTutorialManager.Me.PlayAudioClip("evolve_time");
    }

    public void PlayVicsChallengeAudioMaster()
    {
        NGTutorialManager.Me.PlayAudioClip("things_to_master");
    }

    public void PlayVicsChallengeAudioGawping()
    {
        NGTutorialManager.Me.PlayAudioClip("sit_gawping");
    }

    public void PlayVicsChallengeAudioConquered()
    {
        NGTutorialManager.Me.PlayAudioClip("conquered_all");
    }

    public void PlayVicsChallengeDollyZoom()
    {
        NGTutorialManager.Me.m_dollyAnimator.SetTrigger("zoom");
    }
}
#endif