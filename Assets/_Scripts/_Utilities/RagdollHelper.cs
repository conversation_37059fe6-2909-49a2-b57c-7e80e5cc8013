using System;
using UnityEngine;

public class RagdollHelper : MonoBehaviour
{
	public static bool IsOrWillBeRagdoll(GameObject _o)
	{
		var rc = _o.GetComponentInChildren<RagdollController>();
		return (rc != null) && (rc.IsRagdolled || rc.WillBeRagdolled);
	}
	
	

    public static void StartRagdoll(GameObject _o, Vector3? _initialVelocity, System.Action<bool> _ragdollEndedCallback = null, System.Action _onRagdollStationary = null)
	{
		var rc = _o.GetComponentInChildren<RagdollController>();
		if (rc != null)
		{	
			if (!rc.IsRagdolled)
			{
				rc.StartRagdolledState(_ragdollEndedCallback, _onRagdollStationary);
			}
			else if (_ragdollEndedCallback != null)
				_ragdollEndedCallback(true);
			if (_initialVelocity.HasValue)
			{
				RagdollController.ForceModel forceModel = new RagdollController.ForceModel
				{
					force = _initialVelocity.Value,
					forceMode = ForceMode.Impulse
				};
				rc.ScheduleForceToEveryBone(forceModel);
			}
		}
		else if (_ragdollEndedCallback != null)
		{
			_ragdollEndedCallback(true);
		}
	}

    public static void CancelRagdoll(GameObject _o)
	{
		var rc = _o.GetComponentInChildren<RagdollController>();
		if (rc != null)
		{
			rc.CancelRagdoll();
		}
	}

	public static Vector3 ThrowDest(GameObject _o, Vector3 _dragDelta)
	{
		RaycastHit hit;
		var extrap = TouchManager.FirstInputPosition + _dragDelta * 2;
		var dropPos = Camera.main.transform.position + Camera.main.transform.forward * 20;
		if (GameManager.Me.RaycastAtPoint(extrap, _o, out hit)) dropPos = hit.point;
		return dropPos;
	}

	public static bool WillThrowObject(Vector3 _dragDelta, bool _debugEasyThrow = false)
	{
		var throwThreshold = Screen.height * NGManager.Me.m_characterThrowThreshold;
		var dragVelSqrd = _dragDelta.sqrMagnitude;
		float finalThreshold = throwThreshold * throwThreshold;
		if (_debugEasyThrow)
			finalThreshold *= 0.1f;
		return dragVelSqrd > finalThreshold;
	}

	public static Vector3 ThrowObject(GameObject _o, Vector3 _dragDelta, string _fallingAnim, string _landAnim, System.Action<bool> _complete, bool _canRagdoll = true, bool _debugEasyThrow = false, System.Action _loopCB = null, Action _onRagDollStationary = null)
	{
		var dropPos = ThrowDest(_o, _dragDelta);
		bool ragdoll = _canRagdoll && WillThrowObject(_dragDelta, _debugEasyThrow);
		
		float throwSpeed = ragdoll ? _dragDelta.magnitude*NGManager.Me.m_characterThrowSpeedMultiplier : 30f;
		return ThrowObjectInner(_o, dropPos, ragdoll, _fallingAnim, _landAnim, throwSpeed, _complete, _loopCB, _onRagDollStationary);
	}
	
	public static Vector3 ThrowObjectRagdoll(GameObject _o, Vector3 _targetPos, float _speed, System.Action<bool> _complete, System.Action _loopCB = null)
	{
		return ThrowObjectInner(_o, _targetPos, true, null, null, _speed, _complete, _loopCB);
	}
	public static Vector3 ThrowObjectNonRagdoll(GameObject _o, Vector3 _targetPos, string _fallingAnim, string _landAnim, float _speed, System.Action<bool> _complete, System.Action _loopCB = null)
	{
		return ThrowObjectInner(_o, _targetPos, false, _fallingAnim, _landAnim, _speed, _complete, _loopCB);
	}
	
	static Vector3 ThrowObjectInner(GameObject _o, Vector3 _targetPos, bool _ragdoll, string _fallingAnim, string _landAnim, float _speed, System.Action<bool> _complete, System.Action _loopCB,  System.Action _onRagdollStationary = null)
	{
		AnimationOverride ao = null;
		var character = _o.GetComponent<MACharacterBase>();
		bool isIncapacitated = (character != null) && (character.IsIncapacitated || (character.m_state == NGMovingObject.STATE.MA_DEAD));
		if (!_ragdoll)
		{
			if (!isIncapacitated)
			{
				if (!string.IsNullOrEmpty(_landAnim))
					ao = AnimationOverride.PlayClip(_o.GetComponentInChildren<Animator>().gameObject, null, null, _fallingAnim, _c => { _loopCB?.Invoke(); }, _landAnim, _complete);
			}
			if (character != null)
				character.CheckDropInGrey();
		}

		var body = _o.GetComponent<Rigidbody>();
		var rc = _o.GetComponentInChildren<RagdollController>();
		if ((body == null) && (rc == null))
		{
			GameManager.SendToCrashlyticsAsException($"Object {_o.name} tried to ThrowObjectInner with no Rigidbody");
			_complete(true);
			return Vector3.zero;
		}

		Vector3 vel = Vector3.zero;
		if (_ragdoll && (rc != null))
		{
			var dir = _targetPos - rc.transform.position;
			const float impulseBaseMass = 50f;
			float impulse = impulseBaseMass * _speed;
			var forceDir = dir.normalized;
			var forceDirXZ = forceDir.GetXZNorm();
			float absDirX = Mathf.Abs(forceDirXZ.x);
			float absDirZ = Mathf.Abs(forceDirXZ.z);
			float forceDirY = (absDirX > absDirZ) ? absDirX : absDirZ;
			var forceDirXZY = (forceDirXZ + (Vector3.up * forceDirY * 0.5f)).normalized;
			vel = forceDirXZY * impulse;
			float throwTime = (_targetPos - rc.transform.position).xzMagnitude() / _speed;
			if (isIncapacitated)
			{
				StartRagdoll(_o, vel, null, _onRagdollStationary);
				rc.StartCoroutine(Utility.Co_After(throwTime, () =>
					{
						if (character != null)
							character.CheckDropInGrey();
						_complete(true);
					}));
			}
			else
			{
				StartRagdoll(_o, vel, _complete, _onRagdollStationary);
				rc.StartCoroutine(Utility.Co_After(throwTime, () =>
					{
						if (character != null)
							character.CheckDropInGrey();
					}));
			}
		}
		else if (body != null)
		{
			vel = body.SetVelocityToMoveWithSpeed(_targetPos, _speed, (_r) => {
				if(_r != null)
					_r.linearVelocity *= .1f;
				
				if (ao != null) {
					ao.Interrupt(false);
				} else if (!_ragdoll || (rc == null)) {
					_complete(true);
				}
			});
		}

		return vel;
	}
}
