using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BlockFurnitureControl : MonoBehaviour
{
    public GameObject[] m_removeIfNotAtTop;
    public GameObject[] m_removeIfNotAtBottom;
    public GameObject[] m_randomRotateList;
    public float m_randomRotateAmount = 0;

    public void Execute(bool _isAtTop, bool _isAtBottom)
    {
        if (m_randomRotateList != null && m_randomRotateAmount * m_randomRotateAmount > .001f * .001f)
        {
            foreach (var o in m_randomRotateList)
            {
                o.transform.localEulerAngles = Vector3.up * Random.Range(-m_randomRotateAmount, m_randomRotateAmount);
            }
        }
        
        if (m_removeIfNotAtTop != null)
            foreach (var o in m_removeIfNotAtTop)
            {
                if (o != null)
                    o.SetActive(_isAtTop);
                else
                    Debug.LogError($"{gameObject.name} has null entry in BlockFurnitureControl.m_removeIfNotAtTop", gameObject);
            }
        if (m_removeIfNotAtBottom != null)
            foreach (var o in m_removeIfNotAtBottom)
            {
                if (o != null)
                    o.SetActive(_isAtBottom);
                else
                    Debug.LogError($"{gameObject.name} has null entry in BlockFurnitureControl.m_removeIfNotAtBottom", gameObject);
            }
    }
}
