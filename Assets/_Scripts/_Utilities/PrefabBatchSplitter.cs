using System.Collections.Generic;
using UnityEngine;

public class PrefabBatchSplitter : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IBatchPartitioner
{
    public Component Component() => this;

    public List<List<Transform>> GetExcludedTransforms()
    {
        return new List<List<Transform>> { new() { transform } };
    }

    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }
}
