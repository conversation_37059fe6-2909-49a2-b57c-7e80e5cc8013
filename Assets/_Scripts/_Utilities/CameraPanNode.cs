using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(CameraPanNode))]
public class CameraPanNodeEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var wasContinuous = s_continuousCameraFromNode || s_continuousCameraToNode;

        var cpn = target as CameraPanNode;
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Set node from camera"))
        {
            SetFromCamera(cpn);
        }
        s_continuousCameraToNode = GUILayout.Toggle(s_continuousCameraToNode, "Continuous");
        if (s_continuousCameraToNode) s_continuousCameraFromNode = false;
        GUILayout.EndHorizontal();
        if (GUILayout.Button("Add node from camera"))
        {
            var go = new GameObject("Node");
            go.transform.SetParent(cpn.transform.parent);
            go.transform.SetSiblingIndex(cpn.transform.GetSiblingIndex() + 1);
            var newNode = go.AddComponent<CameraPanNode>();
            newNode.m_nodeSpeed = cpn.m_nodeSpeed;
            SetFromCamera(newNode);
            Selection.activeGameObject = go;
        }
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Set camera from node"))
        {
            SetCameraFrom(cpn);
        }
        s_continuousCameraFromNode = GUILayout.Toggle(s_continuousCameraFromNode, "Continuous");
        if (s_continuousCameraFromNode) s_continuousCameraToNode = false;
        GUILayout.EndHorizontal();
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Prev Node"))
        {
            var index = cpn.transform.GetSiblingIndex();
            if (index > 0)
                Selection.activeGameObject = cpn.transform.parent.GetChild(index - 1).gameObject;
        }
        if (GUILayout.Button("Next Node"))
        {
            var index = cpn.transform.GetSiblingIndex();
            if (index < cpn.transform.parent.childCount - 1)
                Selection.activeGameObject = cpn.transform.parent.GetChild(index + 1).gameObject;
        }
        GUILayout.EndHorizontal();
        
        if (wasContinuous != (s_continuousCameraFromNode || s_continuousCameraToNode))
        {
            if (wasContinuous)
                EditorApplication.update -= ContinuousUpdate;
            else
                EditorApplication.update += ContinuousUpdate;
        }
        
    }

    public static void SetCameraFrom(CameraPanNode cpn)
    {
        Transform camera = CamTransform;
        camera.position = cpn.transform.position;
        camera.rotation = cpn.transform.rotation;
    }

    private static Transform CamTransform => Application.isPlaying ? Camera.main.transform : SceneView.lastActiveSceneView.camera.transform;
    public static void SetFromCamera(CameraPanNode cpn)
    {
        if (cpn != null)
        {
            Transform camera = CamTransform;
            cpn.transform.position = camera.position;
            cpn.transform.rotation = camera.rotation;
        }
    }

    private static bool s_continuousCameraFromNode = false, s_continuousCameraToNode = false;
    private static void ContinuousUpdate()
    {
        if (GameManager.Me == null || GameManager.Me.CameraPanSequenceInProgress) return;
        if (s_continuousCameraFromNode)
        {
            var cpn = Selection.activeGameObject?.GetComponent<CameraPanNode>();
            if (cpn != null) SetCameraFrom(cpn);
        }
        if (s_continuousCameraToNode)
        {
            var cpn = Selection.activeGameObject?.GetComponent<CameraPanNode>();
            if (cpn != null) SetFromCamera(cpn);
        }
    }    
}
#endif

public class CameraPanNode : MonoBehaviour
{
    public static string s_lastCameraPanPoint = "";
    
    public float m_nodeTransitionTime = 0;
    public float m_nodeSpeed = 40;
    public float m_speedBlend = .1f;
    public bool m_fadeInOnStart = true;
    public bool m_fadeOutOnStart = true;
    public bool m_resetCameraOnEnd = false;
    public bool m_destroyParentOnFinish = false;
    public bool m_blockInteractions = true;
    private System.Action m_onFinishAction;
    [Multiline(6)] public string m_nodeData = "";
    private bool m_isController = false;
    private List<CameraPanNode> m_nodes;
    private float m_nodeTime;
    private float m_fadeTime;
    private Vector3 m_smoothPos, m_smoothFwdPos;
    private int m_lastNodeIndex = -1;
    private float m_currentSpeed = 0;
    private Vector3 m_storedCameraPosition;
    private Quaternion m_storedCameraRotation;
    void OnEnable()
    {
        m_isController = transform.GetSiblingIndex() == 0;
        if (m_isController == false)
        {
            gameObject.SetActive(false);
            return;
        }
        m_nodes = new();
        for (int i = 0; i < transform.parent.childCount; ++i)
        {
            var node = transform.parent.GetChild(i).GetComponent<CameraPanNode>();
            if (node != null)
                m_nodes.Add(node);
        }
        m_nodeTime = 0;
        m_fadeTime = m_fadeOutOnStart || !m_fadeInOnStart ? 0 : 1;
        m_smoothPos = Vector3.zero;
        m_lastNodeIndex = -1;
        m_storedCameraPosition = Camera.main.transform.position;
        m_storedCameraRotation = Camera.main.transform.rotation;
        GameManager.Me.StartCameraPanSequence(_blockInteractions: m_blockInteractions);
    }

    void OnDisable()
    {
        if (m_isController)
        {
            GameManager.Me.EndCameraPanSequence(_unblockInteractions: m_blockInteractions);
            if (m_fadeTime > 0)
                Crossfade.Me.SetFade(0);
            else
                GameManager.Me.BlendCameraFromCurrent();
        }
    }
    
    void Update()
    {
        int nodeIndex = (int) m_nodeTime;
        if (m_lastNodeIndex != nodeIndex)
        {
            m_lastNodeIndex = nodeIndex;
            if (nodeIndex >= 0 && nodeIndex < m_nodes.Count)
                ExecuteNodeData(m_nodes[nodeIndex].m_nodeData);
        }
        
        const float c_fadeSpeed = 2;
        if (m_fadeInOnStart && m_nodeTime < .0001f && m_fadeTime < 1f)
        {
            m_fadeTime += Time.deltaTime * c_fadeSpeed;
            if (m_fadeTime > 1) m_fadeTime = 1;
            float f = m_fadeTime * m_fadeTime * (3 - m_fadeTime - m_fadeTime);
            Crossfade.Me.SetFade(f);
            return;
        }
        if (m_fadeTime > 0f)
        {
            m_fadeTime -= Time.deltaTime * c_fadeSpeed;
            if (m_fadeTime < 0) m_fadeTime = 0;
            float f = m_fadeTime * m_fadeTime * (3 - m_fadeTime - m_fadeTime);
            Crossfade.Me.SetFade(f);
        }
        const float c_fwdDistance = 10;
        
        if (nodeIndex >= m_nodes.Count - 1)
        {
            // smooth to end
            var finalNode = m_nodes[^1];
            var finalPos = finalNode.transform.position;
            var finalFwdPos = finalPos + finalNode.transform.forward * c_fwdDistance;
            m_smoothPos = Vector3.Lerp(m_smoothPos, finalPos, .1f);
            m_smoothFwdPos = Vector3.Lerp(m_smoothFwdPos, finalFwdPos, .1f);
            var finalFwd = (m_smoothFwdPos - m_smoothPos).normalized;
            GameManager.Me.UpdateCameraPanSequence(m_smoothPos, finalFwd, finalNode.m_nodeData);
            if ((m_smoothPos - finalPos).sqrMagnitude < .1f * .1f || (m_smoothFwdPos - finalFwdPos).sqrMagnitude < .1f * .1f)
            {
                GameManager.Me.EndCameraPanSequence();
                transform.parent.gameObject.SetActive(false);
                m_onFinishAction?.Invoke();
                if (m_destroyParentOnFinish)
                    Destroy(transform.parent.gameObject);
                if (m_resetCameraOnEnd)
                {
                    Crossfade.Me.Fade(() =>
                    {
                        Camera.main.transform.position = m_storedCameraPosition;
                        Camera.main.transform.rotation = m_storedCameraRotation;
                        return true;
                    }, null);
                }
            }
            return;
        }
        var fraction = m_nodeTime - nodeIndex;
        var node = m_nodes[nodeIndex];
        var nextNode = m_nodes[nodeIndex + 1];
        var toNextNode = nextNode.transform.position - node.transform.position;
        var straightDistance = toNextNode.magnitude;
        if (node.m_nodeTransitionTime > 0)
            node.m_nodeSpeed = straightDistance / node.m_nodeTransitionTime;
        m_currentSpeed = Mathf.Lerp(m_currentSpeed, node.m_nodeSpeed, node.m_speedBlend);
        var tPerSecond = m_currentSpeed / straightDistance;
        m_nodeTime += Time.deltaTime * tPerSecond;
        
        var previous = nodeIndex > 0 ? m_nodes[nodeIndex - 1] : node;
        var nextNext = nodeIndex + 2 < m_nodes.Count ? m_nodes[nodeIndex + 2] : nextNode;
        var pos = CatmullRom(previous.transform.position, node.transform.position, nextNode.transform.position, nextNext.transform.position, fraction);
        var fwdPos = CatmullRom(previous.transform.position + previous.transform.forward * c_fwdDistance, 
            node.transform.position + node.transform.forward * c_fwdDistance, nextNode.transform.position + nextNode.transform.forward * c_fwdDistance,
            nextNext.transform.position + nextNext.transform.forward * c_fwdDistance, fraction);

        if (m_smoothPos.sqrMagnitude < .001f * .001f)
        {
            m_smoothPos = pos;
            m_smoothFwdPos = fwdPos;
        }
        m_smoothPos = Vector3.Lerp(m_smoothPos, pos, .25f);
        m_smoothFwdPos = Vector3.Lerp(m_smoothFwdPos, fwdPos, .25f);
        pos = m_smoothPos; fwdPos = m_smoothFwdPos;
        
        var fwd = (fwdPos - pos).normalized;
        //var rot = Quaternion.Slerp(node.transform.rotation, nextNode.transform.rotation, fraction);
        //var fwd = rot * Vector3.forward;
        GameManager.Me.UpdateCameraPanSequence(pos, fwd, node.m_nodeData);
    }

    private void ExecuteNodeData(string _data)
    {
        if (string.IsNullOrEmpty(_data)) return;
        var lines = _data.Split("\n");
        foreach (var line in lines)
        {
            if (string.IsNullOrEmpty(line)) continue;
            if (line.StartsWith("!"))
            {
                // debug console command - test only, cannot be used for release builds!
                Debug.LogError($"CameraPanNode contains debug-only command {line}\nThis must be replaced before release!");
                DebugConsole.Me.ExecuteConsole(line.Substring(1), true);
            }
            else
            {
                if (line.StartsWith("@"))
                {
                    int firstColon = line.IndexOf(':');
                    if (firstColon > 0)
                    {
                        var waitTime = floatinv.Parse(line[1..firstColon]);
                        var cmd = line[(firstColon+1)..];
                        Utility.After(waitTime, () => ExecuteNodeDataLine(cmd));
                    }
                }
                else
                    ExecuteNodeDataLine(line);
            }
        }
    }

    private void ExecuteNodeDataLine(string _line)
    {
        var bits = _line.Split('=');
        if (bits.Length != 2) return;
        switch (bits[0].ToLower())
        {
            case "overridetod":
                DayNight.SetClock(bits[1]);
                break;
            case "blendtod":
                var sub = bits[1].Split('@');
                if (sub.Length != 2) break;
                DayNight.Me.StartBlend(DayNight.ClockToFraction(sub[0]), floatinv.Parse(sub[1]));
                break;
            case "dof":
                DepthOfFieldController.Me.m_disableDOF = !Utility.SetOrToggle(!DepthOfFieldController.Me.m_disableDOF, bits[1]);
                break;
            case "dofcenter":
                Utility.SetOrToggle(ref DepthOfFieldController.Me.m_focusOnScreenCenter, bits[1]);
                break;
            case "hidehand":
                Utility.SetOrToggle(ref PlayerHandManager.Me.m_forceHideHand, bits[1]);
                break;
            case "nav":
                OverrideCharacterNavigation(transform.parent, bits[1]);
                break;
            case "chranim":
                PlayCharacterAnimation(bits[1]);
                break;
            case "clouds":
                bool instant = bits[1].StartsWith("!");
                if (instant)
                    bits[1] = bits[1].Substring(1);
                if (instant)
                    ;
                break;
            case "buildinganim":
                BCActionBase.s_forceBuildingAnimation = Utility.SetOrToggle(BCActionBase.s_forceBuildingAnimation != 0, bits[1]) ? 1 : 0;
                break;
            case "districtanimspeed":
                DistrictManager.Me.SetAnimationSpeed(floatinv.Parse(bits[1]));
                break;
            case "flow":
                s_lastCameraPanPoint = bits[1];
                break;
        }
    }
    

    public static void PlayCharacterAnimation(string _desc)
    {
        var bits = _desc.Split(":");
        if (bits.Length < 1) return;
        var target = NGManager.Me.FindCharacterByID(int.Parse(bits[0]));
        if (target == null) return;
        var animator = target.GetComponentInChildren<Animator>();
        if (animator == null) return;
        if (bits.Length == 1)
        {
            AnimationOverride.Stop(animator.gameObject);
            return;
        }
        string animIn = null, animLoop = bits[1], animOut = null;
        if (bits.Length >= 3)
        {
            animIn = animLoop;
            animLoop = bits[2];
            if (bits.Length >= 4)
                animOut = bits[3];
        }
        AnimationOverride.PlayClip(animator.gameObject, animIn, null, animLoop, null, animOut, null); 
    }

    public static void OverrideCharacterNavigation(Transform _root, string _desc)
    {
        var bits = _desc.Split(":");
        if (bits.Length < 3) return;
        var target = NGManager.Me.FindCharacterByID(int.Parse(bits[0]));
        if (target == null)
        {
            Debug.LogError($"Couldn't find character {bits[0]} in node cmd {_desc}");
            return;
        }
        var from = _root.FindChildRecursiveByName(bits[1]);
        if (from == null)
        {
            Debug.LogError($"Couldn't find nav point 1 {bits[1]} in node cmd {_desc}");
            return;
        }
        var to = _root.FindChildRecursiveByName(bits[2]);
        if (to == null)
        {
            Debug.LogError($"Couldn't find nav point 2 {bits[2]} in node cmd {_desc}");
            return;
        }
        var nav = target.GetComponent<NavAgent>();
        var pos = from.position.GroundPosition(.1f);
        target.transform.position = pos;
        if (bits.Length >= 5)
            nav.NavigateToDirect(to.position);
        else
            nav.NavigateTo(to.position);
        nav.ForceUnpause();
        var speed = 15f;
        if (bits.Length >= 4)
            speed = floatinv.Parse(bits[3]);
        nav.Speed = speed;
        target.CharacterUpdateState.ApplyState(CharacterStates.Possessed);
    }

    public static void SetFinishAction(GameObject _root, bool _destroyOnFinish, System.Action _finishAction)
    {
        var firstChild = _root.transform.GetChild(0);
        if (firstChild == null) return;
        var cpn = firstChild.GetComponent<CameraPanNode>();
        if (cpn == null) return;
        cpn.m_destroyParentOnFinish = _destroyOnFinish;
        cpn.m_onFinishAction = _finishAction;
    }

    public static void StartCameraPanSequence(string _name, bool _destroyOnFinish = true, System.Action _finishAction = null)
    {
        var resourcePath = $"_Prefabs/CameraSequence/{_name}";
        var prefab = Resources.Load<GameObject>(resourcePath);
        if (prefab == null)
        {
            Debug.LogError($"Couldn't find pan sequence prefab {resourcePath}");
            return;
        }
        StartCameraPanSequence(prefab, _destroyOnFinish, _finishAction);
    }

    public static void StartCameraPanSequence(GameObject _prefab, bool _destroyOnFinish = true, System.Action _finishAction = null)
    {
        var go = Instantiate(_prefab);
        if (go.activeSelf == false)
            go.SetActive(true);
        SetFinishAction(go, _destroyOnFinish, _finishAction);
    }    

    
    public static Vector3 CatmullRom(Vector3 _a, Vector3 _b, Vector3 _c, Vector3 _d, float _t)
    {
        float t1 = _t;
        float t2 = t1 * t1;
        float t3 = t2 * t1;
        const float Tau = 0.5f; //PathSet.s_tension;
        float kTt1 = Tau * t1, kTt2 = Tau * t2, kTt3 = Tau * t3;
        float k3mTt2 = t2 + t2 + t2 - kTt2, k2mTt3 = t3 + t3 - kTt3;
        float mM1 = kTt2 + kTt2 - kTt1 - kTt3; //(-t1 + 2 * t2 - t3) * Tau;
        float m0 = 1 - k3mTt2 + k2mTt3; //1 + (Tau - 3) * t2 + (2 - Tau) * t3;
        float mP1 = kTt1 + k3mTt2 - kTt2 - k2mTt3; //Tau * t1 + (3 - Tau * 2) * t2 + (Tau - 2) * t3;
        float mP2 = kTt3 - kTt2; //(-t2 + t3) * Tau;
        //return _list[indexM1] * mM1 + _list[_index0] * m0 + _list[indexP1] * mP1 + _list[indexP2] * mP2;
        var lM1 = _a;
        var l0 = _b;
        var lP1 = _c;
        var lP2 = _d;
        return new Vector3(lM1.x * mM1 + l0.x * m0 + lP1.x * mP1 + lP2.x * mP2,
            lM1.y * mM1 + l0.y * m0 + lP1.y * mP1 + lP2.y * mP2,
            lM1.z * mM1 + l0.z * m0 + lP1.z * mP1 + lP2.z * mP2);        
    }
}
