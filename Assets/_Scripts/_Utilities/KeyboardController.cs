using UnityEngine;

public enum EKeyboardFunction
{
    None,
    CAT_CameraControl,
    MoveCamera,
    RotateCamera,
    RotateCameraByMouse,
    DollyCamera,
    JumpToOakridge,
    ShowMap,
    CAT_Misc,
    Settings,
    Cancel,
    GestureMenu,
    HandPowerMenu,
    CAT_Design,
    DesignMode,
    LeaveFocus,
    RaiseLowerDesignCamera,
    CAT_Worker,
    AssignJob,
    AssignHome,
    CAT_Hero,
    SetPatrolZone,
    IncPatrolZone,
    DecPatrolZone,
    CAT_Possession,
    Possess,
    PossessRun,
    PossessInfo,
    UnfollowAll,
    PossessTransfer,
};

public class KeyboardController : MonoSingleton<KeyboardController>
{
    public class KeyAssignEntry
    {
        public string m_label;
        public KeyCode[] m_keys;
        public KeyCode[] m_defaults;
        public string[] m_keyLabels;
        public EKeyboardFunction[] m_canConflictWith;

        public void SetKey(int _index, KeyCode _key)
        {
            m_keys[_index] = _key;
            MPlayerPrefs.SetInt(m_label + _index, (int) _key);
        }
        public KeyCode Load(int _index) => (KeyCode)MPlayerPrefs.GetInt(m_label + _index, (int) m_defaults[_index]);

        public string GetString(int _index, bool _colourIfConflicted = false)
        {
            var str = KeyboardShortcutManager.Me.GetKeyString(m_keys[_index]);
            if (_colourIfConflicted && Me.IsConflicted(this, _index))
                str = $"<color=#c00000>{str}</color>";
            return str;
        }
    }

    public KeyAssignEntry[] m_keyAssignRows =
    {
        new(),
        new() {m_label = "Camera Control" },
        new() {m_label = "Move Camera", m_defaults = new[] {KeyCode.W, KeyCode.A, KeyCode.S, KeyCode.D}, m_keyLabels = new[] {"Up", "Left", "Down", "Right"}},
        new() {m_label = "Rotate Camera", m_defaults = new[] {KeyCode.Q, KeyCode.E}, m_keyLabels = new[] {"Left", "Right"}},
        new() {m_label = "Rotate Camera (mouse)", m_defaults = new[] {KeyCode.C}},
        new() {m_label = "Dolly Camera", m_defaults = new[] {KeyCode.Z, KeyCode.X}, m_keyLabels = new[] {"In", "Out"}},
        new() {m_label = "Jump to Oakridge", m_defaults = new[] {KeyCode.Space, KeyCode.Mouse2}},
        new() {m_label = "World Map", m_defaults = new[] {KeyCode.M}},
        new() {m_label = "Miscellaneous" },
        new() {m_label = "Settings", m_defaults = new[] {KeyCode.Escape}, m_canConflictWith = new[] {EKeyboardFunction.Cancel, EKeyboardFunction.LeaveFocus}},
        new() {m_label = "Cancel", m_defaults = new[] {KeyCode.Escape}, m_canConflictWith = new[] {EKeyboardFunction.Settings, EKeyboardFunction.LeaveFocus}},
        new() {m_label = "Gesture Menu", m_defaults = new[] {KeyCode.G}, m_canConflictWith = new[] {EKeyboardFunction.AssignHome, EKeyboardFunction.AssignJob}},
        new() {m_label = "Hand Power Menu", m_defaults = new[] {KeyCode.H}, m_canConflictWith = new[] {EKeyboardFunction.AssignHome, EKeyboardFunction.AssignJob}},
        new() {m_label = "Design"},
        new() {m_label = "Design Mode", m_defaults = new[] {KeyCode.Period}},
        new() {m_label = "Leave Focus", m_defaults = new[] {KeyCode.Escape}, m_canConflictWith = new[] {EKeyboardFunction.Cancel, EKeyboardFunction.Settings}},
        new() {m_label = "Raise Camera", m_defaults = new[] {KeyCode.R, KeyCode.F}, m_keyLabels = new[] {"Raise", "Lower"}},
        new() {m_label = "Held Worker"},
        new() {m_label = "Assign Job", m_defaults = new[] {KeyCode.J}, m_canConflictWith = new[] {EKeyboardFunction.AssignHome, EKeyboardFunction.GestureMenu, EKeyboardFunction.HandPowerMenu}},
        new() {m_label = "Assign Home", m_defaults = new[] {KeyCode.H}, m_canConflictWith = new[] {EKeyboardFunction.AssignJob, EKeyboardFunction.GestureMenu, EKeyboardFunction.HandPowerMenu}},
        new() {m_label = "Held Hero"},
        new() {m_label = "Set patrol zone", m_defaults = new[] {KeyCode.P}, m_canConflictWith = new[] {EKeyboardFunction.Possess}},
        new() {m_label = "Increase patrol zone", m_defaults = new[] {KeyCode.Plus, KeyCode.KeypadPlus, KeyCode.Equals}},
        new() {m_label = "Decrease patrol zone", m_defaults = new[] {KeyCode.Minus, KeyCode.KeypadMinus}},
        new() {m_label = "Possession"},
        new() {m_label = "Possess", m_defaults = new[] {KeyCode.P}, m_canConflictWith = new[] {EKeyboardFunction.SetPatrolZone}},
        new() {m_label = "Run", m_defaults = new[] {KeyCode.LeftShift, KeyCode.RightShift}},
        new() {m_label = "Info", m_defaults = new[] {KeyCode.I}},
        new() {m_label = "Untag All", m_defaults = new[] {KeyCode.U}},
        new() {m_label = "Possess Other", m_defaults = new[] {KeyCode.T}},
    };

    public KeyCode[] GetKeys(EKeyboardFunction _key) => m_keyAssignRows[(int) _key].m_keys;
    public KeyCode GetKey(EKeyboardFunction _key, int _subKey = 0) => GetKeys(_key)[_subKey];

    public static bool GetKeyDown(EKeyboardFunction _key, int _subKey = 0) => Utility.GetKeyDown(Me.GetKey(_key, _subKey));
    public static bool GetKeyHeld(EKeyboardFunction _key, int _subKey = 0) => Utility.GetKey(Me.GetKey(_key, _subKey));

    private System.Collections.Generic.Dictionary<KeyCode, Vector2> m_keyDownPositions = new();
    private bool TrackKeyClick(KeyCode _key)
    {
        if (Utility.GetKeyDown(_key))
        {
            m_keyDownPositions[_key] = Input.mousePosition;
        }
        else if (Utility.GetKeyUp(_key))
        {
            if (m_keyDownPositions.TryGetValue(_key, out var downPos))
            {
                m_keyDownPositions.Remove(_key);
                var threshold = Screen.height * .02f;
                return Vector2.Distance(downPos, Input.mousePosition) < threshold;
            }
        }
        return false;
    }
    public static bool GetKeyClicked(EKeyboardFunction _key, int _subKey = 0) => Me.TrackKeyClick(Me.GetKey(_key, _subKey));

    public static bool AnyKeyDown(EKeyboardFunction _key)
    {
        foreach (var thisKey in Me.GetKeys(_key))
            if (Utility.GetKeyDown(thisKey))
                return true;
        return false;
    }

    public static bool AnyKeyHeld(EKeyboardFunction _key)
    {
        foreach (var thisKey in Me.GetKeys(_key))
            if (Utility.GetKey(thisKey))
               return true;
        return false;
    }
    
    public static bool AnyKeyClicked(EKeyboardFunction _key)
    {
        foreach (var thisKey in Me.GetKeys(_key))
            if (Me.TrackKeyClick(thisKey))
                return true;
        return false;
    }

    public static KeyCode GetKeyClicked(EKeyboardFunction _key)
    {
        foreach (var thisKey in Me.GetKeys(_key))
            if (Me.TrackKeyClick(thisKey))
                return thisKey;
        return KeyCode.None;
    }

    void Start()
    {
        Load();
    }

    void Load()
    {
        for (int i = 0; i < m_keyAssignRows.Length; ++i)
        {
            int numKeys = m_keyAssignRows[i].m_defaults?.Length ?? 0;
            m_keyAssignRows[i].m_keys = new KeyCode[numKeys];
            for (int j = 0; j < numKeys; ++j)
                m_keyAssignRows[i].m_keys[j] = m_keyAssignRows[i].Load(j);
        }
    }

    public void ResetToDefaults()
    {
        for (int i = 0; i < m_keyAssignRows.Length; ++i)
        {
            int numKeys = m_keyAssignRows[i].m_defaults?.Length ?? 0;
            for (int j = 0; j < numKeys; ++j)
                m_keyAssignRows[i].SetKey(j, m_keyAssignRows[i].m_defaults[j]);
        }
    }
    
    public bool IsConflicted(KeyAssignEntry _entry, int _index)
    {
        for (int i = 0; i < m_keyAssignRows.Length; ++i)
        {
            var check = m_keyAssignRows[i];
            if (_entry.m_canConflictWith != null && _entry.m_canConflictWith.Contains((EKeyboardFunction)i))
                continue; // this entry is allowed to conflict with this row
            for (int j = 0; j < m_keyAssignRows[i].m_keys.Length; ++j)
            {
                if (check == _entry && _index == j) continue;
                if (_entry.m_keys[_index] == check.m_keys[j])
                    return true;
            }
        }
        return false;
    }
}
