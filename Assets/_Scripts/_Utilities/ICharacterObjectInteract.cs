public interface ICharacterObjectInteract
{
    string InteractType { get; }
    bool CanInteract(NGMovingObject _chr);
    string GetInteractLabel(NGMovingObject _chr);
    void DoInteract(NGMovingObject _chr);
    bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr);
    void EnableInteractionTriggers(bool _enable);
    float AutoInteractTime { get; }
    string name { get;  }
}
