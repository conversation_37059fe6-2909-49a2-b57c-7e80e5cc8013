using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

public class DebugDisplayUnlocks : MonoBehaviour
{
    NGScrollRect m_scrollRect;
    Transform m_content;
    string m_filter = "";
    bool m_showAll = false;
    void Activate()
    {
        GameManager.SetKeyboardConsumeState(true);
        m_scrollRect = GetComponentInChildren<NGScrollRect>();
        m_content = m_scrollRect.content;
        Utility.DoNextFrame(() => Refresh());
    }

    void OnDestroy()
    {
        GameManager.SetKeyboardConsumeState(false);
    }

    void FillEntry(int _index, List<(string, string)> _entries, GameObject _obj)
    {
        var (name, label) = _entries[_index];
        var text = _obj.GetComponentInChildren<TMPro.TextMeshProUGUI>();
        text.text = label;
        var buttonMinus = _obj.transform.GetChild(0).GetComponent<Button>();
        var buttonPlus = _obj.transform.GetChild(1).GetComponent<Button>();
        buttonMinus.onClick.RemoveAllListeners();
        buttonMinus.onClick.AddListener(() => AdjustUnlock(name, -1));
        buttonPlus.onClick.RemoveAllListeners();
        buttonPlus.onClick.AddListener(() => AdjustUnlock(name, 1));
    }

    GameObject m_pattern = null;
    void Refresh()
    {
        if (m_pattern == null)
        {
            m_pattern = m_content.GetChild(0).gameObject;
            m_pattern.SetActive(false);
            m_pattern.transform.SetParent(transform);
        }
        
        var unlocks = GameManager.Me.m_state.m_unlocks;
        var entries = unlocks.Keys;
        if (m_showAll) entries = NGBlockInfo.s_allBlocks.Keys.ToList();
        bool odd = false;
        var entryList = new List<(string, string)>();
        foreach (var entryName in entries)
        {
            string entry = entryName;
            unlocks.TryGetValue(entry, out int value);
            if (string.IsNullOrEmpty(m_filter) == false && entry.ToLower().Contains(m_filter) == false) continue;
            var colourString = odd ? "f0fff0" : "ffffff";
            entry = $"<color=#{colourString}>{entry}</color>";
            var valueColourString = "c0ffc0";
            if (value == 0) valueColourString = "ffc0c0";
            else if (value > 0) valueColourString = "c0c0ff";
            entry = $"{entry} <size=75%><color=#{valueColourString}>{(value < 0 ? "inf" : value.ToString())}</color></size>";
            entryList.Add((entryName, entry));
        }

        m_scrollRect.Activate(entryList.Count, () => Instantiate(m_pattern, m_content).gameObject, (_i, _obj) => FillEntry(_i, entryList, _obj), m_scrollRect.CurrentIndex());
    }

    public static int AddValuesSimpleClamp(int _a, int _b)
    {
        return Mathf.Max(-1, _a + _b);
    }
    private void AdjustUnlock(string _entry, int _add)
    {
        GameManager.Me.m_state.m_unlocks.AddValue(_entry, _add, AddValuesSimpleClamp);
        Refresh();
    }

    public void UpdateFilter(string _s)
    {
        m_filter = _s.ToLower();
        Refresh();
    }

    public void UpdateShowAll(bool _showAll)
    {
        m_showAll = _showAll;
        Refresh();
    }

    public void Close()
    {
        Destroy(gameObject);
    }

    static DebugConsole.Command s_displayunlocks = new ("showunlocks", _s => Create(NGManager.Me.m_debugDisplayUnlocksPrefab));
    public static DebugDisplayUnlocks Create(GameObject _prefab, Transform _currentCanvas = null)
    {
        if (_currentCanvas == null) _currentCanvas = GameManager.Me.CurrentCanvas;
        var go = Instantiate(_prefab, _currentCanvas);
        var ddu = go.GetComponent<DebugDisplayUnlocks>();
        ddu.Activate();
        return ddu;
    }
}
