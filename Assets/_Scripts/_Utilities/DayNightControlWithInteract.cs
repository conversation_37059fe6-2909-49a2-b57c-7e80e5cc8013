using UnityEngine;

public class DayNight<PERSON>ontrolWithInteract : <PERSON><PERSON><PERSON>C<PERSON>rol, ICharacterObjectInteract
{
    private bool m_interactDisabled = false;

    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr) => m_interactDisabled == false && GameManager.Me.GlobalInteractCheck(_chr, false) &&
                                                    NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);

    public string GetInteractLabel(NGMovingObject _chr) => "Interact";

    public void DoInteract(NGMovingObject _chr)
    {
        Click();
    }

    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => InteractionSequenceNode.AttemptInteraction(_chr, gameObject);

    public void EnableInteractionTriggers(bool _b) => m_interactDisabled = !_b;
    
    public float AutoInteractTime => 0;
}
