using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public interface IDTDragTrigger {
	void DragTriggered(DTDragCategory _me);
}

public static class DragHolder {
	public static Dictionary<int, DragBase> s_activeDrags = new Dictionary<int, DragBase>();
	public static Dictionary<int, DragBase> s_activeLongPresses = new Dictionary<int, DragBase>();
	public static bool AnyDragging => s_activeDrags.Count > 0;
	public static bool IsButtonConsumed(int _button) => s_activeDrags.ContainsKey(_button) || s_activeLongPresses.ContainsKey(_button);

	public static void DropAllHeldItems()
	{
		var toDrop = new List<DragBase>(); 
		foreach (var kvp in s_activeDrags)
			toDrop.Add(kvp.Value); // copy to a list since s_activeDrags will be changed by EndDrag
		foreach (var drag in toDrop)
			if (drag != null && drag.IsHeld)
				drag.EndDrag();
	}

	private static int s_dragIdBase = 0;
	private static int s_longPressIdBase = 65536;
	private static int s_idMask = 0xFFFF;
	public static void RemoveDrag(int _touchId)
	{
		s_activeDrags.Remove(_touchId);
		s_potentiallyOrphaned.Remove(_touchId | s_dragIdBase);
	}
	public static void RemoveLongPress(int _touchId)
	{
		s_activeLongPresses.Remove(_touchId);
		s_potentiallyOrphaned.Remove(_touchId | s_longPressIdBase);
	}
	
	public static Dictionary<int, int> s_potentiallyOrphaned = new();
	public static List<int> s_toRemove = new();

	private static void CheckOrphanSet(Dictionary<int, DragBase> _set, int _idBase, string _type)
	{
		s_toRemove.Clear();
		foreach (var kvp in _set)
		{
			if (GameManager.GetMouseButton(kvp.Key) == false)
			{
				int id = kvp.Key + _idBase;
				int count = s_potentiallyOrphaned.AddCount(id, 1);
				if (count >= 2)
				{
					s_toRemove.Add(kvp.Key);
					s_potentiallyOrphaned.Remove(id);
				}
			}
		}
		foreach (var rem in s_toRemove)
		{
			Debug.LogError($"Orphaned {_type} on ID {rem}");
			_set.Remove(rem);
		}
		// check for entries removed from _set that are still in s_potentiallyOrphaned
		s_toRemove.Clear();
		foreach (var kvp in s_potentiallyOrphaned)
		{
			if ((kvp.Key & ~s_idMask) == _idBase && _set.ContainsKey(kvp.Key & s_idMask) == false)
			{
				// this has been cleaned up, no longer a potential orphan
				s_toRemove.Add(kvp.Key);
			}
		}
		foreach (var pot in s_toRemove)
		{
			Debug.LogError($"Removed no-longer potentially orphaned {_type} on ID {pot}");
			s_potentiallyOrphaned.Remove(pot);
		}
	}
	public static void CheckOrphans()
	{
		CheckOrphanSet(s_activeDrags, s_dragIdBase, "Drag");
		CheckOrphanSet(s_activeLongPresses, s_longPressIdBase, "Long Press");
	}
}

public class DragBase : MonoBehaviour, IPointerDownHandler {
	bool m_dragging = false; public bool IsDragging => m_dragging;
	bool m_dragStarted = false;
	bool m_confirmedNotClick = false; public bool CouldBeClick => !m_confirmedNotClick;
	bool m_dragThreshholdExceeded = false; public bool DragThreshholdExceeded => m_dragThreshholdExceeded;
	Plane m_dragPlane;
	Vector3 m_dragOrigin, m_dragPrevious, m_dragDelta, m_dragInitial, m_dragPreviousScreen, m_smoothedDragDeltaScreen;
	Vector3 m_attractOrigin;
	private bool m_hasTracked;
	public bool m_attracted;
	float m_clickTime; public float ClickTime => m_clickTime;
	GameObject m_line;
	public float TimeSinceClick => Utility.GameTime - m_clickTime;
	public Vector3 DragDelta => m_dragDelta;
	public Vector3 InitialPosition => m_dragInitial;
	virtual public Camera Cam => GameManager.Me.m_camera;
	virtual public Vector3 DragPlaneNormal => Vector3.up;
	virtual public Vector3 DragPlaneOrigin => Vector3.zero;
	virtual public bool AcceptsClicks => false;
	virtual public bool UpdatesDuringClick => false;
	virtual public bool SupportsLongPress => true;
	virtual public bool IsLongPress => false;
	virtual public bool HasBezier => false;
	virtual public Color BezierColour => Color.clear;
	virtual public BezierLine.ControlGeneration BezierShape => BezierLine.ControlGeneration.SmoothBoth;
	PointerEventData.InputButton m_pointerDownButton = (PointerEventData.InputButton)(-1);
	protected float m_smoothedDragFade = 1;
	public Vector3 SmoothedDragDelta => m_smoothedDragDeltaScreen * m_smoothedDragFade;
	Vector3 m_pointerDownWorldPosition, m_pointerDownWorldNormal;
	public Vector3 PointerDownWorldPosition => m_pointerDownWorldPosition;
	public Vector3 PointerDownWorldNormal => m_pointerDownWorldNormal;
	GameObject m_pointerDownObject; public GameObject PointerDownObject => m_pointerDownObject;
	private int m_bezSound;
	private float m_attractRadius;
	private IPickupBehaviour m_behaviour;
	private bool m_attractBegin;
    private float m_attractForce;
    private float m_pointerDownTime;

	public bool IsHeld { get { return m_dragging; } }

	public void OnPointerDown(PointerEventData _data)
	{
		if (TouchManager.TouchInputActive || GameManager.Me.CameraExceededMaxInteractionDistance()) return;
		
		OnInputDown(_data);
	}
	public void OnInputDown(PointerEventData _data) 
	{
		if (GameManager.Me.AreMouseInteractionsBlocked) return;
		if (m_dragging || DesignTableManager.Me.GrabbingIsBlockedByTutorial) return;
		OnPress();
		m_pointerDownButton = _data.button;
		//if (Input.touchCount == 2) m_pointerDownButton = PointerEventData.InputButton.Right;
		m_pointerDownWorldPosition = _data.pointerCurrentRaycast.worldPosition;
		m_pointerDownWorldNormal = _data.pointerCurrentRaycast.worldNormal;
		m_pointerDownObject = _data.pointerCurrentRaycast.gameObject;
		m_pointerDownTime = Utility.GameTime;
		m_smoothedDragDeltaScreen = Vector3.zero;
		m_wasLongPress = false;
		StartDrag();
	}

	public void SetInputId(int _inputID)
	{
		m_pointerDownButton = (PointerEventData.InputButton)_inputID;
	}

	public void CopyParameters(DragBase _from)
	{
		if (_from == null) return;
		m_pointerDownButton = _from.m_pointerDownButton;
		m_pointerDownWorldPosition = _from.m_pointerDownWorldPosition;
		m_pointerDownWorldNormal = _from.m_pointerDownWorldNormal;
		m_pointerDownObject = _from.m_pointerDownObject;
		m_smoothedDragDeltaScreen = Vector3.zero;
	}

	public void ConfirmDrag() { m_confirmedNotClick = true; }
	private bool OnVanOrTrain()
	{
        NGReactPickupAny pickup = this.gameObject.GetComponent<NGReactPickupAny>();
		if (pickup != null)
		{
			foreach (var van in NGManager.Me.MAVehicles)
			{
				
				if (van.VehicleState.State == VehicleStateFactory.VehicleState.kWaitingForCargo)
					return false;

				MADeliveryCart deliVan = van as MADeliveryCart;
				if(deliVan == null)
					return false;

				if (deliVan.ContainsCargo(pickup.m_product.m_uniqueID))
					return true;
				
				if (pickup.IsOnTrain)
					return true;
			}
		}
		return false;
    }
	virtual public bool CanDrag => true;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	public bool DebugDragging = false;
	public void StartDebugDrag(PointerEventData _ped)
	{
		DebugDragging = true;
		Utility.SetInputPos(new Vector3(_ped.position.x - 100, _ped.position.y, 0));
		OnPointerDown(_ped);
		Utility.SetInputPos(Vector3.right * 100, true);
	}
	public void ChangeDebugDrag(Vector3 _move)
	{
		Utility.SetInputPos(_move, true);
	}
	public void EndDebugDrag()
	{
		DebugDragging = false;
		Utility.ClearInputPos();
	}
#else
	public bool DebugDragging => false;
	public void StartDebugDrag(PointerEventData _ped) {}
	public void EndDebugDrag() {}
#endif
	public void StartDrag(bool _isAlreadyDragging = false, int _inputId = -1)
	{		
		if (OnVanOrTrain()) return;
		if (m_dragging)
		{
			return;
		}

		if (!CanDrag && !_isAlreadyDragging)
		{
			return;
		}
		
		m_dragging = true;
		if (_inputId != -1)
			m_pointerDownButton = (PointerEventData.InputButton)_inputId;
		m_confirmedNotClick = _isAlreadyDragging;
		m_dragPlane = new Plane(DragPlaneNormal, DragPlaneOrigin);
		m_dragOrigin = InputPosition;
		m_dragPrevious = RaycastMouse;
		m_dragInitial = transform.position;
		m_attractOrigin = transform.position;
		m_clickTime = Utility.GameTime;
		if (!AcceptsClicks && DesignTableManager.Me.GrabbingIsBlockedByTutorial == false)
			DoDragStart();
		else if (_isAlreadyDragging)
			CreateBezier();
		m_attractRadius = 50.0f;
		m_attractBegin = false;
        m_attractForce = 1.0f;
		StartCoroutine(Co_Drag());
	}
	public void EndDrag() { Cleanup(false); }

	private bool m_lockInputPosition = false;
	private Vector3 m_lockedInputPosition;
	private Ray m_lockedInputRay;

	public void LockInputPosition()
	{
		if (m_lockInputPosition) return;
		m_lockedInputPosition = InputPosition;
		m_lockedInputRay = Cam.RayAtScreenPosition(InputPosition);
		m_lockInputPosition = true;
	}

	public void LockInputPosition(Vector3 _hitPoint)
	{
		if (m_lockInputPosition) return;
		var planePos = RaycastMouse;
		m_lockedInputPosition = InputPosition;
		m_lockedInputRay = new Ray(planePos, _hitPoint - planePos);
		m_lockInputPosition = true;
	}

	protected Vector3 RaycastMouse { 
		get {
			float t = 0;
			var ray = m_lockInputPosition ? m_lockedInputRay : Cam.RayAtScreenPosition(InputPosition);
			m_dragPlane.Raycast(ray, out t);
			return ray.GetPoint(t);
		}
	}

	public void TransferDrag(DragBase _to)
	{
		int id = (int)InputId;
		EndDrag();
		if (_to != null)
		{
			_to.OnDragStart();
			_to.SetInputId(id);
			_to.StartDrag(true);
		}
	}

	private void StopTrackingDrag(bool wasDrag, bool wasLongPress)
	{
		if (wasDrag) DragHolder.RemoveDrag((int) m_pointerDownButton);
		if (wasLongPress) DragHolder.RemoveLongPress((int) m_pointerDownButton);
	}


	public Vector3 GetRaycastMouse() { return RaycastMouse; }
	protected void OnDisable() { Cleanup(false, false); }
	protected void OnDestroy() { Cleanup(false, false); }
	void Cleanup(bool _undo, bool _confirm = true) {
		if (m_dragging) {
			bool wasLongPress = IsLongPress, wasDrag = m_dragStarted;
			if (!_confirm) {
				// cancelled (by disable/destroy or whatever) - do nothing except clean up
				DoDragEnd(_undo, false);
			} else if (!AcceptsClicks || m_confirmedNotClick) {
				DoDragEnd(_undo, true);
			} else if (_undo == false)  {
				//if (IsRightButton) ForceLongClick(); // right click == long left click
				if (IsRightButton || IsLeftButton) OnClick();
			}
			StopTrackingDrag(wasDrag, wasLongPress);
			m_dragging = false;
			m_dragThreshholdExceeded = false;
			m_confirmedNotClick = false;
			m_lockInputPosition = false;
		}
	}
	void ForceLongClick() {
		m_clickTime = Utility.GameTime - 100;
	}
	public void ForceDrag() {
		m_confirmedNotClick = true;
		DoDragStart();
	}
	protected bool IsLeftButton => TouchManager.TouchInputActive == true || m_pointerDownButton == PointerEventData.InputButton.Left;
	protected bool IsRightButton => TouchManager.TouchInputActive == false && m_pointerDownButton == PointerEventData.InputButton.Right;

	public void Attract(Vector3 pos)
	{
		if(m_behaviour == null)
			m_behaviour = GetComponent<IPickupBehaviour>();

		var offset = pos - transform.position;
		transform.position += offset * m_attractForce * Time.deltaTime;
        if (m_attractForce < 5.0f)
            m_attractForce += 0.05f;
	}

	public void Release()
    {
		if (m_behaviour != null)
		{
			var source = m_behaviour?.GetSource();
			var target = m_behaviour.GetBestTarget((int)InputId, source, source.transform.position, out var action);
			/*if (target == null)
			{
				var pickup = source.GetComponent<ReactPickup>();
				if (pickup != null && (pickup.m_contents.Name.Equals(NGCarriableResource.c_rawMaterialAny) || pickup.m_contents.Name.Equals(NGCarriableResource.c_rawResourceAny)))
				{
					Destroy(source.gameObject);
					return;
				}
			}*/
			m_behaviour.OnDrop(source.transform.position, target, source, SmoothedDragDelta, false, action);
		}
	}

	public void ReleaseAttracted()
	{
		m_attractBegin = false;
		if (GameManager.Me.m_state.m_pickups == null)
			return;

		foreach (var pickup in GameManager.Me.m_state.m_pickups)
		{
			var obj = pickup.Pickup as NGMovingObject;
            if (obj != null)
            {
                var p = obj.GetComponent<Pickup>();
                if (p != null && p.m_attracted)
                {
                    p.m_attracted = false;
                    p.Release();
                }
            }
		}
	}

	private void AttractMore(Vector3 point)
    {
		m_attractRadius += Time.deltaTime * 5.0f;
		m_attractBegin = true;
	}

	private void Capture(Vector3 point)
    {
		foreach (var pickup in GameManager.Me.m_state.m_pickups)
		{
			var obj = pickup.Pickup as NGMovingObject;
			var p = obj.GetComponent<Pickup>();
            var thing = obj as NGReactPickupAny;
            if (thing != null && thing.m_holder != null) continue;

			if (p != null && p.m_attracted)
			{
				p.Attract(point);
			}
			else
			{
				var op = obj.transform.position;
				op.y = m_attractOrigin.y;
				var offset = op - m_attractOrigin;

				if (offset.magnitude < m_attractRadius)
				{
					p.m_attracted = true;
					p.m_attractOrigin = p.transform.position;
				}
			}
		}
	}
	
	public PointerEventData.InputButton InputId => m_pointerDownButton;
	public Vector3 InputPosition => m_lockInputPosition ? m_lockedInputPosition : GameManager.InputPosition((int)m_pointerDownButton);
	public Ray RayAtInputPosition(bool _useFingerRaise = false) => Cam.RayAtScreenPosition(InputPosition, _useFingerRaise);

	private void UpdateDragPlane()
	{
		m_dragPlane.SetNormalAndPosition(DragPlaneNormal, DragPlaneOrigin);
	}

	private static bool s_debugDrag = false;
	private static DebugConsole.Command s_debugDragCmd = new ("debugdrag", _s => Utility.SetOrToggle(ref s_debugDrag, _s));
	private void CheckDebugDrag()
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (s_debugDrag != DebugDragging)
		{
			DebugDragging = s_debugDrag;
			if (s_debugDrag)
				Utility.SetInputPos(Utility.mousePosition);
			else
				Utility.ClearInputPos();
		}
#endif
	}
	
	
	private bool CheckShouldHoldDrag()
	{
		var hold = ShouldHoldDrag;
		if (hold && GameManager.GetMouseButtonUp((int)m_pointerDownButton))
			StopTrackingDrag(m_dragStarted, IsLongPress);
		return hold;
	}
	
	protected virtual bool DragWithoutMove => true;

	public const float c_maxClickTime = 0.2f;
	public static float DragThreshold => Screen.height * GameManager.Me.m_dragThreshold;
	
	private float m_lastTime;
	private bool m_wasLongPress = false;
	IEnumerator Co_Drag() {
		while ((DebugDragging || (GameManager.GetMouseButton((int)m_pointerDownButton) && !GameManager.GetMouseButtonUp((int)m_pointerDownButton)) || CheckShouldHoldDrag()) && m_dragging && GameManager.Me.CameraExceededMaxInteractionDistance() == false) {
			UpdateDragPlane();
			var point = RaycastMouse;
			m_dragDelta = m_dragPrevious - point;
			var dragDeltaScreen = (InputPosition - m_dragPreviousScreen) * ((1080f / (Screen.height * 30.0f * Time.deltaTime)));
			m_smoothedDragDeltaScreen = Vector3.Lerp(m_smoothedDragDeltaScreen, dragDeltaScreen, .5f.TCLerp());
			m_dragPreviousScreen = InputPosition; 
			m_dragPrevious = point;
			var screenDragTotal = InputPosition - m_dragOrigin;
			if (AcceptsClicks) {
				if (screenDragTotal.sqrMagnitude > DragThreshold * DragThreshold || (!SupportsLongPress && DragWithoutMove && (Utility.GameTime - m_pointerDownTime) > c_maxClickTime)) {
					m_dragThreshholdExceeded = true;
					var wasNotClick = m_confirmedNotClick;
					m_confirmedNotClick = true;
					if (IsLeftButton && !wasNotClick && DesignTableManager.Me.GrabbingIsBlockedByTutorial == false)
					{
						DoDragStart();
						UpdateDragPlane(); // in case drag start changed the plane parameters
					}
				}
			}
			if (IsLongPress && !m_wasLongPress)
				DragHolder.s_activeLongPresses[(int)m_pointerDownButton] = this;
			m_wasLongPress = IsLongPress;
			if (m_confirmedNotClick || !AcceptsClicks) CheckDebugDrag();
			if (m_confirmedNotClick || !AcceptsClicks || UpdatesDuringClick)
				if (IsLeftButton)
				{
					OnDragUpdate(point, screenDragTotal);
					if (TouchManager.IsTouchCancelled((int)m_pointerDownButton) || Utility.IsMouseShaking((int)m_pointerDownButton) || EKeyboardFunction.Cancel.IsDown() || PlayerHandManager.Me.IsTrapped)
					{
						Cleanup(true);
					}
				}
			//            if (Input.GetKey(KeyCode.A) && m_attracted == false)
			//				AttractMore(point);
			//			if(m_attractBegin)
			//	            Capture(point);

			yield return null;
		}
		Cleanup(false);
	}


	public float ScreenThreshold(float _fraction) { return Screen.height * _fraction; }
	public float ScreenThresholdSqrd(float _fraction) { return ScreenThreshold(_fraction) * ScreenThreshold(_fraction); }

	void CreateBezier() {
		if (HasBezier) {
			m_line = Instantiate(GlobalData.Me.m_pickupLinePrefab);
			m_line.layer = GameManager.Me.IsDesignTable ? GameManager.c_layerDesignTable : GameManager.c_layerTerrain;
		}
	}
	void DoDragStart() {
		m_dragStarted = true;
		DragHolder.s_activeDrags[(int)m_pointerDownButton] = this;
		CreateBezier();
		OnDragStart();
	}

	protected void DisableLights()
	{
		foreach (var light in GetComponentsInChildren<Light>(true))
			light.gameObject.SetActive(false);
	}

	public Rigidbody GetRigidbody()
	{
		var rb = GetComponent<Rigidbody>();
		if (rb == null) rb = gameObject.AddComponent<Rigidbody>();
		rb.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
		return rb;
	}

	public void RemoveRigidbody()
	{
		var rb = GetComponent<Rigidbody>();
		if (rb != null) Destroy(rb);
	}

	public void TrackObject(NGMovingObject _obj) {
		if (_obj == null) return;
		m_hasTracked = true;
		PickupManager.Me.TrackObject(_obj, 1);
	}
	public void TrackTarget(GameObject _target) {
		PickupManager.Me.TrackTarget(_target);
	}
	void DoDragEnd(bool _undo, bool _allowOnDragEnd) {		
		m_dragging = false;
		ReleaseAttracted();
		if (GameManager.Me.IsOKToPlayUISound() && m_bezSound != 0)
        {
            AudioClipManager.Me.StopSound(m_bezSound, GameManager.Me.gameObject);
            m_bezSound = 0;
        }
		DestroyBezier();
		if (IsLeftButton && _allowOnDragEnd)
			OnDragEnd(_undo);
		if (m_hasTracked)
			PickupManager.Me.ClearTrackedObjects();
		m_hasTracked = false;
//		Utility.ResetMouseShake();
	}

	protected void DestroyBezier()
	{
		if (HasBezier && m_line != null)
		{
			Destroy(m_line);
			m_line = null;
		}
	}

	public void DisableBezier() {
		if (!HasBezier || m_line == null) return;
		m_line.SetActive(false);
	}
	private float m_bezierT = 0;
	public void EnableBezier(Vector3 _from, Vector3 _to) {
		if (!HasBezier || m_line == null) return;
		m_line.SetActive(true);
		m_line.GetComponent<BezierLine>().SetControlPoints(_from, _to, BezierShape);
		var lrnd = m_line.GetComponent<LineRenderer>();
		var length = (_from - _to).magnitude;
		float bezierSpeed = 1f / Mathf.Max(1, length * .02f);
		for (int i = 0; i < lrnd.materials.Length; ++i)
			lrnd.materials[i].mainTextureScale = new Vector2(length * .1f, 1);
		m_bezierT = Mathf.Repeat(m_bezierT + bezierSpeed * Time.deltaTime * 2.5f, 100.0f);
		UpdateBezier(lrnd, BezierColour, m_bezierT);
        if (GameManager.Me.IsOKToPlayUISound() && m_bezSound == 0)
            m_bezSound = AudioClipManager.Me.PlaySoundOld("PlaySound_BezierLine_Loop", GameManager.Me.transform);
		AudioClipManager.Me.SetBezierDistance(length);
	}
	public static void UpdateBezier(LineRenderer _r, Color _clr, float _t = -1) {
		AnimateBezier(_r, _t);
		SetBezierColour(_r, _clr);
	}
	public static void UpdateBezier(LineRenderer _r, Color _clr, Color _xray, float _t = -1) {
		AnimateBezier(_r, _t);
		SetBezierColour(_r, _clr);
		SetBezierXrayColour(_r, _xray);
	}
	public static void AnimateBezier(LineRenderer _r, float _t = -1) {
		if (_t < 0) _t = Mathf.Repeat(Time.time * 2.5f, 100);
		for (int i = 0; i < _r.materials.Length; ++i)
			_r.materials[i].mainTextureOffset = new Vector2(-_t, 0);
	}
	const float c_overbright = 4;
	public static void SetBezierColour(LineRenderer _r, Color _colour) {
		if (_colour.a > 0)
			_r.materials[_r.materials.Length - 1].SetColor("_Color", _colour * c_overbright);
	}
	public static void SetBezierXrayColour(LineRenderer _r, Color _colour) {
		//if (_r.materials.Length > 1)
		//	_r.materials[0].SetColor("_Color", _colour);
	}

	virtual public void OnPress() {}
	virtual public void OnDragStart() { }
	virtual public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) { }
	virtual public void OnDragEnd(bool _undo) {}
	virtual public void OnClick() {}
	
	virtual public bool ShouldHoldDrag => false;

	public static float PositionOnSlider(Ray _ray, Vector3 _origin, Vector3 _forward) {
		Vector3 ca, cb;
		Utility.LineSegmentMinimumDistanceSquared(_ray.origin, _ray.origin + _ray.direction * 1000,
			_origin - _forward * 500, _origin + _forward * 500, out ca, out cb);
		// convert cb to t
		return Vector3.Dot(ca - _origin, _forward);
	}
}
