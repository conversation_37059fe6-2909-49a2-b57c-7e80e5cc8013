using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MaterialSetColour : MonoBehaviour
{
    public float m_tintAmount = 0;
    public Color m_colour1;
    public Color m_colour2;
    public Color m_colour3;
    void Start()
    {
        Set();
    }

    void Set()
    {
        if (gameObject.scene.path.IsNullOrWhiteSpace()) // prefab
        {
            var r = GetComponentInChildren<Renderer>();
            MaterialPropertyBlock block = new MaterialPropertyBlock();
            if (r.HasPropertyBlock()) r.GetPropertyBlock(block);
            block.SetColor("_BaseColor", m_colour1);
            block.SetColor("_SpecColor", m_colour2);
            block.SetColor("_EmissionColor", m_colour3);
            block.SetFloat("_ClearCoatMask", m_tintAmount);
            r.SetPropertyBlock(block);
        }
        else
        {
            var r = GetComponentInChildren<Renderer>();
            r.material.SetColor("_BaseColor", m_colour1);
            r.material.SetColor("_SpecColor", m_colour2);
            r.material.SetColor("_EmissionColor", m_colour3);
            r.material.SetFloat("_ClearCoatMask", m_tintAmount);
        }
    }

    void OnValidate()
    {
        Set();
    }

    private static HashSet<GameObject> s_haveRandomised = new ();
    public static void RandomiseAllMaterials(GameObject _go)
    {
        return; //No longer using random tints
        if (s_haveRandomised.Add(_go) == false) return;
        
        var pos = _go.transform.position;
        var posSeed = ((int)pos.x) * 17177 + ((int)pos.z) * 31731;

        void TintMaterials(Renderer rnd, GlobalData.TintParameters parms)
        {
            var localPos = rnd.transform.position;
            var subSeed = ((int)localPos.x) * 11711 + ((int)localPos.z) * 33733 + rnd.transform.GetSiblingIndex() * 1717;
            foreach (var mat in rnd.materials)
            {
                if (mat.HasProperty("_G_Blend") == false) continue;
                var rBlend = mat.HasProperty("_R_ON_OFF") ? "_R_ON_OFF" : "_R_Blend";
                if (mat.GetFloat(rBlend) > .5f) continue; // something else tinted this, leave it alone
                var (c, a) = parms.Generate(posSeed, subSeed);
                mat.SetColor($"_R", c);
                mat.SetFloat(rBlend, a);
                /*mat.SetColor($"_G", c);
                mat.SetFloat("_G_Blend", a);
                mat.SetColor($"_B", c);
                mat.SetFloat("_B_Blend", a);*/
            }
        }

        foreach (var tint in _go.GetComponentsInChildren<MaterialTint>(true))
        {
            var parms = GlobalData.Me.GetTintParameters(tint.m_type);
            if (parms == null) continue;
            if (tint.m_applyToChildren)
            {
                foreach (var rnd in tint.GetComponentsInChildren<Renderer>(true))
                    TintMaterials(rnd, parms);
            }
            else
            {
                var rnd = tint.GetComponent<Renderer>();
                TintMaterials(rnd, parms);
            }
        }

        /*foreach (var rnd in _go.GetComponentsInChildren<Renderer>(true))
        {
            var tint = rnd.gameObject.GetComponentInParent<MaterialTint>();
            if (tint == null) continue;
            if (tint.m_applyToChildren == false && rnd.gameObject != tint.gameObject) continue;
            var parms = GlobalData.Me.GetTintParameters(tint.m_type);
            if (parms == null) continue;
            TintMaterials(rnd, parms);
        }*/
    }
}
