using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AlwaysFaceCamera : MonoBehaviour
{
	public bool m_lockTo2D = false;
	public bool m_freezeXZ = true;
	public float m_pushForward = 0;
	public float m_pushDown = 0;
	public bool m_isTopDown = false;
	private Transform m_transform;

	public void Start()
	{
		m_transform = transform;
		FaceCamera();
	}

	private void LateUpdate()
    {
		FaceCamera();
	}
	
	private void FaceCamera()
	{
		var cam = Camera.main; // can't cache this atm, HDRP often returns SceneCamera
		if (cam == null) return;
		FaceCamera(cam.transform.position, cam.transform.forward, cam.transform.up);
	}
	public void FaceCamera(Vector3 _pos, Vector3 _fwd, Vector3 _up)
	{
		if (m_transform == null) m_transform = transform;
		bool push = m_pushForward * m_pushForward > .001f * .001f; 
		if (push)
			m_transform.localPosition = Vector3.zero;
		
		var toCam = (_pos - m_transform.position).normalized;
		if (m_freezeXZ)
			toCam.y = 0f;

		if (push)
			m_transform.position = m_transform.position + (toCam * m_pushForward) + (Vector3.down*m_pushDown);

		if (m_isTopDown)
		{
			Vector3 cameraUp = _up;
			cameraUp.y = 0.0f;
			m_transform.rotation = Quaternion.LookRotation(-Vector3.up, cameraUp.normalized);
		}
		else
		{
			m_transform.forward = m_lockTo2D ? _fwd : -toCam;
		}
	}
}