using UnityEngine;

public class InteractionSequenceNode_Animate : InteractionSequenceNode_CharacterBase
{
    public string m_workerAnimation = "";
    public string m_heroAnimation = "";
    public string m_animalAnimation = "";
    public bool m_runInteract = false;
    public int m_characterIndex = 0;
    public bool m_isRequired = true;
    public bool m_waitForComplete = true;
    
    private bool m_finished = false;
    
    protected override void Begin()
    {
        if (Chr == null)
        {
            m_finished = true;
            return;
        }
        m_finished = false;
        var anim = "";
        if (Chr is MAWorker) anim = m_workerAnimation; 
        else if (Chr is MAHeroBase) anim = m_heroAnimation; 
        else if (Chr is MAAnimal) anim = m_animalAnimation;
        if (string.IsNullOrEmpty(anim) == false)
        {
            Chr.PlaySingleAnimation(anim, (b) => m_finished = true);
            if (m_runInteract)
                m_interact.DoInteract(Chr);
        }
        else
            m_finished = true;
        if (m_waitForComplete == false) m_root.AddPending(this);
    }
    
    protected override bool IsFinished => m_finished;

    protected override bool Tick() => m_waitForComplete == false || m_finished;
}
