using UnityEngine;

public class InteractionSequenceNode_Wait : InteractionSequenceNode
{
    protected override int CharacterIndex() => 0;
    protected override bool CharacterIndexRequired() => false;
    
    public float m_waitTime = 1f;
    private float m_endTime;
    protected override void Begin()
    {
        m_endTime = Time.time + m_waitTime;
    }

    protected override bool Tick() => Time.time >= m_endTime;
}
