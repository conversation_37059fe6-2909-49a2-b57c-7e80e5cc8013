using System.Collections.Generic;
using UnityEngine;

// Used to position a held item in the hand
public class HoldTransform : MonoBehaviour
{
    private static Dictionary<NGLegacyBase, HoldTransform> s_htLookup = new();
    
    public float m_gripWidth = 1;
    public float m_referenceScale = 1;

    void Start() => ChooseScale();

#if UNITY_EDITOR
    public bool m_refresh = false;
    public bool m_select = false;
    void OnValidate()
    {
        if (m_refresh || m_select)
        {
            if (m_select)
            {
                var dec = GetComponentInParent<NGLegacyBase>();
                if (dec != null)
                    dec.transform.localScale = Vector3.one * m_referenceScale;
            }
            m_refresh = false;
            m_select = false;
            ChooseScale(true);
        }
    }
#endif

    void ChooseScale(bool _force = false)
    {
        var dec = GetComponentInParent<NGLegacyBase>();
        if (dec == null) return;
        var baseScale = dec.transform.localScale.x;
        var hts = dec.GetComponentsInChildren<HoldTransform>();
        if (_force == false && hts[0] != this) return;
        // this is the first HoldTransform in the hierarchy, it can do the scale choice work
        float bestScaleSqrd = 1e23f;
        int bestHT = 0;
        for (int i = 0; i < hts.Length; i++)
        {
            var ht = hts[i];
            var dScale = ht.m_referenceScale - baseScale;
            var dScaleSqrd = dScale * dScale;
            if (dScaleSqrd < bestScaleSqrd)
            {
                bestScaleSqrd = dScaleSqrd;
                bestHT = i;
            }
        }
        var chosenHT = hts[bestHT];
        float fit = 1 - dec.transform.localScale.x / chosenHT.m_referenceScale;
        bool skipScale = false;
        if (fit * fit > .15f * .15f)
        {
            if (hts.Length == 1)
            {
                Debug.LogError($"Skipping single HoldTransform (scale {chosenHT.m_referenceScale}) for {dec.name} scale {dec.transform.localScale.x} with bad fit (fitness {1 - Mathf.Abs(fit)})", gameObject);
                skipScale = true;
            }
            else
                Debug.LogError($"Best HoldTransform (scale {chosenHT.m_referenceScale}) for {dec.name} scale {dec.transform.localScale.x} is not a good fit (fitness {1 - Mathf.Abs(fit)})", gameObject);
        }
        if (skipScale == false)
            dec.transform.localScale = Vector3.one * chosenHT.m_referenceScale;
        s_htLookup[dec] = chosenHT;
    }
    
    public static HoldTransform Get(GameObject _obj)
    {
        var dec = _obj.GetComponent<NGLegacyBase>();
        if (dec == null) return null;
        return s_htLookup.GetValueOrDefault(dec, null);
    }
}
