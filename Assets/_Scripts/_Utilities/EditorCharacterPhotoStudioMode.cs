using System;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering.HighDefinition;

/// <summary>
/// TS - Camera Studio mode is not for testing the game. Please restart Play-mode after using Camera Studio mode to
/// ensure you get the correct game-experience under any circumstances.
/// Also, this is called from MAParser and is used in MOA scripts. Needs to be part of non-editor assembly
/// </summary>
public class EditorCharacterPhotoStudioMode
{
#if UNITY_EDITOR
	private static LayerMask s_backUpCameraCullingMask = 0;
	private static LayerMask s_backUpVolumeLayerMask;
	private static HDAdditionalCameraData.ClearColorMode s_backUpClearColorMode;
	private static Color s_backUpBackgroundColorHDR;
#endif
	
#if UNITY_EDITOR
	public static bool EnableStudioModeValidate()
	{
		Camera captureCam = GameObject.Find("CaptureCamera").GetComponent<Camera>();
		return captureCam.enabled;
		return false;
	}
#else
	public static bool EnableStudioModeValidate() => false;
#endif

	
#if UNITY_EDITOR
	[MenuItem("22Cans/Misc/Toggle Character Photo Studio Mode", true)]
	public static bool ToggleStudioModeValidate()
	{
		bool isEnabled = EnableStudioModeValidate();
		Menu.SetChecked("22Cans/Toggle Character Photo Studio Mode", isEnabled);
		return GameManager.Me != null && GameManager.Me.LoadComplete;
	}
#else
	public static bool ToggleStudioModeValidate() => false;
#endif

#if UNITY_EDITOR
	[MenuItem("22Cans/Misc/Toggle Character Photo Studio Mode")]
	public static void ToggleStudioMode()
	{
		bool isRunning = EnableStudioModeValidate();
		if (!isRunning)
		{
			EnableStudioMode();
		}
		else
		{
			DisableStudioMode();
		}
	}
#else
	public static void ToggleStudioMode() { }
#endif
	
#if UNITY_EDITOR
	public static void EnableStudioMode()
	{
		// Camera.main.transform.GetPositionAndRotation(out var p, out var r);
		SetCharacterContentToLayer("Capture");
		Camera captureCam = GameObject.Find("PortraitCamera").GetComponent<Camera>();// Camera.main;
		// captureCam.transform.SetPositionAndRotation(p, r);
		captureCam.enabled = true;
		if (s_backUpCameraCullingMask == 0)
		{
			s_backUpCameraCullingMask = captureCam.cullingMask;
		}
		//Camera.main.enabled = false;

		var hdcd = captureCam.GetComponentInChildren<HDAdditionalCameraData>();
		s_backUpClearColorMode = hdcd.clearColorMode;
		s_backUpVolumeLayerMask = hdcd.volumeLayerMask;
	
		hdcd.clearColorMode = HDAdditionalCameraData.ClearColorMode.Color;
		hdcd.backgroundColorHDR = Color.black;
		hdcd.volumeLayerMask = 0;

		captureCam.LayerCullingHide(Int32.MaxValue);
		captureCam.LayerCullingShow("Capture");
		//captureCam.GetUniversalAdditionalCameraData().volumeLayerMask = 0;
		

		var canv = UIManager.Me.GetComponentInParent<Canvas>().rootCanvas;
		canv.enabled = false;
	}
#else
	public static void EnableStudioMode() { }
#endif

#if UNITY_EDITOR
	public static void DisableStudioMode()
	{
		SetCharacterContentToLayer("Default");
		Camera cam = GameObject.Find("PortraitCamera").GetComponent<Camera>();// Camera.main;
		cam.enabled = false;
		cam.cullingMask = s_backUpCameraCullingMask;
		s_backUpCameraCullingMask = 0;
    
		var hdcd = cam.GetComponentInChildren<HDAdditionalCameraData>();
		hdcd.clearColorMode = s_backUpClearColorMode;
		hdcd.backgroundColorHDR = s_backUpBackgroundColorHDR;
		hdcd.volumeLayerMask = s_backUpVolumeLayerMask;
    
		cam.LayerCullingHide(Int32.MaxValue);
		cam.LayerCullingShow(s_backUpCameraCullingMask);
		//cam.GetUniversalAdditionalCameraData().volumeLayerMask = 0;

		var canv = UIManager.Me.GetComponentInParent<Canvas>(true).rootCanvas;
		canv.enabled = true;
	}
#else
	public static void DisableStudioMode() { }
#endif
	
#if UNITY_EDITOR
	private static void SetCharacterContentToLayer(string _layerName)
	{
		var allFinds = GameObject.FindObjectsByType<MACharacterBase>(FindObjectsSortMode.None);
		foreach (MACharacterBase maCharacterBase in allFinds)
		{
			foreach (Transform componentsInChild in maCharacterBase.ContentRoot.GetComponentsInChildren<Transform>())
			{
				componentsInChild.gameObject.layer = LayerMask.NameToLayer(_layerName);
			}
		}
	}
#else
	private static void SetCharacterContentToLayer(string _layerName) { }
#endif
}
