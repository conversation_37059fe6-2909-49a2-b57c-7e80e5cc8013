using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CaptureObjectImage : MonoSingleton<CaptureObjectImage> {
	public enum Use {
		Avatar,
		Product,
		Building,
	};
	[System.Serializable]
	public class UseVariables {
		public string m_name;
		public float m_fieldOfView = 60;
		public float m_elevation = 10;
		public float m_rotation = 0;
		public UseVariables(string _name, float _fov, float _elevation, float _rotation) {
			m_name = _name;
			m_fieldOfView = _fov;
			m_elevation = _elevation;
			m_rotation = _rotation;
		}
	}
	public UseVariables[] m_useVariables = new UseVariables[] {
		new UseVariables("Avatar", 60, 10, 0),
		new UseVariables("Product", 60, 10, 0),
		new UseVariables("Building", 60, 10, 0),
	};
	public Camera m_captureCamera;
	public Sprite Capture(GameObject _root, Use _use, int _resolution = 256) {
		return Capture(_root, m_useVariables[(int)_use], _resolution);
	}

	const int c_renderLayerMask = 1 << 2;
	private void SetRendererDetails(GameObject o)
	{
		foreach (var renderer in o.GetComponentsInChildren<Renderer>())
			renderer.renderingLayerMask = c_renderLayerMask;
	}

	private void SetLightDetails(Light _light, float _boundsExtent)
	{
		bool isURP = GameSettings.SRPOptions.IsURP;
		_light.renderingLayerMask = c_renderLayerMask;
		var hdrpLight = _light.GetComponent<UnityEngine.Rendering.HighDefinition.HDAdditionalLightData>();
		var urpLight = _light.GetComponent<UnityEngine.Rendering.Universal.UniversalAdditionalLightData>();
		if (isURP == false)
			hdrpLight.lightlayersMask = (UnityEngine.Rendering.HighDefinition.RenderingLayerMask) c_renderLayerMask;
		else
			urpLight.renderingLayers = c_renderLayerMask;
		float intensityModifier = isURP ? 15 : 250;
		float finalIntensity = intensityModifier * _boundsExtent; 
		_light.intensity = finalIntensity;
		if (isURP == false)
			hdrpLight.SetIntensity(finalIntensity, UnityEngine.Rendering.LightUnit.Lumen);
		_light.gameObject.SetActive(true);
	}

	public Sprite Capture(GameObject _root, UseVariables _settings, int _resolution = 256) {
		var copy = Instantiate(_root);
		copy.transform.position = Vector3.zero;
		copy.transform.rotation = Quaternion.identity;
		Utility.SetGreyable(copy, false);
		copy.EnableKeywordRecursive("_CIRCLECLIP", false);
		var hinges = copy.GetComponentsInChildren<SnapHinge>();
		foreach (var h in hinges) h.gameObject.SetActive(false);
		var bounds = ManagedBlock.GetTotalVisualBounds(copy.gameObject);
		var boundsExtent = bounds.extents.magnitude;
		var localScale = boundsExtent > .1f ? 1.0f / boundsExtent : 1.0f / .1f;
		boundsExtent = 1;
		m_captureCamera.fieldOfView = _settings.m_fieldOfView;
		const float c_annahFudge = 1.1f;
		float distanceToCameraMultiplier = c_annahFudge * .866f / Mathf.Tan(_settings.m_fieldOfView * .5f * Mathf.Deg2Rad);
		var distanceToCamera = boundsExtent * distanceToCameraMultiplier;
		//Debug.LogError($"Capture at size {boundsExtent} distance {distanceToCamera}");
		var camXform = m_captureCamera.transform;
		copy.transform.SetParent(camXform, false);
		copy.transform.localRotation = Quaternion.Euler(-_settings.m_elevation, 0, 0) * Quaternion.Euler(0, -_settings.m_rotation, 0);
		copy.transform.localScale = Vector3.one * localScale;
		SetRendererDetails(copy);
		var centerOffset = copy.transform.TransformPoint(bounds.center);
		var camFwd = camXform.forward;
		copy.transform.position = camXform.position + camFwd * distanceToCamera - centerOffset;
		copy.SetLayerRecursively(m_captureCamera.gameObject.layer);
		var light = m_captureCamera.GetComponentInChildren<Light>(true);
		SetLightDetails(light, boundsExtent);
		var tmpRT = RenderTexture.GetTemporary(_resolution, _resolution, 32);
		m_captureCamera.targetTexture = tmpRT;
		m_captureCamera.Render();
		light.gameObject.SetActive(false);
		copy.SetActive(false);
		Destroy(copy);
		RenderTexture saveActive = RenderTexture.active;
		RenderTexture.active = m_captureCamera.targetTexture;
		int width = m_captureCamera.targetTexture.width;
		int height = m_captureCamera.targetTexture.height;
		Texture2D texture = new Texture2D(width, height);
		texture.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0);
		texture.Apply();
		RenderTexture.active = saveActive;
		m_captureCamera.targetTexture = null;
		RenderTexture.ReleaseTemporary(tmpRT);
		return Sprite.Create(texture, new Rect(0, 0, width, height), new Vector2(0.5f, 0.5f), 100.0f);
	}

	public Texture2D CaptureGeneric(Vector3 _pos, Vector3 _fwd, int _layers, int _width, int _height)
	{
		var tmpRT = RenderTexture.GetTemporary(_width, _height, 32);
		m_captureCamera.targetTexture = tmpRT;
		m_captureCamera.transform.position = _pos;
		m_captureCamera.transform.LookAt(_pos + _fwd, Vector3.up);
		var cullWas = m_captureCamera.cullingMask;
		m_captureCamera.cullingMask = _layers;
		var light = m_captureCamera.GetComponentInChildren<Light>(true);
		light.gameObject.SetActive(false);

		m_captureCamera.Render();
		
		m_captureCamera.cullingMask = cullWas;
		light.gameObject.SetActive(true);
		
		RenderTexture saveActive = RenderTexture.active;
		RenderTexture.active = m_captureCamera.targetTexture;
		Texture2D texture = new Texture2D(_width, _height);
		texture.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0);
		texture.Apply();
		RenderTexture.active = saveActive;
		m_captureCamera.targetTexture = null;
		RenderTexture.ReleaseTemporary(tmpRT);
		
		return texture;
	}
}
