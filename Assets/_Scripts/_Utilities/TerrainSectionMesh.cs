using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TerrainSectionMesh : MonoBehaviour
{
    public Vector3 m_size;
    public float m_raise;
    public float m_uvRotate = 0;
    public float m_uvScale = 1;
    public Material m_material;
    public bool m_addCollider = true;
    public bool m_castShadows = true;
    public float m_minRadius;
    public float m_maxRadius;
    public bool m_isFullCircle = true;
    
    MeshRenderer m_renderer;
    MeshFilter m_filter;
    MeshCollider m_collider;
    
    Vector3 m_meshPosition;
    Vector3 m_meshSize;
    float m_meshUVScale, m_meshUVRotate;

    void Start()
    {
        Initialise();
    }
    
    public void Initialise()
    {
        if (m_renderer != null) return;
        m_renderer = gameObject.AddComponent<MeshRenderer>();
        m_filter = gameObject.AddComponent<MeshFilter>();
        m_renderer.sharedMaterial = m_material;
        m_renderer.shadowCastingMode = m_castShadows ? UnityEngine.Rendering.ShadowCastingMode.On : UnityEngine.Rendering.ShadowCastingMode.Off;
        m_filter.sharedMesh = new Mesh();
        if (m_addCollider)
            m_collider = gameObject.AddComponent<MeshCollider>();
    }

    public void Update()
    {
        if(m_isFullCircle)
            UpdateMesh();
        else
            UpdateRingMesh();
    }

    public TerrainSectionMesh SetMaterial(Material _mat)
    {
        m_material = _mat;
        if (m_renderer != null)
            m_renderer.sharedMaterial = _mat;
        return this;
    }

    public TerrainSectionMesh SetSize(Vector3 _size)
    {
        m_size = _size;
        return this;
    }

    public TerrainSectionMesh SetRaise(float _raise)
    {
        m_raise = _raise;
        return this;
    }

    public TerrainSectionMesh SetMinMaxRadius(float _minRadius, float _maxRadius)
    {
        m_minRadius = _minRadius;
        m_maxRadius = _maxRadius;
        m_isFullCircle = _minRadius <= 0;
        return this;
    }
    
    public TerrainSectionMesh SetPosition(Vector3 _pos)
    {
        transform.position = _pos;
        return this;
    }

    void UpdateMesh()
    {
        if ((transform.position - m_meshPosition).xzSqrMagnitude() < .0001f*.0001f && 
            m_size.Approximately(m_meshSize) &&
            m_meshUVScale.Nearly(m_uvScale) && m_meshUVRotate.Nearly(m_uvRotate))
            return;
        
        m_meshPosition = transform.position;
        m_meshSize = m_size;
        m_meshUVScale = m_uvScale;
        m_meshUVRotate = m_uvRotate;
        
        var rot = m_uvRotate * Mathf.Deg2Rad;
        float sin = Mathf.Sin(rot) * m_uvScale, cos = Mathf.Cos(rot) * m_uvScale;
        float abssin = Mathf.Abs(sin), abscos = Mathf.Abs(cos);
        
        var finalSize = new Vector3(m_size.x * abscos + m_size.z * abssin, 0, m_size.x * abssin + m_size.z * abscos);
        Vector2 uVec = new Vector2(cos, -sin) / m_size.x, vVec = new Vector2(sin, cos) / m_size.z;
        
        int minX = Mathf.FloorToInt(m_meshPosition.x - finalSize.x * .5f), minZ = Mathf.FloorToInt(m_meshPosition.z - finalSize.z * .5f);
        int maxX = Mathf.CeilToInt(m_meshPosition.x + finalSize.x * .5f), maxZ = Mathf.CeilToInt(m_meshPosition.z + finalSize.z * .5f);
        var gd = GlobalData.Me;
        int w = maxX + 1 - minX, h = maxZ + 1 - minZ;
        var verts = new Vector3[w * h];
        var nrms = new Vector3[w * h];
        var uvs = new Vector2[w * h];
        int nextVert = 0;
        for (int z = minZ; z <= maxZ; ++z)
        {
            for (int x = minX; x <= maxX; ++x)
            {
                var pos = new Vector3(x - m_meshPosition.x, gd.GetRawHeight(x, z) + m_raise - m_meshPosition.y, z - m_meshPosition.z);
                verts[nextVert] = pos;
                nrms[nextVert] = Vector3.up;
                var uvPos = new Vector2(pos.x, pos.z);
                var uv = new Vector2(Vector2.Dot(uvPos, uVec) + .5f, Vector2.Dot(uvPos, vVec) + .5f);
                uvs[nextVert] = uv;
                ++nextVert;
            }
        }
        var inds = new int[(w - 1) * (h - 1) * 6];
        int nextInd = 0;
        for (int z = 0; z < h - 1; ++z)
        {
            for (int x = 0; x < w - 1; ++x)
            {
                inds[nextInd++] = (x + 0) + (z + 0) * w;
                inds[nextInd++] = (x + 1) + (z + 1) * w;
                inds[nextInd++] = (x + 1) + (z + 0) * w;
                inds[nextInd++] = (x + 0) + (z + 0) * w;
                inds[nextInd++] = (x + 0) + (z + 1) * w;
                inds[nextInd++] = (x + 1) + (z + 1) * w;
            }
        }
        var mesh = new Mesh();
        mesh.vertices = verts;
        mesh.normals = nrms;
        mesh.uv = uvs;
        mesh.SetIndices(inds, MeshTopology.Triangles, 0);
        mesh.UploadMeshData(false);
        m_filter.sharedMesh = mesh;
        if (m_addCollider)
            m_collider.sharedMesh = mesh;
    }
    
    void UpdateRingMesh()
    {
        if ((transform.position - m_meshPosition).xzSqrMagnitude() < .0001f*.0001f && 
            m_size.Approximately(m_meshSize) &&
            m_meshUVScale.Nearly(m_uvScale) && m_meshUVRotate.Nearly(m_uvRotate))
            return;
        
        m_meshPosition = transform.position;
        m_meshSize = m_size;
        m_meshUVScale = m_uvScale;
        m_meshUVRotate = m_uvRotate;
        
        var rot = m_uvRotate * Mathf.Deg2Rad;
        float sin = Mathf.Sin(rot) * m_uvScale, cos = Mathf.Cos(rot) * m_uvScale;
        float abssin = Mathf.Abs(sin), abscos = Mathf.Abs(cos);
        
        var finalSize = new Vector3(m_size.x * abscos + m_size.z * abssin, 0, m_size.x * abssin + m_size.z * abscos);
        Vector2 uVec = new Vector2(cos, -sin) / m_size.x, vVec = new Vector2(sin, cos) / m_size.z;
        
        int minX = Mathf.FloorToInt(m_meshPosition.x - finalSize.x * .5f), minZ = Mathf.FloorToInt(m_meshPosition.z - finalSize.z * .5f);
        int maxX = Mathf.CeilToInt(m_meshPosition.x + finalSize.x * .5f), maxZ = Mathf.CeilToInt(m_meshPosition.z + finalSize.z * .5f);
        var gd = GlobalData.Me;
        int w = maxX + 1 - minX, h = maxZ + 1 - minZ;
        var verts = new Vector3[w * h];
        var nrms = new Vector3[w * h];
        var uvs = new Vector2[w * h];
        int nextVert = 0;
        for (int z = minZ; z <= maxZ; ++z)
        {
            for (int x = minX; x <= maxX; ++x)
            {
                var pos = new Vector3(x - m_meshPosition.x, gd.GetRawHeight(x, z) + m_raise - m_meshPosition.y, z - m_meshPosition.z);
                verts[nextVert] = pos;
                nrms[nextVert] = Vector3.up;
                var uvPos = new Vector2(pos.x, pos.z);
                var uv = new Vector2(Vector2.Dot(uvPos, uVec) + .5f, Vector2.Dot(uvPos, vVec) + .5f);
                uvs[nextVert] = uv;
                ++nextVert;
            }
        }
        var inds = new int[(w - 1) * (h - 1) * 6];
        int nextInd = 0;
        for (int z = 0; z < h - 1; ++z)
        {
            for (int x = 0; x < w - 1; ++x)
            {
                int i00 = (x + 0) + (z + 0) * w;
                int i10 = (x + 1) + (z + 0) * w;
                int i01 = (x + 0) + (z + 1) * w;
                int i11 = (x + 1) + (z + 1) * w;

                Vector3 v00 = verts[i00];
                Vector3 v10 = verts[i10];
                Vector3 v01 = verts[i01];
                Vector3 v11 = verts[i11];

                float minRadiusSqr = m_minRadius * m_minRadius;

                // Helper function to check if vertex is inside inner hole
                bool InsideInnerHole(Vector3 v)
                {
                    float distSqr = v.x * v.x + v.z * v.z;
                    return distSqr < minRadiusSqr;
                }

                // Include the triangle only if at least one vertex is outside inner hole

                // First triangle (i00, i11, i10)
                if (!(InsideInnerHole(v00) && InsideInnerHole(v11) && InsideInnerHole(v10)))
                {
                    inds[nextInd++] = i00;
                    inds[nextInd++] = i11;
                    inds[nextInd++] = i10;
                }

                // Second triangle (i00, i01, i11)
                if (!(InsideInnerHole(v00) && InsideInnerHole(v01) && InsideInnerHole(v11)))
                {
                    inds[nextInd++] = i00;
                    inds[nextInd++] = i01;
                    inds[nextInd++] = i11;
                }
                
                /*// Check center position of quad to determine distance
                Vector3 quadCenter = (v00 + v11) * 0.5f;
                float distSqr = quadCenter.x * quadCenter.x + quadCenter.z * quadCenter.z;

                float minRadiusSqr = m_minRadius * m_minRadius;
                float maxRadiusSqr = m_maxRadius * m_maxRadius;

                if (distSqr >= minRadiusSqr && distSqr <= maxRadiusSqr)
                {
                    inds[nextInd++] = i00;
                    inds[nextInd++] = i11;
                    inds[nextInd++] = i10;

                    inds[nextInd++] = i00;
                    inds[nextInd++] = i01;
                    inds[nextInd++] = i11;
                }*/
            }
        }
        var mesh = new Mesh();
        mesh.vertices = verts;
        mesh.normals = nrms;
        mesh.uv = uvs;
        mesh.SetIndices(inds, MeshTopology.Triangles, 0);
        mesh.UploadMeshData(false);
        m_filter.sharedMesh = mesh;
        if (m_addCollider)
            m_collider.sharedMesh = mesh;
    }

    public static TerrainSectionMesh Create(string _name, Vector3 _position, Vector3 _size, float _raise, Material _mat)
    {
        var go = new GameObject(_name);
        return go.AddComponent<TerrainSectionMesh>().SetMaterial(_mat).SetSize(_size).SetRaise(_raise).SetPosition(_position);
    }
    
    public static TerrainSectionMesh Create(string _name, Vector3 _position, Vector3 _size, float _raise, Material _mat, float _minRadius, float _maxRadius)
    {
        var go = new GameObject(_name);
        return go.AddComponent<TerrainSectionMesh>().SetMaterial(_mat).SetSize(_size).SetRaise(_raise).SetPosition(_position).SetMinMaxRadius(_minRadius, _maxRadius);
    }
}
