using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SyncCamera : MonoBehaviour
{
    public Camera m_syncWith;
    private Camera m_camera;
    void Start()
    {
        m_camera = GetComponent<Camera>();
    }
    void Update()
    {
        m_camera.fieldOfView = m_syncWith.fieldOfView;
        m_camera.nearClipPlane = m_syncWith.nearClipPlane;
        m_camera.farClipPlane = m_syncWith.farClipPlane;
    }
}
