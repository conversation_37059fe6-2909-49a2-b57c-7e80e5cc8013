using System;
using UnityEngine;

public class MapUIController : <PERSON><PERSON><PERSON><PERSON><PERSON><MapUIController>
{
    public class KeyEntry
    {
        public string m_label;
        public Action<bool> m_set;
        public Func<bool> m_get;
    }

    public static bool s_factories = false;
    public static bool s_dispatches = false;
    public static bool s_taverns = false;
    public static bool s_defences = false;
    public static bool s_resources = false;
    public static bool s_quests = false;
    public static bool s_caves = false;

    void Start() => Show(false);
    
    private static void RefreshMap()
    {
    }

    public KeyEntry[] m_entries = {
        new () {m_label = "Factories", m_get = () => s_factories, m_set = _b => s_factories = _b },
        new () {m_label = "Dispatches", m_get = () => s_dispatches, m_set = _b => s_dispatches = _b},
        new () {m_label = "Taverns", m_get = () => s_taverns, m_set = _b => s_taverns = _b},
        new () {m_label = "Defences", m_get = () => s_defences, m_set = _b => s_defences = _b},
        new () {m_label = "Resources", m_get = () => s_resources, m_set = _b => s_resources = _b},
        new () {m_label = "Quests", m_get = () => s_quests, m_set = _b => s_quests = _b},
        new () {m_label = "Caves", m_get = () => s_caves, m_set = _b => s_caves = _b},
    };
    
    public void Show(bool _show)
    {
        gameObject.SetActive(_show);
        if (_show) RefreshEntries();
    }

    private void RefreshEntries()
    {
        transform.DestroyChildren(true, 2);
        var template = transform.GetChild(1);
        template.gameObject.SetActive(false);
        var templateRT = template.GetComponent<RectTransform>();
        for (int i = 0; i < m_entries.Length; ++i)
        {
            var entry = m_entries[i];
            var inst = Instantiate(template, transform);
            inst.gameObject.SetActive(true);
            inst.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, templateRT.anchoredPosition.y - i * 40);
            var text = inst.GetComponentInChildren<TMPro.TextMeshProUGUI>();
            var toggle = inst.GetComponentInChildren<UnityEngine.UI.Toggle>();
            text.text = entry.m_label;
            toggle.SetIsOnWithoutNotify(entry.m_get());
            toggle.onValueChanged.AddListener(_b => 
            {
                entry.m_set(_b);
                RefreshMap();
            });
        }
    }
}
