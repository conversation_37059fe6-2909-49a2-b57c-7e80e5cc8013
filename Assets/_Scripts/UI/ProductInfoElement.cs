using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class ProductInfoElement : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI m_productName, m_designs, m_sales, m_revenue;

    public void Populate(int _index)
    {
        /*string productLine = NGProductInfo.OwnedProductLines[_index];
        m_productName.text = productLine;
        int designCount = 0;
        int salesCount = 0;
        int revenueCount = 0;

        for (int i = 0; i < GameManager.Me.m_state.m_products.Count; i++)
        {
            GameState_Product product = GameManager.Me.m_state.m_products[i];

            if (product.m_productLine == productLine && product.Design != null)
            {
                designCount++;
                salesCount += product.Sales;
                revenueCount += product.Sales * (int)product.PricePerProduct;
            }
        }

        m_designs.text = designCount.ToString();
        m_sales.text = salesCount.ToString();
        m_revenue.text = "$" + revenueCount.ToString();*/
    }

}
