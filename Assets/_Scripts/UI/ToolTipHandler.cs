using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class TooltipHandler : MonoBehaviour
{
    private const float m_minDisplayTime = 0.1f;
    
    public TMP_Text m_context;
    
    private static Dictionary<string, string> s_toolTips = null;
    
    private static void CreateTooltips()
    {
        if(GameManager.Me.DataReady == false)
            return;
            
        s_toolTips = new Dictionary<string, string>();
        
        s_toolTips["MarketForce"] = $"Impacted by market forces!";
        
        var tmpSprite = MAMessageManager.GetWorkerIcon(MAMessageManager.ESlotState.Occupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Occupied <b>Workstation</b>";
        
        tmpSprite = MAMessageManager.GetWorkerIcon(MAMessageManager.ESlotState.Unoccupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Vacant <b>Workstation</b>";
        
        tmpSprite = MAMessageManager.GetBedroomIcon(MAMessageManager.ESlotState.Occupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Occupied worker <b>Bedroom</b>";
        
        tmpSprite = MAMessageManager.GetBedroomIcon(MAMessageManager.ESlotState.Unoccupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Vacant worker <b>Bedroom</b>";
        
        tmpSprite = MAMessageManager.GetHeroBedroomIcon(MAMessageManager.ESlotState.Occupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Occupied <b>Hero bedroom</b>";
        
        tmpSprite = MAMessageManager.GetHeroBedroomIcon(MAMessageManager.ESlotState.Unoccupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Vacant <b>Hero Bedroom</b>";
        
        tmpSprite = MAMessageManager.GetHeroIcon(MAMessageManager.ESlotState.Occupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Occupied <b>Hero slot<b>";
        
        tmpSprite = MAMessageManager.GetHeroIcon(MAMessageManager.ESlotState.Unoccupied, true);
        s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em>Vacant <b>Hero slot</b>";
        
        foreach(var res in NGCarriableResource.s_carriableResources.Values)
        {
            tmpSprite = res.SpriteIndex;
            if(res.TextSprite.IsNullOrWhiteSpace()) continue;
            s_toolTips[tmpSprite] = $"<size=200%><sprite index={tmpSprite}></size> <voffset=0.8em><b>{res.m_title}</b> {res.m_description}";
        }
    }
    
    void Start()
    {
        if(s_toolTips == null)
        {
            CreateTooltips();
        }
        
        if(m_context == null)
            m_context = GetComponent<TMP_Text>();
        
        if(m_context == null)
            enabled = false;   
    }
    
    void Update()
    {
        if(s_toolTips == null)
            return;
        int linkIndex = TMP_TextUtilities.FindIntersectingLink(m_context, Input.mousePosition, null);

        if (linkIndex != -1)
        {
            TMP_LinkInfo linkInfo = m_context.textInfo.linkInfo[linkIndex];
            
            string linkID = linkInfo.GetLinkID();
            
            if(s_toolTips.TryGetValue(linkID, out var value))
            {
                ShowMessage(linkInfo, value);
            }
            else if(linkID.StartsWith("Lock:"))
            {
                var parts = linkID.Split("Lock:");
                if(parts.Length > 1)
                {
                    ShowMessage(linkInfo, parts[1]);
                }
            }
        }
    }
    
    private void ShowMessage(TMP_LinkInfo _linkInfo, string _msgText)
    {
        var msg = UIInfoMessage.Create(UIInfoMessage.Location.Hand, gameObject, _msgText, null, null, m_minDisplayTime, 100f);
        if(msg != null)
        {
            msg.transform.parent = PlayerHandManager.Me.m_infoMessageHolder.transform.parent;
            msg.transform.position = CalcLinkCenterPosition(m_context.textInfo, _linkInfo) + Vector2.up * 20f;
            msg.ClampToCanvas();
        }
    }
    
    Vector2 CalcLinkCenterPosition(TMP_TextInfo _textInfo, TMP_LinkInfo _linkInfo)
    {
        Transform m_Transform = gameObject.GetComponent<Transform>();

        Vector3 bottomLeft = Vector3.zero;
        Vector3 topRight = Vector3.zero;

        float maxAscender = -Mathf.Infinity;
        float minDescender = Mathf.Infinity;
        
        TMP_CharacterInfo currentCharInfo = _textInfo.characterInfo[_linkInfo.linkTextfirstCharacterIndex];

        maxAscender = Mathf.Max(maxAscender, currentCharInfo.ascender);
        minDescender = Mathf.Min(minDescender, currentCharInfo.descender);

        bottomLeft = new Vector3(currentCharInfo.bottomLeft.x, currentCharInfo.descender, 0);

        bottomLeft = m_Transform.TransformPoint(new Vector3(bottomLeft.x, minDescender, 0));
        topRight = m_Transform.TransformPoint(new Vector3(currentCharInfo.topRight.x, maxAscender, 0));

        float width = topRight.x - bottomLeft.x;
        float height = topRight.y - bottomLeft.y;

        Vector2 centerPosition = bottomLeft;
        centerPosition.x += width / 2;
        centerPosition.y += height / 2;

        return centerPosition;
    }
}
