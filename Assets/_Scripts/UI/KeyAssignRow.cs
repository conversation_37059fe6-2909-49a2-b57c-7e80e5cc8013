using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class KeyAssignRow : MonoBehaviour
{
    public TMPro.TextMeshProUGUI m_label;
    public Button[] m_keyButtons;
    public GameObject m_setKeyObject;
    public TMPro.TextMeshProUGUI m_setKeyLabel;
    public Image m_background;
    public Color m_backgroundEven = new Color(0.8f, 0.8f, 0.8f, .15f);
    public Color m_backgroundOdd = new Color(0.8f, 0.8f, 0.8f, .05f);
    public Color m_titleColour = new Color(0, 0, .5f);
    public TMPro.FontWeight m_titleWeight = TMPro.FontWeight.Bold;
    public Color m_entryColour = new Color(.3f, .1f, 0);
    public TMPro.FontWeight m_entryWeight = TMPro.FontWeight.Regular;
    private KeyboardController.KeyAssignEntry m_entry;
    private KeyAssignController m_controller;

    public void Set(KeyboardController.KeyAssignEntry _entry, KeyAssignController _controller, ref bool _isEven)
    {
        m_entry = _entry;
        m_controller = _controller;
        Refresh();
        if ((m_entry.m_defaults?.Length ?? 0) > 0)
        {
            SetRowColour(_isEven);
            _isEven = !_isEven;
        }
        else
        {
            HideRowColour();
            _isEven = true;
        }
    }
    private void Refresh()
    {
        m_label.text = m_entry.m_label;
        for (int i = 0; i < m_entry.m_keys.Length; ++i)
        {
            m_keyButtons[i].GetComponentInChildren<TMPro.TextMeshProUGUI>().text = m_entry.GetString(i, true);
            m_keyButtons[i].gameObject.SetActive(true);
        }
        for (int i = m_entry.m_keys.Length; i < m_keyButtons.Length; ++i)
            m_keyButtons[i].gameObject.SetActive(false);

        if (m_entry.m_keys.Length == 0)
        {
            m_label.color = m_titleColour;
            m_label.fontWeight = m_titleWeight;
            m_label.fontStyle = FontStyles.SmallCaps;
        }
        else
        {
            m_label.color = m_entryColour;
            m_label.fontWeight = m_entryWeight;
            m_label.fontStyle = 0;
        }
    }

    private void SetRowColour(bool _isEven)
    {
        m_background.enabled = true;
        m_background.color = _isEven ? m_backgroundEven : m_backgroundOdd;
    }
    private void HideRowColour()
    {
        m_background.enabled = false;
    }

    public void OnKeyClick(int _index)
    {
        m_setKeyObject.SetActive(true);
        m_setKeyObject.GetComponent<Image>().StartCoroutine(Co_SetKey(_index));
    }

    private IEnumerator Co_SetKey(int _index)
    {
        var extraDetail = m_entry.m_keyLabels != null && _index < m_entry.m_keyLabels.Length ? m_entry.m_keyLabels[_index] : "";
        m_setKeyLabel.text = $"{m_entry.m_label} {extraDetail}";
        while (true)
        {
            if (Input.anyKeyDown)
            {
                foreach (KeyCode kcode in System.Enum.GetValues(typeof(KeyCode)))
                {
                    if (kcode == KeyCode.Mouse0) continue;
                    if (Input.GetKeyDown(kcode))
                    {
                        m_setKeyObject.SetActive(false);
                        m_entry.SetKey(_index, kcode);
                        m_controller.Refresh();
                        yield break;
                    }
                }
            }
            yield return null;
        }
    }
}
