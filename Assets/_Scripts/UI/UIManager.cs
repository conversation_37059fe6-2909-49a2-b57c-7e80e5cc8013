//#define USE_GAMEOBJECT_DISABLE

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class UIManager : MonoSingleton<UIManager> {
	public GameObject[] m_townUI;
	public GameObject[] m_townAndTutorialUI;
	public GameObject[] m_designUI;
	public GameObject[] m_designInPlaceUI;
	public GameObject[] m_designRoadsUI;
	public GameObject[] m_roadUI;
	public GameObject[] m_visitUI;
	public GameObject[] m_subSceneUI;
	public GameObject[] m_mapOverlayUI;
	public GameObject[] m_productTestUI;
	public GameObject[] m_possessingUI;
	public GameObject[] m_arcadiumUI;
	public GameObject[] m_failScreenUI;
	public GameObject[] m_messageUI;
	public GameObject[] m_smallButtonsUI;
	public GameObject[] m_trappedHandUI;
	public MATag[] m_tagUI;
	public GameObject m_tagPanel;
	public GameObject m_centralInfoPanelUI;
	public MAInfoPanelManager m_centralInfoPanelManager;
	public float m_currencyForceVisibleOverride;
	public CanvasGroup m_possessionInteractionUI;
	public GameObject m_currencyHolder;
	public Image m_dragReticule;
	public Transform m_failUI;
	public Transform m_failTasksUI;
	public TMPro.TextMeshProUGUI m_currentSlotInfo;
	public GameObject m_interactionBlocker;
	
	public Transform m_districtEditUI;
	public Button m_districtEditClose;
	public TMPro.TextMeshProUGUI m_districtEditID;
	public TMPro.TMP_InputField m_districtEditName;
	public TMPro.TMP_InputField m_districtEditAudioName;
	public Button m_districtEditDelete;
	public Toggle m_districtEditHideVisuals;
	public Toggle m_districtEditOverrideUnlocks;
	public Toggle m_districtEditOverridesDistricts;
	public Toggle m_districtEditHideAudioOverrides;
	public Transform m_districtEditOverrideUnlocksUI;
	public Button m_districtUndo;
	public Button m_districtRedo;
	public Transform m_districtDeleteUI;
	public Button m_districtDeleteNo;
	public Button m_districtDeleteYes;
	public Button m_userRoadEditUndoButton;
	public Button m_userRoadEditRedoButton;
	
	public bool m_hideTownUI = false;

	public Transform m_gestureQueue;
	
	public Image m_titleScreenOverlay;
	public GameObject m_titleScreenEndClouds;
	
	public Image m_dayNightSprite;
	
	public Image m_helmetLine;

	private float m_reticuleAlpha = 0, m_reticuleAlphaTarget = 0;
	private float m_reticuleRaiseFraction = .1f;
	
	bool m_inhibitUI = false;
	
	bool[] m_currentStates = new bool[] { true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true };
	GameManager.ECameraZoomState m_lastZoomState;

	Dictionary<GameObject, bool> m_newStates = new Dictionary<GameObject, bool>();
	void EnableObjects(GameObject[] _objs, bool _enable, ref bool _current, bool _allowTutorialDisable = false) {
		if (_allowTutorialDisable && !HUDVisibilityControl.Tutorial_IsVisible(HUDVisibilityControl.HUDElementType.All))
			_enable = false;
		else if (GameManager.Me.FreeCameraMode)
			_enable = false;
		foreach (var o in _objs) {
			if (!m_newStates.TryGetValue(o, out bool _alreadyOn) || !_alreadyOn) m_newStates[o] = _enable;
		}
		_current = _enable;
	}

	public void ShowCurrency()
	{
		m_currencyForceVisibleOverride = 5f;
	}
	
	private void UpdateCurrencyHudVisibility()
	{
		bool active = false;
		if(GameManager.Me.IsPossessing == false || m_currencyForceVisibleOverride > 0)
		{
			active = true;
			m_currencyForceVisibleOverride -= Time.deltaTime;
		}
		
		if(m_currencyHolder.gameObject.activeSelf != active)
			m_currencyHolder.gameObject.SetActive(active);
	}
	
    private void UpdateBusinessObjectiveVisibility(bool _enable)
    {
   //     foreach(Transform child in m_businessObjectiveUI.transform)
   //     {
   //         NGBusinessObjective mgr = child.gameObject.GetComponent<NGBusinessObjective>();
   //         if(mgr != null)
   //         {
   //             mgr.HideUI(_enable);
   //         }
   //     }
    }

    public void InhibitUI(bool _inhibit) => m_inhibitUI = _inhibit;
    static DebugConsole.Command s_inhibitUICommand = new DebugConsole.Command("noui", _s => Utility.SetOrToggle(ref Me.m_inhibitUI, _s));

    protected override void _Awake()
    {
	    base._Awake();
	    m_centralInfoPanelManager = m_centralInfoPanelUI.GetComponentInChildren<MAInfoPanelManager>(true);
    }

    void Start()
    {
	    DesignTableManager.Me.MoveDesignInPlace3DElements();
	    DesignTableManager.Me.EnableDesignInPlaceUI(false);
    }

	void Update() {
		m_newStates.Clear();
		bool isMessagesOnly = MAQuestCutscene.s_isAnyActive || PlayerHandManager.Me.IsTrapped;
		bool isTown = !MAResearchManagerUI.m_isActive && GameManager.Me.IsCountryside && !GameManager.Me.IsRoadEdit &&
		    !GameManager.Me.IsDesignTable && !GameManager.Me.IsPossessing && !m_hideTownUI &&
		    GameManager.Me.IsInTitleScreen() == false && FailSequenceController.Me.IsActive == false && !isMessagesOnly;
		bool isTownManagment = GameManager.Me.CameraZoomState == GameManager.ECameraZoomState.TownManagement;
		bool isMapOverlay= GameManager.Me.CameraZoomState == GameManager.ECameraZoomState.MapOverlay;
		bool isSubScene = GameManager.Me.IsCloseableSubScene;
		bool isInDesign = !MAResearchManagerUI.m_isActive && GameManager.Me.IsDesignTable;
		bool isInDesignInPlace = DesignTableManager.Me.IsInDesignInPlaceActively || (DesignTableManager.Me.m_isInDesignGlobally && DesignTableManager.Me.m_designSingly == false);
		bool isInDesignRoads = RoadManager.Me.RoadBuildMode;
		bool isFail = FailSequenceController.Me.IsActive && GameManager.Me.IsInTitleScreen() == false;
		bool isMessage = isTown || GameManager.Me.IsPossessing || isMessagesOnly;
		bool isSmallButtons = !isMessagesOnly;
		bool isTrappedHandUI = PlayerHandManager.Me.IsTrapped && !GameManager.Me.IsPossessing;
		if (m_inhibitUI || GameManager.Me.IsInTitleScreen())
			isTown = isTownManagment = isMapOverlay = isSubScene = isInDesign = isInDesignInPlace = false;
		int stateIndex = 0;
		EnableObjects(m_townUI, isTown && !isSubScene && !GameManager.IsVisiting && !isTownManagment && !isMapOverlay, ref m_currentStates[stateIndex++]);
		EnableObjects(m_townAndTutorialUI, isTown && !isSubScene && !GameManager.IsVisiting && !isTownManagment && !isMapOverlay, ref m_currentStates[stateIndex++]);
		EnableObjects(m_designUI, isInDesign, ref m_currentStates[stateIndex++]);
		EnableObjects(m_designInPlaceUI, isInDesignInPlace, ref m_currentStates[stateIndex++]);
		EnableObjects(m_designRoadsUI, isInDesignRoads, ref m_currentStates[stateIndex++]);
		EnableObjects(m_roadUI, RoadManager.Me.InDebugEdit, ref m_currentStates[stateIndex++]);
		EnableObjects(m_visitUI, isTown && GameManager.IsVisiting, ref m_currentStates[stateIndex++]);
		EnableObjects(m_subSceneUI, isSubScene, ref m_currentStates[stateIndex++]);
		//EnableObjects(m_townManagementUI, isTown && !GameManager.IsVisiting && !NGTradingHouse.IsGUIActive() && !NGResearchLab.IsGUIActive() && !NGTutorialManager.Me.IsInBoardroom && isTownManagment, ref m_currentStates[stateIndex++]);
        EnableObjects(m_mapOverlayUI, isTown && !GameManager.IsVisiting && !isTownManagment && isMapOverlay, ref m_currentStates[stateIndex++]);
        EnableObjects(m_productTestUI, GameManager.Me.IsProductTestingScene, ref m_currentStates[stateIndex++]);
        EnableObjects(m_possessingUI, GameManager.Me.IsPossessing, ref m_currentStates[stateIndex++]);
		EnableObjects(m_arcadiumUI, MAResearchManagerUI.m_isActive, ref m_currentStates[stateIndex++]);
		EnableObjects(m_failScreenUI, isFail, ref m_currentStates[stateIndex++]);
		EnableObjects(m_messageUI, isMessage, ref m_currentStates[stateIndex++]);
		EnableObjects(m_smallButtonsUI, isSmallButtons, ref m_currentStates[stateIndex++]);
		EnableObjects(m_trappedHandUI, isTrappedHandUI, ref m_currentStates[stateIndex++]);
		UpdateBusinessObjectiveVisibility(GameManager.Me.IsDesignTable);
		UpdateCurrencyHudVisibility();
		
		foreach (var kvp in m_newStates)
		{
#if USE_GAMEOBJECT_DISABLE
			if (kvp.Key.activeSelf != kvp.Value)
				kvp.Key.SetActive(kvp.Value);
#else
			var canvas = kvp.Key.GetComponent<Canvas>();
			var caster = kvp.Key.GetComponent<GraphicRaycaster>();
			if (canvas == null)
				canvas = kvp.Key.AddComponent<Canvas>();
			if (caster == null)
				caster = kvp.Key.AddComponent<GraphicRaycaster>();
			
			if (canvas.enabled != kvp.Value)
			{
				canvas.enabled = kvp.Value;
				caster.enabled = kvp.Value;
			}
#endif
		}
		if(m_lastZoomState != GameManager.Me.CameraZoomState)
        {
			//DistrictUnlockUIController.Close();
		}
		m_lastZoomState = GameManager.Me.CameraZoomState;
		
		UpdateReticule();
	}

	public void ShowReticule(bool _show, float _raise = 1e23f)
	{
		m_reticuleAlphaTarget = _show ? 1 : 0;
		if (_raise < 1e22f)
			m_reticuleRaiseFraction = _raise;
	}
	private void UpdateReticule()
	{
		m_reticuleAlpha = Mathf.Lerp(m_reticuleAlpha, m_reticuleAlphaTarget, .1f);
		m_dragReticule.color = new Color(1, 1, 1, m_reticuleAlpha);
		m_dragReticule.transform.position = Utility.mousePosition + Vector3.up * (m_reticuleRaiseFraction * Screen.height);
	}
}

public class FullScreenUIController : BaseUIController, IStandardClickHandler {
	[SerializeField]
	[Range (0f, 10f)]
	private float m_interactionDelay = 0.7f;
	float m_timer = 0f;
	public void OnStandardClick (PointerEventData eventData) {
		if (m_timer >= m_interactionDelay) {
			Close ();
		}
	}
	protected override void Update () {
		if (Input.GetMouseButtonDown(0)) OnStandardClick(null);
		m_timer += Time.deltaTime;
		base.Update ();
	}
	public void OnOffClickEvent () {
		if (m_timer >= m_interactionDelay) {
			Close ();
		}
	}
}

public static class HUDVisibilityControl {
	[System.Flags]
	public enum HUDElementType {
		None = 0,
		SalesDesigns = 1 << 0,
		HooverSales = 1 << 1,
		PlayerIDAvatar = 1 << 2,
		Money = 1 << 3,
		Gold = 1 << 4,
		IdeaCurrency = 1 << 5,
		SATTownSatisfaction = 1 << 6,
		ToDo = 1 << 7,
		TicketsCurrency = 1 << 8,
		RoyalFavours = 1 << 9,
		LordsFavours = 1 << 10,
		CommonerFavours = 1 << 11,
		MysticFavours = 1 << 12,
		NullOrLength = 1 << 21,
		All = NullOrLength - 1,
	}
	static bool m_isTutorialOverrideInProgress;
	static HUDElementType m_hiddenByTutorial = 0;	public static HUDElementType GetHiddenByTutorial() { return m_hiddenByTutorial; }
	public static void Tutorial_ShowHUDElement(HUDElementType _elementIndex) {
		m_hiddenByTutorial = (m_hiddenByTutorial & ~_elementIndex);
	}
	public static void Tutorial_HideHUDElement(HUDElementType _elementIndex) {
		m_hiddenByTutorial = (m_hiddenByTutorial | _elementIndex);
	}
	public static void Tutorial_HideAllHUD(bool _visibility) { m_isTutorialOverrideInProgress = _visibility; if (_visibility) { m_hiddenByTutorial = HUDElementType.All; } else { m_hiddenByTutorial = HUDElementType.None; }  UpdateElements(); }
	public static void OnTabVisibilityChange (HUDElementType _type, bool _isNowVisibile) {}
	public static void SetTutorialCompleteFlag(bool _complete) {}
	static void UpdateElements () {}
	public static bool Tutorial_IsVisible (HUDElementType _type) {
		if (_type == HUDElementType.All)
			return m_hiddenByTutorial != HUDElementType.All;

		int bit = (int)_type;
		return ((int)m_hiddenByTutorial & bit) != bit;
	}
}

public class ToDoHUDElement : HUDElement<ToDoHUDElement> {
	public override HUDVisibilityControl.HUDElementType Type => HUDVisibilityControl.HUDElementType.ToDo;
	public enum ToDoType {
		AwardsCeremony,
		UnlockDistrict,
		BuildingSite,
	}
	public static bool TryResolveToDo(ToDoType _type) { return false; }
	public GameObject GetIndicator() { return gameObject; }
}
