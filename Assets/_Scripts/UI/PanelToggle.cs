using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class PanelToggle : MonoBehaviour
{
    public GameObject m_target;
    private Toggle m_controllingToggle;
    public Toggle Control => m_controllingToggle;
    void Awake()
    {
        m_controllingToggle = GetComponent<Toggle>();
        
        Update();
    }
    
    public void SetWithoutNotify(bool _on)
    {
        Control.SetIsOnWithoutNotify(_on);
        m_target.SetActive(_on);
    }

    public void Update()
    {
        bool wantActive = m_controllingToggle.isOn;
        bool isActive = m_target.activeSelf;
        if (wantActive != isActive)
        {
            m_target.SetActive(wantActive);

            if (wantActive)
            {
                AudioClipManager.Me.PlayUISound("PlaySound_PauseMenu_CATEGORY");
            }
        }
    }
}
