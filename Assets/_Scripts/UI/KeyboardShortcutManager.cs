using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class KeyboardShortcutManager : Mono<PERSON>ingleton<KeyboardShortcutManager>
{
    public class KeyboardShortcut
    {
        public string m_groupTitle;
        public string m_description;
        public KeyCode[] m_keys;
        public EKeyboardFunction m_function;
        public System.Func<bool> m_predicate;
        public System.Func<bool> m_highlighted;
        public System.Action m_onClick;
        public System.Func<string> m_dynamicTitle;

        public KeyboardShortcut(string _groupTitle, string _description, System.Func<bool> _predicate, System.Func<bool> _highlighted, System.Action _onClick = null, System.Func<string> _dynamicTitle = null, params KeyCode[] _keys)
        {
            m_groupTitle = _groupTitle;
            m_description = _description;
            m_keys = _keys;
            m_predicate = _predicate;
            m_highlighted = _highlighted;
            m_onClick = _onClick;
            m_dynamicTitle = _dynamicTitle;
        }

        public KeyboardShortcut(string _groupTitle, string _description, System.Func<bool> _predicate, System.Func<bool> _highlighted, System.Action _onClick = null, System.Func<string> _dynamicTitle = null, EKeyboardFunction _fn = EKeyboardFunction.None)
        {
            m_groupTitle = _groupTitle;
            m_description = _description;
            m_function = _fn;
            m_predicate = _predicate;
            m_highlighted = _highlighted;
            m_onClick = _onClick;
            m_dynamicTitle = _dynamicTitle;
        }
        
        public KeyboardShortcut(string _groupTitle, string _description, params KeyCode[] _keys)
        {
            m_groupTitle = _groupTitle;
            m_description = _description;
            m_keys = _keys;
            m_predicate = null;
            m_highlighted = null;
            m_onClick = null;
            m_dynamicTitle = null;
        }

        public KeyboardShortcut(string _groupTitle, string _description, EKeyboardFunction _fn)
        {
            m_groupTitle = _groupTitle;
            m_description = _description;
            m_function = _fn;
            m_predicate = null;
            m_highlighted = null;
            m_onClick = null;
            m_dynamicTitle = null;
        }

        public KeyboardShortcut(string _title, KeyCode _key)
        {
            m_groupTitle = _title;
            m_description = "";
            m_keys = new[] {_key};
            m_predicate = null;
            m_highlighted = null;
            m_onClick = null;
            m_dynamicTitle = null;
        }
    }

    public int AddForcedShortcut(EShortcutType _type, KeyboardShortcut _shortcut)
    {
        if (m_shortcutLookup.TryGetValue(_type, out var shortcuts) == false) return -1;
        return AddForcedShortcut(shortcuts, _shortcut);
    }
    public int AddForcedShortcut(KeyboardShortcut _shortcut)
    {
        if (m_shortcutStack.Count == 0) return -1;
        var current = m_shortcutStack[m_shortcutStack.Count - 1];
        return AddForcedShortcut(current, _shortcut);
    }
    public int AddForcedShortcut(string _title, KeyCode _key)
    {
        return AddForcedShortcut(new KeyboardShortcut(_title, _key));
    }

    public void RemoveForcedShortcut(EShortcutType _type, int _id)
    {
        if (m_shortcutLookup.TryGetValue(_type, out var shortcuts) == false) return;
        RemoveForcedShortcut(shortcuts, _id);
    }
    public void RemoveForcedShortcut(EShortcutType _type, string _title, KeyCode _key)
    {
        if (m_shortcutLookup.TryGetValue(_type, out var shortcuts) == false) return;
        RemoveForcedShortcut(shortcuts, _title, _key);        
    }
    public void RemoveForcedShortcut(int _id)
    {
        if (m_shortcutStack.Count == 0) return;
        var current = m_shortcutStack[m_shortcutStack.Count - 1];
        RemoveForcedShortcut(current, _id);
    }
    public void RemoveForcedShortcut(string _title, KeyCode _key)
    {
        if (m_shortcutStack.Count == 0) return;
        var current = m_shortcutStack[m_shortcutStack.Count - 1];
        RemoveForcedShortcut(current, _title, _key);
    }

    private int AddForcedShortcut(ShortcutSet _shortcuts, KeyboardShortcut _shortcut)
    {
        _shortcuts.m_forcedShortcuts[++_shortcuts.m_nextForcedShortcutIndex] = _shortcut;
        Refresh();
        return _shortcuts.m_nextForcedShortcutIndex;
    }
    private void RemoveForcedShortcut(ShortcutSet _shortcuts, string _title, KeyCode _key)
    {
        int found = -1;
        foreach (var kvp in _shortcuts.m_forcedShortcuts)
        {
            if (kvp.Value.m_groupTitle == _title && kvp.Value.m_keys.Length == 1 && kvp.Value.m_keys[0] == _key)
            {
                found = kvp.Key;
                break;
            }
        }
        RemoveForcedShortcut(_shortcuts, found);
    }
    private void RemoveForcedShortcut(ShortcutSet _shortcuts, int _id)
    {
        if (_id == -1) return;
        _shortcuts.m_forcedShortcuts.Remove(_id);
        Refresh();
    }

    private static DebugConsole.Command s_testforcedcmd = new ("forcekey", _s => Debug.LogError($"Force ID: {Me.AddForcedShortcut("Test H", KeyCode.H)}"));
    private static DebugConsole.Command s_testunforcedcmd = new("unforcekey", _s => { if (string.IsNullOrEmpty(_s)) Me.RemoveForcedShortcut("Test H", KeyCode.H); else Me.RemoveForcedShortcut(int.Parse(_s)); });

    public Transform m_shortcutHolder;
    public Sprite m_leftArrowSprite;
    public float m_leftArrowSpriteRotate;
    public Sprite m_rightArrowSprite;
    public float m_rightArrowSpriteRotate;
    public Sprite m_upArrowSprite;
    public float m_upArrowSpriteRotate;
    public Sprite m_downArrowSprite;
    public float m_downArrowSpriteRotate;
    public Sprite m_leftMouseButtonSprite;
    public Sprite m_rightMouseButtonSprite;

    private float m_state = 0, m_stateTarget = 0;
    private ShortcutSet m_nextShortcuts = null;
    
    Dictionary<KeyCode, string> m_keyToString = new () {
        {KeyCode.Escape, "Esc"},
        {KeyCode.LeftShift, "Shift"},
        {KeyCode.RightShift, "RShift"},
        {KeyCode.LeftAlt, "Alt"},
        {KeyCode.RightAlt, "RAlt"},
        {KeyCode.LeftControl, "Ctrl"},
        {KeyCode.RightControl, "RCtrl"},
        {KeyCode.LeftMeta, "Cmd"},
        {KeyCode.RightMeta, "RCmd"},
        {KeyCode.Mouse0, "LMB"},
        {KeyCode.Mouse1, "RMB"},
        {KeyCode.Mouse2, "MMB"},
        {KeyCode.Mouse3, "MB4"},
        {KeyCode.Mouse4, "MB5"},
        {KeyCode.Mouse5, "MB6"},
        {KeyCode.Equals, "="},
        {KeyCode.Minus, "-"},
        {KeyCode.KeypadMinus, "-"},
        {KeyCode.Plus, "+"},
        {KeyCode.KeypadPlus, "+"},
    };
    Dictionary<KeyCode, (Sprite, float)> m_keyToSprite;

    public enum EShortcutType
    {
        Disable,
        DesignGlobally,
        DesignFocused,
        Design,
        DragBlock,
        ProductTesting,
        Town,
        DragWorker,
        Possess,
        PossessTurret,
        EnablePatrolAreaSelection,
        DisableOrAdjustPatrolAreaSelection,
        DistrictEdit,
        HeldObjects,
        BuildingPlacement,
        FailedDay,
    }
    public class ShortcutSet
    {
        public KeyboardShortcut[] m_shortcuts;
        public Dictionary<int, KeyboardShortcut> m_forcedShortcuts = new();
        public int m_nextForcedShortcutIndex = 0;
        public ShortcutSet(KeyboardShortcut[] _shortcuts)
        {
            m_shortcuts = _shortcuts;
        }
    }
    private Dictionary<EShortcutType, ShortcutSet> m_shortcutLookup = new()
    {
        {
            EShortcutType.Disable,
            new(new KeyboardShortcut[]
            {
            })
        },
        {
            EShortcutType.DragBlock,
            new (new[]
            {
                new KeyboardShortcut("Rotate", "Rotate", new[] {KeyCode.LeftArrow, KeyCode.RightArrow}),
                new KeyboardShortcut("Reset", "Reset", EKeyboardFunction.Cancel),
            })
        },
        {
            EShortcutType.BuildingPlacement,
            new (new []
            {
                new KeyboardShortcut("Rotate", "Rotate", new[] {KeyCode.LeftArrow, KeyCode.RightArrow}),
                new KeyboardShortcut("Cancel", "Cancel", EKeyboardFunction.Cancel),
            })
        },
        {
            EShortcutType.DesignGlobally,
            new (new[]
            {
                //new KeyboardShortcut("Exit", "Exit design mode", new[] {KeyCode.Space}),
                new KeyboardShortcut("Rotate", "Rotate", EKeyboardFunction.RotateCamera),
                new KeyboardShortcut("Zoom", "Camera zoom", EKeyboardFunction.DollyCamera),
                new KeyboardShortcut("Focus", "Focus on a building", new[] {KeyCode.Mouse1}),
            })
        },
        {
            EShortcutType.DesignFocused,
            new (new[]
            {
                //new KeyboardShortcut("Exit", "Exit design mode", new[] {KeyCode.Space}),
                new KeyboardShortcut("Rotate", "Rotate", EKeyboardFunction.RotateCamera),
                new KeyboardShortcut("Zoom", "Camera zoom", EKeyboardFunction.DollyCamera),
                new KeyboardShortcut("Raise", "Camera raise", EKeyboardFunction.RaiseLowerDesignCamera),
                new KeyboardShortcut("Unfocus", "Leave focus mode", EKeyboardFunction.LeaveFocus),
                new KeyboardShortcut("Snap Camera", "Snap camera", EKeyboardFunction.JumpToOakridge),
            })
        },
        {
            EShortcutType.Design,
            new (new[]
            {
                new KeyboardShortcut("Rotate", "Rotate", EKeyboardFunction.RotateCamera),
                new KeyboardShortcut("Zoom", "Camera zoom", EKeyboardFunction.DollyCamera),
                new KeyboardShortcut("Raise", "Camera raise", EKeyboardFunction.RaiseLowerDesignCamera),
            })
        },
        {
            EShortcutType.ProductTesting,
            new (new KeyboardShortcut[]
            {
            })
        },
        {
            EShortcutType.Town,
            new (new KeyboardShortcut[]
            {
                new KeyboardShortcut("Change Hand Power", "Change Hand Power", () => DayNight.Me.m_isFullNight && PlayerHandManager.Me.PowerUnlocked("Any"), null, null, null, EKeyboardFunction.HandPowerMenu),
            })
        },
        {
            EShortcutType.DragWorker,
            new (new[]
            {
                new KeyboardShortcut("Assign Job", "Assign Job", EKeyboardFunction.AssignJob),
                new KeyboardShortcut("Assign House", "Assign House", EKeyboardFunction.AssignHome),
            })
        },
        {
            EShortcutType.Possess,
            new (new[]
            {
                new KeyboardShortcut("Exit", "Exit possess mode", () => GameManager.Me.IsPossessing && GameManager.Me.CanEndPossession, () => false, null, null,  EKeyboardFunction.Possess),
                new KeyboardShortcut("Run", "Run", () => GameManager.Me.IsPossessing, () => false, null, null, EKeyboardFunction.PossessRun),
                new KeyboardShortcut("Block/Parry", "Block/Parry", () => GameManager.Me.IsPossessing && GameManager.Me.CanStartBlock, () => false, null, null, new[] { KeyCode.Space}),
                new KeyboardShortcut("Action", "", () => GameManager.Me.IsPossessing && GameManager.Me.GetPossessedActionAvailable(), () => false, null, () => GameManager.Me.GetPossessedActionLabel(), new[] {KeyCode.Mouse0}),
                new KeyboardShortcut("Cast Rune", "Cast Rune Power", () => GameManager.Me.IsPossessing && GameManager.Me.PossessedCharacterWeaponHasPowers(), () => false, null, null, new[] {KeyCode.Mouse1}),
                new KeyboardShortcut("Possess", "Possess Nearby", () => GameManager.Me.IsPossessing == false && (MAQuestManager.Me.RequiredPossessableMovingObjects().Count > 0 || MAQuestManager.Me.RequiredPossessableBuildings().Count > 0), () => false, null, null, EKeyboardFunction.Possess),
                new KeyboardShortcut("Possess", "Possess Other", () => GameManager.Me.PossessedCharacter != null && GameManager.Me.PossessedCharacter.CanDoPossessionTransferAction() != null, () => false, null, null, EKeyboardFunction.PossessTransfer),
                new KeyboardShortcut("Untag", "Untag all tagged", () => GameManager.Me.PossessedCharacter != null && GameManager.Me.PossessedCharacter.HasFollowingCharacters, () => false, null, null, EKeyboardFunction.UnfollowAll),
            })
        },
        {
            EShortcutType.PossessTurret,
            new (new[]
            {
                new KeyboardShortcut("Exit", "Exit possess mode", EKeyboardFunction.Possess),
                new KeyboardShortcut("Cycle", "Cycle Building Turrets", EKeyboardFunction.PossessTransfer),
                //new KeyboardShortcut("Lock", "Lock direction", () => GameManager.Me.IsPossessedTurretLocked() == false, () => false, null, null, new[] {KeyCode.L}),
                //new KeyboardShortcut("Unlock", "Unlock direction", () => GameManager.Me.IsPossessedTurretLocked(), () => false, null, null, new[] {KeyCode.L}),
                new KeyboardShortcut("Fire", "Fire", () => GameManager.Me.CanPossessedTurretFire(), () => false, null, null, new[] {KeyCode.Mouse0}),
            })
        },
        {
            EShortcutType.EnablePatrolAreaSelection,
            new (new[]
            {
                new KeyboardShortcut("Enable Patrol Area Selection", "Toggle Patrol Area Selection", () => NGManager.Me.m_MAHeroList.Find(x => x.CharacterUpdateState.State == CharacterStates.HeldByPlayer), null, null, null, EKeyboardFunction.SetPatrolZone),
            })
        },
        {            
            EShortcutType.DisableOrAdjustPatrolAreaSelection,
            new (new[]
            {
                new KeyboardShortcut("Disable Patrol Area Selection", "Toggle Patrol Area Selection", () => NGManager.Me.m_MAHeroList.Find(x => x.CharacterUpdateState.State == CharacterStates.HeldByPlayer), null, null, null, EKeyboardFunction.SetPatrolZone),
                new KeyboardShortcut("Decrease Radius", "Decrease Radius ", EKeyboardFunction.DecPatrolZone),
                new KeyboardShortcut("Increase Radius", "Increase Radius ", EKeyboardFunction.IncPatrolZone),
            })
        },
        {
            EShortcutType.DistrictEdit,
            new (new[]
            {
                new KeyboardShortcut("Add District", "Add District", KeyCode.Equals),
                new KeyboardShortcut("Delete Vertex", "Delete Vertex", KeyCode.Mouse0, KeyCode.Minus),
                new KeyboardShortcut("Insert Vertex", "Insert Vertex", KeyCode.LeftShift, KeyCode.Mouse0),
                new KeyboardShortcut("Hide Visuals", "Hide Visuals", KeyCode.V),
                new KeyboardShortcut("Add Audio Override", "Add Audio Override", KeyCode.LeftControl, KeyCode.O),
            })
        },
        {
            EShortcutType.HeldObjects,
            new (new[]
            {
                new KeyboardShortcut("Drop Object", "Drop Object", EKeyboardFunction.Cancel),
            })
        },
        {
            EShortcutType.FailedDay,
            new(new KeyboardShortcut[]
            {
            })
        },
    };

    void Start()
    {
        m_keyToSprite = new();
        m_keyToSprite[KeyCode.LeftArrow] = (m_leftArrowSprite, m_leftArrowSpriteRotate);
        m_keyToSprite[KeyCode.RightArrow] = (m_rightArrowSprite, m_rightArrowSpriteRotate);
        m_keyToSprite[KeyCode.UpArrow] = (m_upArrowSprite, m_upArrowSpriteRotate);
        m_keyToSprite[KeyCode.DownArrow] = (m_downArrowSprite, m_downArrowSpriteRotate);
        m_keyToSprite[KeyCode.Mouse0] = (m_leftMouseButtonSprite, 0);
        m_keyToSprite[KeyCode.Mouse1] = (m_rightMouseButtonSprite, 0);
        Hide();
        UpdatePosition();
    }
    
    void Hide()
    {
        m_stateTarget = 0;
    }

    void Show()
    {
        m_stateTarget = 1;
        if (transform.parent.gameObject.activeSelf == false) // something is disabling the parent
            transform.parent.gameObject.SetActive(true);
    }

    void Update()
    {
        CheckPredicatesAndRefresh();
        UpdateTransition();
    }

    void UpdateTransition()
    {
        var d = m_stateTarget - m_state;
        if (d * d < .001f * .001f)
        {
            if (m_stateTarget < .01f && m_nextShortcuts != null)
            {
                SetShortcuts(m_nextShortcuts);
                m_nextShortcuts = null;
            }
            return;
        }
        m_state = Mathf.Lerp(m_state, m_stateTarget, .4f);
        if (d * d < .01f * .01f)
            m_state = m_stateTarget;
        UpdatePosition();
    }

    void UpdatePosition()
    {
        m_shortcutHolder.localPosition = new Vector3(0, -100 * (1 - m_state), 0);
    }

    List<ShortcutSet> m_shortcutStack = new();
    public void PushShortcuts(EShortcutType _type)
    {
        if (m_shortcutLookup.TryGetValue(_type, out var shortcuts))
            PushShortcuts(shortcuts);
    }

    public void PushShortcuts(ShortcutSet _shortcuts)
    {
        //Debug.LogError($"Pushing shortcuts: {FindType(_shortcuts)}");
        m_shortcutStack.Add(_shortcuts);
        SetShortcuts(_shortcuts);
    }

    private EShortcutType FindType(ShortcutSet _set)
    {
        foreach (var kvp in m_shortcutLookup)
        {
            if (kvp.Value == _set)
                return kvp.Key;
        }
        return EShortcutType.Disable;
    }

    public void PopShortcuts(EShortcutType _expectedType = EShortcutType.Disable)
    {
        if (_expectedType != EShortcutType.Disable && (m_shortcutStack.Count == 0 || FindType(m_shortcutStack[m_shortcutStack.Count - 1]) != _expectedType))
        {
            Debug.LogWarning($"PopShortcuts error: Expected {_expectedType} but found {FindType(m_shortcutStack.Count > 0 ? m_shortcutStack[m_shortcutStack.Count - 1] : null)}");
            return;
        }
        
        //Debug.LogError($"Popping shortcuts: {FindType(m_shortcutStack.Count > 0 ? m_shortcutStack[m_shortcutStack.Count - 1] : null)} - next {FindType(m_shortcutStack.Count > 1 ? m_shortcutStack[m_shortcutStack.Count - 2] : null)}");
        if(m_shortcutStack.Count > 0)
            m_shortcutStack.RemoveAt(m_shortcutStack.Count - 1);
            
        if (m_shortcutStack.Count > 0)
            SetShortcuts(m_shortcutStack[m_shortcutStack.Count - 1]);
        else
            Hide();
    }

    public void Refresh()
    {
        if (m_shortcutStack.Count == 0) return;
        if (m_nextShortcuts != null) return;
        var current = m_shortcutStack[m_shortcutStack.Count - 1];
        FillShortcuts(current, true);
    }

    void SetShortcuts(ShortcutSet _shortcuts)
    {
        if (m_state > 0)
        {
            if (m_stateTarget > 0)
                Hide();
            m_nextShortcuts = _shortcuts;
            return;
        }
        FillShortcuts(_shortcuts, false);
    }

    public GameObject MakeShortcutVisual(KeyCode _key, Transform _holder)
    {
        var shortcut = new KeyboardShortcut("", "", _key);
        return MakeShortcutVisual(shortcut, _holder);
    }

    public GameObject MakeShortcutVisual(KeyboardShortcut _shortcut, Transform _holder)
    {
        var template = m_shortcutHolder.GetChild(0).gameObject;
        var go = Instantiate(template, _holder);
        FillShortcut(go, _shortcut, false);
        return go;
    }

    private ulong m_currentPredicateStates;

    private ulong GetPredicateStates(ShortcutSet _shortcuts)
    {
        ulong result = 0, bit = 1;
        for (int i = 0; i < _shortcuts.m_shortcuts.Length; ++i)
        {
            if (_shortcuts.m_shortcuts[i].m_predicate != null && !_shortcuts.m_shortcuts[i].m_predicate())
                result |= bit;
            bit <<= 1;
        }
        return result;
    }

    private void CheckPredicatesAndRefresh()
    {
        if (m_shortcutStack.Count == 0)
        {
            Hide();
            return;
        }
        ulong newStates = GetPredicateStates(m_shortcutStack[m_shortcutStack.Count - 1]);
        if (newStates != m_currentPredicateStates)
            Refresh();
    }

    private void FillShortcuts(ShortcutSet _shortcuts, bool _refreshOnly)
    {
        m_shortcutHolder.DestroyChildren(true, 1);
        var template = m_shortcutHolder.GetChild(0).gameObject;
        int next = 0;
        var states = GetPredicateStates(_shortcuts);
        m_currentPredicateStates = states;
        for (int i = 0; i < _shortcuts.m_shortcuts.Length; ++i, states >>= 1)
        {
            if ((states & 1) != 0) continue;
            bool highlighted = _shortcuts.m_shortcuts[i].m_highlighted != null && _shortcuts.m_shortcuts[i].m_highlighted();
            var go = next > 0 ? Instantiate(template, m_shortcutHolder) : template;
            FillShortcut(go, _shortcuts.m_shortcuts[i], highlighted);
            ++next;
        }
        foreach (var kvp in _shortcuts.m_forcedShortcuts)
        {
            var go = next > 0 ? Instantiate(template, m_shortcutHolder) : template;
            FillShortcut(go, kvp.Value, false);
            ++next;
        }
        if (next == 0 && m_stateTarget == 1) Hide();
        else if (next > 0 && (_refreshOnly == false || m_stateTarget == 0)) Show();
    }
    
    private void FillShortcut(GameObject _go, KeyboardShortcut _shortcut, bool _highlighted)
    {
        var keyButton = _go.GetComponent<UnityEngine.UI.Button>();
        keyButton.onClick.RemoveAllListeners();
        if (_shortcut.m_onClick != null)
        {
            keyButton.onClick.AddListener(() =>
            {
                _shortcut.m_onClick();
            });
        }

        var colour = _highlighted ? Color.yellow : Color.white;
        var tmp = _go.GetComponentInChildren<TMPro.TextMeshProUGUI>();
        tmp.text = _shortcut.m_dynamicTitle != null ? $"{_shortcut.m_dynamicTitle()} " :  $"{_shortcut.m_groupTitle} ";
        tmp.color = colour;
        _go.transform.DestroyChildren(true, 2);
        var keyTemplate = _go.GetComponentInChildren<UnityEngine.UI.Image>();
        if (_shortcut.m_function != EKeyboardFunction.None)
            _shortcut.m_keys = KeyboardController.Me.GetKeys(_shortcut.m_function);
        for (int i = 0; i < _shortcut.m_keys.Length; ++i)
        {
            var keyInst = i > 0 ? Instantiate(keyTemplate, _go.transform) : keyTemplate;
            var keyText = keyInst.transform.GetChild(0).GetComponent<TMPro.TextMeshProUGUI>();
            var keyImage = keyInst.transform.GetChild(1).GetComponent<UnityEngine.UI.Image>();
            var (label, sprite, spriteRot) = GetKeyVisual(_shortcut.m_keys[i]);
            if (sprite != null)
            {
                keyText.gameObject.SetActive(false);
                keyImage.gameObject.SetActive(true);
                keyImage.sprite = sprite;
                keyImage.transform.localEulerAngles = new Vector3(0, 0, spriteRot);
                keyImage.color = colour;
            }
            else
            {
                var keyString = GetKeyString(_shortcut.m_keys[i]);
                keyText.gameObject.SetActive(true);
                keyImage.gameObject.SetActive(false);
                keyText.text = keyString;
                keyText.color = colour;
            }
        }
    }

    public string GetKeyString(KeyCode _key)
    {
        if (!m_keyToString.TryGetValue(_key, out var keyString))
        {
            keyString = _key.ToString();
            if (keyString.StartsWith("Alpha"))
                keyString = keyString.Substring(5);
        }
        return keyString;
    }

    public (string, Sprite, float) GetKeyVisual(KeyCode _key)
    {
        if (m_keyToSprite.TryGetValue(_key, out var spriteAndRot))
            return (null, spriteAndRot.Item1, spriteAndRot.Item2);
        return (GetKeyString(_key), null, 0);
    }
}
