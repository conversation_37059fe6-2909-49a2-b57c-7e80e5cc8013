using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public interface IDragCard {
	void DragCardConfirmed(GameObject _context);
	void OnClick(bool _long);
	void OnEndDragCustom(PointerEventData _eventData, bool _undo);
}
public class DragCard : DragBase 
{
	[Range(0,20)]
	[SerializeField]
	public float m_bezierHookForwardDistance = 15f;
	
	public GameObject BestTaget => m_bestTarget;
	private GameObject m_bestTarget = null;
	override public bool AcceptsClicks => true;
	override public bool UpdatesDuringClick => true;
	public override bool HasBezier => true;
	public override Color BezierColour => m_bezierColor;
	private Color m_bezierColor;
	
	PointerEventData m_eventData = new PointerEventData(null);
	
	override public void OnDragStart() {
		Vector2 input = m_eventData.pressPosition = InputPosition;
		m_eventData.button = InputId;
		m_eventData.delta = new Vector2(input.x, input.y) - m_eventData.position;
		m_eventData.position = input;
		m_eventData.clickCount = 0;
		GetComponentInChildren<IBeginDragHandler>()?.OnBeginDrag(m_eventData);
	}
	
	override public void OnDragEnd(bool _undo) {
		
		Vector3 input = InputPosition;
		m_eventData.delta = new Vector2(input.x, input.y) - m_eventData.position;
		m_eventData.position = input;
		if(_undo)
		{
			var dragCard = GetComponentInChildren<IDragCard>();
			if(dragCard != null)
			{
				dragCard.OnEndDragCustom(m_eventData, _undo);
				return;
			}
		}
		GetComponentInChildren<IEndDragHandler>()?.OnEndDrag(m_eventData);
	}

	public GameObject GetBestTarget(int _inputId)
	{
		var pb = GetComponent<IPickupBehaviour>();
		if (pb != null)
		{
			return pb.GetBestTarget(_inputId, this.gameObject, this.transform.position, out var action);
		}
		return null;
	}
	
	public void UpdateBezierTarget(int _inputId, bool _forceDisableBezier, bool _isWorldSpaceDragging)
	{
		// Must do this before calling GetBestTarget()
		if(_forceDisableBezier)
		{
			DisableBezier();
			return;
		}
			
		var target = GetBestTarget(_inputId); 
		if(target == null)
		{
			var pb = GetComponent<IPickupBehaviour>();
			if(pb != null)
			{
				pb.DropNoBezier();
			}
			DisableBezier();
		}
		else
		{
			if(target != m_bestTarget)
			{
                HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.BezierChange);
				m_bestTarget = target;
            }

			var targetPos = Pickup.GetBestBezierPosition(_inputId, gameObject, target);
			var sourceTransform = transform;
			Vector3 bezierPos;
			if(_isWorldSpaceDragging)
			{
				CursorHeld3DObject ch3 = sourceTransform.GetComponentInParent<CursorHeld3DObject>();
				if(ch3 == null)
				{
					bezierPos = sourceTransform.position + sourceTransform.forward * m_bezierHookForwardDistance;
				}
				else
				{
					bezierPos = ch3.m_cardHolder.position + Camera.main.transform.forward.normalized * 1;
				}
			}
			else
			{
				var bezierDest = gameObject.GetComponentInChildren<BezierAnchor>();
				bezierPos = bezierDest == null ? sourceTransform.position : bezierDest.transform.position;
				bezierPos = Camera.main.ScreenPointToRay(bezierPos)
					.GetPoint(Camera.main.nearClipPlane + m_bezierHookForwardDistance);
			}
			
			if(target.GetComponent<BuildingCardHolderSegmentSlot>() == null)
				m_bezierColor = GlobalData.Me.m_miscPositiveBezierColour;
			else 
				m_bezierColor = GlobalData.Me.m_miscNegativeBezierColour;
			EnableBezier(bezierPos, targetPos + Vector3.up*1);	
		}
	}

	override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		if (CouldBeClick && TimeSinceClick > c_longClickTime) {
			EndDrag();
		}

		Vector3 input = InputPosition;
		m_eventData.delta = new Vector2(input.x, input.y) - m_eventData.position;
		m_eventData.position = input;
		
		if(!CouldBeClick)
			GetComponentInChildren<IDragHandler>()?.OnDrag(m_eventData);
	}
	const float c_longClickTime = .75f;
	override public void OnClick() {
		GetComponentInChildren<IDragCard>()?.OnClick(TimeSinceClick > c_longClickTime);
	}
}
