using System;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class NamedPoint : MonoBehaviour
{
	private static Dictionary<string, NamedPoint> s_namedPoints = new Dictionary<string, NamedPoint>();
	private static bool s_pointsLoaded;
	
	[SerializeField] 
	private MACharacterGroupBehaviour m_spawnedGroup = null;
	
	public GameState_NamedPoint m_gameState;
	
	[SerializeField][ReadOnlyInspector]
	private bool m_isLoaded = false;
	
	[ReadOnlyInspector]
	public List<MASpawnArea> m_spawnAreas = new();

	private int DaySpawnedGroupDied
	{
		get => m_gameState?.m_daySpawnedGroupDied ?? -1;
		set
		{
			if (m_gameState == null) return;
			m_gameState.m_daySpawnedGroupDied = value;
		}
	}

	public static NamedPoint GetNamedPoint(string _name)
	{
		if (_name != null && s_namedPoints.ContainsKey(_name))
		{
			return s_namedPoints[_name];
		}
		return null;
	}

	public static void LoadAll()
	{
		// GenericSubScene subScene = GetComponentInParent<GenericSubScene>(true);
		// if (subScene != null) return;

		var namedPointSaveData = GameManager.Me.m_state.m_namedPoints;
		for (int i = namedPointSaveData.Count - 1; i >= 0; i--)
		{
			var savedNP = namedPointSaveData[i];
			if (savedNP.m_subScene.IsNullOrWhiteSpace() == false)
			{
				var runtimeNP = GetNamedPoint(savedNP.m_name);
				if (runtimeNP == null)
				{//what to do, how do we know the save's subscene even exists?
				}
				else
				{
					runtimeNP.Load(savedNP);
				}
			}
			else
			{
				var runtimeNP = GetNamedPoint(savedNP.m_name);
				if (runtimeNP == null)
				{
					var newPoint = new GameObject(savedNP.m_name, components: typeof(NamedPoint)); //parent to holder
					runtimeNP = newPoint.GetComponent<NamedPoint>();
					newPoint.transform.SetParent(GlobalData.Me.m_namedPointHolder);
					newPoint.transform.position = newPoint.transform.position.GroundPosition();
				}

				runtimeNP.Load(savedNP);
			}
		}
		s_pointsLoaded = true;
	}

	public static void SaveAll()
	{
		foreach (var namedPoint in s_namedPoints.Values)
		{
			namedPoint.Save();
		}
	}

	public static void ResetAll()
	{
		foreach (var namedPoint in GameManager.Me.m_state.m_namedPoints)
		{
			namedPoint.ResetWorkingData();
		}
	}
	
	private void Awake()
	{
		m_gameState = null;
		
		transform.position = transform.position.GroundPosition();
		
		if (Application.isPlaying) // && (GenericSubScene.Me == null || GenericSubScene.Me.IsActive)
		{
			if (s_namedPoints.TryGetValue(name, out var outout) == false)
			{
				s_namedPoints.Add(name, this);
			}
		}

		if (name.IsNullOrWhiteSpace())
		{
			Destroy(gameObject);
			return;
		}
	}

	private void Update()
	{
		if (m_isLoaded == false)
		{
			if (GameManager.Me.LoadComplete && IntroControl.IsInIntro == false &&
			    (GenericSubScene.Me == null || GenericSubScene.Me.IsActive))
			{	
				var p = transform.position;
				if (MASpawnArea.AssertNavPos(p) == false)
				{
					Utility.ShowErrorOnce($"{name} - NamedPoint Pos on bad nav grid location: '{transform.position}'. please move it.");
					return;
				}
				var centralSpawn = MASpawnArea.Create(new GameState_SpawnArea()
					{ m_buildingUid = -1, m_worldPos = p, m_namedPoint = name });
				//centralSpawn.m_seedSave = false;
				m_isLoaded = true;
				if (centralSpawn.m_spawnArea == null)
				{
					Debug.LogError(
						$"{name} - A NamedPoint is not allowed to exist at position: '{transform.position}'. please move it. Name: '{name}'");
					return;
				}
				m_spawnAreas.Add(centralSpawn);
			}
		}
	}
	
	const float c_spawnPosRadius = 8f;

	public void SetupClusterOfSpawnLocations(int _num = 10)
	{
		var centralSpawn = GetComponentInChildren<MASpawnArea>(true);
		if (centralSpawn?.m_spawnArea == null) return;
		var p = transform.position;
		for (int i = 0; i < _num; i++)
		{
			Vector2 randomOffsetV2 = Random.insideUnitCircle * c_spawnPosRadius;
			int tries = 5;
			while (OverlapExists(p.GetXZVector2() + randomOffsetV2) && tries-- > 0)
			{
				randomOffsetV2 = Random.insideUnitCircle * c_spawnPosRadius;
			}

			bool OverlapExists(Vector2 _pos)
			{
				//if (m_spawnAreas.Count == 0) return false;
				for (int j = m_spawnAreas.Count - 1; j >= 0; j--)
				{
					if (m_spawnAreas[j] == null)
					{
						m_spawnAreas.RemoveAt(j);
						continue;
					}

					if (m_spawnAreas[j].m_spawnArea == null)
					{
						m_spawnAreas.RemoveAt(j);
						continue;
					}

					float distSq = (m_spawnAreas[j].m_spawnArea.transform.position.GetXZVector2() - _pos)
						.sqrMagnitude;
					float maxDist = centralSpawn.m_spawnArea.radius * 2f;
					if (distSq < maxDist * maxDist)
					{
						return true;
					}
				}

				return false;
			}

			if (tries > 0)
			{
				var subSpawnArea = MASpawnArea.Create(new GameState_SpawnArea()
				{
					m_buildingUid = -1,
					m_worldPos = transform.position + new Vector3(randomOffsetV2.x, 0, randomOffsetV2.y),
					m_namedPoint = name
				});
				//subSpawnArea.m_seedSave = false;
				m_spawnAreas.Add(subSpawnArea);
			}
		}
	}

	void OnDestroy()
	{
		s_namedPoints.Remove(name);
		if (m_spawnedGroup != null)
		{
			m_spawnedGroup.onGroupDestroyed -= SpawnedGroupDied;
		}
	}

	public void Load(GameState_NamedPoint _saveData)
	{
		m_gameState = _saveData;
		transform.position = _saveData.Position;

		if (MACharacterGroupBehaviour.s_groups.ContainsKey(_saveData.m_spawnedGroupID))
		{
			SetSpawnedGroup(MACharacterGroupBehaviour.s_groups[_saveData.m_spawnedGroupID]);
		}
	}

	public void Save()
	{
		var existingSave = GameManager.Me.m_state.m_namedPoints.Find(x => x.m_name == name);
		if (existingSave == null)
		{
			if (m_gameState == null)
			{
				m_gameState = new GameState_NamedPoint();
			}
			GameManager.Me.m_state.m_namedPoints.Add(m_gameState);
		}
		else
		{
			m_gameState = existingSave;
		}
		
		GenericSubScene subScene = GetComponentInParent<GenericSubScene>(true);
		if (subScene != null)
		{
			m_gameState.m_subScene = subScene.SceneName;
		}

		m_gameState.m_name = name;
		m_gameState.m_position = transform.position.GetXZVector2();
		m_gameState.m_spawnedGroupID = m_spawnedGroup != null ? m_spawnedGroup.ID : -1;
		
		//MASpawnArea.SaveAll();
	}

	public void SetSpawnedGroup(MACharacterGroupBehaviour _group)
	{
		m_spawnedGroup = _group;
		m_spawnedGroup.onGroupDestroyed -= SpawnedGroupDied;
		m_spawnedGroup.onGroupDestroyed += SpawnedGroupDied;
	}

	public void SpawnedGroupDied()
	{
		m_spawnedGroup = null;
		DaySpawnedGroupDied = DayNight.Me.CurrentWorkingDay;
	}

	// Under these spawning rules, creatures can only respawn the day _after_ they died.
	// So if they spawned on Day 4 and died on Day 5, they still can't respawn until Day 6.
	public bool CanSpawn()
	{
		float cameraXZDistSqd = (GameManager.Me.m_camera.transform.position - transform.position).xzSqrMagnitude();
		// RW-02-APR-25: Using s_pointsLoaded here rather than just GameManager.Me.LoadComplete as multiple things
		// in the game are predicated off that and I don't want to run into some order-of-operations issue.
		return NamedPoint.s_pointsLoaded && m_spawnedGroup == null && DaySpawnedGroupDied < DayNight.Me.CurrentWorkingDay && cameraXZDistSqd < GameManager.Me.m_namedPointMaxSpawnDist*GameManager.Me.m_namedPointMaxSpawnDist;
	}

	public List<MASpawnArea> GetSpawnAreas()
	{
		return new List<MASpawnArea>(m_spawnAreas);
	}

	public void AddLoadedSpawnArea(MASpawnArea _spawnArea)
	{
		m_spawnAreas.Add(_spawnArea);
	}
}



#if UNITY_EDITOR
[CustomEditor(typeof(NamedPoint))]
public class NamedPointEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		NamedPoint namedPoint = (NamedPoint)target;

		if (namedPoint.GetComponentInParent<GenericSubScene>() == false)
		{
			if(GUILayout.Button($"Save All Points!"))
			{
				var all = GameObject.FindObjectsByType<NamedPoint>(FindObjectsSortMode.None);
				for (int i = GameManager.Me.m_state.m_namedPoints.Count - 1; i >= 0; i--)
				{
					int iFound = Array.FindIndex(all, x=> x.m_gameState == GameManager.Me.m_state.m_namedPoints[i]);
					if (iFound == -1)
					{
						GameManager.Me.m_state.m_namedPoints.RemoveAt(i);
					}
				}
				NamedPoint.SaveAll();
			}
		}

		int num = 10;
		if(GUILayout.Button($"Create Cluster of {num} locations around NamedPoint. (You need to save them manually)"))
		{
			namedPoint.SetupClusterOfSpawnLocations(num);
		}
		
		if(GUILayout.Button($"Snap To Ground"))
		{
			namedPoint.transform.position = namedPoint.transform.position.GroundPosition();
		}
		
		if(GUILayout.Button($"Delete this NamedPoint's Save"))
		{
			DestroyImmediate(namedPoint);
			NamedPoint.SaveAll();
		}

		if(GUILayout.Button($"Delete all NamedPoint Saves"))
		{
			GameManager.Me.m_state.m_namedPoints.Clear();
			var all = GameObject.FindObjectsByType<NamedPoint>(FindObjectsSortMode.None);
			foreach (NamedPoint point in all)
			{
				DestroyImmediate(point.gameObject);
			}
			NamedPoint.SaveAll();
		}
		
		if(GUILayout.Button($"Reset Working Save Data for this point"))
		{
			namedPoint.m_gameState.ResetWorkingData();
		}
	}
}
#endif