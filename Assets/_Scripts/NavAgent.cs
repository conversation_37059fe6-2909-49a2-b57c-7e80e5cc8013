//#define ENABLE_DEBUG_NAV_AGENT

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;
using Unity.Collections;
using Unity.Mathematics;

#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(NavAgent))]
public class NavAgentEditor : Editor
{
	private bool m_isExpanded;
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		if(Application.isPlaying == false) return;
		var agent = target as NavAgent;
		var info = agent.CollisionInfo;
		var objs = agent.CollisionObjects;
		var buttonStyle = EditorStyles.label;
		var boldButtonStyle = EditorStyles.boldLabel;
		GUILayout.Space(8);
		GUILayout.Label(info);
		GUILayout.Label(GameManager.Me.IsPossessed(agent.Owner) && agent.DidSeaLevelCheckBlockCharacter ? "Blocked by SeaLevel check" : " ", boldButtonStyle);
		int numRows = m_isExpanded ? 20 : 6;
		int row = 0;
		for (; row < objs.Count; ++row)
		{
			var (obj, impulse, separation) = objs[row];
			if (obj == null)
				continue;
			
			if (row == numRows - 1 && row + 1 < objs.Count)
			{
				// on last row and there's more to come
				GUILayout.Label($"{objs.Count - row} more...", boldButtonStyle);
				++row;
				break;
			}
			bool hit = false;
			GUILayout.BeginHorizontal();
			hit |= GUILayout.Button(obj.name, boldButtonStyle, GUILayout.Width(150));
			hit |= GUILayout.Button($"{impulse:n1}", buttonStyle, GUILayout.Width(100));
			hit |= GUILayout.Button($"{separation:n2}", buttonStyle, GUILayout.Width(50));
			GUILayout.EndHorizontal();
			if (hit) Selection.activeGameObject = obj;
		}
		while (row++ < numRows)
			GUILayout.Label("", boldButtonStyle);
		if (GUILayout.Button(m_isExpanded ? "^" : "v", GUILayout.Width(40)))
			m_isExpanded = !m_isExpanded;
	}
}
#endif

public class NavAgent : MonoBehaviour
{
	private NativeList<float3> m_path;
	private bool m_pathValid = false;

	private Transform m_transform = null;
	private NGMovingObject m_owner; public NGMovingObject Owner => m_owner;
	private Collider m_collider;
	
	private float m_stuckTime;
	public float StuckTime => m_stuckTime;
	
	private bool m_cyclic = false;
	private INavigator m_navigator;
	private int m_navigatorContext;

	private GlobalData.SubNavData m_subNav;

	public bool DidSeaLevelCheckBlockCharacter { get; set; }

	private Vector2 lastAnimSpeed;
	private int animSpeedIndex = 0;
	private Vector2[] animSpeeds = new Vector2[5];

	private const float c_lookAheadSeconds = 0.4f;
	public const float c_distanceToFinalPositionThreshold = .4f; //do

	private float m_distanceToFinalPositionThreshold = c_distanceToFinalPositionThreshold;
	private int m_staticColliderContactCount = 0;

	private float[] m_costSet = GlobalData.s_pedestrianCosts;

	private Action OnNavGridLoaded = null;

	private bool m_waitingForPath;
	private bool m_allowStuck = false;

#if UNITY_EDITOR
	public int m_pathTrackerIndex;
	public int m_pathTrackerIndexMax;
	public float m_pathTrackerTimeTaken;
	public float m_pathTrackerGenerateTime;
#endif

	public Rigidbody m_body;
	public float m_sqrDistanceMovedLastFrame;

	public void SetSubNav(GlobalData.SubNavData _subNav)
	{
		m_subNav = _subNav;
	}
	
	public event Action<float> m_onStuckTimeExceeded;
	private bool m_isStuck = false;
	public bool IsStuck
	{
		get => m_isStuck;
	}

	private bool m_isUnderground;
	public void SetIsUnderground(bool _isUnderground)
	{
		m_isUnderground = _isUnderground;
	}

	[SerializeField] private NavAgentSettings m_settings;

	[SerializeField] [ReadOnlyInspector] private float m_currentNavPos = -1;

	[SerializeField] private bool m_paused = false;

	private LayerMask m_collisionBobbingLayerMask = new LayerMask();
	
	public Vector3 m_lookAheadPosition, m_actualPosition, m_nearestPosition;

	public Collider Collider => m_collider;

	private Coroutine endStuck = null;

	public bool IsPaused => m_paused || m_pauseCount.Count > 0 || Time.timeScale < .001f || (NGManager.Me && NGManager.Me.m_pauseCharacters);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	public bool PathPending => m_waitingForPath || OnNavGridLoaded != null;
#else
	public bool PathPending => false;
#endif
	public bool PathExists => m_pathValid && m_path.Length > 0;
	public bool ComplexPathExists => m_pathValid && m_path.Length > 1;
	public bool IsTravelling => PathExists && !IsPaused; //Are we currently trying to navigate
	public bool IsNavigating => (PathExists || PathPending) && !IsPaused; //Are we currently trying to navigate or waiting for a path
	public bool TargetReached => !IsPaused && !PathExists && !PathPending;
	
	public Vector3? FirstPathPos => PathExists ? m_path[0] : null;
	public Vector3? LastPathPos => PathExists ? m_path[^1] : null;
	
	private int terrainMask = 0;

	public void AddLastPos(Vector3 _pos)
	{
		if (!PathExists)
			return;
		m_path.Add(_pos);
	}

	public void SetNavCosts(float[] _costSet)
	{
		m_costSet = _costSet;
	}

	void SetNativePath(NativeList<float3> _path)
	{
		if (m_pathValid)
			m_path.Dispose();
		m_path = _path;
		m_pathValid = true;
	}

	void ClearNativePath()
	{
		if (!m_pathValid)
			return;
		m_path.Dispose();
		m_pathValid = false;
	}

	public List<float3> CopyTenthOfPath()
	{
		if (!m_pathValid)
			return null;
		return GlobalData.CopyTenthOfPath(m_path);
	}

	private void Awake()
	{
		if (m_settings == null)
		{
			Debug.LogError(
				$"{GetType()} - {name} - No NavAgent settings asset assigned, using default from 'Resources/Settings/NavAgentSettingsDefault'");
			m_settings = Resources.Load("Settings/NavAgentSettingsDefault") as NavAgentSettings;
		}

		m_transform = transform;
		if (m_body == null) m_body = GetComponent<Rigidbody>();
		m_collider = GetComponent<Collider>();

		terrainMask = LayerMask.GetMask(new string[] { "Default", "Terrain", "Roads", "Ignore Camera"});
	}

	public void Initialise(NGMovingObject _owner, float[] _costSet = null)
	{
		m_transform = transform;
		m_owner = _owner;

		if (_costSet != null) m_costSet = _costSet;
		if (m_body == null) m_body = GetComponent<Rigidbody>();
		m_collider = GetComponent<Collider>();

		m_previousPosition = m_transform.position;
		
		foreach (var pb in GetComponentsInChildren<PathBlock>()) pb.SetNavAgent(this);
	}

	public void SetTarget(Vector3 _target, bool _direct = false, float _corridorSize = 0f,
		Action<NavInfo> _onNavFindComplete = null, float _remoteDestinationDistance = 0f, float _destinationRadius = 0f)
	{
		if (_direct)
		{
			NavigateToDirect(_target);
			return;
		}

		if (GameManager.Me.LoadComplete == false || GlobalData.Me.BatchTerrainOperationsInProgress)
		{
			OnNavGridLoaded = () => NavigateTo(_target, _corridorSize, _onNavFindComplete, _remoteDestinationDistance);
			StartCoroutine(WaitUntilLoaded());
			return;
		}

		NavigateTo(_target, _corridorSize, _onNavFindComplete, _remoteDestinationDistance, _destinationRadius);
	}

	private void OnDestroy()
	{
		if (m_pathValid)
		{
			m_path.Dispose();
			m_pathValid = false;
		}

		m_onPathReady = null;

		if (endStuck != null)
		{
			StopCoroutine(endStuck);
			endStuck = null;
		}
	}

	private IEnumerator WaitUntilLoaded()
	{
		yield return new WaitUntil(() =>
			GlobalData.Me.BatchTerrainOperationsInProgress == false && GameManager.Me.LoadComplete);
		OnNavGridLoaded?.Invoke();
		OnNavGridLoaded = null;
	}

	public void Pause(bool _clearPaths, bool _freeze = false)
	{
		m_paused = true;
		if (_clearPaths)
			ClearPath();
		if (_freeze)
			ZeroVelocity();
	}

	public void Unpause()
	{
		m_paused = false;
	}

	[TextArea(1, 5)] public string m_debug = "";

	private HashSet<string> m_pauseCount = new();

	public void PushPause(string _label, bool _clearPaths = false, bool _freeze = false)
	{
		//Debug.LogError($"PushPause {_label} {gameObject.name} - was {m_pauseCount.Count}");
		m_pauseCount.Add(_label);
		if (_clearPaths)
			ClearPath();
		if (_freeze)
			ZeroVelocity();
	}

	public void PopPause(string _label)
	{
		//Debug.LogError($"PopPause {_label} {gameObject.name} - was {m_pauseCount.Count}");
		m_pauseCount.Remove(_label);
	}

	public void ForceUnpause() // dangerous, for debug purposes only
	{
		m_paused = false;
		m_pauseCount.Clear();
	}

	private string DebugInfo()
	{
#if UNITY_EDITOR
		var s = $"Paused:   bool:{m_paused}   labelled:{m_pauseCount.Count}>0   ts:{Time.timeScale < .001f}";
		if (m_pauseCount.Count > 0)
		{
			s += "\n";
			foreach (var reason in m_pauseCount)
				s += $"{reason}  ";
		}
		s += $"\nLastNav req:{m_lastNavRequestFrame} succ:{m_lastNavSuccessFrame} res:{m_lastNavResult} len:{m_lastNavPathLength} [{m_lastNavGenFrame} / {GlobalData.Me.m_navGenerationFrame} {Time.frameCount}]\n{m_lastSourcePos} to {m_lastDestPos}";
		s += $"\nLastDirectNav req:{m_lastNavDirectFrame}\n{m_lastNavDirectSource} to {m_lastNavDirectTarget}";
		s += $"\nStuck: m_stuckTime {m_stuckTime}. m_isStuck {m_isStuck}. m_allowStuck {m_allowStuck}";
		return s;
#else
		return null;
#endif
	}

	private void OnDisable()
	{
		m_staticColliderContactCount = 0;
	}

	public float DistanceToTarget => PathExists ? math.length((m_path[^1] - (float3)m_transform.position).xz) : 0;

	public bool TryGetTravelDistanceToTarget(out float outDistance)
	{
		outDistance = 0f;
		if (PathExists)
		{
			for (int i = 0; i < m_path.Length - 1; ++i)
				outDistance += math.distance(m_path[i].xz, m_path[i + 1].xz);
			return true;
		}
		return false;
	}

	public Vector3 GetPositionIn(float _seconds)
	{
		if (DistanceToTarget == 0)
			return transform.position;
		return PredictPos(_seconds);
	}

	[Tooltip("This is used to store the original requested target position that may or may not be reachable" +
	         "NavAgent returns 'TargetReached' as true when we are as close as possible to this position " +
	         "Use this as a comparison. Also check NavInfo.m_finalPosOffset and m_finalPosOffsetDistanceXZSqr " +
	         "for more information on the final position reached.")]
	private float3 m_originalTargetPosition;

	public Vector3 OriginalTargetPosition
	{
		get => m_originalTargetPosition;
		set => m_originalTargetPosition = value;
	}
	
	public float DistanceToOriginalTarget => (GameManager.Me.LoadComplete && PathExists) ? math.length((m_originalTargetPosition - (float3)m_transform.position).xz) : float.MaxValue;

	private float m_originalCorridorWidth = 0;
	private float m_originalRemoteDestinationDistance = 0;

	public void RefreshNavigation()
	{
		NavigateTo(m_originalTargetPosition, m_originalCorridorWidth, m_onPathReady);
	}

	public void StopNavigation()
	{
		m_onPathReady = null;
		Pause(true, true);
		Unpause();
		m_navigatorContext = 0;
	}

	public void NavigateTo(Vector3 _pos, float _corridorWidth = 0f, Action<NavInfo> _onPathFindComplete = null,
		float _remoteDestinationDistance = 0, float _destinationRadius = 0f, float[] _costSetOverride = null)
	{
		m_currentNavStrut = null;
		m_nextNavStrut = null;

		m_cyclic = false;
		if (_pos.sqrMagnitude < .001f * .001f)
		{
			Debug.LogError($"Attempting to nav to origin");
			return;
		}

		if ((m_navigator as Object) != null)
		{
			m_navigator.OnLeave(this, _pos);
		}

		m_navigator = null;
		m_onPathReady = _onPathFindComplete;
		m_originalTargetPosition = _pos;
		m_originalCorridorWidth = _corridorWidth;
		m_originalRemoteDestinationDistance = _remoteDestinationDistance;

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (GameManager.Me.IsInPlayground)
		{
			NavigateToDirect(_pos);
		}
		else
		{
			FindPathAsync(m_transform.position, _pos, _corridorWidth, true, _destinationRadius, _costSetOverride);
		}
#else
		NavigateToDirect(_pos);
#endif
	}

	private static bool s_debugLogNavs = false;
	private static DebugConsole.Command s_debugLogNavsCmd = new ("lognavs", _s => Utility.SetOrToggle(ref s_debugLogNavs, _s));

	private int m_lastNavRequestFrame = -1;
	private int m_lastNavSuccessFrame = -1;
	private int m_lastNavPathLength = -1;
	private GlobalData.NavJob.EResult m_lastNavResult;
	private Vector3 m_lastSourcePos, m_lastDestPos;

	private void FindPathAsync(Vector3 _currentPos, Vector3 _pos, float _corridorWidth, bool _abandonExisting = true, float _destinationRadius = 0f, float[] _costSetOverride = null)
	{
		m_waitingForPath = true;
		m_lastNavGenFrame = GlobalData.Me.m_navGenerationFrame;
		m_lastNavRequestFrame = Time.frameCount;
		m_lastSourcePos = _currentPos; m_lastDestPos = _pos;
		
		GlobalData.Me.FindPathAsync(GetInstanceID(), _currentPos, _pos, _costSetOverride ?? m_costSet, true, (_path, _res) =>
		{
			if (s_debugLogNavs)
			{
				var clr = _path.Length <= 0 ? "ff8080" : (((Vector3)_path[^1] - _pos).xzSqrMagnitude() > 5*5 ? "c0c080" : "80ff80");
				Debug.LogError($"<color=#c0c0c0>FindPathAsync</color><color=#ffffff> {gameObject.name.FirstChrs(10)}</color><color=#{clr}> res:{_res} steps:{_path.Length} {(_path.Length <= 0 ? "No Path" : $"{_path[0]:n0}...{_path[^1]:n0}")} - request: {_currentPos:n0}...{_pos:n0}</color>", gameObject);
			}

			m_lastNavSuccessFrame = Time.frameCount;
			m_lastNavPathLength = -1;
			m_lastNavPathLength = _path.Length;
			m_lastNavResult = _res;

			m_waitingForPath = false;
			
			if (_path.Length <= 0)
				return;
			
			SetNativePath(_path);
			m_currentNavPos = -1;
			if (PathExists)
			{
				if (m_originalRemoteDestinationDistance > 0)
					m_distanceToFinalPositionThreshold = m_originalRemoteDestinationDistance;

				ProcessPath();

				m_onPathReady?.Invoke(new NavInfo
					{ m_finalPosOffsetDistanceXZSqr = math.lengthsq((m_originalTargetPosition - m_path[^1]).xz), m_result = _res });
				m_onPathReady = null;
			}
		}, _corridorWidth, _abandonExisting, m_subNav, _destinationRadius);
	}

	private static float FinalSpeed(float _baseSpeed)
	{
		return _baseSpeed * .25f * TerrainBlock.GlobalScale;
	}

	public void SetPath(List<Vector3> _path, bool _cyclic = false, bool _applyJitter = false)
	{
		SetNativePath(GlobalData.NativePathFromList(_path));
		m_navigator = null;
		m_currentNavPos = -1;
		m_stuckTime = 0;
		m_cyclic = _cyclic;
		ProcessPath();
	}

	public void NavigateToDirect(INavigator _nav, float _speed = .5f, int _context = 0)
	{
		NavigateToDirect(_nav.Target(_context));
		m_navigator = _nav;
		m_navigatorContext = _context;
	}

	private Vector3 m_lastNavDirectSource, m_lastNavDirectTarget;
	private int m_lastNavDirectFrame;
	public void NavigateToDirect(Vector3 _pos, float _speed = .5f)
	{
		m_lastNavDirectSource = transform.position; m_lastNavDirectTarget = _pos;
		m_lastNavDirectFrame = Time.frameCount;
	
		GlobalData.Me.KillExistingNav(GetInstanceID());
		m_waitingForPath = false;
		
		if ((m_navigator as Object) != null) m_navigator.OnLeave(this, _pos);
		m_currentNavPos = -1;
		SetNativePath(new NativeList<float3>(1, Allocator.Persistent) { _pos.GetXZ() });
		m_stuckTime = 0;
		gameObject.SetActive(true);
		m_cyclic = false;
		m_lastNavGenFrame = Int32.MaxValue; // GlobalData.Me.m_navGenerationFrame;
	}

	public static Vector3 GetPathPosition(List<float3> _path, Vector3 _currentPos)
	{
		if (_path == null)
			return _currentPos;

		return _path[(int)PointOnPath(_path, _currentPos)];
	}

	private static float PointOnPath(List<float3> _path, Vector3 _pos)
	{
		float bestDSqrd = 1e23f;
		float bestPos = 0;
		for (int i = 0; i < _path.Count - 1; ++i)
		{
			float2 p1 = _path[i].xz;
			float2 p2 = _path[i + 1].xz;
			CheckDistanceBetweenPoints(_pos.GetXZVector2(), p1, p2, ref bestDSqrd, ref bestPos, i);
		}

		return bestPos;
	}

	private static float PointOnPath(NativeList<float3> _path, Vector3 _pos)
	{
		return GlobalData.GetPointOnPath(_path, _pos);
	}

	public static float PointOnPathV2(Vector2[] _path, Vector2 _pos)
	{
		float bestDSqrd = 1e23f;
		float bestPos = 0;
		for (int i = 0; i < _path.Length - 1; ++i)
		{
			float2 p1 = _path[i];
			float2 p2 = _path[i + 1];
			CheckDistanceBetweenPoints(_pos, p1, p2, ref bestDSqrd, ref bestPos, i);
		}

		return bestPos;
	}

	private static void CheckDistanceBetweenPoints(float2 _pos, float2 _p1, float2 _p2, ref float _bestDSq,
		ref float _bestPos, int i)
	{
		var fwd = _p2 - _p1;
		float k = math.dot(_pos - _p1, fwd) / math.lengthsq(fwd);
		k = math.clamp(k, 0f, 1f);
		var dSqrd = math.lengthsq((_p1 + fwd * k - _pos));
		if (dSqrd < _bestDSq)
		{
			_bestPos = i + k;
			_bestDSq = dSqrd;
		}
	}

	private Vector3 PathPosition(float _posOnPath)
	{
		if (_posOnPath < 0)
			return m_path[0];
		if (_posOnPath >= m_path.Length - 1)
			return m_path[^1];

		int index = Mathf.FloorToInt(_posOnPath);
		float frac = _posOnPath - index;
		return Vector3.Lerp(m_path[index], m_path[index + 1], frac);
	}

	private Vector3 PredictPos(float _time)
	{
		if (!IsTravelling)
			return m_transform.position;

		var totDist = _time * m_finalSpeed;
		float3 currPos = m_transform.position;
		var startIndex = (int)PointOnPath(m_path, currPos) + 1;
		return GlobalData.PredictPosAlongPath(m_path, startIndex, currPos, totDist);
	}

	private Vector3 PredictPosAlongPathNonBurst(int startIndex, float3 currPos, float totDist)
	{ 
		Vector3 rest = Vector3.zero;
		for (int index = startIndex; index < m_path.Length; ++index)
		{
			var nextPoint = m_path[index];
			var nextDist = math.length((currPos - nextPoint).xz);
			totDist -= nextDist;
			if (totDist <= 0)
			{
				rest = math.lerp(currPos, nextPoint, 1 + totDist);
				return rest;
			}
			currPos = nextPoint;
		}
		rest  = m_path[^1];
		return rest;
	}

	private Vector3 NextPointFromPath(int _strutY)
    {
		for(int i = 0; i < m_path.Length - 1; i++)
        {
			if((int)m_path[i].y == _strutY)
            {
				return m_path[i+1];
            }
        }

		return m_path[^1];
	}

	private Vector3 GetPathEndPoint()
	{
		Vector3 end = m_path[^1].xyz;

		if(end.y < 0)
        {
			var strut = NavStrut.s_allStruts[(int)-end.y - 2];

			if (m_path.Length > 1)
			{
				Vector3 prevPos = m_path[m_path.Length - 2].xyz;
				end = (prevPos - strut.WorldStart).xzSqrMagnitude() < (prevPos - strut.WorldEnd).xzSqrMagnitude() ? strut.WorldEnd : strut.WorldStart;
			}
		}

		return end;
	}

	private void ClearPath()
	{
		m_waitingForPath = false;
		m_currentNavPos = -1;
		ClearNativePath();
		m_navigator = null;
	}

	private void SetVelocity(Vector3 _vel)
	{
		if (m_body == null || m_body.isKinematic)
			return;
		
		var velocityChange = _vel - m_body.linearVelocity;
		// if (_vel.y <= 0)
		// 	velocityChange.y = 0;
		m_body.AddForce(velocityChange * m_body.mass, ForceMode.Impulse);
	}

	private Vector3 GetVelocity()
	{
		if (m_body == null || m_body.isKinematic)
			return Vector3.zero;
		return m_body.linearVelocity;
	}

	public void SetTargetVelocity(Vector3 _vel)
	{
		m_targetVelocity = _vel;
		m_targetSpeed = _vel.magnitude;
	}

	public void SetShoveVelocity(Vector3 _vel)
	{
		m_shoveSpeed = _vel.magnitude;
		m_shoveDir = _vel.normalized;
	}


	[SerializeField] private float m_slowDownFriction = 0.85f;
	
	public void GradualStop(bool _slowDownY = false)
	{
		StartCoroutine(GradualSlowDown(_slowDownY));
	}
	
	private IEnumerator GradualSlowDown(bool _slowDownY = false)
	{
		if(IsNavigating) yield break;

		while (m_targetVelocity.sqrMagnitude > 1f)
		{
			m_targetVelocity = new Vector3(
				m_targetVelocity.x * m_slowDownFriction,
				_slowDownY ? m_targetVelocity.y * m_slowDownFriction : m_targetVelocity.y,
				m_targetVelocity.z * m_slowDownFriction);
			yield return new WaitForEndOfFrame();
			if(IsNavigating || GameManager.Me.IsPossessed(m_owner)) yield break;
		}
		ZeroVelocity();
	}
	
	public void ZeroVelocity(bool _zeroY = false)
	{
		SetTargetVelocity(Vector3.zero);
		SetShoveVelocity(Vector3.zero);

		if (_zeroY)
			SetVelocity(Vector3.zero);
		else
			SetVelocity(Vector3.up * GetVelocity().y);
	}

	static float s_minimumMaxSpeed = 0;

	static DebugConsole.Command s_setminmaxspeed =
		new DebugConsole.Command("minmaxspeed", _s => s_minimumMaxSpeed = float.Parse(_s));

	private Action m_onInstaTurn = null;
	private Action<NavInfo> m_onPathReady = null;

	public bool m_instaTurn = false;

	[SerializeField] private float m_speed = 0f;

	public float Speed
	{
		protected get => m_speed;
		set => m_speed = value;
	}

	public float m_finalSpeed;
	private const float c_speedAnimMultiplier = .75f;
	private Vector3 m_previousPosition, m_targetVelocity, m_previousPositionFixed;
	private float m_targetSpeed;
	private int m_lastEarlyUpdateFrame = -1;
	private float m_currentLookAhead;

	public float TargetSpeed => m_targetSpeed;
	
	private NavStrut m_currentNavStrut = null, m_nextNavStrut = null;
	private Vector3 m_currentNavStrutSrc, m_currentNavStrutDest;
	private float m_currentNavStrutLength;
	private float m_currentNavStrutProgress = 0;
	private int m_currentNavStrutPathIndex = -1;

	private float m_shoveSpeed = 0f;
	private Vector3 m_shoveDir = Vector3.zero;

	private void SetupStrutValues(Vector3 _to)
	{
		m_currentNavStrutSrc = m_currentNavStrut.ClosestContainedPoint(m_transform.position, out var overflow);
		m_currentNavStrutDest = _to;
		m_currentNavStrutLength = (m_currentNavStrutDest - m_currentNavStrutSrc).magnitude;
		m_currentNavStrutProgress = 0;
	}

	private void SetupStrutValues()
	{
		var to = m_currentNavStrut.GetNavPoint(m_nextNavStrut);
		SetupStrutValues(to);
	}

	private bool CheckNavStrut(float _currentNavPos) //BURST ME
	{
		int index = (int)(_currentNavPos + .1f);
		var point = m_path[index];
		if (point.y >= 0) return false;

		m_body.isKinematic = true;
		m_currentNavStrutPathIndex = index;
		var prevStrut = m_currentNavStrut;
		m_currentNavStrut = NavStrut.s_allStruts[(int)-point.y - 2];
		if (m_currentNavStrutPathIndex + 1 < m_path.Length)
		{
			var nextStrutIndex = (int)m_path[m_currentNavStrutPathIndex + 1].y;
			m_nextNavStrut = nextStrutIndex < 0 ? NavStrut.s_allStruts[-nextStrutIndex - 2] : null;
			SetupStrutValues();
		}
		else
		{
			m_nextNavStrut = null;
			var to = m_currentNavStrut.GetPointAtStrutHeight(m_path[m_currentNavStrutPathIndex]);
			SetupStrutValues(to);
		}

		return true;
	}

	private bool UpdateNavStrut()
	{
		if (m_currentNavStrut == null) return false;

		bool isTransitioning = NavStrut.IsAnimationTransitionPlaying(gameObject);

		var fwd = m_currentNavStrut.GetForward((m_currentNavStrutDest - m_currentNavStrutSrc).GetXZNorm());
		if (m_currentNavStrutProgress == 0 && (m_transform.position - m_currentNavStrutSrc).xzSqrMagnitude() > .25f * .25f) // KW: issues lerping to height, use xy distance
		{
			if (isTransitioning == false)
			{
				bool canMove = true;
				var toSrc = m_currentNavStrutSrc - m_transform.position;
				if (toSrc.xzSqrMagnitude() > .25f * .25f)
				{
					var fwdToSrc = toSrc.GetXZNorm();
					if (Vector3.Dot(fwdToSrc, m_transform.forward) < .8f)
					{
						m_transform.rotation = Quaternion.Slerp(m_transform.rotation,
							Quaternion.LookRotation(fwdToSrc, Vector3.up), .2f);
						canMove = false;
					}
				}

				if (canMove)
				{
					m_transform.position = Vector3.Lerp(m_transform.position, m_currentNavStrutSrc, .2f);
					if ((m_transform.position - m_currentNavStrutSrc).xzSqrMagnitude() <= .1f * .1f) // KW: issues lerping to height, use xy distance
						m_transform.position = m_currentNavStrutSrc;
				}
			}
		}
		else if (m_currentNavStrutProgress == 0 && Vector3.Dot(fwd, m_transform.forward) < .95f)
		{
			m_transform.rotation =
				Quaternion.Slerp(m_transform.rotation, Quaternion.LookRotation(fwd, Vector3.up), .2f);
			if (Vector3.Dot(fwd, m_transform.forward) >= .95f)
				m_transform.rotation = Quaternion.LookRotation(fwd, Vector3.up);
		}
		else
		{
			if (m_currentNavStrutProgress == 0 && NavStrut.IsAnimationPlaying(gameObject) == false)
				m_currentNavStrut?.StartAnimation(gameObject, m_currentNavStrutSrc, m_currentNavStrutDest);
			var rot = Quaternion.LookRotation(fwd, Vector3.up);
			if (!isTransitioning)
				m_transform.rotation = Quaternion.Slerp(m_transform.rotation, rot, .3f);
			var speed = isTransitioning || Vector3.Dot(fwd.normalized, m_transform.forward) < .5f ? 0 : m_speed * .5f;

			m_currentNavStrutProgress += speed * Time.fixedDeltaTime;
			if (m_currentNavStrutProgress > m_currentNavStrutLength)
				m_currentNavStrutProgress = m_currentNavStrutLength;
			m_transform.position = Vector3.Lerp(m_currentNavStrutSrc, m_currentNavStrutDest,
				m_currentNavStrutProgress / m_currentNavStrutLength);
		}

		if (m_currentNavStrutProgress >= m_currentNavStrutLength)
		{
			m_currentNavStrut.EndAnimation(gameObject);
			if (!NavStrut.IsAnimationTransitionPlaying(
				    gameObject)) // don't move to next strut until transition is complete
			{
				int nextPathIndex = m_currentNavStrutPathIndex + 1;
				if (nextPathIndex < m_path.Length - 1)
				{
					var prevStrut = m_currentNavStrut;
					m_currentNavStrut = m_nextNavStrut;
					m_currentNavStrutPathIndex = nextPathIndex;
					var nextStrutIndex = (int)m_path[nextPathIndex + 1].y; //Should be inside burst?
					if (m_currentNavStrut != null)
					{
						m_nextNavStrut = nextStrutIndex < 0 ? NavStrut.s_allStruts[-nextStrutIndex - 2] : null;
						SetupStrutValues();
					}
					else
					{
						m_body.isKinematic = false;
					}
				}
				else if (nextPathIndex == m_path.Length - 1 && m_nextNavStrut != null)
				{
					var (_, to) =
						m_nextNavStrut.NavInStrut(m_currentNavStrut,
							m_path[nextPathIndex]); //Should be inside burst?
					m_currentNavStrut = m_nextNavStrut;
					m_nextNavStrut = null;
					SetupStrutValues(to);
				}
				else
				{
					// arrived
					m_currentNavStrut = null;
					m_body.isKinematic = false;
				}
			}
		}

		return true;
	}

	private static bool s_globallyDisableStuckMode = false;
	private static DebugConsole.Command s_disableStuckModeCmd = new DebugConsole.Command("disablestuckmode", _s => Utility.SetOrToggle(ref s_globallyDisableStuckMode, _s));

	private void CheckStuck(float _dt)
	{
		if (!IsTravelling)
			return;
		if (s_globallyDisableStuckMode)
			return;
		if (m_settings.m_stuckTimeExcessLimit > 10)
			return;
		if(m_allowStuck)
			return;
		
		var move = m_transform.position - m_previousPositionFixed;
		var targetMinMove = _dt * 0.5f * m_targetVelocity;
		
		var hasMovedSufficiently = Vector3.Dot(move, targetMinMove) >= targetMinMove.sqrMagnitude;
		var hasSufficientVelocity = GetVelocity().xzSqrMagnitude() > m_targetVelocity.xzSqrMagnitude() * .5f * .5f;

		if (HasColliderWithCharacterThisFrame)
			; // collided with a character this frame, don't advance or return stuck time
		else if (m_isStuck || hasSufficientVelocity)
			m_stuckTime = Mathf.Max(0, m_stuckTime - _dt*10);
		else
			m_stuckTime += _dt;

		if (m_stuckTime > m_settings.m_stuckTimeExcessLimit)
		{
			if (!m_isStuck)
				ToggleStuck(true);
		}

		m_previousPositionFixed = m_transform.position;
	}

	public void ToggleStuck(bool stuck)
	{
		if (stuck)
		{
			Debug.LogWarning($"NavAgent - {name} - {m_owner.m_ID} has been stuck for {m_stuckTime} seconds, going kinematic for 10 seconds.");

			m_isStuck = true;
			endStuck = Utility.After(10f, ()=>
			{
				endStuck = null;
				ToggleStuck(false);
			});
		}
		else
		{
			if (endStuck != null)
			{
				StopCoroutine(endStuck);
				endStuck = null;
			}

			m_isStuck = false;
		}
	}

	public void AllowStuck(bool _allowStuck)
	{
		m_allowStuck = _allowStuck;
	}

	private bool OnArrived()
	{
		if (m_cyclic == false)
		{
			if (m_currentNavStrut != null)
			{
				// finished walking on a strut
				m_currentNavStrut = null;
				m_body.isKinematic = false;
			}

			m_currentNavPos = -1;
			ClearNativePath();
			ZeroVelocity();
			m_stuckTime = 0;
			if ((m_navigator as Object) != null)
			{
				StartCoroutine(m_navigator.OnArrive(this, m_navigatorContext));
			}

			return true;
		}

		Debug.LogError($"{gameObject.name} - CHANGING STUFF");
		Vector3 toFirst = m_path[^1];
		NativeArray<float3>.Copy(m_path.AsArray(), 0, m_path.AsArray(), 1, m_path.Length - 1);
		m_path[0] = toFirst;
		return false;
	}

	private float SlowSpeedForEndOfPath(float _speed)
	{
		const float c_minSpeed = 1.5f;

		var destDist = math.length((m_path[^1] - (float3)m_transform.position).xz);

		if (_speed < c_minSpeed)
			return _speed;

		var maxSpeed = destDist * 30f;
		return Mathf.Min(_speed, maxSpeed);
	}

	private void UpdateTurning(Vector3 _fwd)
	{
		if (m_targetVelocity.sqrMagnitude > .01f * .01f)
		{
			if (m_instaTurn)
			{
				m_transform.forward = _fwd.normalized;
				m_instaTurn = false;
				m_onInstaTurn?.Invoke();
				m_onInstaTurn = null;
			}
			else
				m_transform.rotation = Quaternion.Slerp(m_transform.rotation,
					Quaternion.LookRotation(m_targetVelocity.GetXZ(), Vector3.up), .3f);
		}
	}

	private void CheckArrived()
	{
		if (!IsTravelling) return;
		var destDistSqr = math.lengthsq((m_path[^1] - (float3) m_transform.position).xz);
		if (destDistSqr < m_distanceToFinalPositionThreshold * m_distanceToFinalPositionThreshold)
			OnArrived();
	}

	private void EarlyUpdate()
	{
		var currentPos = m_transform.position;
		var move = currentPos - m_previousPosition;
		m_sqrDistanceMovedLastFrame = move.sqrMagnitude;
		m_previousPosition = currentPos;

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (!IsTravelling)
			return;
#else
		if (IsPaused)
			return;
#endif
		
		var speed = Mathf.Max(m_speed, s_minimumMaxSpeed);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		speed = SlowSpeedForEndOfPath(speed);
#endif
		m_finalSpeed = FinalSpeed(speed);

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		//KW: NavStrut update is now handled below
        //if (UpdateNavStrut())
        //    return;
        m_currentNavPos = PointOnPath(m_path, m_transform.position);
        //if (CheckNavStrut(m_currentNavPos))
        //    return;
        const float c_lookAheadSpeed = 8;
		m_currentLookAhead = c_lookAheadSpeed * c_lookAheadSeconds; //This is the *distance* we will look ahead
		var currentLookAheads = new[]{ m_currentLookAhead * 0.25f, m_currentLookAhead * 0.5f, m_currentLookAhead * 1f };
		float4 output = GlobalData.Me.GetLookAhead(m_path, m_transform.position, m_currentNavPos, currentLookAheads, m_costSet, 0, m_isUnderground);
		Vector3 destPos;
		if (output.y < 0)
		{
			var strut = NavStrut.s_allStruts[(int)-output.y - 2];
			var nextNavPoint = NextPointFromPath((int)output.y);
			var forwardPoint = nextNavPoint.y < 0 ? (int)nextNavPoint.y == (int)output.y ? GetPathEndPoint() : NavStrut.s_allStruts[(int)-nextNavPoint.y - 2].WorldCenter : nextNavPoint;
			var forwardSign = (strut.WorldStart - forwardPoint).xzSqrMagnitude() > (strut.WorldEnd - forwardPoint).xzSqrMagnitude() ? 1 : -1;
			destPos = strut.ClosestContainedPoint(m_transform.position, out var overflow, .2f * forwardSign);
			if (overflow) destPos = forwardPoint;
		}
		else
			destPos = output.xyz;
#else
		Vector3 destPos = currentPos;
		if (m_path.IsCreated && (m_path.Length > 0))
			destPos = m_path[0];
		var character = m_owner as MACharacterBase;
		if ((character != null) && (character.TargetObject != null))
			destPos = character.TargetObject.transform.position;
#endif
		
		m_actualPosition = m_transform.position;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		m_nearestPosition = PathPosition(m_currentNavPos);
#endif
		m_lookAheadPosition = destPos;
		
		//GameManager.Me.ClearGizmos($"{m_owner.m_ID}");
		//GameManager.Me.AddGizmoCube($"{m_owner.m_ID}", destPos.GroundPosition(0.5f), Vector3.one * 0.2f, Color.red);

		var fwd = (destPos - m_transform.position).GetXZNorm();
		var targetVel = m_finalSpeed * fwd;
		if (m_settings.m_shakyForwardYEnabled)
			targetVel = Quaternion.Euler(0, Random.Range(-1f, 1f) * 2f, 0) * targetVel;
		targetVel.y = 0f;
		SetTargetVelocity(targetVel);
		UpdateTurning(fwd);
	}

	private void FixedUpdate()
	{
		RunFixedUpdate(Time.fixedDeltaTime);
	}
	private void RunFixedUpdate(float _dt, bool _isFakeUpdate = false)
	{
		if (m_lastEarlyUpdateFrame < Time.frameCount)
			EarlyUpdate();
		m_lastEarlyUpdateFrame = Time.frameCount;
		
		CheckArrived();
		CheckStuck(_dt);
		if (m_isStuck)
			UpdateKinematicMovement(_dt);
		else
			TweakFwdFromCollisions(_isFakeUpdate);
		ResetMostUpwardNormal();
	}

	private void UpdateKinematicMovement(float _dt)
	{
		var newPos = transform.position + m_targetVelocity * _dt;
		
		var ray = new Ray(newPos + Vector3.up, Vector3.down);
		var hits = Physics.RaycastAll(ray, 100f);
		var highestY = float.NegativeInfinity;
		foreach (var hit in hits)
		{
			if (Physics.GetIgnoreLayerCollision(hit.transform.gameObject.layer, gameObject.layer))
				continue;
			if (Physics.GetIgnoreCollision(hit.collider, Collider))
				continue;
			if (hit.transform.IsChildOf(transform))
				continue;
			highestY = Mathf.Max(highestY, hit.point.y);
		}
		if (float.IsFinite(highestY))
			newPos.y = highestY;
		transform.position = newPos;
	}

	static bool s_disableStepDetect = false;
	static DebugConsole.Command s_disableStepDetectCmd = new("disablestepdetect", _s => Utility.SetOrToggle(ref s_disableStepDetect, _s), "Disables the nav/possess step detection system", "<bool>");

	static DebugConsole.Command s_maxForceMagnitudeCmd = new ("maxpushforce", _s =>
	{
		if (floatinv.TryParse(_s, out var f)) s_maxForceMagnitude = f;
		else s_maxForceMagnitude = 20;
	}, "Defines the maximum force that will be applied in pushing characters up steps or slopes", "<float,.1,10>");
	static float s_maxForceMagnitude = 20;
	static DebugConsole.Command s_constForceMagnitudeCmd = new("constpushforce", _s =>
	{
		if (floatinv.TryParse(_s, out var f)) s_constantForceMagnitude = f;
		else s_constantForceMagnitude = 20;
	}, "Defines the constant force that will be applied in pushing characters up steps or slopes", "<float,0,10>");
	static float s_constantForceMagnitude = 20;

	public class TerrainCollisionInfo
	{
		public Vector3 position = Vector3.zero;
		public Vector3 force = Vector3.zero;
		public Vector3 normal = Vector3.zero;
		public bool isStep = false;
		public bool isSteepSlope = false;
		public bool isFloorOrShallowSlope = false;
	};
	private List<TerrainCollisionInfo> m_collisions = new();
	private Vector3 previousThirdPersonVelocity = Vector3.zero;
	private bool shouldLerpThirdPersonVelocity = false;
	private float resetThirdPersonVelocityLerpElapsedTime = 0f;
	private void TweakFwdFromCollisions(bool _isFakeUpdate)
	{
		var vel = m_targetVelocity;
		var targetSpeed = vel.magnitude;
		var currentVel = GetVelocity();
		var steepSlopes = new List<Vector3>();
		Vector3 steepSlopeNorm = Vector3.zero;
		bool isStep = false;
		float stepBoostUp = 5f;
		foreach (var coll in m_collisions)
		{
			if (coll.isStep)
			{
				isStep = true;
				vel += Vector3.up * stepBoostUp * Mathf.Clamp01(targetSpeed / 10f);
			}

			if (coll.isSteepSlope)
			{
				steepSlopes.Add(coll.position);
				steepSlopeNorm += coll.normal;
			}
		}

		m_collisions.Clear();

		if (IsTravelling)
		{
			var facingMult = Mathf.Max(0.2f, Vector3.Dot(vel.GetXZNorm(), m_transform.forward.GetXZNorm()));
			vel.x *= facingMult;
			vel.z *= facingMult;
		}

		float dotVelNorm = 0f;
		bool isSteepSlope = steepSlopes.Count > 0;
		bool isPossessed = GameManager.Me.IsPossessed(m_owner);
		if (isPossessed)
		{
			if (!isSteepSlope && !isStep)
			{
				var dir = vel.normalized;
				if (vel.sqrMagnitude > (0.01f * 0.01f))
				{
					float stepHeight = m_settings.m_colliderBumpUpMaxHeight;
					float dist = shouldLerpThirdPersonVelocity ? 2f : 1f;
					float upOffset = shouldLerpThirdPersonVelocity ? 1f : (stepHeight * 0.5f);
					if (Physics.Raycast(transform.position + (Vector3.up * upOffset), dir, out var hit, dist, terrainMask))
					{
						var collisionInfo = CalculateTerrainTypeCollision(hit.point, Vector3.zero);
						if (shouldLerpThirdPersonVelocity)
						{
							if (collisionInfo.isSteepSlope)
							{
								isSteepSlope = true;
								steepSlopes.Add(collisionInfo.position);
								steepSlopeNorm += collisionInfo.normal;
							}
						}
						else if (collisionInfo.isStep)
						{
							isStep = true;
							vel += Vector3.up * stepBoostUp * Mathf.Clamp01(targetSpeed / 10f);
						}
					}
				}
			}

			if (isSteepSlope)
			{
				var norm = steepSlopeNorm.normalized;
				dotVelNorm = Vector3.Dot(m_targetVelocity, norm);
				if (dotVelNorm <= 0f)
				{
					var proj = Vector3.ProjectOnPlane(vel, norm);
					if (proj.y > 0f)
					{
						proj.y = -proj.y;
						proj.x *= 0.5f;
						proj.z *= 0.5f;
					}
					if (proj.y > -1f)
						proj.y = -5f;
					if ((steepSlopes.Count > 1) && (proj.y > -5f))
						proj.y = -5f;
					vel = proj;
				}
			}
		}

		if (!isStep && (currentVel.y < -0.1f) && (vel.y > currentVel.y))
			vel.y = currentVel.y;
		
		if (isPossessed)
		{
			if (dotVelNorm > 0f)
			{
				resetThirdPersonVelocityLerpElapsedTime = 0f;
				shouldLerpThirdPersonVelocity = false;
			}
			if (shouldLerpThirdPersonVelocity)
			{
				vel.y = Mathf.Lerp(previousThirdPersonVelocity.y, vel.y, 0.01f);
				vel.x = Mathf.Lerp(previousThirdPersonVelocity.x, vel.x, 0.1f);
				vel.z = Mathf.Lerp(previousThirdPersonVelocity.z, vel.z, 0.1f);
				previousThirdPersonVelocity = vel;

				if (resetThirdPersonVelocityLerpElapsedTime >= 0.5f)
				{
					resetThirdPersonVelocityLerpElapsedTime = 0f;
					if (dotVelNorm >= 0f)
						shouldLerpThirdPersonVelocity = false;
				}
				else
				{
					resetThirdPersonVelocityLerpElapsedTime += Time.fixedDeltaTime;
				}
			}
			if (dotVelNorm < 0f)
			{
				resetThirdPersonVelocityLerpElapsedTime = 0f;
				previousThirdPersonVelocity = vel;
				shouldLerpThirdPersonVelocity = true;
			}
		}

		// Debug.DrawRay(transform.position, vel * 1f, Color.blue);
		// Debug.DrawRay(transform.position, steepSlopeNorm.normalized * 10f, Color.magenta);

		var shoveVel = m_shoveDir * m_shoveSpeed;
		if (_isFakeUpdate == false)
			SetVelocity(vel + shoveVel);
		m_shoveSpeed -= m_shoveSpeed * Time.fixedDeltaTime * 2f;
		if (m_shoveSpeed <= 0.1f)
		{
			m_shoveSpeed = 0f;
			m_shoveDir = Vector3.zero;
		}
	}

	private int m_lastNavGenFrame = 0;

	public class NavInfo
	{
		public float m_finalPosOffsetDistanceXZSqr;
		public GlobalData.NavJob.EResult m_result;
	}

	private void CheckRenewNavRequest()
	{
		if (m_settings.m_autoNavigationEnabled && m_lastNavGenFrame < GlobalData.Me.m_navGenerationFrame && !GameManager.Me.IsRoadEdit)
		{
			FindPathAsync(m_transform.position, BuildingNav.RenavLookup(m_originalTargetPosition), m_originalCorridorWidth);
		}
	}

	private void LateUpdate()
	{
		if (m_lastEarlyUpdateFrame < Time.frameCount)
			RunFixedUpdate(0, true);
		
		m_debug = DebugInfo();

		SetAnimatorSpeed();

		if (IsNavigating)
			CheckRenewNavRequest();
	}

	private void ProcessPath()
	{
		if (ComplexPathExists) //If it's direct (i.e. length 1), no point jittering
			GlobalData.JitterPath(m_path, m_settings.m_constantJitter, m_settings.m_localJitter);
	}

	private void SetAnimatorSpeed()
	{
		m_owner.SetTravelSoundSpeed(GetVelocity().magnitude);
		var controller = (m_owner is MACharacterBase character) ? character.m_ragdollController : null;
        if (controller == null && !(m_owner is MAAnimal))
        {
            return;
        }
        var onFeet = controller == null || controller.IsAnimated || controller.IsResponsive;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		var falling = GetVelocity().y < -5f;// || transform.position.y > transform.position.GroundPosition(2f).y; // GL - removing this until we can get a better test; it's causing issues with characters who are on static geometry that isn't terrain
#else
		var falling = false;
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		onFeet &= !falling;
		if (!onFeet)
			SetAnimatorSpeed(Vector3.zero);
		else
			SetAnimatorSpeed(m_body.isKinematic ? m_targetVelocity : GetVelocity());
	}

	public void SetAnimatorSpeed(Vector3 _vel)
	{
		var fwd = Vector3.Dot(_vel, transform.forward);
		// RW-24-JUL-25: Avoid using back or sideways motion, now we only use forward-facing loco.
		// Probably need to clean up the back and side anims in the AC at some point.
		fwd = Mathf.Max(fwd, 0);
		var side = 0;//Vector3.Dot(_vel, transform.right);
		animSpeeds[animSpeedIndex] = new Vector2(fwd, side);
		animSpeedIndex = ++animSpeedIndex % animSpeeds.Length;

		Vector2 maxVel = new(0f, 0f);
		foreach (var aS in animSpeeds)
		{
			if (Mathf.Abs(aS.x) > Mathf.Abs(maxVel.x))
				maxVel.x = aS.x;
			if (Mathf.Abs(aS.y) > Mathf.Abs(maxVel.y))
				maxVel.y = aS.y;
		}

		if (!lastAnimSpeed.sqrMagnitude.IsZero())
			maxVel = Vector2.Lerp(lastAnimSpeed, new Vector2(fwd, side), 0.3f);
		lastAnimSpeed = maxVel;

		if (lastAnimSpeed.x is < 0.01f and > -0.01f)
			lastAnimSpeed.x = 0f;
		if (lastAnimSpeed.y is < 0.01f and > -0.01f)
			lastAnimSpeed.y = 0f;

		if (m_owner != null)
		{
			var scaleFactor = 1.2f / m_owner.transform.lossyScale.z;
			var finalSpeed = lastAnimSpeed.x * scaleFactor * c_speedAnimMultiplier;
			var finalSideSpeed = lastAnimSpeed.y * scaleFactor * c_speedAnimMultiplier;
			var idleness = 1f - Mathf.Clamp01(lastAnimSpeed.sqrMagnitude);

			m_owner.SetNavAnimatorValues(finalSpeed, finalSideSpeed, idleness);
		}
	}

	private bool AssertCollisionAllowed(string _colliderName)
	{
		foreach (var str in m_settings.m_avoidHandleCollisionsWithNamesContaining)
		{
			if (_colliderName.Contains(str))
				return false;
		}

		return true;
	}

	private static Dictionary<GameObject, bool> s_collisionAllowedCache = new();
	private bool AssertCollisionAllowed(GameObject _obj)
	{
		if (s_collisionAllowedCache.TryGetValue(_obj, out var allowed) == false)
			s_collisionAllowedCache[_obj] = allowed = AssertCollisionAllowed(_obj.name);
		return allowed;
	}

	private (Vector3 point, Vector3 force) AverageContacts(Collision _cll) // GL - uses contactCount/GetContact to avoid the expensive and garbagy .contacts array creation
	{
		Vector3 totForce = Vector3.zero, avgPoint = Vector3.zero;
		var count = _cll.contactCount;
		for (int i = 0; i < count; ++i)
		{
			var contact = _cll.GetContact(i);
			totForce += contact.impulse;
			avgPoint += contact.point;
		}

		avgPoint /= count;
		totForce /= m_body.mass;

		return (avgPoint, totForce);
	}

	private void AddCollision(Vector3 _point, Vector3 _force, Collider _coll)
	{
		if (GameManager.Me.IsPossessed(m_owner))
			HandleCollisionComplex(_point, _force, _coll);
		else
			HandleCollisionSimple(_point, _force, _coll);
	}

	private void HandleCollisionComplex(Vector3 _point, Vector3 _force, Collider _coll)
	{
		if ((terrainMask & (1 << _coll.gameObject.layer)) != 0)
			m_collisions.Add(CalculateTerrainTypeCollision(_point, _force));
		else
			HandleCollisionSimple(_point, _force, _coll);
	}

	private const float c_rayHeight = 6f;
	private const float steepSlopeMinAngle = 45f;
	private TerrainCollisionInfo CalculateTerrainTypeCollision(Vector3 _point, Vector3 _force)
	{
		float stepHeight = m_settings.m_colliderBumpUpMaxHeight;
		var stepEdge = transform.position.y + stepHeight;
		var collisionInfo = new TerrainCollisionInfo
		{
			position = _point,
			force = _force
		};

		// var shallowSlopeColor = Color.cyan;
		// var steepSlopeColor = Color.magenta;
		// var stepColor = Color.yellow;

		var pointNormal = GetAverageSurfaceNormal(_point);
		var nextPos = _point;
		nextPos.y = transform.position.y;
		var pointDist = nextPos - transform.position;
		if (pointDist.sqrMagnitude < (0.05f * 0.05f))
		{
			collisionInfo.normal = pointNormal;
			collisionInfo.isFloorOrShallowSlope = true;
			return collisionInfo;
		}

		var offsetUp = Vector3.up * c_rayHeight;
		bool blocked1 = Physics.Raycast(nextPos + offsetUp, Vector3.down,
			out var hitInfo1, c_rayHeight, terrainMask, QueryTriggerInteraction.Ignore);
		if (blocked1)
		{
			var averageNormal1 = GetAverageSurfaceNormal(hitInfo1.point);
			var contactPoint = _point;
			contactPoint.y = transform.position.y;
			var dirFromContact = (transform.position - contactPoint).normalized;
			float normPosDot = Vector3.Dot(averageNormal1, dirFromContact);
			if (normPosDot < 0f)
			{
				if (hitInfo1.point.y > (transform.position.y + 2f))
				{
					// Debug.DrawRay(hitInfo1.point, hitInfo1.normal * 2f, steepSlopeColor);

					collisionInfo.normal = dirFromContact;
					collisionInfo.isSteepSlope = true;
					return collisionInfo;
				}
			}
			
			bool isStep1 = (hitInfo1.point.y <= stepEdge) && ((hitInfo1.point.y - transform.position.y) > 0.2f);
			var surfaceRight = Vector3.Cross(Vector3.up, hitInfo1.normal);
			var surfaceForward = Vector3.Cross(hitInfo1.normal, surfaceRight);
			var nextPos2 = _point + (surfaceForward * 0.5f);
			nextPos2.y = transform.position.y;
			bool blocked2 = Physics.Raycast(nextPos2 + offsetUp, Vector3.down,
				out var hitInfo2, c_rayHeight, terrainMask, QueryTriggerInteraction.Ignore);
			if (blocked2)
			{
				var averageNormal2 = GetAverageSurfaceNormal(hitInfo2.point);
				bool isStep2 = (hitInfo2.point.y <= stepEdge) && (Mathf.Abs(hitInfo2.point.y - hitInfo1.point.y) < 0.05f);
				if (isStep1 && isStep2)
				{
					// Debug.DrawRay(hitInfo2.point, hitInfo2.normal * 2f, stepColor);

					collisionInfo.normal = Vector3.up;
					collisionInfo.isStep = true;
					return collisionInfo;
				}

				if (hitInfo2.point.y > stepEdge)
				{
					var nextPos2F = hitInfo2.point;
					nextPos2F.y = stepEdge + 0.2f;
					var rayDir = nextPos2F - transform.position;
					rayDir.y = 0f;
					rayDir = rayDir.normalized;
					var averageNormal2F = GetAverageSurfaceNormal(nextPos2F, rayDir);
					float angle = Vector3.Angle(averageNormal2F, Vector3.up);
					bool isWall = angle >= steepSlopeMinAngle;
					if (isWall)
					{
						// Debug.DrawRay(nextPos2F, averageNormal2F * 2f, steepSlopeColor);

						collisionInfo.normal = dirFromContact;
						collisionInfo.isSteepSlope = true;
						return collisionInfo;
					}
				}

				var nextPos3 = _point + (surfaceForward * 1f);
				nextPos3.y = transform.position.y;
				bool blocked3 = Physics.Raycast(nextPos3 + offsetUp, Vector3.down,
					out var hitInfo3, c_rayHeight, terrainMask, QueryTriggerInteraction.Ignore);
				if (blocked3)
				{
					var averageNormal3 = GetAverageSurfaceNormal(hitInfo3.point);
					var norm = averageNormal1 + averageNormal2 + averageNormal3;
					norm = norm.normalized;
					float angle = Vector3.Angle(norm, Vector3.up);
					float angleNorm3 = Vector3.Angle(averageNormal3, Vector3.up);
					if (angleNorm3 > angle)
					{
						norm = averageNormal3;
						angle = angleNorm3;
					}
					bool isSteepSlope3 = (angle >= steepSlopeMinAngle) && (norm.y > 0f);
					if (isSteepSlope3)
					{
						// Debug.DrawRay(hitInfo3.point, norm * 2f, steepSlopeColor);

						collisionInfo.normal = norm;
						collisionInfo.isSteepSlope = true;
						return collisionInfo;
					}

					var nextPos4 = _point + (surfaceForward * 1.5f);
					nextPos4.y = transform.position.y;
					bool blocked4 = Physics.Raycast(nextPos4 + offsetUp, Vector3.down,
						out var hitInfo4, c_rayHeight, terrainMask, QueryTriggerInteraction.Ignore);
					if (blocked4)
					{
						var averageNormal4 = GetAverageSurfaceNormal(hitInfo4.point);
						norm += averageNormal4;
						norm = norm.normalized;
						angle = Vector3.Angle(norm, Vector3.up);
						bool isSteepSlope4 = (angle >= steepSlopeMinAngle) && (norm.y > 0f);
						if (isSteepSlope4)
						{
							// Debug.DrawRay(hitInfo4.point, norm * 2f, steepSlopeColor);

							collisionInfo.normal = norm;
							collisionInfo.isSteepSlope = true;
							return collisionInfo;
						}

						var nextPos5 = _point + (surfaceForward * 2f);
						nextPos5.y = transform.position.y;
						bool blocked5 = Physics.Raycast(nextPos5 + offsetUp, Vector3.down,
							out var hitInfo5, c_rayHeight, terrainMask, QueryTriggerInteraction.Ignore);
						if (blocked5)
						{
							var averageNormal5 = GetAverageSurfaceNormal(hitInfo5.point);
							norm += averageNormal5;
							norm = norm.normalized;
							angle = Vector3.Angle(norm, Vector3.up);
							bool isSteepSlope5 = (angle >= steepSlopeMinAngle) && (norm.y > 0f);
							if (isSteepSlope5)
							{
								// Debug.DrawRay(hitInfo5.point, norm * 2f, steepSlopeColor);

								collisionInfo.normal = norm;
								collisionInfo.isSteepSlope = true;
								return collisionInfo;
							}
						}
					}
				}
			}
		}
		
		// Debug.DrawRay(_point, pointNormal * 2f, shallowSlopeColor);
		collisionInfo.normal = pointNormal;
		collisionInfo.isFloorOrShallowSlope = true;
		return collisionInfo;
	}

	private Vector3 GetAverageSurfaceNormal(Vector3 point, Vector3? frontDir = null)
    {
        Vector3 norm = Vector3.zero;
		
		bool isFront = frontDir.HasValue;
		float dist = isFront ? 5f : c_rayHeight;
		var dir = isFront ? frontDir.Value : Vector3.down;
		int gridSize = 5;
        for (int i = -(gridSize + 2); i <= (gridSize + 2); ++i)
        {
            for (int j = -gridSize; j <= gridSize; ++j)
			{
				var offset = isFront ? new Vector3(i * 0.1f, j * 0.1f, 0f) : new Vector3(i * 0.1f, 0f, j * 0.1f);
				Vector3 pos = point - (dir * dist) + offset;
				if (Physics.Raycast(pos, dir, out var hit, dist + 1f, terrainMask, QueryTriggerInteraction.Ignore))
				{
					// Debug.DrawRay(hit.point, dir * -0.25f, Color.green);
					norm += hit.normal;
				}
				else
				{
					// Debug.DrawRay(pos + (dir * (dist + 1f)), dir * -0.25f, Color.red);
				}
			}
        }

        return norm.normalized;
    }

	private void HandleCollisionSimple(Vector3 _point, Vector3 _force, Collider _coll)
	{
		const float c_maxLookAhead = 0.5f;
		const float c_timeAhead = .5f; //Looking ahead further reduces the max slope we can climb

		var lookAhead = m_targetVelocity.GetXZ() * c_timeAhead;
		if (lookAhead.sqrMagnitude > c_maxLookAhead * c_maxLookAhead)
			lookAhead = lookAhead.normalized * c_maxLookAhead;
		
		var nextPos = _point + lookAhead;
		nextPos.y = transform.position.y;
		var ray = new Ray(nextPos + Vector3.up * c_rayHeight, Vector3.down);
		var blocked = _coll.Raycast(ray, out var hitInfo, c_rayHeight);
		var step = blocked && hitInfo.point.y <= transform.position.y + m_settings.m_colliderBumpUpMaxHeight;
		//GameManager.Me.AddGizmoLine($"{m_owner.m_ID}", ray.origin, ray.origin + Vector3.down * c_rayHeight, step ? Color.black : Color.white);
		m_collisions.Add(new TerrainCollisionInfo
			{
				position = _point,
				force = _force,
				isStep = step
			});
	}

	private int m_collidedWithCharacterFrame;
	private Dictionary<GameObject, bool> m_isCharacterCache = new();
	private void CheckCharacterCollision(GameObject _other)
	{
		if (m_isCharacterCache.TryGetValue(_other, out var isCharacter) == false)
			m_isCharacterCache[_other] = isCharacter = _other.GetComponent<MACharacterBase>() != null;
		if (isCharacter)
			m_collidedWithCharacterFrame = Time.frameCount;
	}
	private bool HasColliderWithCharacterThisFrame => m_collidedWithCharacterFrame == Time.frameCount;

	private Vector3 m_mostUpwardNrm;
	private void ResetMostUpwardNormal()
	{
		m_mostUpwardNrm = Vector3.zero;
	}
	private void CheckMostUpwardNormal(Collision _c)
	{
		var count = _c.contactCount;
		for (int i = 0; i < count; ++i)
		{
			var nrm = _c.GetContact(i).normal;
			if (nrm.y > m_mostUpwardNrm.y)
				m_mostUpwardNrm = nrm;
		}
	}

#if UNITY_EDITOR
	private int m_collisionInfoFrame = 0;
	private string m_collisionInfo = ""; public string CollisionInfo => m_collisionInfo;
	private List<(GameObject, Vector3, float)> m_collisionObjects = new(); public List<(GameObject, Vector3, float)> CollisionObjects => m_collisionObjects;
#endif
	private void HandleCollision(Collision _c)
	{
#if UNITY_EDITOR
		if (Selection.activeGameObject == gameObject)
		{
			if (m_collisionInfoFrame != Time.frameCount)
			{
				m_collisionInfo = $"TgtV:{m_targetVelocity:n2}   PhysV:{m_body.linearVelocity:n2}\nStuck:{m_stuckTime:n2}\n\n";
				m_collisionInfoFrame = Time.frameCount;
				m_collisionObjects.Clear();
			}
			for (int i = 0; i < _c.contactCount; ++i)
				m_collisionObjects.Add((_c.GetContact(i).otherCollider.gameObject, _c.GetContact(i).impulse, _c.GetContact(i).separation));
		}
#endif
		
		CheckMostUpwardNormal(_c);

		bool isPossessed = GameManager.Me.IsPossessed(m_owner);
		
		if (!isPossessed)
		{
			if (m_targetVelocity.xzSqrMagnitude() < 0.1f * 0.1f)
				return;
		}

		if (!m_settings.m_isRoadVehicle)
		{
			const float dotThreshold = 0f; //Mathf.Sin(Mathf.Deg2Rad * 0f);

			var (avgPoint, totForce) = AverageContacts(_c);
			var avgNormal = totForce.normalized;


			if (!isPossessed)
			{
				var dotCollisionVsVel = Vector3.Dot(m_targetVelocity.normalized, avgNormal);
				if (dotCollisionVsVel >= dotThreshold)
					return;
			}

			var gO = _c.gameObject;
			CheckCharacterCollision(gO);
			if (AssertCollisionAllowed(gO) == false)
				return;

			AddCollision(avgPoint, totForce, _c.collider);
		}
		else if (GetVelocity().y > 0.01f)
		{
			var gO = _c.gameObject;
			CheckCharacterCollision(gO);
			if (AssertCollisionAllowed(gO) == false)
				return;

			Vector3 v = GetVelocity().NewY(0f);
			SetVelocity(v);
		}
	}

	private void OnCollisionEnter(Collision _c)
	{
		if (_c.rigidbody == null)
		{
			m_staticColliderContactCount++;
		}

		HandleCollision(_c);
	}

	private void OnCollisionStay(Collision _c)
	{
		HandleCollision(_c);
	}

	private void OnCollisionExit(Collision _c)
	{
		if (_c.rigidbody == null)
		{
			m_staticColliderContactCount--;
		}
	}

#if UNITY_EDITOR
	void OnDrawGizmosSelected()
	{
		if (GlobalData.Me?.m_drawAllNavPaths ?? false) return;
		DrawPathGizmo();
	}

	void OnDrawGizmos()
	{
		// Handles.color = Color.green;
		// Handles.DrawLine(transform.position, transform.position + (m_body.linearVelocity * 100f));

		if (GlobalData.Me != null)
		{
			Gizmos.color = Color.white;
			Gizmos.DrawSphere(m_actualPosition.GroundPosition(), .12f);
			Gizmos.color = Color.red;
			Gizmos.DrawSphere(m_nearestPosition.GroundPosition(), .11f);
			Gizmos.color = Color.green;
			Gizmos.DrawSphere(m_lookAheadPosition.GroundPosition(), .1f);

			if (!GlobalData.Me.m_drawAllNavPaths) return;

			DrawPathGizmo();
		}
	}

	void DrawPathGizmo()
	{
		if (PathExists)
		{
			Handles.color = Color.green;
			GlobalData.DrawPathGizmo(GlobalData.NativePathToList(m_path));
			Handles.color = Color.cyan;
			var vf = GetVelocity();
			vf.y = 0;
			vf.Normalize();
			Handles.DrawLine(m_body.position, m_body.position + vf, 3f);
			var currentNavPos = PointOnPath(m_path, m_transform.position);
			var destNavPos = Mathf.Min(currentNavPos + m_currentLookAhead, m_path.Length - 1 - .001f);
			var p1 = PathPosition(currentNavPos);
			p1.y = GlobalData.Me.GetHeight(p1);
			var p2 = PathPosition(destNavPos);
			p2.y = GlobalData.Me.GetHeight(p2);
			Gizmos.color = Color.white;
			Gizmos.DrawCube(p1, Vector3.one * .1f);
			Gizmos.color = Color.red;
			Gizmos.DrawCube(p2, Vector3.one * .1f);
		}
	}
#endif
}