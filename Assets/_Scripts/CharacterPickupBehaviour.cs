using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CharacterPickupBehaviour : MonoBehaviour, IPickupBehaviour
{
	[SerializeField]
	private bool m_DebugEasyRagDollThrow = false;
	
	public AkEventHolder m_putDownAudio;
	public AkEventHolder m_throwAudio;
	
	private bool m_held = false;
	private bool m_falling = false;
	private NGMovingObject m_movingObject;
	private bool m_discreteCollisionDetectionMode = false;
	
	public bool AllowPutDown => true;
	public float PutDownReleaseHeight => 3f;
	
	public bool Held => m_held;
	public bool Falling => m_falling;
	
    private void Awake()
    {
		m_movingObject = GetComponent<NGMovingObject>();
		if (gameObject.GetComponent<CameraSoundOcclusion>() == null)
		{
			gameObject.AddComponent<CameraSoundOcclusion>();
		}
	}
    public bool DropNoBezier()
    {
	    return false;
    }
    
    //===== IPickupBehaviour
    public void OnPickup() {
		if(m_movingObject == null)
			m_movingObject = GetComponent<NGMovingObject>();
		
		var rc = m_movingObject.GetComponentInChildren<RagdollController>();
		if (rc != null)
		{
			rc.PrepareForDraggedState();
		}
		m_movingObject.m_nav.ToggleStuck(false);

		var rb = GetComponent<Rigidbody>();
		if(rb)
		{
			if(rb.collisionDetectionMode == CollisionDetectionMode.ContinuousDynamic && rb.isKinematic == false)
			{//this is to suppress an extremely spammy & pointless internal Unity LogError that happens when you set isKinematic to true while in continuous collision detection mode, hoping Unity will remove that
				m_discreteCollisionDetectionMode = true;
				rb.collisionDetectionMode = CollisionDetectionMode.Discrete;
			}
			rb.isKinematic = true;
		}
		m_held = true;
		
		AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_PickUp", transform);
		
		m_movingObject.m_nav.PushPause("HeldByPlayer", true, true);
		m_movingObject.PrepareForDropInGrey();
		m_movingObject.SetHeldMode(true);
		m_movingObject.SetupHeldByPlayer(null);
    }
	void ThrowCarrying(Vector3 _vel) {
	}
	
	public void OnDrop(Vector3 _pos, GameObject _target, GameObject _source, Vector3 _smoothedDragMove, bool _undo, SpecialHandlingAction _action) {
		foreach(Transform child in _source.transform)
			child.GetComponentInChildren<NGPickupThing>()?.DestroyMe();
		
		
		KeyboardShortcutManager.Me.PopShortcuts(KeyboardShortcutManager.EShortcutType.HeldObjects);
		MACharacterBase character = _source.GetComponent<MACharacterBase>();
		
		character?.SetHeldMode(false);
		
		GetComponentInChildren<NGPickupBase>()?.DestroyMe();
		var assignAction = _action ?? GetRestrictedAction();
		m_held = false;
		m_falling = true;
		float workerHeight = transform.position.y - GameManager.Me.HeightAtPoint(transform.position);
		
		var rb = GetComponent<Rigidbody>();
		if(rb)
		{
			rb.isKinematic = false;
			if(m_discreteCollisionDetectionMode)
			{
				rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
				m_discreteCollisionDetectionMode = false;
			}
		}

		// make sure we don't drop a character into unnavable positions
		var currentPos = transform.position;
		var dropPos = GlobalData.Me.GetClosestLegalCharacterDropPoint(currentPos);
		if ((dropPos - currentPos).xzSqrMagnitude() > .25f * .25f)
		{
			transform.position = dropPos;
			if (rb != null) rb.position = dropPos;
		}

		// Do the assignment immediately
		NGCommanderBase building = null;
		MADecorationActionBase dab = null;

		if (_target != null)
		{
			building = _target.GetComponentInChildren<NGCommanderBase>();
			dab = _target.GetComponentInChildren<MADecorationActionBase>();
		}

		MADropTargetInfo dropTargetInfo = null;

        if (dab != null)
		{
			character.FinallyDroppedByPlayer(dab, assignAction);
			dropTargetInfo = dab.GetDropTargetInfo(_source);
		}
		else
		{
			character.FinallyDroppedByPlayer(building, assignAction);
		}

		if (dropTargetInfo != null)
		{
			AnimationOverride ao = null;

			if (!string.IsNullOrEmpty(dropTargetInfo.m_landAnim))
			{
				ao = AnimationOverride.PlayClip(_source.GetComponentInChildren<Animator>().gameObject, null, null, "WorkerFallingLoop", OnLoopCB, dropTargetInfo.m_landAnim, OnDropTargetComplete);

				//if (character?.RigidBody != null)
				//{
				//	Utility.LerpRigidbody(character.RigidBody, dropTargetInfo.m_position, dropTargetInfo.m_rotation, 0.25f, () =>
				//	{
				//		if (ao != null)
				//		{
				//			ao.Interrupt(false);
				//		}
				//	});
				//}
				//else
                {
					Utility.LerpTransform(_source.transform, dropTargetInfo.m_position, dropTargetInfo.m_rotation, 0.5f, () =>
					{
						if (ao != null)
						{
							ao.Interrupt(false);
						}

						m_falling = false;
						dab.OnDropTargetComplete();
					}, 4.0f);
				}

				void OnLoopCB(bool _interrupted)
				{
					AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Land", transform);
				}

				void OnDropTargetComplete(bool _interrupted)
				{
					//m_falling = false;
					//dab.OnDropTargetComplete();
				}
			}
		}
		else
		{
			var vel = Vector3.zero; 
			vel = RagdollHelper.ThrowObject(_source, _smoothedDragMove, "WorkerFallingLoop", "WorkerLanding",
				OnRagDollCompleteAndBlendedToAnimation, true, m_DebugEasyRagDollThrow, () =>
				{
					AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Land", transform);
				},
				OnRagDollStationary);
			
			if ((character != null) && (character.IsIncapacitated || character.m_state == NGMovingObject.STATE.MA_DEAD))
				vel = Vector3.zero;

			void OnRagDollStationary()
			{
				if (_source == null)
					return;
				
				m_falling = false;
				MACharacterBase character = _source.GetComponent<MACharacterBase>();
				character.m_ragdollController.RemoveRagDollStationaryListener(OnRagDollStationary);
				
				float damage = vel.magnitude * NGManager.Me.m_characterThrowDamageMultiplier;
				character.ApplyDamageEffect(IDamageReceiver.DamageSource.ThrownByHand, damage, character.transform.position);
			}

			void OnRagDollCompleteAndBlendedToAnimation(bool _interrupted)
			{
				if (_source == null)
					return;
				
				MACharacterBase character = _source.GetComponent<MACharacterBase>();
				if ((character.m_state == NGMovingObject.STATE.HELD_BY_PLAYER) || character.InState(CharacterStates.HeldByPlayer))
					return;
				
				character.m_nav.PopPause("HeldByPlayer");
				character.m_nav.RefreshNavigation();
				m_falling = false;

				if ((character.m_state == NGMovingObject.STATE.MA_WAITING_TO_TELEPORT) || character.InState(CharacterStates.WaitingToTeleport))
					character.StartTeleport();
			}
		}

		var isRagdoll = RagdollHelper.IsOrWillBeRagdoll(_source);
		AudioClipManager.Me.SetWorkerHeight(workerHeight, gameObject);
		var eventName = isRagdoll ?
			AkEventHolder.SafeEvent(m_throwAudio, "PlaySound_HandThrowItem") :
			AkEventHolder.SafeEvent(m_putDownAudio, "PlaySound_HandDropItem");
		if (character != null && character.IsAlive)
			AudioClipManager.Me.PlaySound(eventName, transform);
	}	
	
	public Vector3 Throw(Vector3 _landPos, float _speed) {
		if (m_falling) return Vector3.zero;
		m_held = false;
		m_falling = true;
		float workerHeight = transform.position.y - GameManager.Me.HeightAtPoint(transform.position);
		AudioClipManager.Me.SetWorkerHeight(workerHeight, gameObject);
		AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Drop", transform);
		var vel = RagdollHelper.ThrowObjectRagdoll(gameObject, _landPos, _speed, (_b) => {
			m_falling = false;
		}, () => 
		{ 
			AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Land", transform); 
		});
		if (RagdollHelper.IsOrWillBeRagdoll(gameObject))
			ThrowCarrying(vel);
		return vel;
	}
	
	public Vector3 HandleCollision(Vector3 _vel, float _throwTime = 3f, float _throwSpeedMultiplier = .7f) {
		if (m_falling) return Vector3.zero;
		var vel = _vel; vel.y = 0;
		if (vel.sqrMagnitude < .01f*.01f) return Vector3.zero;
		AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_CarHit", transform);
		var speed = vel.magnitude;
		var landPos = transform.position + vel * _throwTime;

		if (m_movingObject != null)
		{
			m_movingObject.m_nav.PushPause("HeldByPlayer", true, true);
			m_movingObject.SetupHeldByPlayer(null);
		}
		return Throw(landPos.GroundPosition(), speed * _throwSpeedMultiplier);
	}
	static DebugConsole.Command s_cll = new DebugConsole.Command("throw", _s => {
		var v = Vector3.zero;
		var bits = _s.Split(',');
		if (bits.Length < 2) return;
		v.x = float.Parse(bits[0]); v.z = float.Parse(bits[1]);
		float throwTime = 3f, throwSpeedMult = .7f;
		if (bits.Length >= 3) throwTime = float.Parse(bits[2]);
		if (bits.Length >= 4) throwSpeedMult = float.Parse(bits[3]);
		foreach (Transform t in GlobalData.Me.m_characterHolder) {
			if (t.gameObject.activeSelf) {
				var cc = t.GetComponent<CharacterPickupBehaviour>();
				if (cc != null && !cc.Falling) {
					cc.HandleCollision(v, throwTime, throwSpeedMult);
					return;
				}
			}
		}
	});
	
	public SpecialHandlingAction GetRestrictedAction()
	{
		if(m_movingObject as MATourist != null)
			return null;

		var worker = m_movingObject.GetComponent<MAWorker>();
		if(worker != null)
		{
			if(EKeyboardFunction.AssignJob.IsHeld())
				return SpecialHandlingAction.AssignJob;
			if(EKeyboardFunction.AssignHome.IsHeld())
				return SpecialHandlingAction.AssignHome;
			if(worker.Job == null)
				return SpecialHandlingAction.AssignJob;
			if(worker.Home == null)
				return SpecialHandlingAction.AssignHome;
		}
		var hero = m_movingObject.GetComponent<MAHeroBase>();
		if(hero != null)
		{
			if(hero.Home == null)
				return SpecialHandlingAction.AssignHome;
		}
		return null;
	}

	public bool UseObjectPosition()
    {
		return true;	// This seems more consistent in general
		return m_movingObject is MATourist;
    }

	public GameObject GetBestTarget(int _inputId, GameObject _obj, Vector3 _pos, out SpecialHandlingAction _action) 
	{
		var rob = _obj.GetComponentInChildren<NGMovingObject>();

		var restrictionAction = GetRestrictedAction();

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		MAMessageManager.Me.ShowQuickTip();
#endif

		bool useObjectPosition = UseObjectPosition();
		var obj = InputUtilities.NGFindBestCollider(_inputId, rob, out _action, restrictionAction, useObjectPosition)?.gameObject;
		
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if(_action == SpecialHandlingAction.AssignJob)
		{
			var building = obj.GetComponent<MABuilding>();
			var worker = m_movingObject.GetComponent<MAWorker>();
			var nextJob = building?.GetNextJob(worker);
			if(nextJob != null)
			{
				
				UIInfoMessage.Create(UIInfoMessage.Location.Top, _obj, $"{_action.m_action} - {nextJob.m_info.m_title}", _action.m_tmpIcon, _action.m_tmpPrefix);
			}
		}
		else if(_action != null)
		{
			UIInfoMessage.Create(UIInfoMessage.Location.Top, _obj, _action.m_action, _action.m_tmpIcon, _action.m_tmpPrefix);
		}
		else if(restrictionAction == SpecialHandlingAction.AssignHome)
		{
			UIInfoMessage.Create(UIInfoMessage.Location.Top, _obj, "No Bedrooms Slots Available", "NoBedroom");
		}
		else if(restrictionAction == SpecialHandlingAction.AssignJob)
		{
			UIInfoMessage.Create(UIInfoMessage.Location.Top, _obj, "No Job Slots Available", "NoWorker");
		}
#endif
		
		return obj;
	}
	public GameObject GetSource()
	{
		if (GameManager.IsVisitingInProgress)
			return null;

		if (m_falling)
		{
			var rc = m_movingObject?.GetComponentInChildren<RagdollController>();
			if (rc == null)
			{
				return null;
			}
			
			m_falling = false;
			rc.CancelRagdoll();
		}
			
		if(!m_movingObject.CanPickup())
			return null;
			
		//NGPickupThing.Create(gameObject);
		return gameObject;
	}
	public Color BezierColour(GameObject _s, GameObject _t)
	{ 
		if(NGDirectionCardBase.DraggingCard != null && _t != null && _t.GetComponent<BuildingCardHolderSegmentSlot>() != null)
		{
			return GlobalData.Me.m_miscNegativeBezierColour;
		}
		return GlobalData.Me.m_miscPositiveBezierColour;
	}
    //=======
    private void Update()
    {
		UpdateSplashSound();
    }

	private float m_waterHeight = 89.0f;
	private float m_waterHeightThreshold = 0.5f;
	private bool m_isUnderwater = false;
	private void UpdateSplashSound()
    {
		if(m_isUnderwater)
        {
			if (transform.position.y > (m_waterHeight + m_waterHeightThreshold))
			{
				AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Splash_Out", transform);
				m_isUnderwater = false;
			}
		}
		else
        {
			if (transform.position.y < (m_waterHeight - m_waterHeightThreshold))
			{
				AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Splash", transform);
				m_isUnderwater = true;
			}
        }		
	}
}
