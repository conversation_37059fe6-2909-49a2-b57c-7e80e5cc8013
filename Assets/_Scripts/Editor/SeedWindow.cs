using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using Unity.Mathematics;
using UnityEditor.IMGUI.Controls;

public class SeedWindow : EditorWindow {

	GameState m_seedState;
	GameState m_currentState;

	void OnEnable() {
		Refresh();
	}

	const string c_seedPath = "Assets/Resources/seedmoa.bytes";
	void Refresh()
	{
		var seed = System.IO.File.ReadAllText(c_seedPath);
		m_seedState = JsonUtility.FromJson<GameState>(seed);
		if (Application.isPlaying && GameManager.Me != null && GameManager.Me.IsInTitleScreen() == false)
		{
			m_currentState = GameManager.Me.m_state;
		}
		else
		{
			var slot = GameManager.SelectedSaveSlot;
			var path = $"{Application.persistentDataPath}/save{(slot == 0 ? "" : slot.ToString())}.dat";
			if (System.IO.File.Exists(path))
			{
				var current = System.IO.File.ReadAllText(path);
				m_currentState = JsonUtility.FromJson<GameState>(current);
			}
		}
	}

	void Save()
	{
		var seed = JsonUtility.ToJson(m_seedState, true);
		System.IO.File.WriteAllText(c_seedPath, seed);
	}

	void CreateStyles()
	{
		m_colour1 = new Color(1f, 1f, .7f, 1);
		m_colour2 = new Color(.8f, 1f, .8f, 1);
		m_colourSearch = new Color(1f, .4f, .2f, 1);

		/*var s = "";
		foreach (var styleIt in GUI.skin)
		{
			var style = styleIt as GUIStyle;
			s += $"{style.name}\n";
		}
		Debug.LogError($"Styles:\n{s}");*/
		
		foldoutStyle = GUI.skin.GetStyle("Foldout");
		labelStyle = new GUIStyle(GUI.skin.GetStyle("label"));
		smallLabelStyle = new GUIStyle(GUI.skin.GetStyle("label"));
		textFieldStyle = new GUIStyle(GUI.skin.GetStyle("textField"));
		dividerStyle = new GUIStyle(GUI.skin.GetStyle("button"));
		buttonStyle = new GUIStyle(GUI.skin.GetStyle("button"));
		tabStyle = new GUIStyle(GUI.skin.GetStyle("label"));
		dropdownStyle = new GUIStyle(GUI.skin.GetStyle("DropDown"));
		toggleStyle = new GUIStyle(GUI.skin.GetStyle("toggle"));
		labelStyle.fontSize = foldoutStyle.fontSize * 7 / 8;
		labelStyle.fixedHeight = foldoutStyle.fixedHeight;
		textFieldStyle.fontSize = foldoutStyle.fontSize;
		textFieldStyle.fixedHeight = foldoutStyle.lineHeight;
		textFieldStyle.margin = new RectOffset(0, 0, 3, 0);
		textFieldStyle.padding = new RectOffset(3, 3, 0, 0);
		smallLabelStyle.fixedHeight = foldoutStyle.fixedHeight;
		smallLabelStyle.fontSize = foldoutStyle.fontSize * 2 / 3;
		smallLabelStyle.fontStyle = FontStyle.Italic;
		dividerStyle.margin = new RectOffset(0, 0, 0, 0);
		dividerStyle.padding = new RectOffset(0, 0, 0, 0);
		buttonStyle.fixedHeight = foldoutStyle.fixedHeight;
		buttonStyle.margin = new RectOffset(0, 0, 2, 2);
		buttonStyle.padding = new RectOffset(3, 3, 1, 1);
		tabStyle.fontStyle = FontStyle.Bold;
		tabStyle.alignment = TextAnchor.MiddleCenter;
		dropdownStyle.fixedHeight = foldoutStyle.fixedHeight;
		dropdownStyle.margin = new RectOffset(0, 0, 0, 0);
		dropdownStyle.padding = new RectOffset(3, 3, 0, 0);
		toggleStyle.margin = new RectOffset(0, 0, 2, 2);
	}

	Dictionary<string, bool> m_expanded = new();
	const float c_indent = 12;
	const float c_foldoutSize = 12;
	float labelWidth = 150, fieldWidth = 250, shortFieldWidth = 120;

	GUIStyle foldoutStyle;
	GUIStyle labelStyle;
	GUIStyle smallLabelStyle;
	GUIStyle textFieldStyle;
	GUIStyle dividerStyle;
	GUIStyle buttonStyle;
	GUIStyle tabStyle;
	GUIStyle dropdownStyle;
	GUIStyle toggleStyle;

	System.Type m_matchingType;
	string m_matchingID;
	
	string Key1(string _key, bool _swap) => _swap ? Key2(_key, false) : _key;
	string Key2(string _key, bool _swap) => _swap ? _key : $"{_key}::2";

	bool ShouldDisplay(FieldInfo _field)
	{
		if (_field.IsPublic) return true;
		if (_field.GetCustomAttribute<SerializeField>() != null) return true;
		return false;
	}

	void ShowObjectGUI(float _indent, object o, object o2, string _name, bool _swap = false)
	{
		var fields = o.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

		foreach (var field in fields)
			if (ShouldDisplay(field))
				ShowFieldGUI(_indent, field.GetValue(o), o2 == null ? null : field.GetValue(o2), field.Name, _name, field, o, o2, _swap);
	}

	void SearchObject(object o, object o2, string _name, string _replace = null, string _replaceKey = null, bool _swap = false)
	{
		var fields = o.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
		foreach (var field in fields)
			if (ShouldDisplay(field))
				SearchField(field.GetValue(o), o2 == null ? null : field.GetValue(o2), field.Name, _name,_replace, _replaceKey, field, o, o2, _swap);
	}

	string GetFieldID(object o)
	{
		if (o == null) return "";
		var type = o.GetType();
		var idField = type.GetField("m_id");
		if (idField == null) idField = type.GetField("id");
		if (idField == null) idField = type.GetField("m_name");
		if (idField == null) idField = type.GetField("name");
		if (idField == null) idField = type.GetField("m_wildBlockID");
		if (idField == null) return "";
		return idField.GetValue(o).ToString();
	}

	string GetFieldIDs(object o, object o2)
	{
		var id1 = GetFieldID(o);
		var id2 = GetFieldID(o2);
		string id1Original = id1, id2Original = id2;
		if (id2 == id1) id2 = "";
		if (id1 != "" && id2 != "" && id1 != id2) id1 = $"({id1} : {id2})";
		else if (id1 != "") id1 = $"({id1})";
		else if (id2 != "") id1 = $"(- : {id2})";
		if (((o != null && o.GetType() == m_matchingType) || (o2 != null && o2.GetType() == m_matchingType)) && (id1Original == m_matchingID || id2Original == m_matchingID))
			id1 = $"{id1}!";
		return id1;
	}

	void SearchField(object fieldValue, object fieldValue2, string _fieldName, string _name, string _replace, string _replaceKey, FieldInfo field, object o, object o2, bool _swap)
	{
		bool expanded = true, isExpander = false;
		if (_fieldName.StartsWith("m_")) _fieldName = _fieldName[2..];
		var key = _name + "." + _fieldName;
		bool isClass = field.FieldType.IsClass && field.FieldType.FullName.StartsWith("System.") == false;
		bool isList = fieldValue is IList;
		bool isArray = field.FieldType.IsArray;
		bool isDictionary = fieldValue is IDictionary;
		bool isSDictionary = field.FieldType.Name.StartsWith("SDictionary");
		if (isClass || isList || isArray || isDictionary || isSDictionary)
		{
			//if (m_expanded.TryGetValue(key, out expanded) == false)
			//	expanded = false;
			isExpander = true;
		}
		if (isExpander)
		{
		}
		else if (field.FieldType == typeof(bool))
		{
		}
		else if (field.FieldType.IsEnum)
		{
		}
		else if (field.FieldType == typeof(Vector3) || field.FieldType == typeof(float3))
		{
		}
		else if (field.FieldType == typeof(Vector2))
		{
		}
		else
		{
			if (field.FieldType == typeof(int))
				;
			else if (field.FieldType == typeof(System.Int64))
				;
			else if (field.FieldType == typeof(float))
				;
			else if (field.FieldType == typeof(string))
			{
				var thisKey = Key1(key, _swap);
				if (_replace != null && (_replaceKey == null || _replaceKey == thisKey))
					field.SetValue(o, DoReplace(fieldValue as string, _replace));
				else if ((fieldValue as string).ToLower().Contains(m_searchTextLower))
					m_searchHits.Add(thisKey);
			}
			else if (o2 == null)
				;
			if (o2 != null)
			{
				if (field.FieldType == typeof(int))
					;
				else if (field.FieldType == typeof(System.Int64))
					;
				else if (field.FieldType == typeof(float))
					;
				else if (field.FieldType == typeof(string))
				{
					var thisKey = Key2(key, _swap);
					if (_replace != null && (_replaceKey == null || _replaceKey == thisKey))
						field.SetValue(o, DoReplace(fieldValue2 as string, _replace));
					else if (fieldValue2 != null && (fieldValue2 as string).ToLower().Contains(m_searchTextLower))
						m_searchHits.Add(thisKey);
				}
			}
		}

		if (isExpander)
		{
			if (expanded)
			{
				if (isSDictionary)
				{
					bool isNull = true, isNull2 = true;
					var keys = GetList(field, "m_keys", fieldValue, out isNull);
					var values = GetList(field, "m_values", fieldValue, out _);
					var keys2 = fieldValue2 == null ? null : GetList(field, "m_keys", fieldValue2, out isNull2);
					var values2 = fieldValue2 == null ? null : GetList(field, "m_values", fieldValue2, out _);
					var combinedKeys = CombineKeys(keys, keys2);
					for (int index = 0; index < combinedKeys.Count; ++index)
					{
						var keyRoot = combinedKeys[index];
						var index1 = keys.IndexOf(keyRoot);
						var index2 = keys2 != null ? keys2.IndexOf(keyRoot) : -1;
						var keyRef = index1 != -1 ? keyRoot : null;
						var keyRef2 = index2 != -1 ? keyRoot : null;
						var keyFinal1 = keyRef ?? keyRef2;
						var keyFinal2 = (object)null;
						var value = index1 != -1 ? values[index1] : null;
						var value2 = index2 != -1 ? values2[index2] : null;
						bool isPlain = IsPlainField(value, value2);
						SearchFieldOrObject(ref keyFinal1, ref keyFinal2, $"{key}[{index}]", _replace, _replaceKey);
						SearchFieldOrObject(ref value, ref value2,$"{key}[{index}]", _replace, _replaceKey);
						if (index1 != -1)
						{
							keys[index1] = keyFinal1;
							values[index1] = value;
						}
						if (value2 != null)
						{
							if (index2 != -1)
							{
								keys2[index2] = keyFinal1;
								values2[index2] = value2;
							}
						}
					}
				}
				else if (isDictionary)
				{
					var dict = fieldValue as IDictionary;
					var dict2 = fieldValue2 as IDictionary;
					int index = 0;
					var combinedKeys = CombineKeys(dict?.Keys, dict2?.Keys);
					foreach (var keyIt in combinedKeys)
					{
						var keyRef = dict.Contains(keyIt) ? keyIt : null;
						var keyRef2 = dict2 != null && dict2.Contains(keyIt) ? keyIt : null;
						var keyFinal1 = keyRef ?? keyRef2;
						var keyFinal2 = (object) null;
						var value = dict.Contains(keyIt) ? dict[keyIt] : null;
						var value2 = dict2 == null ? null : (dict2.Contains(keyIt) ? dict2[keyIt] : null);
						bool isPlain = IsPlainField(value, value2);
						SearchFieldOrObject(ref keyFinal1, ref keyFinal2, $"{key}[{index}]", _replace, _replaceKey);
						SearchFieldOrObject(ref value, ref value2, $"{key}[{index}]", _replace, _replaceKey);
						if (keyFinal1.Equals(keyRef)) dict[keyFinal1] = value;
						else
						{
							dict.Remove(keyRef);
							dict[keyFinal1] = value;
						}
						if (value2 != null && keyFinal1.Equals(keyRef2)) dict2[keyFinal1] = value2;
						else
						{
							dict2.Remove(keyRef2);
							dict2[keyFinal1] = value2;
						}
					}
				}
				else if (isList)
				{
					var list = fieldValue as IList;
					var list2 = fieldValue2 as IList;
					var count = Mathf.Max(list.Count, list2?.Count ?? 0);
					for (int index = 0; index < count; ++index)
					{
						var item = index < list.Count ? list[index] : null;
						var item2 = list2 != null && index < list2.Count ? list2[index] : null;
						SearchFieldOrObject(ref item, ref item2, $"{key}[{index}]", _replace, _replaceKey);
						if (item != null && index < list.Count) list[index] = item;
						if (item2 != null && index < list2.Count) list2[index] = item2;
					}
				}
				else
					SearchObject(fieldValue, fieldValue2, key, _replace, _replaceKey, _swap);
			}
		}
	}

	bool IsPlainField(object _item)
	{
		return _item.GetType().IsPrimitive || _item is string || _item is float3;
	}

	bool IsPlainField(object _item, object _item2)
	{
		if (_item != null) return IsPlainField(_item);
		return IsPlainField(_item2);
	}

	const float c_applyButtonWidth = 64;

	bool CheckDiff(object _v1, object _v2)
	{
		if (m_showDiffs == false) return false;
		bool res = false;
		if (_v1 == null && _v2 == null) return res;
		if (_v1 == null || _v2 == null || _v1.Equals(_v2) == false)
		{
			ColourApply();
			if (GUILayout.Button("< to seed", buttonStyle, GUILayout.Width(c_applyButtonWidth)))
			{
				res = true;
			}
		}
		return res;
	}

	bool CheckDiff(ref object _v1, ref object _v2, bool _swap)
	{
		bool res = CheckDiff(_v1, _v2);
		if (res)
		{
			if (_swap)
				_v2 = _v1;
			else
				_v1 = _v2;
		}
		return res;
	}

	bool CheckDiff(object _v1, object _v2, FieldInfo _field, object _o)
	{
		bool res = CheckDiff(_v1, _v2);
		if (res)
			_field.SetValue(_o, _v2);
		return res;
	}

	bool AddButton(float _indent)
	{
		bool res = false;
		ColourAdd();
		GUILayout.BeginHorizontal();
		GUILayout.Label("", GUILayout.Width(_indent + 24));
		if (GUILayout.Button("+", buttonStyle, GUILayout.Width(c_addButtonWidth)))
		{
			res = true;
		}
		GUILayout.EndHorizontal();
		return res;
	}

	bool ColourSearch(string _key)
	{
		if (string.IsNullOrEmpty(m_currentSearchKey)) return false;
		if (_key != m_currentSearchKey) return false;
		GUI.backgroundColor = m_colourSearch;
		GUI.color = m_colourSearch;
		GUI.contentColor = Color.Lerp(m_colourSearch, Color.white, .5f);
		if (Event.current.type == EventType.Repaint && m_updateSearchScroll)
		{
			const float c_searchMargin = 20;
			var rect = GUILayoutUtility.GetLastRect();
			bool scrolled = false;
			if (rect.y - m_scrollPosition.y < c_searchMargin)
			{
				m_scrollPosition.y = rect.y - c_searchMargin;
				scrolled = true;
			}
			else if (rect.yMax - m_scrollPosition.y > m_scrollViewHeight - c_searchMargin)
			{
				m_scrollPosition.y = rect.yMax - (m_scrollViewHeight - c_searchMargin);
				scrolled = true;
			}
			if (scrolled) GUI.changed = true;
		}
		return true;
	}

	void Colour1(string _key, bool _swap = false)
	{
		if (_swap) { Colour2(_key); return; }
		if (ColourSearch(_key)) return;
		GUI.backgroundColor = m_colour1;
		GUI.color = m_colour1;
		GUI.contentColor = Color.Lerp(m_colour1, Color.white, .5f);
	}

	void Colour2(string _key, bool _swap = false)
	{
		if (_swap) { Colour1(_key); return; }
		if (ColourSearch(Key2(_key, false))) return;
		GUI.backgroundColor = m_colour2;
		GUI.color = m_colour2;
		GUI.contentColor = Color.Lerp(m_colour2, Color.white, .5f);
	}
	
	void ColourDefault(string _key, bool _showSearch = false)
	{
		if (_showSearch && ColourSearch(_key)) return;
		GUI.backgroundColor = Color.white;
		GUI.color = Color.white;
		GUI.contentColor = Color.white;
	}

	void ColourApply()
	{
		GUI.backgroundColor = Color.green;
		GUI.color = Color.white;
		GUI.contentColor = Color.white;
	}

	void ColourDelete()
	{
		GUI.backgroundColor = Color.red;
		GUI.color = Color.white;
		GUI.contentColor = Color.white;
	}

	void ColourAdd()
	{
		GUI.backgroundColor = Color.blue;
		GUI.color = Color.white;
		GUI.contentColor = Color.white;
	}

	GUILayoutOption StdH => GUILayout.Height(textFieldStyle.fixedHeight);

	IList GetList(FieldInfo _info, string _fieldName, object _fieldValue, out bool _isNull)
	{
		_isNull = true;
		if (_fieldValue == null) return new List<object>();
		var keysField = _info.FieldType.GetField(_fieldName);
		if (keysField == null) return new List<object>();
		_isNull = false;
		return keysField.GetValue(_fieldValue) as IList;
	}

	void SetList(FieldInfo _info, string _fieldName, object _fieldValue, IList _list)
	{
		if (_fieldValue == null) return;
		var keysField = _info.FieldType.GetField(_fieldName);
		if (keysField == null) return;
		keysField.SetValue(_fieldValue, _list);
	}

	void ShowFieldGUI(float _indent, object fieldValue, object fieldValue2, string _fieldName, string _name, FieldInfo field, object o, object o2, bool _swap)
	{
		if (_fieldName.StartsWith("m_")) _fieldName = _fieldName[2..];
		var key = _name + "." + _fieldName;
		ColourDefault(key);
		GUILayout.Label("", dividerStyle, GUILayout.Height(1));
		GUILayout.BeginHorizontal();
		bool expanded = false, isExpander = false;
		bool isClass = field.FieldType.IsClass && field.FieldType.FullName.StartsWith("System.") == false;
		bool isList = fieldValue is IList;
		bool isArray = field.FieldType.IsArray;
		bool isDictionary = fieldValue is IDictionary;
		bool isSDictionary = field.FieldType.Name.StartsWith("SDictionary");
		if (isClass || isList || isArray || isDictionary || isSDictionary)
		{
			if (m_expanded.TryGetValue(key, out expanded) == false)
				expanded = false;
			GUILayout.Label("", GUILayout.Width(_indent));
			expanded = EditorGUILayout.Foldout(expanded, "");//_fieldName);
			var rect = GUILayoutUtility.GetLastRect();
			rect.x += c_foldoutSize;
			GUI.Label(rect, _fieldName, labelStyle);
			m_expanded[key] = expanded;

			string smallLabel = null;
			if (isDictionary) smallLabel = $"  {field.FieldType.Name}  {field.FieldType.GenericTypeArguments[0].Name} -> {field.FieldType.GenericTypeArguments[1].Name} [{(fieldValue as IDictionary).Keys.Count}{(o2 == null ? "" : $", {(fieldValue2 as IDictionary).Keys.Count}")}]";
			else if (isSDictionary) smallLabel = $"  {field.FieldType.Name}  {field.FieldType.GenericTypeArguments[0].Name} -> {field.FieldType.GenericTypeArguments[1].Name} [{GetList(field, "m_keys", fieldValue, out _).Count}{(o2 == null ? "" : $", {GetList(field, "m_keys", fieldValue2, out _).Count}")}]";
			else if (isClass) smallLabel = $"  {field.FieldType.Name}";
			else if (isArray) smallLabel = $"  {field.FieldType.GetElementType().Name}[{(fieldValue as IList).Count}{(o2 == null ? "" : $", {(fieldValue2 as IList).Count}")}]";
			else if (isList) smallLabel = $"  List<{field.FieldType.GenericTypeArguments[0].Name}>[{(fieldValue as IList).Count}{(o2 == null || fieldValue2 == null ? "" : $", {(fieldValue2 as IList).Count}")}]";
			if (smallLabel != null)
			{
				rect.x = labelWidth;
				GUI.Label(rect, smallLabel, smallLabelStyle);
			}
			
			isExpander = true;
		}
		else
		{
			GUILayout.Label("", GUILayout.Width(c_foldoutSize + _indent));
			GUILayout.Label(_fieldName, labelStyle, GUILayout.Width(labelWidth - _indent - 18), StdH);
		}
		if (isExpander)
		{
		}
		else if (field.FieldType == typeof(bool))
		{
			Colour1(key, _swap);
			bool newValue = GUILayout.Toggle((bool) fieldValue, "", toggleStyle, GUILayout.Width(fieldWidth), StdH);
			field.SetValue(o, newValue);
			if (o2 != null)
			{
				Colour2(key, _swap);
				var newValue2 = GUILayout.Toggle((bool) fieldValue2, "", toggleStyle, GUILayout.Width(fieldWidth), StdH);
				field.SetValue(o2, newValue2);
				CheckDiff(newValue, newValue2, field, o);
			}
		}
		else if (field.FieldType.IsEnum)
		{
			Colour1(key, _swap);
			var enumValue = EditorGUILayout.EnumPopup((System.Enum) fieldValue, dropdownStyle, GUILayout.Width(fieldWidth));
			field.SetValue(o, enumValue);
			if (o2 != null)
			{
				Colour2(key, _swap);
				var enumValue2 = EditorGUILayout.EnumPopup((System.Enum) fieldValue2, dropdownStyle, GUILayout.Width(fieldWidth));
				field.SetValue(o2, enumValue2);
				CheckDiff(enumValue, enumValue2, field, o);
			}
		}
		else if (field.FieldType == typeof(Vector3) || field.FieldType == typeof(float3))
		{
			Colour1(key, _swap);
			var newValue = EditorGUILayout.Vector3Field("", (Vector3) fieldValue, GUILayout.Width(fieldWidth), StdH);
			field.SetValue(o, newValue);
			if (o2 != null)
			{
				Colour2(key, _swap);
				var newValue2 = EditorGUILayout.Vector3Field("", (Vector3) fieldValue2, GUILayout.Width(fieldWidth), StdH);
				field.SetValue(o2, newValue2);
				CheckDiff(newValue, newValue2, field, o);
			}
		}
		else if (field.FieldType == typeof(Vector2))
		{
			Colour1(key, _swap);
			var newValue = EditorGUILayout.Vector2Field("", (Vector2) fieldValue, GUILayout.Width(fieldWidth));
			field.SetValue(o, newValue);
			if (o2 != null)
			{
				Colour2(key, _swap);
				var newValue2 = EditorGUILayout.Vector2Field("", (Vector2) fieldValue2, GUILayout.Width(fieldWidth));
				field.SetValue(o2, newValue2);
				CheckDiff(newValue, newValue2, field, o);
			}
		}
		else
		{
			Colour1(key, _swap);
			var obj = EditorGUILayout.DelayedTextField(fieldValue?.ToString() ?? "", textFieldStyle, GUILayout.Width(fieldWidth), StdH);
			object objVal = null;
			if (field.FieldType == typeof(int))
				objVal = int.Parse(obj);
			else if (field.FieldType == typeof(System.Int64))
				objVal = System.Int64.Parse(obj);
			else if (field.FieldType == typeof(float))
				objVal = float.Parse(obj);
			else if (field.FieldType == typeof(string))
				objVal = obj;
			else if (o2 == null)
				GUILayout.Label($"Unsupported type: {field.FieldType}");
			if (objVal != null)
				field.SetValue(o, objVal);
			if (o2 != null)
			{
				Colour2(key, _swap);
				var obj2 = EditorGUILayout.DelayedTextField(fieldValue2?.ToString() ?? "", textFieldStyle, GUILayout.Width(fieldWidth), StdH);
				object obj2Val = null;
				if (field.FieldType == typeof(int))
					obj2Val = int.Parse(obj2);
				else if (field.FieldType == typeof(System.Int64))
					obj2Val = System.Int64.Parse(obj2);
				else if (field.FieldType == typeof(float))
					obj2Val = float.Parse(obj2);
				else if (field.FieldType == typeof(string))
					obj2Val = obj2;
				if (obj2Val != null)
					field.SetValue(o2, obj2Val);
				CheckDiff(objVal, obj2Val, field, o);
			}
		}
		GUILayout.EndHorizontal();

		if (isExpander)
		{
			if (expanded)
			{
				if (isSDictionary)
				{
					bool isNull = true, isNull2 = true;
					var keys = GetList(field, "m_keys", fieldValue, out isNull);
					var values = GetList(field, "m_values", fieldValue, out _);
					var keys2 = fieldValue2 == null ? null : GetList(field, "m_keys", fieldValue2, out isNull2);
					var values2 = fieldValue2 == null ? null : GetList(field, "m_values", fieldValue2, out _);
					var combinedKeys = CombineKeys(keys, keys2);
					for (int index = 0; index < combinedKeys.Count; ++index)
					{
						GUILayout.BeginHorizontal();
						GUILayout.Label("", GUILayout.Width(_indent + c_indent));
						var keyRoot = combinedKeys[index];
						var index1 = keys.IndexOf(keyRoot);
						var index2 = keys2 != null ? keys2.IndexOf(keyRoot) : -1;
						var keyRef = index1 != -1 ? keyRoot : null;
						var keyRef2 = index2 != -1 ? keyRoot : null;
						var keyFinal1 = keyRef ?? keyRef2;
						var keyFinal2 = (object)null;
						var value = index1 != -1 ? values[index1] : null;
						var value2 = index2 != -1 ? values2[index2] : null;
						bool isPlain = IsPlainField(value, value2);
						var keyWidth = labelWidth - _indent - c_indent - 10;
						if (isPlain == false) keyWidth -= c_foldoutSize - 6;
						ShowFieldOrObjectGUI(ref keyFinal1, ref keyFinal2, _indent, $"{key}[{index}]", true, _showApply:false, _defaultColour:true, _fieldWidth: keyWidth);
						//GUILayout.Label(">", GUILayout.Width(16));
						var (delete, delete2) = ShowFieldOrObjectGUI(ref value, ref value2, _indent, $"{key}[{index}]", true, _showDelete: true);
						if (delete)
						{
							keys.RemoveAt(index1);
							values.RemoveAt(index1);
						}
						else if (index1 != -1)
						{
							keys[index1] = keyFinal1;
							values[index1] = value;
						}
						else if (index1 == -1 && value != null)
						{
							keys.Add(keyFinal1);
							values.Add(value);
						}
						if (value2 != null)
						{
							if (delete2)
							{
								keys2.RemoveAt(index2);
								values2.RemoveAt(index2);
							}
							else if (index2 != -1)
							{
								keys2[index2] = keyFinal1;
								values2[index2] = value2;
							}
							else
							{
								keys2.Add(keyFinal1);
								values2.Add(value2);
							}
						}
						if (isPlain)
							GUILayout.EndHorizontal();
					}
					if (AddButton(_indent + c_indent))
					{
						keys.Add(System.Activator.CreateInstance(keys.GetType().GenericTypeArguments[0]));
						values.Add(System.Activator.CreateInstance(values.GetType().GenericTypeArguments[0]));
						if (isNull)
						{
							var sdict = System.Activator.CreateInstance(field.FieldType);
							field.SetValue(o, sdict);
							sdict.GetType().GetField("m_keys").SetValue(sdict, keys);
							sdict.GetType().GetField("m_values").SetValue(sdict, values);
						}
						if (keys2 != null)
						{
							keys2.Add(System.Activator.CreateInstance(keys2.GetType().GenericTypeArguments[0]));
							values2.Add(System.Activator.CreateInstance(values2.GetType().GenericTypeArguments[0]));
							if (isNull2)
							{
								var sdict = System.Activator.CreateInstance(field.FieldType);
								field.SetValue(o2, sdict);
								sdict.GetType().GetField("m_keys").SetValue(sdict, keys2);
								sdict.GetType().GetField("m_values").SetValue(sdict, values2);
							}
						}
					}
				}
				else if (isDictionary)
				{
					var dict = fieldValue as IDictionary;
					var dict2 = fieldValue2 as IDictionary;
					int index = 0;
					var combinedKeys = CombineKeys(dict?.Keys, dict2?.Keys);
					foreach (var keyIt in combinedKeys)
					{
						GUILayout.BeginHorizontal();
						GUILayout.Label("", GUILayout.Width(_indent + c_indent));
						var keyRef = dict.Contains(keyIt) ? keyIt : null;
						var keyRef2 = dict2 != null && dict2.Contains(keyIt) ? keyIt : null;
						var keyFinal1 = keyRef ?? keyRef2;
						var keyFinal2 = (object) null;
						var keyWidth = labelWidth - _indent - c_indent - 10;
						var value = dict.Contains(keyIt) ? dict[keyIt] : null;
						var value2 = dict2 == null ? null : (dict2.Contains(keyIt) ? dict2[keyIt] : null);
						bool isPlain = IsPlainField(value, value2);
						if (isPlain == false) keyWidth -= c_foldoutSize - 6;
						ShowFieldOrObjectGUI(ref keyFinal1, ref keyFinal2, _indent, $"{key}[{index}]", true, _showApply:false, _defaultColour:true, _fieldWidth: keyWidth);
						//GUILayout.Label(">", GUILayout.Width(16));
						var (delete, delete2) = ShowFieldOrObjectGUI(ref value, ref value2, _indent, $"{key}[{index}]", true, _showDelete: true);
						if (delete) dict.Remove(keyIt);
						else if (keyFinal1.Equals(keyRef)) dict[keyFinal1] = value;
						else
						{
							dict.Remove(keyRef);
							dict[keyFinal1] = value;
						}
						if (delete2) dict2.Remove(keyIt);
						else if (value2 != null && keyFinal1.Equals(keyRef2)) dict2[keyFinal1] = value2;
						else
						{
							dict2.Remove(keyRef2);
							dict2[keyFinal1] = value2;
						}
						++index;
						if (isPlain)
							GUILayout.EndHorizontal();
					}
					if (AddButton(_indent + c_indent))
					{
					}
				}
				else if (isList)
				{
					var list = fieldValue as IList;
					var list2 = fieldValue2 as IList;
					var count = Mathf.Max(list.Count, list2?.Count ?? 0);
					var labelW = labelWidth - _indent - c_indent - 6;
					for (int index = 0; index < count; ++index)
					{
						GUILayout.BeginHorizontal();
						GUILayout.Label("", GUILayout.Width(_indent + c_indent));
						//GUILayout.Label($"{index}", GUILayout.Width(64));
						var item = index < list.Count ? list[index] : null;
						var item2 = list2 != null && index < list2.Count ? list2[index] : null;
						var id = GetFieldIDs(item, item2);
						var (delete, delete2) = ShowFieldOrObjectGUI(ref item, ref item2, _indent, $"{key}[{index}]", false, $"{index}  {id}", _showDelete:true, _labelWidth: labelW);
						if (delete)
							list.RemoveAt(index);
						else if (item != null)
							list[index] = item;
						if (delete2)
							list2.RemoveAt(index);
						else if (item2 != null)
							list2[index] = item2;
					}
					if (AddButton(_indent + c_indent))
					{
						list.Add(System.Activator.CreateInstance(list.GetType().GenericTypeArguments[0]));
						list2.Add(System.Activator.CreateInstance(list.GetType().GenericTypeArguments[0]));
					}
				}
				else
					ShowObjectGUI(_indent + c_indent, fieldValue, fieldValue2, key, _swap);
			}
		}
	}

	string DoReplace(string _s, string _replaceWith)
	{
		var sLower = _s.ToLower();
		int start = 0;
		while (true)
		{
			int found = sLower.IndexOf(m_searchTextLower, start);
			if (found == -1) break;
			_s = _s.Substring(0, found) + _replaceWith + _s.Substring(found + m_searchText.Length);
			sLower = _s.ToLower();
			start = found + 1;
		}
		return _s;
	}

	Color m_colour1;
	Color m_colour2;
	Color m_colourSearch;
	List<object> CombineKeys(ICollection _a, ICollection _b)
	{
		var res = new List<object>();
		if (_a != null)
			foreach (var v in _a) res.Add(v);
		if (_b != null)
			foreach (var v in _b) res.AddUnique(v);
		return res;
	}

	void SearchFieldOrObject(ref object item, ref object item2, string _key, string _replace, string _replaceKey)
	{
		bool swap = false;
		if (item == null)
		{
			swap = true;
			(item, item2) = (item2, item);
		}
		if (IsPlainField(item, item2))
		{
			if (item is bool)
			{
			}
			else if (item is System.Enum)
			{
			}
			else if (item is Vector3 || item is float3)
			{
			}
			else if (item is Vector2 v2)
			{
			}
			else
			{
				if (item is int)
					;
				else if (item is System.Int64)
					;
				else if (item is float)
					;
				else if (item is string s)
				{
					var thisKey = Key1(_key, swap);
					if (_replace != null && (_replaceKey == null || _replaceKey == thisKey))
						item = DoReplace(s, _replace);
					if (s.ToLower().Contains(m_searchTextLower))
						m_searchHits.Add(thisKey);
				}
				else if (item2 == null)
					;
				if (item2 != null)
				{
					if (item2 is int)
						;
					else if (item2 is System.Int64)
						;
					else if (item2 is float)
						;
					else if (item2 is string s)
					{
						var thisKey = Key2(_key, swap);
						if (_replace != null && (_replaceKey == null || _replaceKey == thisKey))
							item2 = DoReplace(s, _replace);
						if (s.ToLower().Contains(m_searchTextLower))
							m_searchHits.Add(thisKey);
					}
				}
			}
		}
		else
		{
			SearchObject(item, item2, _key, _replace, _replaceKey, swap);
		}
	}

	static Color c_matchColour = new Color(1, .7f, .7f, 1);
	Color m_backupColour;
	string PrepareLabel(string _label)
	{
		m_backupColour = GUI.color;
		if (_label.EndsWith("!"))
		{
			_label = _label[0..^1];
			GUI.color = c_matchColour;
		}
		return _label;
	}
	void PostLabel() => GUI.color = m_backupColour;

	void ShowStandardLabel(string _label, float _labelWidth)
	{
		if (_label != null)
		{
			_label = PrepareLabel(_label);
			GUILayout.Label(_label, GUILayout.Width(_labelWidth));
			PostLabel();
		}
	}

	void ShowStandardLabel(string _label, Rect _rect)
	{
		if (_label != null)
		{
			_label = PrepareLabel(_label);
			GUI.Label(_rect, _label);
			PostLabel();
		}
	}

	(bool, bool) ShowFieldOrObjectGUI(ref object item, ref object item2, float _indent, string _key, bool _inhibitEndHorizontal = false, string _label = null, bool _showApply = true, bool _showDelete = false, bool _defaultColour = false, float _labelWidth = 64, float _fieldWidth = -1)
	{
		ColourDefault(_key, true);
		bool res = false, res2 = false;
		bool swap = false;
		float extraIndent = 0;
		if (item == null)
		{
			swap = true;
			(item, item2) = (item2, item);
		}
		var fieldW = _fieldWidth > 0 ? _fieldWidth : fieldWidth;
		var fullFieldW = fieldW;
		if (_showDelete) fieldW -= c_deleteButtonWidth;
		if (IsPlainField(item, item2))
		{
			//var adjust = shortFieldWidth - fieldWidth;
			//fieldW += adjust;
			//fullFieldW += adjust;
			//ShowFieldGUI(_indent + c_indent * 2, item, "", $"{key}[{index}]");
			ShowStandardLabel(_label, _labelWidth);
			if (swap)
				GUILayout.Label("", GUILayout.Width(fullFieldW));
			if (item is bool)
			{
				if (!_defaultColour) Colour1(_key, swap);
				bool newValue = GUILayout.Toggle((bool) item, "", toggleStyle, GUILayout.Width(fieldW), StdH);
				item = newValue;
				if (_showDelete) res = DeleteButton();
				if (item2 != null)
				{
					Colour2(_key, swap);
					bool newValue2 = GUILayout.Toggle((bool) item2, "", toggleStyle, GUILayout.Width(fieldW), StdH);
					item2 = newValue2;
					if (_showDelete) res2 = DeleteButton();
					if (_showApply) CheckDiff(ref item, ref item2, swap);
				}
			}
			else if (item is System.Enum)
			{
				if (!_defaultColour) Colour1(_key, swap);
				var enumValue = EditorGUILayout.EnumPopup((System.Enum) item, dropdownStyle, GUILayout.Width(fieldW));
				item = enumValue;
				if (_showDelete) res = DeleteButton();
				if (item2 != null)
				{
					Colour2(_key, swap);
					var enumValue2 = EditorGUILayout.EnumPopup((System.Enum) item2, dropdownStyle, GUILayout.Width(fieldW));
					item2 = enumValue2;
					if (_showDelete) res2 = DeleteButton();
					if (_showApply) CheckDiff(ref item, ref item2, swap);
				}
			}
			else if (item is Vector3 || item is float3)
			{
				if (!_defaultColour) Colour1(_key, swap);
				var v = item is Vector3 ? (Vector3) item : (Vector3) (float3) item;
				var newValue = EditorGUILayout.Vector3Field("", v, GUILayout.Width(fieldW), GUILayout.Height(textFieldStyle.fixedHeight));
				if (item is Vector3)
					item = newValue;
				else
					item = (float3) newValue;
				if (_showDelete) res = DeleteButton();
				if (item2 != null)
				{
					Colour2(_key, swap);
					var v2 = item2 is Vector3 ? (Vector3) item2 : (Vector3) (float3) item2;
					var newValue2 = EditorGUILayout.Vector3Field("", v2, GUILayout.Width(fieldW), GUILayout.Height(textFieldStyle.fixedHeight));
					if (item2 is Vector3)
						item2 = newValue2;
					else
						item2 = (float3) newValue2;
					if (_showDelete) res2 = DeleteButton();
					if (_showApply) CheckDiff(ref item, ref item2, swap);
				}
			}
			else if (item is Vector2 v2)
			{
				if (!_defaultColour) Colour1(_key, swap);
				var newValue = EditorGUILayout.Vector2Field("", v2, GUILayout.Width(fieldW));
				item = newValue;
				if (_showDelete) res = DeleteButton();
				if (item2 != null)
				{
					Colour2(_key, swap);
					var newValue2 = EditorGUILayout.Vector2Field("", (Vector2) item2, GUILayout.Width(fieldW));
					item2 = newValue2;
					if (_showDelete) res2 = DeleteButton();
					if (_showApply) CheckDiff(ref item, ref item2, swap);
				}
			}
			else
			{
				if (!_defaultColour) Colour1(_key, swap);
				var obj = EditorGUILayout.DelayedTextField(item.ToString(), textFieldStyle, GUILayout.Width(fieldW));
				if (item is int)
					item = int.Parse(obj);
				else if (item is System.Int64)
					item = System.Int64.Parse(obj);
				else if (item is float)
					item = float.Parse(obj);
				else if (item is string s)
					item = s;
				else if (item2 == null)
					GUILayout.Label($"Unsupported type: {item.GetType().Name}");
				if (_showDelete) res = DeleteButton();
				if (item2 != null)
				{
					Colour2(_key, swap);
					var obj2 = EditorGUILayout.DelayedTextField(item2.ToString(), textFieldStyle, GUILayout.Width(fieldW));
					if (item2 is int)
						item2 = int.Parse(obj);
					else if (item2 is System.Int64)
						item2 = System.Int64.Parse(obj);
					else if (item2 is float)
						item2 = float.Parse(obj);
					else if (item2 is string s)
						item2 = s;
					if (_showDelete) res2 = DeleteButton();
					if (_showApply) CheckDiff(ref item, ref item2, swap);
				}
			}
			//if (_showApply) CheckDiff(ref item, ref item2, swap);
			if (_inhibitEndHorizontal == false)
				GUILayout.EndHorizontal();
		}
		else
		{
			var expanded = false;
			if (m_expanded.TryGetValue(_key, out expanded) == false)
				expanded = false;
			expanded = EditorGUILayout.Foldout(expanded, "");
			m_expanded[_key] = expanded;
			var rect = GUILayoutUtility.GetLastRect();
			rect.x += 20; rect.width = _labelWidth - rect.x;
			ShowStandardLabel(_label, rect);
			if (_showDelete && expanded)
			{
				rect.x = labelWidth + 8 + 24*0; rect.width = 20;
				if (swap) rect.x += fieldWidth;
				res = DeleteButton(rect);
				if (item2 != null)
				{
					rect.x += fieldWidth - 24 * 0;
					res2 = DeleteButton(rect);
				}
			}
			if (swap && _showApply) CheckDiff(ref item, ref item2, swap);
			GUILayout.EndHorizontal();
			if (expanded)
			{
				if (swap) labelWidth += fieldWidth;
				ShowObjectGUI(_indent + c_indent * 2, item, item2, _key, swap);
				if (swap) labelWidth -= fieldWidth;
			}
		}
		if (swap)
		{
			(item, item2) = (item2, item);
			(res, res2) = (res2, res);
		}
		return (res, res2);
	}

	// https://gist.github.com/MattRix/c1f7840ae2419d8eb2ec0695448d4321
	GUIContent GetStandardIcon(string _s) => EditorGUIUtility.IconContent(_s);

	const float c_deleteButtonWidth = 20;
	const float c_addButtonWidth = 30;
	bool DeleteButton(Rect _r = default)
	{
		ColourDelete();
		bool res;
		var content = GetStandardIcon("TreeEditor.Trash");
		if (_r.width < .0001f)
			res = GUILayout.Button(content, buttonStyle, GUILayout.Width(c_deleteButtonWidth));
		else
			res = GUI.Button(_r, content, buttonStyle);
		return res;
	}

	Vector2 m_scrollPosition;
	bool m_showDiffs = false;
	string m_timeString;
	string m_gameTimeString;

	void CheckTime(string _time, string _gameTime)
	{
		if (_time == m_timeString && _gameTime == m_gameTimeString)
			return;
		if (DayNight.Me == null)
			DayNight.SetEditorMe();
		if (_time != m_timeString)
		{
			m_timeString = _time;
			m_gameTimeString = DayNight.ClockToFraction(m_timeString).ToStringInv();
		}
		else
		{
			m_gameTimeString = _gameTime;
			m_timeString = DayNight.ClockToString(DayNight.StageToClock(DayNight.FractionToStage(float.Parse(_gameTime))), false);
		}
	}

	private List<string> m_searchHits = new(); 
	private string m_searchText = "", m_searchTextLower = "";
	private string m_replaceText = "";
	private string m_currentSearchKey = "";
	private int m_currentSearchIndex = -1;
	private bool m_updateSearchScroll = false;

	void FindNext(int _direction)
	{
		m_currentSearchKey = "";
		if (m_searchHits.Count == 0) return;
		m_updateSearchScroll = true;
		m_currentSearchIndex = (m_currentSearchIndex + _direction + m_searchHits.Count) % m_searchHits.Count;
		m_currentSearchKey = m_searchHits[m_currentSearchIndex];
		ExpandKeys(m_currentSearchKey);
	}
	void Replace()
	{
		if (m_searchHits.Count == 0) return;
		var nextSearchKey = m_searchHits[(m_currentSearchIndex + 1) % m_searchHits.Count];
		m_searchHits.Clear();
		SearchObject(m_seedState, m_showDiffs ? m_currentState : null, "", m_replaceText, m_currentSearchKey);
		m_currentSearchIndex = m_searchHits.IndexOf(nextSearchKey);
		FindNext(0);
	}
	void ReplaceAll()
	{
		if (m_searchHits.Count == 0) return;
		m_searchHits.Clear();
		SearchObject(m_seedState, m_showDiffs ? m_currentState : null, "", m_replaceText, null);
		m_currentSearchIndex = -1;
	}

	void UpdateSearch()
	{
		m_searchHits.Clear();
		if (string.IsNullOrEmpty(m_searchText))
		{
			m_currentSearchKey = "";
			m_currentSearchIndex = -1;
			return;
		}
		SearchObject(m_seedState, m_showDiffs ? m_currentState : null, "");
		m_currentSearchIndex = -1;
		FindNext(1);
	}

	void ExpandKeys(string _keys)
	{
		do
		{
			m_expanded[_keys] = true;
			var dotIndex = _keys.LastIndexOf('.');
			var sqrIndex = _keys.LastIndexOf('[');
			var index = Mathf.Max(dotIndex, sqrIndex);
			_keys = _keys.Substring(0, index);
		} while (_keys.Contains('.') || _keys.Contains('['));
	}

	void Match(object _state)
	{
		m_matchingType = _state.GetType();
		m_matchingID = GetFieldID(_state);
	}

	void FindMatch(GameObject _sel)
	{
		m_matchingType = null;
		m_matchingID = "";
		if (_sel == null) return;
		var mab = _sel.GetComponentInParent<MABuilding>();
		if (mab != null) { Match(mab.m_stateData); return; }
		var wb = _sel.GetComponentInParent<MAWildBlock>();
		if (wb != null) { Match(wb.m_wildBlockState); return; }
	}

	private float m_scrollViewHeight;
	void OnGUI()
	{
		CreateStyles();

		FindMatch(Selection.activeGameObject);
		
		GUILayout.BeginHorizontal();
		if (GUILayout.Button("Refresh", GUILayout.Width(140)))
			Refresh();
		if (GUILayout.Button("Save", GUILayout.Width(140)))
			Save();
		var wasShowingDiffs = m_showDiffs;
		m_showDiffs = GUILayout.Toggle(m_showDiffs, "Show Diffs", GUILayout.Width(100));
		if (wasShowingDiffs != m_showDiffs)
			UpdateSearch();
		
		GUILayout.Label(GetStandardIcon("UnityEditor.AnimationWindow"), labelStyle, GUILayout.Width(16));
		var timeString = EditorGUILayout.DelayedTextField(m_timeString, textFieldStyle, GUILayout.Width(64));
		GUILayout.Label(GetStandardIcon("d_UnityEditor.GameView"), labelStyle, GUILayout.Width(16));
		var gameTimeString = EditorGUILayout.DelayedTextField(m_gameTimeString, textFieldStyle, GUILayout.Width(64));
		CheckTime(timeString, gameTimeString);
		GUILayout.EndHorizontal();

		const float c_buttonHeight = 16;
		const float c_endGap = 28;
		const float c_replaceButtonWidth = 80;

		var searchFieldWidth = (position.width - 32 - (c_buttonHeight + 8) * 3 - 64 - c_replaceButtonWidth * 2 - 24 - 8) * .5f;
		if (searchFieldWidth > 240) searchFieldWidth = 240;

		GUILayout.BeginHorizontal();
		GUILayout.Label("Find", GUILayout.Width(32));
		var search = EditorGUILayout.DelayedTextField(m_searchText, textFieldStyle, GUILayout.Width(searchFieldWidth));
		if (search != m_searchText)
		{
			m_searchText = search;
			m_searchTextLower = m_searchText.ToLower();
			UpdateSearch();
		}
		GUI.enabled = m_searchHits.Count > 0;
		if (GUILayout.Button(GetStandardIcon("animationvisibilitytoggleon"), GUILayout.Width(c_buttonHeight + 8), GUILayout.Height(c_buttonHeight)))
			FindNext(0);
		if (GUILayout.Button(GetStandardIcon("HoverBar_Up"), GUILayout.Width(c_buttonHeight+8), GUILayout.Height(c_buttonHeight)))
			FindNext(-1);
		if (GUILayout.Button(GetStandardIcon("HoverBar_Down"), GUILayout.Width(c_buttonHeight+8), GUILayout.Height(c_buttonHeight)))
			FindNext(1);
		GUI.enabled = true;
		GUILayout.Label($"{1 + m_currentSearchIndex}/{m_searchHits.Count}", GUILayout.Width(64));
		m_replaceText = GUILayout.TextField(m_replaceText, textFieldStyle, GUILayout.Width(searchFieldWidth));
		GUI.enabled = m_searchHits.Count > 0;
		if (GUILayout.Button("Replace", GUILayout.Width(c_replaceButtonWidth), GUILayout.Height(c_buttonHeight)))
			Replace();
		if (GUILayout.Button("Replace All", GUILayout.Width(c_replaceButtonWidth), GUILayout.Height(c_buttonHeight)))
			ReplaceAll();
		GUI.enabled = true;
		GUILayout.EndHorizontal();

		var headerRect = GUILayoutUtility.GetLastRect();
		var headerBottomY = headerRect.y + headerRect.height; 
		
		var width = position.width;
		if (m_showDiffs) width = width / (.4f + .6f + .6f) - (c_applyButtonWidth - c_endGap) * .6f;
		labelWidth = width * .4f;
		fieldWidth = width * .6f - c_endGap;
		const int c_maxLabelWidth = 300;
		if (labelWidth > c_maxLabelWidth)
		{
			fieldWidth += labelWidth - c_maxLabelWidth;
			labelWidth = c_maxLabelWidth;
		}
		shortFieldWidth = fieldWidth * .6f;
		
		m_scrollViewHeight = position.height - headerBottomY;
		
		const float c_columnHeaderHeight = 20;
		Colour1("");
		GUI.Box(new Rect(c_foldoutSize / 2 + labelWidth, headerBottomY, fieldWidth, m_scrollViewHeight), "");
		GUI.color = Color.yellow;
		GUI.Box(new Rect(c_foldoutSize / 2 + labelWidth, headerBottomY, fieldWidth, c_columnHeaderHeight), "");
		if (m_showDiffs)
		{
			Colour2("");
			GUI.Box(new Rect(c_foldoutSize / 2 + labelWidth + fieldWidth, headerBottomY, fieldWidth, m_scrollViewHeight), "");
			GUI.color = Color.green;
			GUI.Box(new Rect(c_foldoutSize / 2 + labelWidth + fieldWidth, headerBottomY, fieldWidth, c_columnHeaderHeight), "");
		}
		
		ColourDefault("");
		GUILayout.BeginHorizontal();
		GUILayout.TextField("Name", tabStyle, GUILayout.Width(labelWidth));
		Colour1(""); 
		GUILayout.TextField("Seed Value", tabStyle, GUILayout.Width(fieldWidth));
		if (m_showDiffs)
		{
			Colour2("");
			GUILayout.TextField("Save Value", tabStyle, GUILayout.Width(fieldWidth));
		}
		ColourDefault("");
		GUILayout.EndHorizontal();

		ColourDefault("");
		//EditorGUILayout.BeginVertical(textFieldStyle, GUILayout.MinHeight(m_scrollViewHeight - c_columnHeaderHeight));
		m_scrollPosition = GUILayout.BeginScrollView(m_scrollPosition);
		ShowObjectGUI(0, m_seedState, m_showDiffs ? m_currentState : null, "");
		GUILayout.Label("");
		ColourDefault("");
		GUILayout.EndScrollView();
		//EditorGUILayout.EndVertical();

		if (Event.current.type == EventType.Repaint)
			m_updateSearchScroll = false;
	}

	[MenuItem("22Cans/Save/Seed Window")]
	static void ShowWindow() {
		var window = GetWindow<SeedWindow>();
		window.titleContent = new GUIContent("Seed");
		window.Show();
	}
}
