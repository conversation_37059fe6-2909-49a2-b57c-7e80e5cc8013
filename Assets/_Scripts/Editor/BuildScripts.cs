using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using UnityEditor.AddressableAssets.Build;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEditor.Rendering;
using UnityEngine.Rendering;
#if UNITY_IOS
using UnityEditor.iOS.Xcode;
#endif

public class BuildScripts {
	static string[] s_scenes = FindEnabledEditorScenes();
	static string s_rootPath = "../Builds/";
	private static string[] FindEnabledEditorScenes() {
		List<string> EditorScenes = new List<string>();
		foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes) {
			if (!scene.enabled) continue;
			EditorScenes.Add(scene.path);
		}
		return EditorScenes.ToArray();
	}

	static void SetVersion() {
		PlayerSettings.bundleVersion = BuildInfo.VersionString == "Editor" ? "0.4617" : BuildInfo.VersionString;
#if UNITY_IOS
		PlayerSettings.iOS.buildNumber = BuildInfo.VersionString == "Editor" ? "0.4617" : BuildInfo.VersionString;
#elif UNITY_ANDROID
		PlayerSettings.Android.bundleVersionCode = BuildInfo.VersionString == "Editor" ? 4617 : int.Parse(BuildInfo.VersionString.Substring(2)); // substring(2) - skip the 0.
#endif
	}

	public static void CopyFilesRecursively(DirectoryInfo source, DirectoryInfo target) {
		foreach (DirectoryInfo dir in source.GetDirectories())
			CopyFilesRecursively(dir, target.CreateSubdirectory(dir.Name));
		foreach (FileInfo file in source.GetFiles())
			file.CopyTo(Path.Combine(target.FullName, file.Name), true);
	}
	public static void CopyFilesRecursively(string _source, string _target) {
		CopyFilesRecursively(new DirectoryInfo(_source), new DirectoryInfo(_target));
	}
	static string BuildFolder(BuildTarget _target) {
		switch (_target) {
			//case BuildTarget.StandaloneWindows64: return BuildTarget.StandaloneOSX.ToString();// UnityEngine.AddressableAssets.AddressablesPlatform.Windows.ToString();
			//case BuildTarget.StandaloneOSX: return UnityEngine.AddressableAssets.AddressablesPlatform.OSX.ToString();
			default: return _target.ToString();
		}
	}
	static string BuildFolderAlt(BuildTarget _target) {
		switch (_target) {
			case BuildTarget.StandaloneWindows64: return BuildTarget.StandaloneOSX.ToString();// UnityEngine.AddressableAssets.AddressablesPlatform.Windows.ToString();
			case BuildTarget.StandaloneOSX: return BuildTarget.StandaloneWindows64.ToString();// UnityEngine.AddressableAssets.AddressablesPlatform.OSX.ToString();
			default: return _target.ToString();
		}
	}
	static BuildTargetGroup TargetGroup(BuildTarget _target) {
		switch (_target) {
			case BuildTarget.iOS:
				return BuildTargetGroup.iOS;
			case BuildTarget.Android:
				return BuildTargetGroup.Android;
			default:
			case BuildTarget.StandaloneWindows64:
			case BuildTarget.StandaloneOSX:
				return BuildTargetGroup.Standalone;
		}
	}

	private static string AssetsPath => Application.streamingAssetsPath + "/Assets/";
	static void PreBuildSetup(BuildTarget _target) {
		if (Directory.Exists(AssetsPath))
			Directory.Delete(AssetsPath, true);
		CopyFilesRecursively($"ServerData/{BuildFolder(_target)}/", AssetsPath);
	}
	static void PostBuildTeardown(BuildTarget _target) {
		Directory.Delete(AssetsPath, true);
	}
	
	static void DeleteFiles(string _path, string _searchPattern) {
		try {
			var files = Directory.GetFiles(_path, _searchPattern);
			foreach (var f in files) {
				File.Delete(f);
			}
		} catch (System.Exception) {
		}
	}

	static void RefreshTextureArrays()
	{
		try
		{
			AssetDatabase.StartAssetEditing();
			AssetDatabase.ImportAsset("Assets/_Art/Terrain/Textures/Surfaces/TXA_Terrain_Albedo.texture2darray", ImportAssetOptions.ForceUpdate);
			AssetDatabase.ImportAsset("Assets/_Art/Terrain/Textures/Surfaces/TXA_Terrain_Albedo_NFT.texture2darray", ImportAssetOptions.ForceUpdate);
			AssetDatabase.ImportAsset("Assets/_Art/Terrain/Textures/Surfaces/TXA_Terrain_Normal.texture2darray", ImportAssetOptions.ForceUpdate);
		}
		catch (System.Exception _e) { Debug.LogError($"{_e}"); }
		finally { AssetDatabase.StopAssetEditing(); }
	}

	static void RunBuild(string _path, BuildTarget _target, BuildOptions _options)
	{
		//_options |= BuildOptions.DetailedBuildReport;
		RefreshTextureArrays();
		SetBuildDetails();
		SetVersion();
		EditorUserBuildSettings.SwitchActiveBuildTarget(TargetGroup(_target), _target);
#if UNITY_EDITOR && PLATFORM_STANDALONE_OSX
		UnityEditor.OSXStandalone.UserBuildSettings.createXcodeProject = s_createProject;
		if (s_createProject)
		{
			EditorUserBuildSettings.development = true;
			EditorUserBuildSettings.allowDebugging = true;
			PlayerSettings.SetIl2CppCompilerConfiguration(BuildTargetGroup.Standalone, Il2CppCompilerConfiguration.Debug);
			PlayerSettings.SetPropertyInt("cPlusPlusDebugger", 2, BuildTargetGroup.Standalone);
		}
#endif
		if (System.IO.Directory.Exists("ServerData"))
			System.IO.Directory.Delete("ServerData", true);
		Caching.ClearCache(); 
		UnityEditor.AddressableAssets.Settings.AddressableAssetSettings.CleanPlayerContent(UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.Settings.ActivePlayerDataBuilder);
		UnityEditor.AddressableAssets.Settings.AddressableAssetSettings.BuildPlayerContent(out var res);
		Debug.Log($"Addressable build player content {res.OutputPath} {res.Error}");
		UnityEditor.Build.Reporting.BuildReport report;
		try {
			PreBuildSetup(_target);
			var buildOptions = new BuildPlayerOptions
			{
				scenes = s_scenes,
				locationPathName = _path,
				target = _target,
				options = _options
			};
			report = BuildPipeline.BuildPlayer(buildOptions);
			DumpReport(report);
		} finally {
			PostBuildTeardown(_target);
		}
		if (Application.isBatchMode && report.summary.result != UnityEditor.Build.Reporting.BuildResult.Succeeded)
			EditorApplication.Exit(100 + (int)report.summary.result);
	}

	static void DumpReport(BuildReport report)
	{
		File.WriteAllText("../buildReport.json",
			JsonUtility.ToJson(report.summary, true));
		Debug.Log($"Build Step Timings:");
		foreach (var step in report.steps)
			Debug.Log($"{step.name.PadRight(30)} : {step.duration.TotalSeconds:n2}s");
	}

	const string c_mobileProductName = "MOA";
	const string c_galaProductName = "MOA";
	static string s_executableName => c_mobileProductName.ToLower();
	private static void SetBuildDetails()
	{
		var logos = new PlayerSettings.SplashScreenLogo[1];
		Sprite cans = (Sprite)AssetDatabase.LoadAssetAtPath("Assets/Resources/_Art/22Cans_Blocklogo_trans.png", typeof(Sprite));
		PlayerSettings.productName = c_mobileProductName;
		PlayerSettings.applicationIdentifier = $"com.22cans.{s_executableName}";
		logos[0] = PlayerSettings.SplashScreenLogo.Create(2, cans);
		PlayerSettings.SplashScreen.logos = logos;
	}
	
	[MenuItem("22Cans/CI/Build iOS")]
	static void iOSBuild() {
		var target = BuildTarget.iOS;
		RunBuild($"{s_rootPath}{s_executableName}.iOS", target, BuildOptions.None);
	}
	[MenuItem("22Cans/CI/Build iOS (Dev)")]
	static void iOSBuildDev() {
		var target = BuildTarget.iOS;
		RunBuild($"{s_rootPath}{s_executableName}.iOS", target, BuildOptions.Development);
	}

	static bool s_createProject = false;
	static void SetCreateProject(bool _create)
	{
		s_createProject = _create;
	}

	static void Build(BuildTarget _target, string _appName, bool _createProjectFile, BuildOptions _options)
	{
		SetCreateProject(_createProjectFile);
		RunBuild(s_rootPath + _appName, _target, _options);
	}

	[MenuItem("22Cans/CI/Build Mac OS X")]
	static void MacBuild()
	{
		Build(BuildTarget.StandaloneOSX, $"Mac/{s_executableName}.app", false, BuildOptions.None);
	}
	[MenuItem("22Cans/CI/Build Mac OS X (Dev)")]
	static void MacBuildDev()
	{
		Build(BuildTarget.StandaloneOSX, $"Mac/{s_executableName}.app", false, BuildOptions.Development);
	}

	[MenuItem("22Cans/CI/Build Mac OS X (Dev&Xcode)")]
	static void MacBuildDevXcode()
	{
		Build(BuildTarget.StandaloneOSX, $"Mac/{s_executableName}", true, BuildOptions.Development | BuildOptions.AllowDebugging);
	}

	[MenuItem("22Cans/CI/Build Mac OS X (Deep Profile)")]
	static void MacBuildDeepProfile()
	{
		Build(BuildTarget.StandaloneOSX, $"Mac/{s_executableName}.app", false, BuildOptions.Development | BuildOptions.EnableDeepProfilingSupport);
	}

	[MenuItem("22Cans/CI/Build Windows")]
	static void WinBuild()
	{
		Build(BuildTarget.StandaloneWindows64, $"Win/{s_executableName}.exe", false, BuildOptions.None);
	}
	[MenuItem("22Cans/CI/Build Windows (Dev)")]
	static void WinBuildDev()
	{
		Build(BuildTarget.StandaloneWindows64, $"Win/{s_executableName}.exe", false, BuildOptions.Development);
	}

	[MenuItem("22Cans/CI/Build Windows (Deep Profile)")]
	static void WinBuildDeepProfile()
	{
		Build(BuildTarget.StandaloneWindows64, $"Win/{s_executableName}.exe", false, BuildOptions.Development | BuildOptions.EnableDeepProfilingSupport);
	}

	[MenuItem("22Cans/CI/Build Linux")]
	static void LinuxBuild()
	{
		var target = BuildTarget.StandaloneLinux64;
		RunBuild(s_rootPath + $"Linux/{s_executableName}", target, BuildOptions.None);
	}
	[MenuItem("22Cans/CI/Build Linux (Dev)")]
	static void LinuxBuildDev()
	{
		var target = BuildTarget.StandaloneLinux64;
		RunBuild(s_rootPath + $"Linux/{s_executableName}", target, BuildOptions.Development);
	}
}

class ShaderVariantReductionBuildProcessor : IPreprocessShaders
{
	private List<string> m_keywordsToStripString = new()
	{
		"DYNAMICLIGHTMAP_ON",
		"LIGHTMAP_ON", "LIGHTMAP_SHADOW_MIXING",
		"DIRLIGHTMAP_COMBINED",
		"VERTEXLIGHT_ON",
		"FOG_LINEAR",
		"FOG_EXP",
		"FOG_EXP2",
		"_LIGHT_LAYERS",
		"DEBUG_DISPLAY",
		"_FORWARD_PLUS",
		"_DBUFFER_MRT1",
		"_DBUFFER_MRT2",
		"_DBUFFER_MRT3",
		"_RECEIVE_SHADOWS_OFF",
		"_DETAIL_MULX2 _DETAIL_SCALED",
		"_SPECULARHIGHLIGHTS_OFF",
		"_ENVIRONMENTREFLECTIONS_OFF",
		"_SPECULAR_SETUP",
		//"_ADDITIONAL_LIGHT_SHADOWS",
		//"_ADDITIONAL_LIGHTS_VERTEX",
		//"_ADDITIONAL_LIGHTS",
		"_REFLECTION_PROBE_BLENDING",
		"_REFLECTION_PROBE_BOX_PROJECTION",
		"_WRITE_RENDERING_LAYERS",
		"LIGHTMAP_SHADOW_MIXING",
		"DIRLIGHTMAP_COMBINED",
		"LIGHTMAP_ON",
		"DYNAMICLIGHTMAP_ON",
	};
	List<ShaderKeyword> m_keywordsToStrip;
	public ShaderVariantReductionBuildProcessor()
	{
		m_keywordsToStrip = new List<ShaderKeyword>();
		foreach (var r in m_keywordsToStripString)
		{
			m_keywordsToStrip.Add(new ShaderKeyword(r));
		}
	}
	public int callbackOrder => 0;
	public void OnProcessShader(Shader shader, ShaderSnippetData snippet, IList<ShaderCompilerData> shaderCompilerData)
	{
		return;
		for (int i = shaderCompilerData.Count-1; i >= 0; --i)
		{
			for (int j = 0; j < m_keywordsToStrip.Count; ++j)
			{
				if (shaderCompilerData[i].shaderKeywordSet.IsEnabled(m_keywordsToStrip[j]))
				{
					shaderCompilerData.RemoveAt(i);
					break;
				}
			}
		}
	}
}

#if UNITY_IOS
class iOSPostProcess : IPostprocessBuildWithReport
{
	public int callbackOrder
	{
		get { return 0; }
	}

	public void OnPostprocessBuild(BuildReport report)
	{
		if (report.summary.platform == BuildTarget.iOS)
		{
			string projectPath = report.summary.outputPath + "/Unity-iPhone.xcodeproj/project.pbxproj";
			
			PBXProject pbxProject = new PBXProject();
			pbxProject.ReadFromFile(projectPath);
			
			//Disabling Bitcode on all targets
			
			//Main
			string target = pbxProject.GetUnityMainTargetGuid();
			pbxProject.SetBuildProperty(target, "ENABLE_BITCODE", "NO");
			
			//Unity Tests
			target = pbxProject.TargetGuidByName(PBXProject.GetUnityTestTargetName());
			pbxProject.SetBuildProperty(target, "ENABLE_BITCODE", "NO");
			
			//Unity Framework
			target = pbxProject.GetUnityFrameworkTargetGuid();
			pbxProject.SetBuildProperty(target, "ENABLE_BITCODE", "NO");
			
			const bool c_fixMissingIcons = true;
			if (c_fixMissingIcons)
			{
				var guidARREF = pbxProject.FindFileGuidByProjectPath("Unity-iPhone/ARReferenceImages.xcassets");
				pbxProject.RemoveFile(guidARREF);
				string xcAssetsFileGuid = pbxProject.AddFile("Unity-iPhone/Images.xcassets", "Unity-iPhone/Images.xcassets");
				pbxProject.AddFileToBuild(pbxProject.GetUnityMainTargetGuid(), xcAssetsFileGuid);
			}

			pbxProject.WriteToFile(projectPath);
			
			var pbxString = File.ReadAllText(projectPath);
			pbxString = pbxString.Replace("ENABLE_BITCODE = YES;", "ENABLE_BITCODE = NO;");
			File.WriteAllText(projectPath, pbxString);
		}
	}
}
#endif
