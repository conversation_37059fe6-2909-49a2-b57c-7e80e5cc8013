#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using Object = UnityEngine.Object;

public class FindPrefabsWithComponent : EditorWindow
{
    private const int c_lineCountPerOutputTextBlock = 50;
    
    private bool m_FolderMustBeExact = false;
    private List<string> m_PrefabsInResourcesFoldersFound = new List<string>();
    
    private TextField m_TextField = null;
    private TextField m_TextFieldIManipulate = null;
    
    private Type m_TypeToSearchFor = null;
    private IPrefabEdit m_EditPrefab = null;
    
    private Vector2 m_Scroll;

    private string m_OutputMessage = "";
    
    [MenuItem("22Cans/FindPrefabsWithComponent")]
    private static void Init()
    {
        FindPrefabsWithComponent window = (FindPrefabsWithComponent)EditorWindow.GetWindow(typeof(FindPrefabsWithComponent));
        window.Show();
    }

    private string m_FolderNameToSearch = "Resources";
    
    private void OnGUI()
    {
        if(m_PrefabsInResourcesFoldersFound.Count == 0)
        {
            m_FolderNameToSearch = GUILayout.TextField(m_FolderNameToSearch);

            if (GUILayout.Button($"Find All Prefabs in {(!string.IsNullOrEmpty(m_FolderNameToSearch) ? m_FolderNameToSearch : "root")} Folders"))
            {
                FindPrefabs();
            }

            GUILayout.Space(50);
            bool result = GUILayout.Toggle(m_FolderMustBeExact,
                $"Tick to search for exact name '{m_FolderNameToSearch}'. Untick to find '{m_FolderNameToSearch}' as part of a folder name");
            
            GUILayout.Space(50);
            if (result != m_FolderMustBeExact)
            {
                m_FolderMustBeExact = result;
                FindPrefabs();
            }
            else
            {
                m_FolderMustBeExact = result;
            }
        }
        
        if (m_PrefabsInResourcesFoldersFound.Count > 0)
        {
            CreateClassTypeDragField();
            
            GUILayout.Space(50);
            
            if(m_TypeToSearchFor == null)
            {
                if (m_TextField != null && m_TextField.value.Length > 0)
                {
                    try
                    {
                        Type type = Type.GetType(m_TextField.value);
                        if (type != null)
                        {
                            m_TypeToSearchFor = type;
                        }
                    }
                    catch (Exception e)
                    {
                        m_OutputMessage = $"Could not find type '{m_TextField.value}'. If it's a UnityEngine type, drag one in from the inspector";
                    }
                }
            }
            
            if (m_TypeToSearchFor != null)
            {
                if (m_TypeToSearchFor.Name != m_TextField.value)
                {
                    m_TextField.value = "";
                    m_TypeToSearchFor = null;
                    m_PrefabsInResourcesFoldersFound.Clear();
                }
                else
                {
                    m_OutputMessage = $"'{m_TextField.value}' Ready to be applied";

                    GUILayout.Space(50);
                    
                    if (GUILayout.Button($"Find Prefabs with type {m_TypeToSearchFor.Name} components"))
                    {
                        List<string> assetsWithComponentType = new List<string>();
                        foreach (string prefabAssetPath in m_PrefabsInResourcesFoldersFound)
                        {
                            try
                            {
                                UnityEngine.Object assetLoaded = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(prefabAssetPath);
                                Component component = (assetLoaded as GameObject)?.GetComponent(m_TypeToSearchFor) as Component;
                                if (component != null)
                                {
                                    assetsWithComponentType.Add(prefabAssetPath);
                                }
                            }
                            catch (Exception e)
                            {
                                m_OutputMessage = $"Asset: {prefabAssetPath}, Exception: {e}";
                                Debug.LogError($"Asset: {prefabAssetPath}, Exception: {e}");
                                throw;
                            }
                        }

                        m_PrefabsInResourcesFoldersFound = assetsWithComponentType;
                    }
                }
            }


            if (m_EditPrefab != null)
            {
                m_OutputMessage = $"'{m_TextField.value}' Ready to Manipulate Prefab with script {m_EditPrefab.GetType().Name}";

                if (GUILayout.Button($"Manipulate Prefabs with {m_EditPrefab.GetType().Name} script"))
                {
                    string outputString = "";
                    foreach (string prefabAssetPath in m_PrefabsInResourcesFoldersFound)
                    {
                        outputString += m_EditPrefab.ManipulateObject(prefabAssetPath) + "\n";
                    }

                    AssetDatabase.SaveAssets();
                    Debug.Log($"Completed manipulation of {m_PrefabsInResourcesFoldersFound.Count} elements:\n " +
                              $"{outputString}");
                    m_OutputMessage = "Completed manipulation of {m_PrefabsInResourcesFoldersFound.Count} elements";
                }
            }

            List<string> listOfStringsToDisplay = m_PrefabsInResourcesFoldersFound;
            
            GUILayout.Space(50);
            GUILayout.Label($"Results found: {listOfStringsToDisplay.Count}");
            GUILayout.Space(50);
            
            if (GUILayout.Button($"Reset Search"))
            {
                m_FolderNameToSearch = "Resources";
                m_OutputMessage = "Search Cleared";
                m_PrefabsInResourcesFoldersFound.Clear();
                DeleteClassTypeDragField();
                return;
            }

            m_Scroll = EditorGUILayout.BeginScrollView(m_Scroll);
            for(int i = 0; i < listOfStringsToDisplay.Count; i += c_lineCountPerOutputTextBlock)
            {
                int addedLineCount = (i + c_lineCountPerOutputTextBlock < listOfStringsToDisplay.Count)
                    ? c_lineCountPerOutputTextBlock : (listOfStringsToDisplay.Count - i);
                List<string> range = listOfStringsToDisplay.GetRange(i, addedLineCount);
                string rows = string.Join('\n', range.ToArray());
                EditorGUILayout.TextArea(rows);
            }
            EditorGUILayout.EndScrollView();
        }
        else
        {
            m_OutputMessage = "No Prefabs found";
            DeleteClassTypeDragField();
        }
        
        GUILayout.Label($"Output message: {m_OutputMessage}");
    }
    
    private void FindPrefabs()
    {
        m_OutputMessage = "";
        DeleteClassTypeDragField();
        m_PrefabsInResourcesFoldersFound.Clear();
        
        List<string> allResourcesFolders = FindResourcesSubFolderRecursively(m_FolderNameToSearch);
        string[] assetsFound = AssetDatabase.FindAssets("t:prefab", allResourcesFolders.ToArray());

        List<UnityEngine.Object> alteredAssets = new List<Object>();
        foreach (string assetGuId in assetsFound)
        {
            string path = AssetDatabase.GUIDToAssetPath(assetGuId);
            m_PrefabsInResourcesFoldersFound.Add(path);
        }
        m_OutputMessage = "Found Prefab Paths";
    }
    
    private List<string> FindResourcesSubFolderRecursively(string startingFolder)
    {
        if(startingFolder.IsNullOrWhiteSpace())
        {
            startingFolder = "Assets";
        }
        string[] allSubFolders = AssetDatabase.GetSubFolders(startingFolder);
        List<string> allResourcesFolders = new List<string>();
        foreach (string subFolder in allSubFolders)
        {
            string[] splitPath = subFolder.Split('/');
            Func<string, bool> pathIsTrue = null;
            if (m_FolderMustBeExact)
            {
                if (m_FolderNameToSearch.Contains('/') && 
                    m_FolderNameToSearch.Split('/').Length > 1 && 
                    subFolder.ToLowerInvariant() == m_FolderNameToSearch.ToLowerInvariant())
                {
                    allResourcesFolders.Add(subFolder);
                    continue;
                }
                pathIsTrue = folderName=> folderName.ToLowerInvariant() == m_FolderNameToSearch.ToLowerInvariant();
            }
            else
            {
                if (m_FolderNameToSearch.Contains('/') && 
                    m_FolderNameToSearch.Split('/').Length > 1 && 
                    subFolder.ToLowerInvariant().Contains(m_FolderNameToSearch.ToLowerInvariant()))
                {
                    allResourcesFolders.Add(subFolder);
                    continue;
                }
                pathIsTrue = folderName => folderName.ToLowerInvariant().Contains(m_FolderNameToSearch.ToLowerInvariant());
            }
            string[] resourcesFolders = Array.FindAll(splitPath, x => pathIsTrue(x) );
            if (resourcesFolders.Length > 0)
            {
                allResourcesFolders.Add(subFolder);
            }
            else
            {
                allResourcesFolders.AddRange(FindResourcesSubFolderRecursively(subFolder));
            }
        }
        allResourcesFolders.Add(startingFolder);

        return allResourcesFolders;
    }

    private void CreateClassTypeDragField()
    {
        if (m_TextField == null)
        {
            m_TextField = new TextField();
            m_TextField.label = "Drag-in a Component Script \n to Filter Assets Here: ";
            GUILayout.Space(50);
            GUILayout.BeginVertical();
            rootVisualElement.Add(m_TextField);
            m_TextField.RegisterCallback<DragExitedEvent>(OnDragExited);


            m_TextFieldIManipulate = new TextField();
            m_TextFieldIManipulate.label = "Optionally Drag-in a IPrefabEdit Script \n to Batch Manipulate Assets Here: ";
            rootVisualElement.Add(m_TextFieldIManipulate);
            m_TextFieldIManipulate.RegisterCallback<DragExitedEvent>(OnDragExitedIPrefabManipulate);
            GUILayout.EndVertical();
        }
    }

    private void DeleteClassTypeDragField()
    {
        if (m_TextField != null)
        {
            rootVisualElement.Remove(m_TextField);
            m_TextField.UnregisterCallback<DragExitedEvent>(OnDragExited);
            m_TextField = null;
            m_TypeToSearchFor = null;
        }

        if (m_TextFieldIManipulate != null)
        {
            rootVisualElement.Remove(m_TextFieldIManipulate);
            m_TextFieldIManipulate.UnregisterCallback<DragExitedEvent>(OnDragExitedIPrefabManipulate);
            m_TextFieldIManipulate = null;
            m_EditPrefab = null;
        }
    }

    private void OnDragExited(DragExitedEvent evt)
    {
        if (DragAndDrop.objectReferences.Length == 1 && DragAndDrop.objectReferences[0] is MonoScript treeAsset)
        {
            Type classType = treeAsset.GetClass();
            if (classType.IsSubclassOf(typeof(Component)))
            {
                m_TextField.value = classType.Name;
                m_TypeToSearchFor = classType;
                DragAndDrop.visualMode = DragAndDropVisualMode.Link;
                DragAndDrop.AcceptDrag();
            }
            else
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Rejected;
            }
            return;
        }
        
        if (DragAndDrop.objectReferences.Length == 1 && (DragAndDrop.objectReferences[0].GetType().IsSubclassOf(typeof(Component))))
        {
            m_TypeToSearchFor = DragAndDrop.objectReferences[0].GetType();
            m_TextField.value = m_TypeToSearchFor.Name;
            DragAndDrop.visualMode = DragAndDropVisualMode.Link;
            DragAndDrop.AcceptDrag();
            return;
        }
        DragAndDrop.visualMode = DragAndDropVisualMode.Rejected;
    }

    private void OnDragExitedIPrefabManipulate(DragExitedEvent evt)
    {
        if (DragAndDrop.objectReferences.Length == 1 && DragAndDrop.objectReferences[0] is MonoScript treeAsset)
        {
            Type classType = treeAsset.GetClass();
            if (Array.FindIndex(classType.GetInterfaces(), x => x == typeof(IPrefabEdit)) > -1)
            {
                object thingie = Activator.CreateInstance(classType);
                m_EditPrefab = thingie as IPrefabEdit;
                DragAndDrop.visualMode = DragAndDropVisualMode.Link;
                DragAndDrop.AcceptDrag();
            }
            else
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Rejected;
            }
            return;
        }
        DragAndDrop.visualMode = DragAndDropVisualMode.Rejected;
    }
}
#endif
