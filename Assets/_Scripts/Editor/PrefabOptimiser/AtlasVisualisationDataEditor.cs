using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(AtlasVisualisationData))]
public class AtlasVisualisationDataEditor : Editor
{
    public override void OnInspectorGUI()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        if (GUILayout.Button("Open", GUILayout.Height(40), GUILayout.Width(200)))
        {
            var window = EditorWindow.GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
            window.visualizationData = (AtlasVisualisationData)target;
        }
        GUILayout.FlexibleSpace();
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        DrawDefaultInspector();
    }
}