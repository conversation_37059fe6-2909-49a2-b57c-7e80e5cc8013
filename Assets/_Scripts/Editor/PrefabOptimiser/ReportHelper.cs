using System;
using System.Text;
using UnityEngine;

public class ReportHelper
{
    private readonly char[] c_ToEscape = { '\\', '`', '*', '_', '{', '}', '#', '!', '|' };
    private StringBuilder m_builder = new(16384);
    public int IndentLevel;
    public int BulletLevel;

    public void AppendLine(string _line, int headingLevel = 0)
    {
        if (headingLevel > 0)
            IndentLevel = headingLevel - 1;

        m_builder.Append('>', IndentLevel);
        if (IndentLevel > 0)
            m_builder.Append(' ');

        if (BulletLevel > 0)
        {
            m_builder.Append(' ', 4 * (BulletLevel - 1));
            m_builder.Append('-');
            m_builder.Append(' ');
        }
        else
        {
            m_builder.Append('\n');
            m_builder.Append('>', IndentLevel);
            if (IndentLevel > 0)
                m_builder.Append(' ');
        }
        
        m_builder.Append('#', headingLevel);
        if (headingLevel > 0)
        {
            m_builder.Append(' ');
            if (headingLevel > IndentLevel + 1)
            {
                Debug.LogWarning("Bad heading size: must be at most one level smaller than the previous heading");
            }
            IndentLevel = headingLevel;
        }

        foreach (var c in _line)
        {
            if (c_ToEscape.Contains(c))
                m_builder.Append('\\');
            m_builder.Append(c);
        }
        m_builder.Append('\n');
    }

    public override string ToString()
    {
        return m_builder.ToString();
    }
}