using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using JBooth.MicroSplat;
using TMPro;
using UnityEngine;
using UnityEditor;
using UnityEditor.Animations;
using UnityEditorInternal;
using Object = UnityEngine.Object;
using PropertyOverride = MaterialPropertyOverride.PropertyOverride;
using FM = MeshUtils.FlexibleMesh;
using static KitBashOptimiser;
using AnimatorController = UnityEditor.Animations.AnimatorController;
using BlendTree = UnityEditor.Animations.BlendTree;

public class PrefabBatcher
{
    private static readonly Type[] c_BlacklistedComponents = { typeof(TextMeshPro) };
    private static readonly Dictionary<Type, Func<Component, IBatchWhenSame>> c_Adaptors = new()
    {
        { typeof(MicroSplatBlendableObject), c => new MSBOAdaptor((MicroSplatBlendableObject)c) }
    };
    private static readonly string[] c_BlacklistedShaders = { "Shader Graphs/WaterStreamFalls" };

    private struct DictList<T>
    {
        public List<T> Contents;

        public DictList(List<T> _contents)
        {
            Contents = _contents;
        }

        public override int GetHashCode()
        {
            int hash = 0;
            foreach (var item in Contents)
                hash ^= item.GetHashCode();
            return hash;
        }

        public override bool Equals(object obj)
        {
            if (obj is not DictList<T> other || other.Contents.Count != Contents.Count)
                return false;
            for (int i = 0; i < Contents.Count; ++i)
            {
                if (!Contents[i].Equals(other.Contents[i]))
                    return false;
            }
            return true;
        }

        public static bool operator ==(DictList<T> _a, DictList<T> _b) => _a.Equals(_b);
        public static bool operator !=(DictList<T> _a, DictList<T> _b) => !_a.Equals(_b);
    }

    private struct MatInstance
    {
        private Material m_baseMat; public Material BaseMat => m_baseMat;
        private List<PropertyOverride> m_overrides;
        private DictList<int> m_batchID;
        private int m_LOD;

        public MatInstance(MeshRenderer _mr, int _sMI, List<int> _lods)
        {
            m_baseMat = _mr.sharedMaterials[_sMI];
            var mpo = _mr.GetComponents<MaterialPropertyOverride>().Find(t => t.m_subMeshIndex == _sMI);
            m_overrides = mpo?.m_propertyOverrides;
            m_batchID = new DictList<int>(new());
            m_LOD = 0;
            foreach (var lod in _lods)
                m_LOD |= 1 << lod;
        }

        public void ClearMPO()
        {
            m_overrides = null;
        }

        public void AddBatchId(int groupID)
        {
            var intToUse = groupID >> 5;
            while (intToUse >= m_batchID.Contents.Count)
                m_batchID.Contents.Add(0);
            m_batchID.Contents[intToUse] |= 1 << (groupID & 31);
        }

        public void RemoveBatchID(int groupID)
        {
            var intToUse = groupID >> 5;
            if (intToUse >= m_batchID.Contents.Count)
                return;
            m_batchID.Contents[intToUse] &= ~(1 << (groupID & 31));
        }

        public void ClearBatchID()
        {
            m_batchID.Contents = new();
        }

        public override bool Equals(object obj)
        {
            var other = (MatInstance)obj;
            return Equivalent(this, other);
        }

        private static bool Equivalent(MatInstance _a, MatInstance _b)
        {
            if (_a.m_baseMat != _b.m_baseMat)
                return false;
            if (_a.m_batchID != _b.m_batchID)
                return false;
            if (_a.m_LOD != _b.m_LOD)
                return false;
            if (_a.m_overrides == null)
                return _b.m_overrides == null;
            if (_b.m_overrides == null)
                return false;
            if (_a.m_overrides.Count != _b.m_overrides.Count)
                return false;
            for (int i = 0; i < _a.m_overrides.Count; ++i)
            {
                var overrideA = _a.m_overrides[i];
                var overrideB = _b.m_overrides[i];
                if (overrideA.m_name != overrideB.m_name)
                    return false;
                if (!PropertyOverride.IsEq(overrideA.m_overrideC, overrideB.m_overrideC))
                    return false;
                if (!PropertyOverride.IsEq(overrideA.m_overrideF, overrideB.m_overrideF))
                    return false;
                if (overrideA.m_overrideI != overrideB.m_overrideI)
                    return false;
                if (!PropertyOverride.IsEq(overrideA.m_overrideV, overrideB.m_overrideV))
                    return false;
            }
            return true;
        }

        public override int GetHashCode()
        {
            int hash = m_baseMat.GetHashCode();
            hash = (hash * 31) ^ m_batchID.GetHashCode();
            hash = (hash * 31) ^ m_LOD.GetHashCode();
            if (m_overrides != null)
            {
                foreach (var prop in m_overrides)
                {
                    int propID = prop.m_name.GetHashCode();
                    hash ^= propID * GetVectorHashCode(prop.m_overrideC);
                    hash ^= propID * GetFloatHashCode(prop.m_overrideF);
                    hash ^= propID * prop.m_overrideI;
                    hash ^= propID * GetVectorHashCode(prop.m_overrideV);
                    hash *= 31;
                }
            }
            return hash;
        }

        private int GetVectorHashCode(Vector4 _v)
        {
            return GetFloatHashCode(_v.x) ^ GetFloatHashCode(_v.y) << 2 ^ GetFloatHashCode(_v.z) >> 2 ^ GetFloatHashCode(_v.w) >> 1;
        }

        private int GetFloatHashCode(float _f)
        {
            if (float.IsNaN(_f))
                return float.NaN.GetHashCode();
            if (float.IsPositiveInfinity(_f))
                return float.PositiveInfinity.GetHashCode();
            if (float.IsNegativeInfinity(_f))
                return float.NegativeInfinity.GetHashCode();
            return _f.GetHashCode();
        }
    }

    public static PrefabBatcher Me;

    private int m_originalRends;
    private int m_newRends;
    private Dictionary<Component, List<List<Transform>>> m_partitions;
    private List<(MatInstance, Transform)> m_originalMatInstances;
    private Dictionary<DictList<Transform>, int> m_groupIDs;
    private Dictionary<MeshRenderer, MeshRenderer> m_oldToNewRendererMap;
    private Dictionary<FM, List<MeshRenderer>> m_fmToOldRenderers;
    private int m_numShaders;
    private int m_numMaterials;

    private KitBashOptimiser m_manager;
    private GameObject m_prefab;

    private Dictionary<FM, Transform> m_fmToParentTransform = new();
    private Dictionary<FM, List<IBatchWhenSame>> m_fmComponents = new();

    public (int, int) GetRendererCount()
    {
        return (m_originalRends, m_newRends);
    }

    public void GetStatReport(ReportHelper _rep)
    {
        _rep.AppendLine($"Original MeshRenderers: {m_originalRends}");
        _rep.AppendLine($"New MeshRenderers: {m_newRends}");
        _rep.BulletLevel++;
        _rep.AppendLine($"Number of shaders (incl. variants): {m_numShaders}");
        _rep.AppendLine($"Number of materials: {m_numMaterials}");
        _rep.AppendLine($"Number of components that move or disable renderers: {m_partitions.Count}");
        _rep.BulletLevel--;
    }

    public void GetDetailedStatReport(GameObject _prefab, ReportHelper _rep)
    {
        var componentSplits = CalculateSplits(m_originalMatInstances, m_groupIDs);
        if (componentSplits.Count > 0)
        {
            _rep.AppendLine("Components that split up renderers within the batch (these do not necessarily add up):");
            componentSplits.Sort((x, y) => y.Item2.CompareTo(x.Item2));
            _rep.BulletLevel++;
            foreach (var (component, direct, indirect) in componentSplits)
            {
                //Currently not using indirect as it doesn't really make much sense
                _rep.AppendLine(
                    $"{component.GetType()} \"{component.gameObject.name}\" directly resulted in {direct} extra renderer{(direct > 1 ? "s" : "")}.");
            }

            _rep.BulletLevel--;
        }
    }

    public static void Run(GameObject _prefab, KitBashOptimiser _manager)
    {
        Me = new PrefabBatcher
        {
            m_manager = _manager,
            m_partitions = new()
        };

        Me.BatchPrefab(_prefab);
    }

    private void BatchPrefab(GameObject _prefab)
    {
        m_prefab = _prefab;
        m_oldToNewRendererMap = new Dictionary<MeshRenderer, MeshRenderer>();
        m_fmToOldRenderers = new Dictionary<FM, List<MeshRenderer>>();
        m_groupIDs = new Dictionary<DictList<Transform>, int>();
        m_originalMatInstances = new();
        
        var (rendererLODs, sortedRenderers, lodLevels) = SortRendererLODs();
        var matInstances = GetMatInstances(m_prefab, rendererLODs, out m_numShaders, out m_numMaterials);

        PartitionByAnimators(matInstances);
        PartitionByIBPs(matInstances);
        PartitionByIBWSs(matInstances);

        var matCounts = CountMats(matInstances);
        var meshPath = m_manager.GetMeshPath(m_prefab);
        var (materialMeshMap, dhMeshes, componentsToDestroy) = CollectMatMeshes(meshPath, matInstances, matCounts);
        CreateNewMeshes(materialMeshMap, dhMeshes, meshPath);
        CreateMasterLODGroup(sortedRenderers, lodLevels);

        foreach (var excluder in m_prefab.GetComponentsInChildren<IBatchPartitioner>(true))
            excluder.OnBatch(m_oldToNewRendererMap);
        
        DestroyCombinedRenderersAndFilters(componentsToDestroy);
    }
    
    private void PartitionByAnimators(Dictionary<(MeshRenderer, int), MatInstance> matInstances)
    {
        var animators = m_prefab.GetComponentsInChildren<Animator>(true);
        foreach (var animator in animators)
        {
            var groups = FindTransformsAffectedByAnimator(animator.gameObject).ConvertAll(x => new List<Transform> { x });
            m_partitions.Add(animator, new List<List<Transform>>());
            foreach (var group in groups)
                AddGroup(animator, group, m_groupIDs, matInstances);
        }
    }
    
    private void PartitionByIBPs(Dictionary<(MeshRenderer, int), MatInstance> matInstances)
    {
        foreach (var excluder in m_prefab.GetComponentsInChildren<IBatchPartitioner>(true))
        {
            var groups = excluder.GetExcludedTransforms();
            var component = excluder.Component();
            m_partitions.Add(component, new List<List<Transform>>());
            foreach (var group in groups)
                AddGroup(component, group, m_groupIDs, matInstances);
        }
    }

    private void PartitionByIBWSs(Dictionary<(MeshRenderer, int), MatInstance> matInstances)
    {
        var excluderDict = new Dictionary<(Type, int), List<Transform>>();
        foreach (var excluder in m_prefab.GetComponentsInChildren<IBatchWhenSame>(true))
        {
            var type = excluder.GetType();
            var excluderHash = excluder.BatchHash();
            if (excluderDict.TryGetValue((type, excluderHash), out var list))
                list.AddUnique(excluder.Component().transform);
            else
            {
                list = new List<Transform> { excluder.Component().transform };
                excluderDict.Add((type, excluderHash), list);
            }
        }
        foreach (var group in excluderDict.Values)
            AddGroup(null, group, m_groupIDs, matInstances);
    }

    private (Dictionary<Renderer, List<int>>, List<List<Renderer>>, List<float>) SortRendererLODs()
    {
        var groups = m_prefab.GetComponentsInChildren<LODGroup>(true);
        if (groups.Length == 0)
            return (null, null, null);
        
        int maxLevels = 0;
        foreach (var group in groups)
            maxLevels = Mathf.Max(maxLevels, group.lodCount);

        var clusters = new List<List<LOD>>(); // list of lods in each cluster
        for (int i = 0; i < maxLevels; ++i)
            clusters.Add(new List<LOD>());
        var clusterAssignments = new Dictionary<(LODGroup, int), List<int>>(); //which clusters each lod belongs to

        var groupList = new List<LODGroup>(groups);
        for (var g = 0; g < groupList.Count; ++g)
        {
            var group = groupList[g];
            if (group.lodCount < maxLevels)
                continue;
            groupList.RemoveAt(g--);
            var lods = group.GetLODs();
            for (int l = 0; l < lods.Length; ++l)
            {
                clusterAssignments[(group, l)] = new() { l };
                clusters[l] ??= new();
                clusters[l].Add(lods[l]);
            }
        }
        
        var centroids = new List<float>();
        foreach (var cluster in clusters)
        {
            float centroid = 0f;
            foreach (var lod in cluster)
                centroid += lod.screenRelativeTransitionHeight;
            centroid /= cluster.Count;
            centroids.Add(centroid);
        }
        
        foreach (var group in groupList)
        {
            var lods = group.GetLODs();
            var assignedClusters = new HashSet<int>();
            
            
            for (int l = 0; l < lods.Length; ++l)
            {
                var bestClust = -1;
                var bestDistSqr = float.MaxValue;
                var srth = lods[l].screenRelativeTransitionHeight;
                for (int c = 0; c < centroids.Count; ++c)
                {
                    var dist = srth - centroids[c];
                    var sqrDist = dist * dist;
                    if (sqrDist < bestDistSqr)
                    {
                        bestDistSqr = sqrDist;
                        bestClust = c;
                    }
                }
                clusterAssignments[(group, l)] = new() { bestClust };
                clusters[bestClust].Add(lods[l]);
                assignedClusters.Add(bestClust);
            }
            for (int c = 0; c < centroids.Count; ++c)
            {
                if (assignedClusters.Contains(c))
                    continue;
                
                var bestLOD = -1;
                var bestDistSqr = float.MaxValue;
                var srth = centroids[c];
                for (int l = 0; l < lods.Length; ++l)
                {
                    var dist = srth - lods[l].screenRelativeTransitionHeight;
                    var distSqr = dist * dist;
                    if (distSqr < bestDistSqr)
                    {
                        bestDistSqr = distSqr;
                        bestLOD = l;
                    }
                }
                clusterAssignments[(group, bestLOD)].Add(c);
                clusters[c].Add(lods[bestLOD]);
            }
        }
        var lodRends = new Dictionary<Renderer, List<int>>();
        foreach (var ((group, level), cluster) in clusterAssignments)
        {
            var lod = group.GetLODs()[level];
            foreach (var rend in lod.renderers)
            {
                if (rend == null)
                    continue;
                
                if (!lodRends.TryGetValue(rend, out var list))
                {
                    list = new List<int>();
                    lodRends.Add(rend, list);
                }
                list.AddRange(cluster);
            }
        }
        var rendLODS = new List<List<Renderer>>();
        foreach (var cluster in clusters)
        {
            var renderers = new List<Renderer>();
            foreach (var lod in cluster)
                renderers.AddRange(lod.renderers);
            rendLODS.Add(renderers);
        }
        centroids.Clear();
        foreach (var cluster in clusters)
        {
            float centroid = 0f;
            foreach (var lod in cluster)
                centroid += lod.screenRelativeTransitionHeight;
            centroid /= cluster.Count;
            centroids.Add(centroid);
        }
        
        foreach (var lodGroup in groups)
            Object.DestroyImmediate(lodGroup);

        return (lodRends, rendLODS, centroids);
    }
    
    private void CreateMasterLODGroup(List<List<Renderer>> _rendLODs, List<float> _lodLevels)
    {
        if (_lodLevels == null || _lodLevels.Count == 0)
            return;
        
        for (int i = 0; i < _rendLODs.Count; ++i)
        {
            var rends = _rendLODs[i];
            var newRends = new List<Renderer>();
            foreach (var rend in rends)
            {
                if (rend is MeshRenderer mr && m_oldToNewRendererMap.TryGetValue(mr, out var newRend))
                    newRends.AddUnique(newRend);
                else
                    newRends.AddUnique(rend);
            }
            _rendLODs[i] = newRends;
        }
        
        var masterLODGroup = m_prefab.AddComponent<LODGroup>();
        var lods = new LOD[_rendLODs.Count];
        for (int i = 0; i < _rendLODs.Count; ++i)
        {
            var renderers = _rendLODs[i].ToArray();
            lods[i] = new LOD(_lodLevels[i], renderers);
        }
        masterLODGroup.SetLODs(lods);
        masterLODGroup.RecalculateBounds();
    }

    private bool GetAllValidSubMeshes(Renderer _renderer, out List<int> _validSubmeshes)
    {
        _validSubmeshes = new();
        foreach (var comp in c_BlacklistedComponents)
        {
            if (_renderer.GetComponent(comp) != null)
            {
                Debug.Log($"Not batching {_renderer} as it has blacklisted component {comp.Name}");
                return false;
            }
        }

        var filter = _renderer.GetComponent<MeshFilter>();
        var mesh = filter?.sharedMesh;
        if (mesh == null)
        {
            Debug.LogWarning($"{_renderer} has null mesh!");
            return false;
        }

        if (mesh.uv.Length < mesh.vertexCount)
        {
            Debug.Log($"{_renderer} has mesh {mesh} with no UVs, skipping");
            return false;
        }

        for (int j = 0; j < mesh.subMeshCount; ++j)
        {
            var mat = _renderer.sharedMaterials[j];
            if (mat == null)
            {
                Debug.LogWarning($"{_renderer} has null mat on submesh {j}");
                continue;
            }
            if (c_BlacklistedShaders.Contains(mat.shader.name))
            {
                Debug.Log($"{_renderer} has blacklisted shader {mat.shader.name} on submesh {j}, skipping");
                continue;
            }
            _validSubmeshes.Add(j);
        }
        return true;
    }

    private Dictionary<(MeshRenderer, int), MatInstance> GetMatInstances(GameObject _prefab, Dictionary<Renderer, List<int>> _rendLODs, out int _shaders, out int _mats)
    {
        var shaders = new HashSet<Material>();
        var mats = new HashSet<MatInstance>();

        var matInstances = new Dictionary<(MeshRenderer, int), MatInstance>();
        var rends = _prefab.GetComponentsInChildren<MeshRenderer>(true);
        m_newRends = m_originalRends = rends.Length;
        foreach (var mr in rends)
        {
            if (_rendLODs == null || !_rendLODs.TryGetValue(mr, out var lods))
                lods = new();
            if (GetAllValidSubMeshes(mr, out var smIs))
            {
                foreach (var j in smIs)
                {
                    var mat = mr.sharedMaterials[j];
                    shaders.Add(mat);
                    var matInst = new MatInstance(mr, j, lods);
                    mats.Add(matInst);
                    m_originalMatInstances.Add((matInst, mr.transform));
                    matInstances.Add((mr, j), matInst);
                }
            }
        }
        _shaders = shaders.Count;
        _mats = mats.Count;
        return matInstances;
    }

    private void AddGroup(Component _excluder, List<Transform> _transforms,
        Dictionary<DictList<Transform>, int> groupIDs, Dictionary<(MeshRenderer, int), MatInstance> matInstances)
    {
        _transforms = _transforms.FindAll(x => x != null);
        if (_transforms.Count == 0)
            return;
        var dictList = new DictList<Transform>(_transforms);
        if (groupIDs.ContainsKey(dictList))
            return;
        if (_excluder != null)
            m_partitions[_excluder].Add(_transforms);
        var key = groupIDs.Count;
        groupIDs.Add(dictList, key);
        foreach (var transform in _transforms)
        {
            var rends = transform.GetComponentsInChildren<MeshRenderer>(true);
            foreach (var rend in rends)
            {
                if (GetAllValidSubMeshes(rend, out var smIs))
                {
                    foreach (var smI in smIs)
                        matInstances[(rend, smI)].AddBatchId(key);
                }
            }
        }
    }

    private Dictionary<MatInstance, int> CountMats(Dictionary<(MeshRenderer, int), MatInstance> _matInsts)
    {
        var matCounts = new Dictionary<MatInstance, int>();
        foreach (var (_, matInst) in _matInsts)
        {
            if (!matCounts.TryAdd(matInst, 1))
                matCounts[matInst]++;
        }
        return matCounts;
    }

    private (Dictionary<MatInstance, FM>, Dictionary<FM, List<DecorationHolder>>, List<Component>) CollectMatMeshes(
        string _meshPath, Dictionary<(MeshRenderer, int), MatInstance> _matInsts, Dictionary<MatInstance, int> _matCounts)
    {
        var materialMeshMap = new Dictionary<MatInstance, FM>();
        var dhMeshes = new Dictionary<FM, List<DecorationHolder>>();
        var renderersToCombine = new HashSet<MeshRenderer>();

        foreach (var ((mr, smi), matInstance) in _matInsts)
        {
            if (mr == null || _matCounts[matInstance] <= 1)
                continue;

            renderersToCombine.Add(mr);

            var mf = mr.GetComponent<MeshFilter>();
            var mesh = mf.sharedMesh;
            var dh = mr.GetComponentInParent<DecorationHolder>();
            
            materialMeshMap.TryAdd(matInstance, new FM(true));
            var fM = materialMeshMap[matInstance];
            var matrix = m_prefab.transform.worldToLocalMatrix * mr.transform.localToWorldMatrix;
            fM.AddMesh(mesh, matrix, false, smi);
            bool isFirst = !m_fmToOldRenderers.ContainsKey(fM);
            if (isFirst)
            {
                m_fmToOldRenderers.Add(fM, new List<MeshRenderer>());
                m_fmToParentTransform.Add(fM, mr.transform);
                m_fmComponents.Add(fM, new List<IBatchWhenSame>());
            }
            else
                m_fmToParentTransform[fM] = FindSharedParent(m_fmToParentTransform[fM], mr.transform);
            
            var specialComps = m_fmComponents[fM];
            var bwss = mr.transform.GetComponents<IBatchWhenSame>().ToList();
            foreach (var (type, adaptor) in c_Adaptors)
            {
                foreach (var component in mr.transform.GetComponents(type))
                    bwss.Add(adaptor(component));
            }
            foreach (var bws in bwss)
            {
                if (!bws.IsApplicable(smi))
                    continue;
                if (isFirst)
                    specialComps.Add(bws);
                else
                    Object.DestroyImmediate(bws.Component());
            }

            m_fmToOldRenderers[fM].AddUnique(mr);

            if (dh == null)
                continue;
                
            if (!dhMeshes.TryGetValue(fM, out var list))
            {
                list = new List<DecorationHolder>();
                dhMeshes.Add(fM, list);
            }
            list.AddUnique(dh);
        }
        
        var componentsToDestroy = new List<Component>();
        foreach(var mr in renderersToCombine)
        {
            m_newRends--;
            componentsToDestroy.Add(mr);
            componentsToDestroy.Add(mr.GetComponent<MeshFilter>());
        }
        
        var processedStragglers = new HashSet<MeshRenderer>();
        foreach (var ((mr, _), _) in _matInsts)
        {
            if (mr == null || renderersToCombine.Contains(mr) || !processedStragglers.Add(mr))
                continue;
            
            var mf = mr.GetComponent<MeshFilter>();
            if (mf == null)
                continue;
            var mesh = mf.sharedMesh;
            var meshAssetPath = Path.Combine(_meshPath, $"{mf.name}({mf.GetInstanceID()}).asset");
            if (!AssetDatabase.Contains(mesh))
                SaveAsset(mesh, meshAssetPath);
        }

        return (materialMeshMap, dhMeshes, componentsToDestroy);
    }

    private void CreateNewMeshes(Dictionary<MatInstance, FM> _materialMeshMap,
        Dictionary<FM, List<DecorationHolder>> _dhMeshMap, string _meshPath)
    {
        foreach (var (mI, fM) in _materialMeshMap)
        {
            var mat = mI.BaseMat;
            var pathSafeName = string.Join("_", mat.name.Split(Path.GetInvalidPathChars()));

            var parentTransform = m_fmToParentTransform[fM];

            var holder = new GameObject($"CM_{pathSafeName}");
            holder.transform.SetParent(m_prefab.transform, false);
            holder.transform.localPosition = Vector3.zero;
            holder.transform.localRotation = Quaternion.identity;
            holder.transform.localScale = Vector3.one;
            // Bit hacky but gets the job done
            holder.transform.SetParent(parentTransform, true);
            

            fM.Optimise();
            var mesh = fM.Mesh();

            m_newRends++;
            var mf = holder.AddComponent<MeshFilter>();

            var meshAssetPath = Path.Combine(_meshPath, $"{mf.name}({mf.GetInstanceID()}).asset");
            SaveAsset(mesh, meshAssetPath);
            mf.sharedMesh = mesh;
            var mr = holder.AddComponent<MeshRenderer>();
            var components = m_fmComponents[fM];
            foreach (var bws in components) //Must be after adding the renderer, some components require it
            {
                var component = bws.Component();
                ComponentUtility.CopyComponent(component);
                ComponentUtility.PasteComponentAsNew(holder);
                bws.OnBatch(holder.GetComponent(component.GetType()));
                try
                {
                    Object.DestroyImmediate(component);
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"Exception destroying component {component.GetType().Name} on {holder.name}: {e.Message}");
                }
            }

            mr.sharedMaterial = mat;
            if (m_fmToOldRenderers.TryGetValue(fM, out var oldRenderers))
            {
                foreach (var oldRenderer in oldRenderers)
                {
                    m_oldToNewRendererMap[oldRenderer] = mr;
                }
            }

            if (!_dhMeshMap.TryGetValue(fM, out var dhs))
                continue;
            var myDH = mr.GetComponentInParent<DecorationHolder>();
            foreach (var dh in dhs)
            {
                if (dh == myDH)
                    continue;
                dh.m_externalMeshRenderers.AddUnique(mr);
            }
        }
    }

    private void DestroyCombinedRenderersAndFilters(List<Component> componentsToDestroy)
    {
        foreach(var component in componentsToDestroy)
        {
            if (component != null)
                Object.DestroyImmediate(component);
        }
    }

    private List<(Component, int, int)> CalculateSplits(List<(MatInstance, Transform)> _matInsts, Dictionary<DictList<Transform>, int> _groupIDs)
    {
        var current = new HashSet<MatInstance>(_matInsts.ConvertAll(x => x.Item1)).Count;
        var partitionsDirect = new List<(Component, int, int)>();
        foreach (var (component, groups) in m_partitions)
        {
            var matInstsCopy = new List<(MatInstance, Transform)>(_matInsts);
            foreach (var group in groups)
            {
                var groupID = _groupIDs[new DictList<Transform>(group)];
                for (int i = 0; i < matInstsCopy.Count; ++i)
                {
                    var (matInst, t) = matInstsCopy[i];
                    matInst.RemoveBatchID(groupID);
                    matInstsCopy[i] = (matInst, t);
                }
            }
            var direct = current - new HashSet<MatInstance>(matInstsCopy.ConvertAll(x => x.Item1)).Count;

            for (int i = 0; i < matInstsCopy.Count; ++i)
            {
                var (matInst, t) = matInstsCopy[i];
                matInst.ClearBatchID();
                matInstsCopy[i] = (matInst, t);
            }
            var min = new HashSet<MatInstance>(matInstsCopy.ConvertAll(x => x.Item1)).Count;
            foreach (var group in groups)
            {
                var groupID = _groupIDs[new DictList<Transform>(group)];
                foreach (var (matInst, t) in matInstsCopy)
                {
                    foreach (var transform in group)
                    {
                        if (!t.IsChildOf(transform))
                            continue;
                        matInst.AddBatchId(groupID);
                        break;
                    }
                }
            }
            var indirect = new HashSet<MatInstance>(matInstsCopy.ConvertAll(x => x.Item1)).Count - min;
            if (direct > 0)
                partitionsDirect.Add((component, direct, indirect));
        }
        return partitionsDirect;
    }

    public static List<Transform> FindTransformsAffectedByAnimator(GameObject _anim)
    {
        var aC = _anim.GetComponent<Animator>().runtimeAnimatorController as AnimatorController;
        if (aC == null)
        {
            Debug.LogWarning($"Animator on {_anim} has no controller!");
            return new();
        }

        var transforms = new HashSet<Transform>();
        foreach (AnimationClip clip in GetAllClips(aC))
            ProcessClip(clip, _anim.transform, transforms);
        return new List<Transform>(transforms);
    }

    private static List<AnimationClip> GetAllClips(AnimatorController controller)
    {
        var clips = new List<AnimationClip>();
        foreach (var layer in controller.layers)
            TraverseStateMachine(layer.stateMachine, clips);
        return clips;
    }

    private static void TraverseStateMachine(AnimatorStateMachine stateMachine, List<AnimationClip> clips)
    {
        foreach (var state in stateMachine.states)
            ProcessMotion(state.state.motion, clips);
        foreach (var subStateMachine in stateMachine.stateMachines)
            TraverseStateMachine(subStateMachine.stateMachine, clips);
    }

    private static void ProcessMotion(Motion motion, List<AnimationClip> clips)
    {
        if (motion is AnimationClip clip)
        {
            if (!clips.Contains(clip))
                clips.Add(clip);
        }
        else if (motion is BlendTree blendTree)
        {
            foreach (ChildMotion child in blendTree.children)
                ProcessMotion(child.motion, clips);
        }
    }

    private static void ProcessClip(AnimationClip clip, Transform root, HashSet<Transform> transforms)
    {
        foreach (var binding in AnimationUtility.GetCurveBindings(clip))
        {
            if (binding.type == typeof(Transform))
            {
                Transform target = FindTransform(root, binding.path);
                if (target != null) transforms.Add(target);
            }
        }
    }

    private static Transform FindTransform(Transform root, string path)
    {
        if (string.IsNullOrEmpty(path))
            return root;
        Transform result = root.Find(path);
        if (result == null)
            Debug.LogWarning($"Animator \"{root.name}\" references non-existent transform: {path}");
        return result;
    }
    
    private static Transform FindSharedParent(Transform a, Transform b)
    {
        if (a == null || b == null)
            return null;
        while (a != null && !b.IsChildOf(a))
            a = a.parent;
        return a;
    }
}