using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

[CustomEditor(typeof(MaterialPropertyOverride))]
public class MaterialPropertyOverrideEditor : Editor
{
    MaterialPropertyOverride mpo;
    
    void OnEnable()
    {
        mpo = (MaterialPropertyOverride)target;
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        DrawDefaultInspector();
        
        EditorGUI.BeginChangeCheck();
        bool newValue = EditorGUILayout.Toggle("Apply Editor Preview", mpo.EditorPreview);
        if (EditorGUI.EndChangeCheck())
        {
            mpo.EditorPreview = newValue;
            mpo.ApplyEditorPreview();
        }

        var overridesProp = serializedObject.FindProperty("m_materialPropertyOverrides");
        if (overridesProp is not { isArray: true, arraySize: > 0 }) 
            return;
        
        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField($"Property Overrides ({overridesProp.arraySize})", EditorStyles.boldLabel);

        for (int i = 0; i < overridesProp.arraySize; i++)
        {
            var elem = overridesProp.GetArrayElementAtIndex(i);
            var nameProp = elem.FindPropertyRelative("m_name");
            var colorProp = elem.FindPropertyRelative("m_overrideC");
            var vectorProp = elem.FindPropertyRelative("m_overrideV");
            var floatProp = elem.FindPropertyRelative("m_overrideF");
            var intProp = elem.FindPropertyRelative("m_overrideI");

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(nameProp.stringValue, GUILayout.Width(150));

            var rend = mpo.GetComponent<Renderer>();
            if (rend != null &&
                rend.sharedMaterials.Length > mpo.m_subMeshIndex &&
                rend.sharedMaterials[mpo.m_subMeshIndex]?.shader != null)
            {
                var mat = rend.sharedMaterials[mpo.m_subMeshIndex];
                int idx = mat.shader.FindPropertyIndex(nameProp.stringValue);
                if (idx >= 0)
                {
                    var type = mat.shader.GetPropertyType(idx);
                    switch (type)
                    {
                        case ShaderPropertyType.Color:
                            EditorGUILayout.PropertyField(colorProp, GUIContent.none);
                            break;
                        case ShaderPropertyType.Vector:
                            EditorGUILayout.PropertyField(vectorProp, GUIContent.none);
                            break;
                        case ShaderPropertyType.Float:
                        case ShaderPropertyType.Range:
                            EditorGUILayout.PropertyField(floatProp, GUIContent.none);
                            break;
                        case ShaderPropertyType.Int:
                            EditorGUILayout.PropertyField(intProp, GUIContent.none);
                            break;
                    }
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndVertical();
    }
}