using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

public class HierarchicalSelector : EditorWindow
{
    private List<string> _initialPaths;
    private TaskCompletionSource<List<string>> _taskCompletionSource;

    private PathTreeView _treeView;
    private TreeViewState _treeState;

    public static Task<List<string>> GetSelection(List<string> paths)
    {
        var window = GetWindow<HierarchicalSelector>(true, "Path Selector");
        window.minSize = new Vector2(300, 400);
        
        return window.Initialize(paths);
    }

    private Task<List<string>> Initialize(List<string> paths)
    {
        _initialPaths = paths;
        _taskCompletionSource = new TaskCompletionSource<List<string>>();

        _treeState = new TreeViewState();
        var commonRoot = GetCommonRoot(_initialPaths);
        var root = BuildHierarchy(_initialPaths, commonRoot);
        _treeView = new PathTreeView(_treeState, root);
        _treeView.ExpandAll();
        
        return _taskCompletionSource.Task;
    }

    private void OnGUI()
    {
        if (_treeView == null) return;

        EditorGUILayout.LabelField("Select folders to run:", EditorStyles.boldLabel);
        
        Rect viewRect = GUILayoutUtility.GetRect(0f, 0f, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
        _treeView.OnGUI(viewRect);

        if (GUILayout.Button("Confirm Selection"))
        {
            var selectedPaths = _treeView.GetSelectedLeafPaths();
            _taskCompletionSource.SetResult(selectedPaths);
            Close();
        }
    }

    private void OnDestroy()
    {
        _taskCompletionSource?.TrySetResult(null);
    }
    
    private static string GetCommonRoot(List<string> paths)
    {
        if (paths == null || paths.Count == 0)
            return string.Empty;

        var split = paths
            .Select(p => p.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries))
            .ToArray();
        
        if (!split.Any()) return string.Empty;

        var shortest = split.OrderBy(a => a.Length).First();
        int i = 0;
        while (i < shortest.Length && split.All(s => s[i] == shortest[i]))
            i++;

        return string.Join("/", shortest.Take(i));
    }

    private static TreeElement BuildHierarchy(List<string> paths, string basePath)
    {
        int idCounter = 0;
        var validPaths = paths?.Where(p => !string.IsNullOrEmpty(p)).ToList() ?? new List<string>();

        var hiddenRoot = new TreeElement(id: idCounter++, depth: -1, nameSegment: "HiddenRoot") { FullPath = "" };

        if (validPaths.Count == 0) return hiddenRoot;

        var rootName = basePath.Contains('/') ? basePath.Substring(basePath.LastIndexOf('/') + 1) : basePath;
        if (string.IsNullOrEmpty(rootName)) rootName = "Project Root";
        
        var visualRoot = new TreeElement(id: idCounter++, depth: 0, nameSegment: rootName)
        {
            FullPath = basePath
        };
        hiddenRoot.AddChild(visualRoot);

        var map = new Dictionary<string, TreeElement> { [basePath] = visualRoot };

        foreach (var p in validPaths)
        {
            if (p == basePath) continue;

            if (!p.StartsWith(basePath)) continue;

            var rel = p.Substring(basePath.Length).TrimStart('/');
            var parts = rel.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);

            string accum = basePath;
            TreeElement parent = visualRoot;

            foreach (var part in parts)
            {
                accum = string.IsNullOrEmpty(accum) ? part : accum + "/" + part;

                if (!map.TryGetValue(accum, out var node))
                {
                    node = new TreeElement(id: idCounter++, depth: parent.depth + 1, nameSegment: part)
                    {
                        FullPath = accum
                    };
                    parent.AddChild(node);
                    map[accum] = node;
                }
                parent = node;
            }
        }
        
        return hiddenRoot;
    }
    
    private enum CheckState
    {
        Unchecked,
        Checked,
        Third
    }

    private class TreeElement : TreeViewItem
    {
        public string FullPath { get; set; }
        public TreeElement(int id, int depth, string nameSegment)
            : base(id, depth, nameSegment) { }
    }

    private class PathTreeView : TreeView
    {
        private readonly TreeElement _rootElement;
        private readonly Dictionary<int, CheckState> _checked = new();

        public PathTreeView(TreeViewState state, TreeElement root)
            : base(state)
        {
            _rootElement = root;
            Reload();
        }

        protected override TreeViewItem BuildRoot()
        {
            _rootElement.children ??= new List<TreeViewItem>();
            SetupDepthsFromParentsAndChildren(_rootElement);
            SetCheckRecursive(_rootElement, CheckState.Checked);
            return _rootElement;
        }

        protected override void RowGUI(RowGUIArgs args)
        {
            var item = (TreeElement)args.item;
            Rect toggleRect = args.rowRect;
            toggleRect.x += GetContentIndent(item);
            toggleRect.width = 16;

            // Ensure default state exists
            if (!_checked.ContainsKey(item.id))
                _checked[item.id] = CheckState.Checked;

            var currentState = _checked[item.id];
            var e = Event.current;

            // Detect click on the checkbox area
            if (e.type == EventType.MouseDown && e.button == 0 && toggleRect.Contains(e.mousePosition))
            {
                bool shift = e.shift;
                CheckState newState;
                if (shift)
                    newState = currentState == CheckState.Third ? CheckState.Unchecked : CheckState.Third;
                else
                    newState = currentState == CheckState.Third ? CheckState.Unchecked : 
                              (currentState == CheckState.Checked ? CheckState.Unchecked : CheckState.Checked);

                SetCheckRecursive(item, newState);
                SetParentChecks(item.parent as TreeElement);
                e.Use();
            }

            // Draw a normal toggle but use boolean for appearance
            bool boolState = currentState == CheckState.Checked;
            EditorGUI.Toggle(toggleRect, boolState);
            
            // Overlay "!" if in the third state, centered in the box
            if (currentState == CheckState.Third)
            {
                var style = new GUIStyle(GUI.skin.label)
                {
                    alignment = TextAnchor.MiddleCenter,
                    fontStyle = FontStyle.Bold
                };
                GUI.Label(toggleRect, "!", style);
            }
               
            args.rowRect.xMin += 20;
            base.RowGUI(args);
        }

        private void SetCheckRecursive(TreeElement el, CheckState state)
        {
            _checked[el.id] = state;
            if (!el.hasChildren) return;
            foreach (TreeElement child in el.children)
                SetCheckRecursive(child, state);
        }

        private void SetParentChecks(TreeElement parent)
        {
            if (parent == null || parent.children == null || parent.children.Count == 0)
                return;
                
            // If any child is not unchecked then set parent as Checked
            bool anySet = parent.children.Cast<TreeElement>().Any(c => _checked.ContainsKey(c.id) && _checked[c.id] != CheckState.Unchecked);
            _checked[parent.id] = anySet ? CheckState.Checked : CheckState.Unchecked;
            SetParentChecks(parent.parent as TreeElement);
        }

        public List<string> GetSelectedLeafPaths()
        {
            var res = new List<string>();
            CollectCheckedLeafPaths(_rootElement, res);
            return res;
        }

        private void CollectCheckedLeafPaths(TreeElement el, List<string> outList)
        {
            if (!_checked.TryGetValue(el.id, out var state))
                return;

            // Only add leaf if state is Checked
            if (!el.hasChildren)
            {
                if (state == CheckState.Checked)
                    outList.Add(el.FullPath);
            }
            else
            {
                foreach (TreeElement child in el.children)
                    CollectCheckedLeafPaths(child, outList);
            }
        }
    }
}