using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

public class AssetRemapperWindow
{
    public static int RemapAssetReferences(
        Dictionary<string, string> guidMap,
        List<Object> targets)
    {
        var assetPaths = new List<string>();
        foreach (var tgt in targets)
        {
            var path = AssetDatabase.GetAssetPath(tgt);
            if (string.IsNullOrEmpty(path))
                continue;
            if (path.EndsWith(".unity") || path.EndsWith(".prefab"))
                assetPaths.Add(path);
        }

        return RemapGuidsInAssets(guidMap, assetPaths);
    }

    private static int RemapGuidsInAssets(
        Dictionary<string, string> guidMap,
        List<string> assetPaths)
    {
        int totalReplacements = 0;

        foreach (var path in assetPaths)
        {
            if (!File.Exists(path))
                continue;

            var text = File.ReadAllText(path);
            var original = text;

            foreach (var kv in guidMap)
                text = text.Replace(kv.Key, kv.Value);

            if (text != original)
            {
                File.WriteAllText(path, text);
                totalReplacements += CountReplacements(original, guidMap);
                AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
            }
        }

        return totalReplacements;
    }

    private static int CountReplacements(
        string before,
        Dictionary<string, string> guidMap)
    {
        int count = 0;
        foreach (var kv in guidMap)
        {
            var newGuid = kv.Value;
            int idx = 0;
            while ((idx = before.IndexOf(kv.Key, idx)) != -1)
            {
                count++;
                idx += kv.Key.Length;
            }
        }
        return count;
    }
}