using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

public class MeshRendererDiffWindow : EditorWindow
{
    private MeshRenderer rendA;
    private MeshRenderer rendB;
    private bool showFirstOnly = true;
    private bool showFullPaths = false;
    private string diffResult = "Please select two MeshRenderers to compare.";

    [MenuItem("Art Tools/MeshRenderer Diff")] 
    public static void ShowWindow()
    {
        var window = GetWindow<MeshRendererDiffWindow>("MeshRenderer Diff");
        window.minSize = new Vector2(300, 150);
    }
    
    private void OnEnable()
    {
        EditorGUI.hyperLinkClicked += OnLinkClicked;
    }

    private void OnDisable()
    {
        EditorGUI.hyperLinkClicked -= OnLinkClicked;
    }

    private static void OnLinkClicked(EditorWindow _window, HyperLinkClickedEventArgs _args)
    {
        var stringID = _args.hyperLinkData["ping"];
        if (int.TryParse(stringID, out var id))
        {
            var obj = EditorUtility.InstanceIDToObject(id);
            if (obj != null)
            {
                EditorGUIUtility.PingObject(obj);
                Selection.activeObject = obj;
            }
        }
    }

    private void OnGUI()
    {
        GUILayout.Space(10);
        
        GUIStyle titleStyle = new (EditorStyles.largeLabel) { fontSize = 18, fontStyle = FontStyle.Bold, alignment = TextAnchor.MiddleCenter };
        EditorGUILayout.LabelField("MeshRenderer Combine Troubleshooter", titleStyle);
        
        GUILayout.Space(5);
        
        DrawLine();

        GUILayout.Space(10);
        EditorGUI.indentLevel++;

        EditorGUI.BeginChangeCheck();
        rendA = (MeshRenderer)EditorGUILayout.ObjectField("Mesh Renderer A", rendA, typeof(MeshRenderer), true);
        rendB = (MeshRenderer)EditorGUILayout.ObjectField("Mesh Renderer B", rendB, typeof(MeshRenderer), true);
        var refresh = EditorGUI.EndChangeCheck();

        EditorGUI.indentLevel--;
        GUILayout.Space(10);

        refresh |= GUILayout.Button("Refresh");

        GUILayout.Space(10);

        EditorGUI.BeginChangeCheck();
        EditorGUILayout.BeginHorizontal();
        showFirstOnly = EditorGUILayout.Toggle("Show First Only", showFirstOnly);
        showFullPaths = EditorGUILayout.Toggle("Show Full Paths", showFullPaths);
        EditorGUILayout.EndHorizontal();
        refresh |= EditorGUI.EndChangeCheck();

        if (refresh)
        {
            if (rendA != null && rendB != null && rendA != rendB)
            {
                try
                {
                    diffResult = GetMeshRendererDiff(rendA, rendB, showFirstOnly, showFullPaths);
                }
                catch (ReturnException e)
                {
                    diffResult = e.Value;
                }
                catch (Exception e)
                {
                    diffResult = $"An error occurred while comparing MeshRenderers: {e.Message}";
                }
            }
            else
                diffResult = "Please select two different MeshRenderers to compare.";
        }

        GUILayout.Space(10);
        EditorGUILayout.LabelField("Why these didn't combine:", EditorStyles.boldLabel);

        var bigInfoStyle = new GUIStyle(EditorStyles.helpBox) { fontSize = 14, richText = true, wordWrap = true };

        var content = new GUIContent(diffResult);
        float width = EditorGUIUtility.currentViewWidth - 35;
        float height = bigInfoStyle.CalcHeight(content, width);

        EditorGUILayout.SelectableLabel(diffResult, bigInfoStyle, GUILayout.Height(height));
    }
    
    private void DrawLine()
    {
        var r = GUILayoutUtility.GetRect(1, 1);
        r.xMin = 0; r.xMax = EditorGUIUtility.currentViewWidth;
        EditorGUI.DrawRect(r, new Color(0.4f, 0.4f, 0.4f, 0.5f));
    }

    private class ReturnException : Exception
    {
        public string Value { get; }
        public ReturnException(string value) : base(value) => Value = value;
    }

    //Why the renderers didn't get merged
    private static string GetMeshRendererDiff(MeshRenderer a, MeshRenderer b, bool showFirstOnly, bool showFullPaths)
    {
        var output = "";
        var Output = showFirstOnly ? (Action<string>)(reason => throw new ReturnException(reason)) : reason => output += $"{reason}\n\n";

        string Name(Transform t)
        {
            var display = showFullPaths ? t.Path(9) : t.name;
            return $"<a ping=\"{t.GetInstanceID()}\"><color=#569CD6><u>{display}</u></color></a>";
        }
        
        if (a.transform.root != b.transform.root)
            Output($"<u>These MeshRenderers are not in the same prefab:</u>\n<b>A:</b> {a.transform.root.name}\n<b>B:</b> {b.transform.root.name}");
        
        var matA = a.sharedMaterial;
        var matB = b.sharedMaterial;
        
        if (matA.shader != matB.shader)
            Output($"<u>Shaders differ:</u>\nA: {matA.shader.name}\nB: {matB.shader.name}");
        
        var keywordsA = new HashSet<string>(matA.shaderKeywords);
        var keywordsB = new HashSet<string>(matB.shaderKeywords);
        var keywordsBUnique = new HashSet<string>(keywordsB);

        foreach (var keyword in keywordsA)
            keywordsBUnique.Remove(keyword);
        foreach (var keyword in keywordsB)
            keywordsA.Remove(keyword);

        if (keywordsA.Count > 0 || keywordsBUnique.Count > 0)
        {
            var aString = string.Join(", ", keywordsA);
            var bString = string.Join(", ", keywordsBUnique);
            Output($"<u>Shader keywords differ:</u>\n<b>A only:</b> {aString}\n<b>B only:</b> {bString}");
        }
        
        keywordsA.Clear();
        keywordsB.Clear();
        keywordsBUnique.Clear();
        var mpoA = a.GetComponent<MaterialPropertyOverride>();
        var mpoB = b.GetComponent<MaterialPropertyOverride>();
        
        if (mpoA != null)
        {
            foreach (var prop in mpoA.m_propertyOverrides)
                keywordsA.Add(GetString(prop, matA.shader));
        }
        if (mpoB != null)
        {
            foreach (var prop in mpoB.m_propertyOverrides)
                keywordsB.Add(GetString(prop, matB.shader));
        }
        keywordsBUnique = new HashSet<string>(keywordsB);
        foreach (var keyword in keywordsA)
            keywordsBUnique.Remove(keyword);
        foreach (var keyword in keywordsB)
            keywordsA.Remove(keyword);
        if (keywordsA.Count > 0 || keywordsBUnique.Count > 0)
        {
            var aString = string.Join(",  ", keywordsA);
            var bString = string.Join(",  ", keywordsBUnique);
            Output($"<u>Material Property Overrides differ:</u>\n<b>A only:</b> {aString}\n<b>B only:</b> {bString}");
        }
        
        var allAffected = new List<(Component, Transform)>();
        var affectingA = new List<(Component, Transform)>();
        var affectingB = new List<(Component, Transform)>();
        foreach (var animator in a.GetComponentsInParent<Animator>())
        {
            foreach (var t in PrefabBatcher.FindTransformsAffectedByAnimator(animator.gameObject))
                allAffected.Add((animator, t));
        }
        foreach (var animator in b.GetComponentsInParent<Animator>())
        {
            foreach (var t in PrefabBatcher.FindTransformsAffectedByAnimator(animator.gameObject))
                allAffected.Add((animator, t));
        }
        foreach (var (animator, t) in allAffected)
        {
            if (t == null)
                continue;
            bool isA = a.transform.IsChildOf(t);
            bool isB = b.transform.IsChildOf(t);
            if (isA && !isB)
                affectingA.Add((animator, t));
            else if (isB && !isA)
                affectingB.Add((animator, t));
        }

        if (affectingA.Count > 0 || affectingB.Count > 0)
        {
            var aString = string.Join(", ", affectingA.ConvertAll(tup => $"Animator on {Name(tup.Item1.transform)} animates {Name(tup.Item2)}"));
            var bString = string.Join(", ", affectingB.ConvertAll(tup => $"Animator on {Name(tup.Item1.transform)} animates {Name(tup.Item2)}"));
            Output($"<u>Animated differently:</u>\n<b>A only:</b> {aString}\n<b>B only:</b> {bString}");
        }
        
        
        

        return output[..^2]; // Remove the last newline character
    }

    private static string GetString(MaterialPropertyOverride.PropertyOverride prop, Shader shader)
    {
        var start = $"<i>{prop.m_name}</i> : ";
        var type = shader.GetPropertyType(shader.FindPropertyIndex(prop.m_name));
        return type switch
        {
            ShaderPropertyType.Color => $"{start}({prop.m_overrideC.r:F2},{prop.m_overrideC.g:F2},{prop.m_overrideC.b:F2},{prop.m_overrideC.a:F2})",
            ShaderPropertyType.Vector => $"{start}({prop.m_overrideV.x:F2},{prop.m_overrideV.y:F2},{prop.m_overrideV.z:F2},{prop.m_overrideV.w:F2})",
            ShaderPropertyType.Float => $"{start}{prop.m_overrideF}",
            ShaderPropertyType.Range => $"{start}{prop.m_overrideF}",
            ShaderPropertyType.Int => $"{start}{prop.m_overrideI}",
            _ => null
        };
    }
}