using UnityEngine;

public partial class PlayerHandManager
{
    enum EUnlockPowerStage
    {
        Start,
        BringHandToFront,
        PreGem,
        DropGem,
        EarlyPostGem,
        PostGem,
        ShowIcon,
        Show,
        ReturnHand,
        End,
        // possess sequence
        PossessStart,
        PossessTapHero,
        PossessHeroTurns,
        PossessHandFliesToCamera,
        PossessHandBrightens,
        PossessCameraChaseHero,
        PossessCameraHitsHero,
        PossessBlendToBrain,
        PossessBlendComplete,
        PossessBlendFromBrain,
        PossessHeroFromBright,
        PossessEnd,
    }
    const float c_bringToFrontTime = 1f;
    const float c_preGemTime = 1f;
    const float c_dropGemTime = .4f;
    const float c_earlyPostGemTime = .4f;
    const float c_postGemTime = .3f;
    const float c_showIconTime = .4f;
    const float c_showTime = 2f;
    EUnlockPowerStage m_unlockPowerStage = EUnlockPowerStage.Start;
    float m_unlockPowerStageTime = 0;
    float m_unlockPowerStagePreviousTime = 0;

    public AkEventHolder m_unlockPowerHandToFrontAudio;
    public AkEventHolder m_unlockPowerPreGemAudio;
    public AkEventHolder m_unlockPowerDropGemAudio;
    public AkEventHolder m_unlockPowerEarlyPostGemAudio;
    public AkEventHolder m_unlockPowerPostGemAudio;
    public AkEventHolder m_unlockPowerShowIconAudio;
    public AkEventHolder m_unlockPowerShowAudio;
    public AkEventHolder m_unlockPowerReturnHandAudio;
    
    public AkEventHolder m_possessStartAudio;
    public AkEventHolder m_possessTapHeroAudio;
    public AkEventHolder m_possessHeroTurnsAudio;
    public AkEventHolder m_possessHandFliesToCameraAudio;
    public AkEventHolder m_possessHandBrightensAudio;
    public AkEventHolder m_possessCameraChaseHeroAudio;
    public AkEventHolder m_possessCameraHitsHeroAudio;
    public AkEventHolder m_possessBlendToBrainAudio;
    public AkEventHolder m_possessBlendCompleteAudio;
    public AkEventHolder m_possessBlendFromBrainAudio;
    public AkEventHolder m_possessHeroFromBrightAudio;

    void PlayUnlockPowerStageAudio()
    {
        switch (m_unlockPowerStage)
        {
            case EUnlockPowerStage.BringHandToFront:
                m_unlockPowerHandToFrontAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PreGem:
                m_unlockPowerPreGemAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.DropGem:
                m_unlockPowerDropGemAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.EarlyPostGem:
                m_unlockPowerEarlyPostGemAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PostGem:
                m_unlockPowerPostGemAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.ShowIcon:
                m_unlockPowerShowIconAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.Show:
                m_unlockPowerShowAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.ReturnHand:
                m_unlockPowerReturnHandAudio?.Play(GameManager.Me.gameObject);
                break;
            
            case EUnlockPowerStage.PossessStart:
                m_possessStartAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessTapHero:
                m_possessTapHeroAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessHeroTurns:
                m_possessHeroTurnsAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessHandFliesToCamera:
                m_possessHandFliesToCameraAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessHandBrightens:
                m_possessHandBrightensAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessCameraChaseHero:
                m_possessCameraChaseHeroAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessCameraHitsHero:
                m_possessCameraHitsHeroAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessBlendToBrain:
                m_possessBlendToBrainAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessBlendComplete:
                m_possessBlendCompleteAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessBlendFromBrain:
                m_possessBlendFromBrainAudio?.Play(GameManager.Me.gameObject);
                break;
            case EUnlockPowerStage.PossessHeroFromBright:
                m_possessHeroFromBrightAudio?.Play(GameManager.Me.gameObject);
                break;
        }
    }
    
    float UnlockPowerUpdateStage(float _stageTime)
    {
        m_unlockPowerStagePreviousTime = m_unlockPowerStageTime;
        m_unlockPowerStageTime += Time.deltaTime;
        var t = Mathf.Clamp01(m_unlockPowerStageTime / _stageTime);
        // bring hand to front by t
        if (m_unlockPowerStageTime > _stageTime)
        {
            ++m_unlockPowerStage;
            m_unlockPowerStageTime = 0;
            PlayUnlockPowerStageAudio();
        }
        return t;
    }
    
    bool UnlockPowerUpdateJustPassed(float _stageTime) => m_unlockPowerStageTime >= _stageTime && m_unlockPowerStagePreviousTime < _stageTime;

    float m_unlockPowerSequenceLock = 0; public float UnlockPowerSequenceLevel => m_unlockPowerSequenceLock;
    float m_unlockPowerSequenceLockRock = 0;
    int m_unlockPowerSequenceLockType = 0;
    
    void UnlockPowerBringHandToFront(float _t, int _type = 0)
    {
        m_unlockPowerSequenceLock = _t;
        m_unlockPowerSequenceLockType = _type;
    }

    void UnlockPowerDropGem(float _t, int _index)
    {
        if (m_unlockLevels[_index] == 0)
            m_bracelet.UnlockGem(_index - 1, _t);
        else
            m_bracelet.PowerUpGem(_index - 1, _t, m_unlockLevels[_index] + 1);
    }
    
    void UnlockPowerPostGem(float _t, int _index)
    {
        ActivatePower(c_allPowers[_index]);
    }

    void UnlockPowerShowIcon(float _t, int _index)
    {
        m_bracelet.SetIcon(c_allPowers[_index], _t);
    }

    void UnlockPowerShow(float _t, int _index)
    {
        //m_unlockPowerSequenceLockRock = _t * _t * (3 - _t - _t) * 360 * 2;//Mathf.Sin(_t * Mathf.PI * 2) * 1;
    }

    void UnlockPowerReturnHand(float _t)
    {
        m_unlockPowerSequenceLock = 1 - _t;
    }
    
    
    public bool IsInPowerUnlockSequence => m_unlockPowerStage != EUnlockPowerStage.Start;

    static bool s_holdPowerUnlockSequence = false;
    static DebugConsole.Command s_holdPowerUnlockSequenceCmd = new ("holdpowerunlock", _s => Utility.SetOrToggle(ref s_holdPowerUnlockSequence, _s));
    
    void UnlockPowerUpdate(int _index)
    {
        float t;
        switch (m_unlockPowerStage)
        {
            case EUnlockPowerStage.Start:
                m_unlockPowerStage = EUnlockPowerStage.BringHandToFront;
                m_unlockPowerStageTime = 0;
                m_handAnimator.SetBool("UnlockPower", true);
                ActivatePower("");
                Marcos_Procedural_Sky.Me.SetDarkening(this, NGManager.Me.m_handPowerUnlockLightingDampen);
                break;
            case EUnlockPowerStage.BringHandToFront:
                t = UnlockPowerUpdateStage(c_bringToFrontTime);
                UnlockPowerBringHandToFront(t);
                break;
            case EUnlockPowerStage.PreGem:
                t = UnlockPowerUpdateStage(c_preGemTime);
                break;
            case EUnlockPowerStage.DropGem:
                t = UnlockPowerUpdateStage(c_dropGemTime);
                UnlockPowerDropGem(t, _index);
                break;
            case EUnlockPowerStage.EarlyPostGem:
                t = UnlockPowerUpdateStage(c_earlyPostGemTime);
                break;
            case EUnlockPowerStage.PostGem:
                if (s_holdPowerUnlockSequence == false)
                    t = UnlockPowerUpdateStage(c_postGemTime);
                else
                    t = 0;
                UnlockPowerPostGem(t, _index);
                break;
            case EUnlockPowerStage.ShowIcon:
                t = UnlockPowerUpdateStage(c_showIconTime);
                UnlockPowerShowIcon(t, _index);
                break;
            case EUnlockPowerStage.Show:
                t = UnlockPowerUpdateStage(c_showTime);
                UnlockPowerShow(t, _index);
                break;
            case EUnlockPowerStage.ReturnHand:
                m_handAnimator.SetBool("UnlockPower", false);
                t = UnlockPowerUpdateStage(c_bringToFrontTime);
                UnlockPowerReturnHand(t);
                ActivatePower("");
                break;
            case EUnlockPowerStage.End:
                Marcos_Procedural_Sky.Me.SetDarkening(this, 0);
                m_unlockPowerStage = EUnlockPowerStage.Start;
                m_unlockLevels[_index] = (byte)PowerLevel(c_allPowers[_index]);
                break;
        }
    }

    public void StartPossessSequence()
    {
        m_unlockPowerStage = EUnlockPowerStage.PossessStart;
        m_initialPossessSequenceCameraForward = Camera.main.transform.forward;
        m_initialPossessSequenceCameraForwardFlat = Camera.main.transform.forward.GetXZNorm();
        m_initialPossessSequenceCameraPosition = m_runningPossessSequenceCameraPosition = Camera.main.transform.position;
        m_initialPossessSequenceCameraRotation = Camera.main.transform.rotation;
    }

    public void StopPossessSequence()
    {
        if (IsInPossessSequence == false) return;
        m_unlockPowerStage = EUnlockPowerStage.Start;
        m_handAnimator.SetBool("UnlockPower", false);
        PossessSequenceSuperBrightHand(0);
        PossessSequenceSuperBrightPossessed(0);
        UnlockPowerBringHandToFront(0, 1);
    }

    public Vector3 GetPossessSequenceCharacterMovement()
    {
        var v = m_possessSequenceCharacterMovement;
        m_possessSequenceCharacterMovement = Vector3.zero;
        return v;
    }

    public string m_possessSequenceHeroTapAnimation = "";
    public string m_possessSequenceHeroImpactAnimation = "";
    public string m_possessSequenceHeroPostBrainAnimation = "HeroShout";
    public float m_possessSequenceHeroPostBrainAnimationTime = 0.5f;
    public string m_possessSequenceHeroBrainCompleteAnimation = "HeroShout";
    Vector3 m_possessSequenceCharacterMovement;
    Vector3 m_initialPossessSequenceCameraForwardFlat, m_initialPossessSequenceCameraForward, m_initialPossessSequenceCameraPosition, m_runningPossessSequenceCameraPosition;
    Quaternion m_initialPossessSequenceCameraRotation;
    void UpdateGeneralSequence()
    {
        if (m_unlockPowerStage < EUnlockPowerStage.PossessStart) return;
        //GameManager.SetConsoleDisplay(() => $"{m_unlockPowerStage} {m_unlockPowerStageTime:n3} {m_runningPossessSequenceCameraPosition:n2}  {PossessSequenceCharacterHeadPosition:n2}");
        float t, tSmooth;
        switch (m_unlockPowerStage)
        {
            case EUnlockPowerStage.PossessStart:
                if (MAUnlocks.Me.m_usePossessSequence == false)
                {
                    if (GameManager.Me.AdvanceInitialPossess())
                        m_unlockPowerStage = EUnlockPowerStage.Start;
                    return;
                }
                m_unlockPowerStageTime = 0;
                m_handAnimator.SetBool("UnlockPower", true);
                ActivatePower("");
                ++m_unlockPowerStage;
                PossessSequenceTurnCharacterToCamera();
                PossessSequenceSetCharacterHeadTracker(m_pointer);
                break;
            case EUnlockPowerStage.PossessTapHero:
                ++m_unlockPowerStage;
                PossessSequenceTurnCharacterToCamera();
                PlayPossessSequenceCharacterAnimation(m_possessSequenceHeroTapAnimation);
                break;
            case EUnlockPowerStage.PossessHeroTurns:
                t = UnlockPowerUpdateStage(.3f);
                PossessSequenceTurnCharacterToCamera();
                break;
            case EUnlockPowerStage.PossessHandFliesToCamera:
                t = UnlockPowerUpdateStage(.5f);
                UnlockPowerBringHandToFront(t, 1);
                PossessSequenceTurnCharacterToCamera();
                PossessSequencePullCameraBack(t * .5f);
                break;
            case EUnlockPowerStage.PossessHandBrightens:
                t = UnlockPowerUpdateStage(.6f);
                PossessSequenceSuperBrightHand(t);
                PossessSequencePullCameraBack(.5f + t * 2);
                break;
            case EUnlockPowerStage.PossessCameraChaseHero:
                PossessSequenceSetCharacterHeadTracker(Camera.main.transform);
                t = UnlockPowerUpdateStage(.6f);
                m_possessSequenceCharacterMovement = Vector3.forward;
                GameManager.Me.SetInitialPossessCameraBlend(0);
                tSmooth = t * t * (3 - t - t);
                GameManager.Me.SetInitialPossessCameraFrom(Vector3.Lerp(m_runningPossessSequenceCameraPosition, PossessSequenceCharacterHeadPosition, tSmooth));
                PossessSequenceTurnCharacterFromCamera();
                break;
            case EUnlockPowerStage.PossessCameraHitsHero:
                m_possessSequenceCharacterMovement = Vector3.forward;
                PossessSequenceSuperBrightPossessed(1);
                GameManager.Me.SetInitialPossessCameraFrom(PossessSequenceCharacterHeadPosition);
                PlayPossessSequenceCharacterAnimation(m_possessSequenceHeroImpactAnimation);
                ++m_unlockPowerStage;
                break;
            case EUnlockPowerStage.PossessBlendToBrain:
                t = UnlockPowerUpdateStage(.4f);
                if (t < .3f)
                    m_possessSequenceCharacterMovement = Vector3.forward;
                GameManager.Me.SetInitialPossessCameraFrom(PossessSequenceCharacterHeadPosition);
                PossessBrain.Me.SetAlpha(t, true);
                break;
            case EUnlockPowerStage.PossessBlendComplete:
                PossessSequenceSetCharacterHeadTracker(null);
                PossessSequenceSuperBrightHand(0);
                UnlockPowerBringHandToFront(0, 1);
                GameManager.Me.SetInitialPossessCameraFrom(PossessSequenceCharacterHeadPosition);
                m_handAnimator.SetBool("UnlockPower", false);
                ++m_unlockPowerStage;
                PlayPossessSequenceCharacterAnimation(m_possessSequenceHeroBrainCompleteAnimation);
                break;
            case EUnlockPowerStage.PossessBlendFromBrain:
                t = UnlockPowerUpdateStage(.4f);
                PossessBrain.Me.SetAlpha(1 - t, false);
                GameManager.Me.SetInitialPossessCameraFrom(PossessSequenceCharacterHeadPosition);
                if (UnlockPowerUpdateJustPassed(m_possessSequenceHeroPostBrainAnimationTime))
                    PlayPossessSequenceCharacterAnimation(m_possessSequenceHeroPostBrainAnimation);
                break;
            case EUnlockPowerStage.PossessHeroFromBright:
                t = UnlockPowerUpdateStage(.6f);
                PossessSequenceSuperBrightPossessed(1 - t);
                GameManager.Me.SetInitialPossessCameraFrom(PossessSequenceCharacterHeadPosition);
                GameManager.Me.SetInitialPossessCameraBlend(t);
                break;
            case EUnlockPowerStage.PossessEnd:
                m_unlockPowerStage = EUnlockPowerStage.Start;
                break;
        }
    }
    
    const float c_possessSequencePullBackDistance = 20;
    private void PossessSequencePullCameraBack(float _t)
    {
        _t = Mathf.Clamp01(_t);
        _t = _t * _t * (3 - _t - _t);
        var cam = Camera.main.transform;
        cam.position = m_runningPossessSequenceCameraPosition = m_initialPossessSequenceCameraPosition + m_initialPossessSequenceCameraForward * (_t * -c_possessSequencePullBackDistance);
        GameManager.Me.SetInitialPossessCameraFrom(m_runningPossessSequenceCameraPosition);
    }

    private void PlayPossessSequenceCharacterAnimation(string _animName)
    {
        if (string.IsNullOrEmpty(_animName)) return;
        var chr = GameManager.Me.PossessedCharacter;
        if (chr == null) return;
        chr.PlaySingleAnimation(_animName, (b) => { });
    }
    
    private void PossessSequenceSetCharacterHeadTracker(Transform _target)
    {
        var chr = GameManager.Me.PossessedCharacter;
        if (chr == null) return;
        var tracker = chr.GetComponentInChildren<HeadTracker>();
        if (tracker == null) return;
        tracker.Override(_target);
    }
    
    private Vector3 PossessSequenceCharacterHeadPosition => GameManager.Me.PossessedCharacter.GetHeadTransform().position - m_initialPossessSequenceCameraForwardFlat * .75f;

    static Color s_superBrightColour = new Color(10, 10, 10, 1);
    private Color SuperBright(float _multiplier) => new Color(s_superBrightColour.r * _multiplier, s_superBrightColour.g * _multiplier, s_superBrightColour.b * _multiplier, s_superBrightColour.a);
    void PossessSequenceSuperBrightHand(float _brightness)
    {
        var superBright = SuperBright(_brightness);
        HandMainMaterial.SetColor("_Emission", superBright);
    }
    void PossessSequenceSuperBrightPossessed(float _brightness)
    {
        var chr = GameManager.Me.PossessedCharacter;
        if (chr == null) return;
        var superBright = SuperBright(_brightness);
        foreach (var rnd in chr.GetComponentsInChildren<Renderer>())
        {
            if (rnd == null || rnd.sharedMaterial == null) continue;
            rnd.sharedMaterial.SetColor("_EmisivColor", superBright);
        }
    }

    void PossessSequenceTurnCharacterToCamera()
    {
        PossessSequenceTurnCharacter(-m_initialPossessSequenceCameraForwardFlat);
    }

    void PossessSequenceTurnCharacterFromCamera()
    {
        PossessSequenceTurnCharacter(m_initialPossessSequenceCameraForwardFlat);
    }

    void PossessSequenceTurnCharacter(Vector3 _forward)
    {
        var chr = GameManager.Me.PossessedCharacter;
        if (chr == null) return;
        chr.transform.rotation = Quaternion.Slerp(chr.transform.rotation, Quaternion.LookRotation(_forward), 0.1f.TCLerp());
        Camera.main.transform.rotation = m_initialPossessSequenceCameraRotation;
    }
    

    public bool IsInPossessSequence => m_unlockPowerStage >= EUnlockPowerStage.PossessStart;
    public bool IsInPossessSequenceVisibleHand => m_unlockPowerStage >= EUnlockPowerStage.PossessStart && m_unlockPowerStage <= EUnlockPowerStage.PossessBlendComplete;
    public bool IsInPossessSequenceCameraControl => m_unlockPowerStage >= EUnlockPowerStage.PossessStart && m_unlockPowerStage <= EUnlockPowerStage.PossessBlendComplete;
}
