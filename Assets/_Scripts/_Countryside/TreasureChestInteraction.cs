using System.Collections.Generic;
using UnityEngine;

public class TreasureChestInteraction : Mono<PERSON><PERSON><PERSON><PERSON>, ICharacterObjectInteract
{
    public string m_heroAnimation;
    public AkEventHolder m_openAudio;
    
    private GameState_TreasureChest m_state;
    
    private bool m_disabled = false;
    
    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr) => m_disabled == false && m_state.m_isOpen == false && _chr is MAHeroBase;

    public string GetInteractLabel(NGMovingObject _chr)
    {
        return "Open";
    }

    public bool RunInteractSequence(List<FollowChild> _chr) => InteractionSequenceNode.AttemptInteraction(_chr, gameObject);
    public void EnableInteractionTriggers(bool _b) => m_disabled = !_b;

    public string PosToId => $"{transform.position.x:n0}_{transform.position.z:n0}";
    
    void Activate(GameState_TreasureChest _state, bool _isLoad)
    {
        m_state = _state;
        _state.Object = this;
        transform.position = _state.m_position;
        transform.eulerAngles = new Vector3(0, _state.m_rotation, 0);
        if (_state.m_isOpen)
            SetOpen(false);
    }

    public void DoInteract(NGMovingObject _chr)
    {
        if (m_state.m_isOpen) return;
        SetOpen(true);
        m_state.m_isOpen = true;
        _chr.transform.forward = (transform.position - _chr.transform.position).GetXZNorm();
        _chr.PlaySingleAnimation(m_heroAnimation, null);
    }
    void SetOpen(bool _collectContent)
    {
        var anim = GetComponentInChildren<Animator>();
        anim.SetTrigger("Finish");
        anim.SetBool("Digging", true);
        if (_collectContent)
        {
            m_openAudio?.Play(gameObject);
            GiveGiftContents();
        }
    }

    void GiveGiftContents()
    {
        List<NGBusinessGift> gifts = new();

        foreach (var giftString in m_state.m_content.Split('\n', '|', ';'))
        {
            var gift = NGBusinessGift.GetNonFlowGift(giftString);
            if (gift != null)
                gifts.Add(gift);
        }
        if (gifts.Count > 0)
        {
            NGBusinessRewardsSequence.Create(gifts, MAParserManager.m_updatingMaParserSection);
            NGBusinessGiftsPanel.CreateOrCall(gifts);
        }
    }

    public float AutoInteractTime => 0;

    public static void LoadAll()
    {
        foreach (var state in GameManager.Me.m_state.m_treasureChests)
        {
            Create(state, true);
        }
    }
    
    public static TreasureChestInteraction Create(Vector3 _position, float _yRotation, string _content)
    {
        const float c_buryDepth = .2f;
        _position = _position.GroundPosition(-c_buryDepth);
        var state = new GameState_TreasureChest() {m_position = _position, m_rotation = _yRotation, m_content = _content, };
        GameManager.Me.m_state.m_treasureChests.Add(state);
        return Create(state, false);
    }

    public static TreasureChestInteraction Create(GameState_TreasureChest _state, bool _isLoad)
    {
        var prefab = _state.m_prefab;
        if (string.IsNullOrEmpty(prefab)) prefab = "MA_TreasureChest";
        var obj = Instantiate(Resources.Load<GameObject>($"_Art/Chests/{prefab}"), PlayerHandManager.Me.transform);
        var tci = obj.GetComponent<TreasureChestInteraction>();
        tci.Activate(_state, _isLoad);
        return tci;
    }
}
