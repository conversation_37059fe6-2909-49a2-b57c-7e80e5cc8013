using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(SwitchOnBedrockType))]
public class SwitchOnBedrockTypeEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        var sobt = target as SwitchOnBedrockType;
        EditorGUILayout.LabelField("Visible for types:");
        var cfg = CameraRenderSettings.SplatConfig;
        if (cfg == null) return;
        for (int i = 0; i < 16; ++i)
        {
            var name = cfg.sourceTextures[i]?.diffuse?.name;
            if (name == null)
                continue;
            bool isVisible = (sobt.VisibleForTypes & (1u << i)) != 0;
            GUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"{i}: {name}", GUILayout.Width(200));
            bool newVisible = EditorGUILayout.Toggle("", isVisible);
            GUILayout.EndHorizontal();
            if (newVisible != isVisible)
            {
                if (newVisible)
                    sobt.VisibleForTypes |= (1u << i);
                else
                    sobt.VisibleForTypes &= ~(1u << i);
            }
        }
    }
}
#endif


public class SwitchOnBedrockType : MonoBehaviour, IBatchPartitioner
{
    public GameObject[] m_objectsToSwitch;
    public float m_snapHeight = BuildingPlacementManager.c_mabaseSnapHeight;
    public string m_debug;
    public bool m_forceOff = false;
    [SerializeField] private UInt32 m_visibleForTypes = 0xFFFF;
    public UInt32 VisibleForTypes
    {
        get => m_visibleForTypes;
        set
        {
            m_visibleForTypes = value;
            Switch();
        }
    }
    void Start() => Switch();
    void OnValidate() => Switch();
    
    public void Switch()
    {
        if (Application.isPlaying == false || CameraRenderSettings.Me == null)
            return;
        var bedrockType = CameraRenderSettings.Me.BedrockAtPoint(transform.position);
#if UNITY_EDITOR
        m_debug = $"Bedrock type: {bedrockType} at pos {transform.position}";
#endif
        var visible = ((m_visibleForTypes >> bedrockType) & 1) != 0;
        var visibleReal = visible;
        visible &= m_forceOff == false;
        foreach (var rnd in GetComponentsInChildren<Renderer>())
            if (rnd.enabled != visible) // unity property setters can be expensive!
                rnd.enabled = visible;
        if (m_objectsToSwitch != null)
        {
            foreach (var o in m_objectsToSwitch)
            {
                if (o != null)
                {
                    var sid = o.GetComponent<ShowInDesign>();
                    if (sid != null)
                        sid.CheckAndShow(visible);
                    else
                        o.SetActive(visible);
                }
            }
        }
        if (visibleReal)
        {
            float heightAdjust = 0;
            var block = GetComponentInParent<Block>();
            if (block != null)
            {
                const float c_noBaseSnapHeight = .4f;
                var snapHeight = visible ? m_snapHeight : c_noBaseSnapHeight;
                foreach (Transform t in block.m_toHinges)
                {
                    var hinge = t.GetComponent<SnapHinge>();
                    if (hinge != null && hinge.HingeDirection == SnapHinge.EType.Top)
                    {
                        var pos = t.position;
                        t.localPosition = t.localPosition.NewY(snapHeight);
                        heightAdjust = t.position.y - pos.y;
                    }
                }
                if (heightAdjust * heightAdjust > .001f * .001f)
                {
                    foreach (Transform t in block.transform.parent)
                    {
                        if (t == block.transform) continue;
                        t.position += Vector3.up * heightAdjust;
                    }
                }
            }
        }
    }

    public Component Component()
    {
        return this;
    }
    
    public List<List<Transform>> GetExcludedTransforms()
    {
        var list = new List<Transform> { transform };
        foreach (var o in m_objectsToSwitch)
            list.Add(o.transform);
        return new List<List<Transform>> { list };
    }
    
    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }

    private static DebugConsole.Command s_disableAllSwitchOnBedrockTypeCmd = new ("disableswitchonbedrock", _s => {
        if (int.TryParse(_s, out int buildingId))
        {
            var root = NGManager.Me.FindBuildingByID(buildingId);
            if (root != null)
            {
                DisableAll(root.gameObject, true);
                Debug.LogError($"Disabled all SwitchOnBedrockType visuals for building {buildingId}");
            }
            else
                Debug.LogError($"Building with ID {buildingId} not found.");
        }
        else
            Debug.LogError($"Invalid building ID provided {_s}");
    }, "Disables all SwitchOnBedrockType components for the specified building", "<int>");

    public static void DisableAll(GameObject _root, bool _disable = true)
    {
        foreach (var sobt in _root.GetComponentsInChildren<SwitchOnBedrockType>())
        {
            if (sobt != null)
            {
                sobt.m_forceOff = _disable;
                sobt.Switch();
            }
        }
    }
}
