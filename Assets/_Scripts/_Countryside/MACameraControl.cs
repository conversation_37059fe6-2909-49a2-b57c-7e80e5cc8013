using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

[Serializable]
public class MACameraControl
{
    public static List<MACameraControl> s_all = new();
    public static List<MACameraControl> GetList=>s_all;
    public string DebugDisplayName => m_name;
    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_displayName;
    public float m_height;
    public float m_tilt;
    public int m_steps;

    public static int NumStages
    {
        get
        {
            Check();
            return s_all.Count;
        }
    }

    // remove once knack wired up
    private static void Check()
    {
       /* if (s_all.Count == 0)
        {
            s_all.Add(new MACameraControl {m_name = "0", m_height = 20, m_tilt = 30});
            s_all.Add(new MACameraControl {m_name = "1", m_height = 50, m_tilt = 50});
            s_all.Add(new MACameraControl {m_name = "2", m_height = 100, m_tilt = 50});
            s_all.Add(new MACameraControl {m_name = "3", m_height = 200, m_tilt = 50});
            s_all.Add(new MACameraControl {m_name = "4", m_height = 1200, m_tilt = 50});
        }*/
    }

    public static MACameraControl Get(int _stage)
    {
        Check();
        _stage = Mathf.Clamp(_stage, 0, s_all.Count - 1);
        return s_all[_stage];
    }

    public static float GetStageIndexFromHeight(float _height)
    {
        Check();
        for (int i = 0; i < s_all.Count; i++)
        {
            if (s_all[i].m_height >= _height)
            {
                if (i == 0) return 0;
                return Mathf.InverseLerp(s_all[i - 1].m_height, s_all[i].m_height, _height) + i - 1;
            }
        }
        return s_all.Count - 1;
    }

    public static MACameraControl GetInfo(string _name)
    {
        return s_all.Find(o=> o.m_name == _name);
    }

    public static bool PostImport(MACameraControl _what)
    {
        return true;
    }

    
    public static List<MACameraControl> LoadInfo()
    { 
        s_all  = NGKnack.ImportKnackInto<MACameraControl>(PostImport);
        return s_all;
    }
    
}
