using System.Collections.Generic;
using UnityEngine;

public class PlantInstanceMesh : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IBatchPartitioner
{
    protected MeshRenderer m_meshRenderer;
    protected float m_currentLevel;
    protected PlantController m_controller;
    protected float m_fractionMin = 0, m_fractionInvRange = 1;

    protected void Awake()
    {
        m_meshRenderer = GetComponentInChildren<MeshRenderer>();
        if (m_meshRenderer == null)
        {
            enabled = false;
            return;
        }
        m_meshRenderer.enabled = false;
        m_currentLevel = 0;
        m_controller = GetComponentInParent<PlantController>();
        if (m_controller != null) m_controller.Check(this);
        SetOrientation();
    }

    protected void SetOrientation()
    {
        var worldUpDotForward = transform.forward.y; //Vector3.Dot(Vector3.up, transform.forward);
        var worldUpProjected = (Vector3.up - transform.forward * worldUpDotForward).normalized;
        var cos = Vector3.Dot(worldUpProjected, transform.up);
        cos = Mathf.Clamp(cos, -1f, 1f);
        var theta = Mathf.Acos(cos);
        var cross = Vector3.Cross(worldUpProjected, transform.up);
        if (cross.z < 0) theta = -theta;
        var sin = Mathf.Sin(theta);
        m_meshRenderer.material.SetVector("_PlantRotate", new Vector4(cos, sin, -sin, cos));
    }
    
    public void SetLevel(float _level)
    {
        _level = Mathf.Clamp((_level - m_fractionMin) * m_fractionInvRange, 0, .999f);
        if (m_currentLevel.Nearly(_level)) return;
        m_currentLevel = _level;
        if (m_meshRenderer == null) return; 
        m_meshRenderer.enabled = _level > 0;
        m_meshRenderer.material.SetFloat("_PlantStage", _level);
        SetOrientation();
    }

    public (float, float) GetHeightMinMax()
    {
        var block = GetComponentInParent<Block>();
        float yMin = 1e23f, yMax = -1e23f;
        if (block != null)
        {
            foreach (Transform h in block.m_toHinges)
            {
                var y = h.position.y;
                yMin = Mathf.Min(yMin, y);
                yMax = Mathf.Max(yMax, y);
            }
        }
        return (yMin, yMax);
    }

    public void SetHeightFractions(float _min, float _max)
    {
        m_fractionMin = _min;
        m_fractionInvRange = _max > _min + .01f ? 1.0f / (_max - _min) : 1f;
    }

    public Component Component() => this;
    
    public List<List<Transform>> GetExcludedTransforms()
    {
        return new List<List<Transform>> { new() { transform } };
    }

    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }
}
