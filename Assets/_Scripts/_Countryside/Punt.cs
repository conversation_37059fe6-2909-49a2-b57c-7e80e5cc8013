using UnityEngine;
using System;
using System.Collections.Generic;

public class Punt : DragBase
{
    public LineRenderer m_line;
    public AkEventHolder m_stretchSound;
    public AkRTPCHolder m_stretchRTPC;
    private int m_stretchSoundId;
    public AkEventHolder m_puntSound;
    public Color m_lowStretchColour = Color.green;
    public Color m_highStretchColour = Color.red;
    private Plane m_dragPlane;
    private Vector3 m_dragStart;
    private Punt m_childPunt = null;
    private Vector3 m_puntVelocity;
    private float m_childPuntAt = 0;

    const float c_minVelocity = 4f;
    const float c_maxVelocity = 15f;
    const float c_velocityMultiplier = 2;


    void LateUpdate()
    {
        var rb = GetComponent<Rigidbody>();
        if (rb != null)
        {
            var v = rb.linearVelocity;
            bool isMoving = false;
            if (v.xzSqrMagnitude() > .2f * .2f)
            {
                isMoving = true;
                var dir = v.GetXZNorm();
                var rot = Quaternion.LookRotation(dir, Vector3.up);
                transform.rotation = Quaternion.Slerp(transform.rotation, rot, .2f);
            }
            var anim = GetComponent<Animator>();
            if (anim != null)
                anim.SetBool("Running", isMoving);
        }
    }
    

    private Vector3 GetDragPos()
    {
        var ray = Cam.RayAtMouse();
        m_dragPlane.Raycast(ray, out var enter);
        return ray.GetPoint(enter);
    }

    public override void OnDragStart()
    {
        m_dragPlane = new Plane(Vector3.up, transform.position);
        m_dragStart = GetDragPos();
        if (m_stretchSound != null) m_stretchSoundId = m_stretchSound.Play(gameObject);
        UpdateStretchRTPC(0);
    }

    public override void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag)
    {
        var dragPos = GetDragPos();
        var delta = dragPos - m_dragStart;
        var velocity = DragVelocity(delta);
        m_puntVelocity = velocity;
        SetLine(velocity);
        UpdateStretchRTPC(velocity.xzMagnitude() / c_maxVelocity);
    }
    
    private void UpdateStretchRTPC(float _value)
    {
        if (m_stretchRTPC != null)
            m_stretchRTPC.Set(_value, gameObject);
    }

    public override void OnDragEnd(bool _undo)
    {
        m_line.enabled = false;
        if (m_puntVelocity.sqrMagnitude >= c_minVelocity * c_minVelocity)
            DoPunt();
        EndChildPunt();
        AudioClipManager.Me.StopId(ref m_stretchSoundId);
    }
    void DoPunt()
    {
        var rb = GetComponent<Rigidbody>();
        if (rb != null)
        {
            if (m_puntSound != null) m_puntSound.Play(gameObject);
            rb.linearVelocity = m_puntVelocity;
            if (m_childPunt != null)
            {
                Physics.IgnoreCollision(GetComponentInChildren<Collider>(), m_childPunt.GetComponentInChildren<Collider>());
                m_childPunt.DoPuntAfter(m_childPuntAt);
            }
        }
    }
    void DoPuntAfter(float _time)
    {
        Utility.After(_time, DoPunt);
    }

    private Vector3 DragVelocity(Vector3 _drag)
    {
        var velocity = -_drag * c_velocityMultiplier;
        if (velocity.sqrMagnitude > c_maxVelocity * c_maxVelocity)
        {
            velocity = velocity.GetXZNorm() * c_maxVelocity;
            velocity.y = c_maxVelocity;
        }
        else
            velocity.y = velocity.xzMagnitude();
        return velocity;
    }
    
    public static bool Cast(Vector3 _start, Vector3 _end, out Vector3 _hit, out GameObject _hitObject, GameObject _ignore = null)
    {
        var hits = Physics.RaycastAll(_start, _end - _start, (_end - _start).magnitude);
        foreach (var hit in hits)
        {
            if(_ignore != null && hit.collider.transform.IsChildOf(_ignore.transform))
                continue;
            //if (hit.collider.gameObject == _ignore)
              //  continue;
            _hit = hit.point;
            _hitObject = hit.collider.gameObject;
            return true;
        }
        _hit = Vector3.zero;
        _hitObject = null;
        return false;
    }
    
    public void ShowImpact(Vector3 _impact, float _speed)
    {
        var direction = (transform.position - _impact).GetXZNorm();
        var velocity = direction * _speed;
        velocity.y = _speed;
        m_puntVelocity = velocity;
        SetLine(velocity);
    }

    public void ClearImpact()
    {
        m_line.enabled = false;
        if (m_childPunt != null)
            m_childPunt.ClearImpact();
    }

    private void EndChildPunt()
    {
        if (m_childPunt != null)
        {
            m_childPunt.ClearImpact();
            m_childPunt = null;
        }
    }
    
    private void SetLine(Vector3 _velocity)
    {
        var points = new List<Vector3>();
        var pos = transform.position;
        var dt = .1f;
        Punt hitPunt = null; 
        Vector3 hitPos = Vector3.zero;
        float t = 0;
        if (_velocity.sqrMagnitude < c_minVelocity * c_minVelocity)
        {
            if (m_line.enabled)
                m_line.enabled = false;
        }
        else
        {
            var gravity = Physics.gravity;
            for (int i = 0; i < 1000; ++i) // max 1000 steps
            {
                var prev = pos;
                points.Add(pos);
                t += dt;
                pos += _velocity * dt;
                _velocity += gravity * dt;
                if (Cast(prev, pos, out hitPos, out var hitObject, gameObject))
                {
                    points.Add(hitPos);
                    hitPunt = hitObject.GetComponentInParent<Punt>();
                    break;
                }
            }
            m_line.positionCount = points.Count;
            m_line.SetPositions(points.ToArray());
            if (m_line.enabled == false)
                m_line.enabled = true;
            float stretch = Mathf.Clamp01(_velocity.xzMagnitude() / c_maxVelocity);
            UpdateBezier(m_line, Color.Lerp(m_lowStretchColour, m_highStretchColour, stretch));
        }
        if (hitPunt != null)
        {
            var velocityFalloff = Mathf.Min(1, 1.0f / (.05f + t * .75f));
            hitPunt.ShowImpact(hitPos, _velocity.xzMagnitude() * velocityFalloff);
            m_childPunt = hitPunt;
            m_childPuntAt = t - dt * 2;
        }
        else
        {
            EndChildPunt();
        }
    }
}
