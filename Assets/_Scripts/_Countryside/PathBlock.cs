//#define USE_UPDATER
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.Collections;
using Unity.Mathematics;

public class PathBlock : MonoBehaviour
{
    public static NativeList<float4> PathBlockData => s_allPathBlockData;
    public static List<PathBlock> s_allPathBlocks = new ();
    public static void CleanUp() => s_allPathBlocks.Clear();
    private static bool s_haveRegisteredUpdater = false;

    private static void CheckUpdater()
    {
    #if USE_UPDATER
        if (s_haveRegisteredUpdater) return;
        s_haveRegisteredUpdater = true;
        GlobalData.Me.RegisterUpdater(RunUpdater);
#endif
    }

    private static void RunUpdater()
    {
        if (RoadManager.Me == null || GameManager.Me == null) return; // shutting down
        if (GameManager.Me.PathsLoaded == false) return;
        
        for (int i = s_allPathBlocks.Count - 1; i >= 0; --i)
        {
            var pb = s_allPathBlocks[i];
            if (pb == null)
            {
                s_allPathBlocks.RemoveAt(i);
                continue;
            }
            if (pb.isActiveAndEnabled == false) continue;
            pb.UpdateNavGrid(false);
        }
    }

    public static bool IsInside(float3 _pos, NativeList<float4> _list)
    {
        for (int i = 0; i < _list.Length; i += 2)
        {
            float f = _pos.x * _list[i + 0].x + _pos.z * _list[i + 0].y - _list[i + 0].z;
            if (f * f > _list[i + 0].w) continue;
            f = _pos.x * _list[i + 1].x + _pos.z * _list[i + 1].y - _list[i + 1].z;
            if (f * f > _list[i + 1].w) continue;
            return true;
        }
        return false;
    }

    public static void FillNavData(Vector3 _min, Vector3 _max)
    {
        //GameManager.Me.ClearGizmos($"blockers");
        for (int i = 0; i < s_allPathBlocks.Count; ++i)
        {
            var pb = s_allPathBlocks[i];
            if (pb.m_isActive == false || pb.m_isBaked == false) continue;
            pb.FillNav(_min, _max);
        }
    }

    private static void Register(PathBlock _this)
    {
        CheckUpdater();
        s_allPathBlocks.Add(_this);
    }

    private static void Unregister(PathBlock _this) => s_allPathBlocks.Remove(_this);
    private static NativeList<float4> s_allPathBlockData = new NativeList<float4>(64, Allocator.Persistent);
    private static void UpdatePathBlockData()
    {
        s_allPathBlockData.Clear();
        for (int i = 0; i < s_allPathBlocks.Count; ++i)
        {
            var pb = s_allPathBlocks[i];
            if (pb.m_isActive == false || pb.m_isBaked) continue;
            var (a, b) = pb.Get();
            s_allPathBlockData.Add(a);
            s_allPathBlockData.Add(b);
        }
    }

    public bool m_isActive, m_isBaked, m_ignoreAngle, m_hasEntrance;
    public float m_width, m_length, m_scaledWidth, m_scaledLength, m_margin;
    private Vector3 m_lastPosition;
    private Quaternion m_lastRotation;
    private float m_lastWidth, m_lastLength;
    private Vector3 m_lastExtents;
    private bool m_lastActive;
    private Vector3 m_min = Vector3.one * 1e23f, m_max = Vector3.one * -1e23f;
    private BoxCollider m_baseCollider = null;
    private GeometryUtilities.OrientedBoundingBox2D m_bounds2D;
    
    private Transform m_transform;
    private GameObject m_gameObject;
    private NavAgent m_nav;

    void Awake()
    {
        m_transform = transform;
        m_gameObject = gameObject;
    }

    void Start()
    {
        Register(this);
        // RW-04-JUL-25: Ensure m_nav is initialised if it ought to be.
        OnTransformParentChanged();
    }

    void OnDestroy()
    {
        if (Utility.IsShuttingDown) return;
        m_isActive = false;
        if (GameManager.Me.LoadStarted)
            UpdateNavGrid(true); // update data if this was currently enabled
        Unregister(this);
    }
    
    private void FillNav(Vector3 _min, Vector3 _max)
    {
        if (Overlaps(_min, _max)) // && pb.IsConditionallyDisabled == false)
        {
            MarkLastMinMax();
            Vector3 pos;
            float angle, width, length;
            if (m_baseCollider != null)
                (pos, angle, width, length) = (m_bounds2D.m_center.V3XZ(), -m_bounds2D.m_angle, (m_bounds2D.m_extents.x + m_margin) * 2, (m_bounds2D.m_extents.y + m_margin) * 2);
            else
                (pos, angle, width, length) = (m_transform.position, m_ignoreAngle ? 0 : m_transform.eulerAngles.y, m_scaledWidth * 2, m_scaledLength * 2);
            PathManager.FillHollowNavRectangle(pos, angle, width, length, 0, (byte) GlobalData.NavCostTypes.LowNoNav, (byte) GlobalData.NavCostTypes.LowNoNav, false, _min, _max);
            if (m_hasEntrance)
            {
                var entranceWidth = 1;
                var entranceLength = length * .5f;
                var entrance = pos + angle.EulerY() * (entranceLength * .5f);
                PathManager.FillHollowNavRectangle(entrance, angle, entranceWidth, entranceLength, 0, (byte) GlobalData.NavCostTypes.EnterBuilding, (byte) GlobalData.NavCostTypes.EnterBuilding, false, _min, _max);
            }
        }
    }

    private (Vector3, Vector3) GetMinMax()
    {
        if (m_baseCollider != null) return m_bounds2D.CalculateMinMax(m_margin);
        
        var p = m_transform.position;
        var angle = m_ignoreAngle ? 0 : m_transform.eulerAngles.y * Mathf.Deg2Rad;
        float sin = Mathf.Sin(angle), cos = Mathf.Cos(angle);
        float asin = Mathf.Abs(sin), acos = Mathf.Abs(cos);
        var afwd = new Vector3(asin, 0, acos) * m_scaledLength;
        var aright = new Vector3(acos, 0, asin) *  m_scaledWidth;
        Vector3 bdmin = p - afwd - aright, bdmax = p + afwd + aright;
        return (bdmin, bdmax);
    }
    
    private void MarkLastMinMax()
    {
        var (bmin, bmax) = GetMinMax();
        m_min = Vector3.Min(m_min, bmin);
        m_max = Vector3.Max(m_max, bmax);
    }

    public bool Overlaps(Vector3 _min, Vector3 _max)
    {
        var (bmin, bmax) = GetMinMax();
        bmin = Vector3.Min(bmin, m_min);
        bmax = Vector3.Max(bmax, m_max);
        var bcenter = (bmin + bmax) * .5f;
        var center = (_min + _max) * .5f;
        var totalExtents = ((_max - _min) + (Vector3.one * .5f + bmax - bmin)) * .5f; // add .5 to bmin/max to ensure we don't miss
        var diff = bcenter - center;
        bool result = false;
        result = diff.x * diff.x <= totalExtents.x * totalExtents.x && diff.z * diff.z <= totalExtents.z * totalExtents.z;

        //GameManager.Me.AddGizmoCube($"blockers", (bmax + bmin) * 0.5f, bmax - bmin, result ? Color.green : Color.red);
        return result;
    }

    public (float4, float4) Get()
    {
        var side = (m_ignoreAngle ? Vector3.right : m_transform.right) * m_width;
        var fwd = (m_ignoreAngle ? Vector3.forward : m_transform.forward) * m_length;
        var sideMagSqrd = side.xzSqrMagnitude();
        var fwdMagSqrd = fwd.xzSqrMagnitude();
        var side4 = new float4(side.x, side.z, Vector3.Dot(side, m_transform.position), sideMagSqrd * sideMagSqrd);
        var fwd4 = new float4(fwd.x, fwd.z, Vector3.Dot(fwd, m_transform.position), fwdMagSqrd * fwdMagSqrd);
        return (side4, fwd4);
    }

#if !USE_UPDATER
    void Update()
    {
        UpdateNavGrid(false);
    }
#endif

    private bool IsConditionallyDisabled {
        get {
            var drag = GetComponentInParent<DragBase>();
            if (drag != null && drag.IsDragging)
                return true;
            var body = GetComponentInParent<Rigidbody>();
            if (body != null && body.linearVelocity.sqrMagnitude > .01f * .01f)
                return true;
            return false;
        }
    }
    
    private bool IsChanged(bool _hasChanged, Vector3 _pos, Quaternion _rot)
    {
        if (m_isActive != m_lastActive) return true; 
        var lossyScale = m_transform.lossyScale;
        if (m_baseCollider != null)
        {
            Vector3 scale = Vector3.Scale(m_baseCollider.size, lossyScale);
            if (scale.AlmostEquals(m_lastExtents, .1f) == false)
                return true;
        }
        else if (m_lastWidth.Nearly(m_width * lossyScale.x) == false || m_lastLength.Nearly(m_length * lossyScale.z) == false)
                return true;
        if (_hasChanged == false) return false;
        return (m_lastPosition.AlmostEquals(_pos, .1f) && (m_ignoreAngle || m_lastRotation.AlmostEquals(_rot))) == false;
    }

    private void StoreChanges(Vector3 pos, Quaternion rot)
    {
        m_lastPosition = pos;
        m_lastRotation = rot;
        var lossyScale = m_transform.lossyScale;
        if (m_baseCollider != null)
        {
            m_lastExtents = Vector3.Scale(m_baseCollider.size, lossyScale);
        }
        else
        {
            m_lastWidth = m_width * lossyScale.x;
            m_lastLength = m_length * lossyScale.z;
        }
        m_lastActive = m_isActive;
    }
    
    void OnTransformParentChanged()
    {
        if (Utility.IsShuttingDown) return;
        var ownerNG = GetComponentInParent<NGMovingObject>();
        if (ownerNG != null)
            m_nav = ownerNG.GetComponentInChildren<NavAgent>();
    }
    
    public void SetNavAgent(NavAgent _nav)
    {
        m_nav = _nav;
    }
    
    private float m_updateMetric = 0;
    const float c_updateMetricThreshold = 3f;

    private void UpdateNavGrid(bool isDestroying)
    {
        if (m_nav != null) m_isActive = m_nav.IsNavigating == false;
        
        var previousMetric = m_updateMetric;
        m_transform.GetPositionAndRotation(out var pos, out var rot);
        var hasChanged = m_transform.hasChanged;
        m_transform.hasChanged = false;
        if (!isDestroying)
        {
            if (IsChanged(hasChanged, pos, rot) == false || IsConditionallyDisabled)
            {
                // RW-22-MAY-25: Gary no longer thinks this optimisation is safe. With this on,
                // the NavBlockers in the Oakridge Graveyard were ineffective. We need to have a flag 
                // like "isPermanent" rather than relying on if buildings are static to do this.

                // no changes, if this is static we never need to update again
                //if (m_gameObject.isStatic)
                    //enabled = false;
                m_updateMetric = Mathf.Max(0, m_updateMetric - Time.deltaTime);
                //if (m_updateMetric < c_updateMetricThreshold && previousMetric >= c_updateMetricThreshold) Debug.LogError($"UpdateMetric settled for {m_transform.Path()}");
                return;
            }
        }

        // moved significantly, refresh all nav paths
        m_updateMetric = m_isActive ? m_updateMetric + 1 : 0;
        //if (previousMetric < c_updateMetricThreshold && m_updateMetric >= c_updateMetricThreshold) Debug.LogError($"UpdateMetric fired for {m_transform.Path()}");
        
        StoreChanges(pos, rot);
        UpdatePathBlockData();
        if (m_baseCollider != null)
        {
            m_bounds2D = GeometryUtilities.Find2DBoundsFromOBB(m_baseCollider.transform.TransformPoint(m_baseCollider.center), Vector3.Scale(m_baseCollider.size * .5f, transform.lossyScale), m_baseCollider.transform.rotation);
				}
        else
        {
            m_scaledWidth =  m_width * transform.lossyScale.x;
            m_scaledLength =  m_length * transform.lossyScale.z;
        }
        bool hasNavChanged = true;
        if (m_isBaked)
        {
            var bmin = m_min;
            var bmax = m_max;
            if (m_isActive)
            {
                (m_min, m_max) = GetMinMax();
                if (bmin.sqrMagnitude < .001f * .001f)
                    (bmin, bmax) = (m_min, m_max);
                else
                    (bmin, bmax) = (Vector3.Min(m_min, bmin), Vector3.Max(m_max, bmax));
                GameManager.Me.ClearGizmos($"blockers");
                GameManager.Me.AddGizmoCubeMinMax($"blockers", bmin, bmax, new Color(1, 1, 1, .15f));
                GameManager.Me.AddGizmoLabel($"blockers", (bmin + bmax) * .5f, $"{m_transform.Path()}", Color.white);
            }
            else
            {
                m_min = Vector3.one * 1e23f;
                m_max = Vector3.one * -1e23f;
            }
            if (bmin.x < bmax.x)
                RoadManager.Me.m_pathSet.UpdateNavDeferred(bmin, bmax);
            else
                hasNavChanged = false;
        }
        if (hasNavChanged)
            GlobalData.Me.SetNavGenerationFrameToCurrent();
    }
    
    private static DebugConsole.Command s_updatenavcmd = new ("updatenav", _s => RoadManager.Me.m_pathSet.UpdateNav(GlobalData.c_terrainMin, GlobalData.c_terrainMax));

    public static PathBlock Create(GameObject _target, float _halfWidth, float _halfHeight, float _margin = 1, bool _baked = false, bool _ignoreAngle = false, bool _hasEntrance = false)
    {
        var pb = _target.AddComponent<PathBlock>();
        pb.m_width = _halfWidth + _margin;
        pb.m_length = _halfHeight + _margin;
        pb.m_isActive = true;
        pb.m_isBaked = _baked;
        pb.m_ignoreAngle = _ignoreAngle;
        pb.m_hasEntrance = _hasEntrance;
        return pb;
    }

    public static PathBlock Create(GameObject _target, BoxCollider _box, float _margin = 1, bool _baked = false, bool _hasEntrance = false)
    {
        var pb = _target.AddComponent<PathBlock>();
        pb.m_isActive = true;
        pb.m_isBaked = _baked;
        pb.m_margin = _margin;
        pb.m_baseCollider = _box;
        pb.m_hasEntrance = _hasEntrance;
        return pb;
    }

    void OnDrawGizmos()
    {
        Gizmos.color = m_isActive ? Color.white : Color.grey;
        Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, new Vector3(m_width, 1, m_length));
        Gizmos.DrawWireCube(Vector3.zero, Vector3.one * 2);
        Gizmos.matrix = Matrix4x4.identity;
        Gizmos.color = Color.blue;
        Gizmos.DrawWireCube((m_min + m_max) * .5f, m_max - m_min);

        var c = transform.position;
        var rot = Quaternion.Euler(0, m_ignoreAngle ? 0 : transform.eulerAngles.y, 0);
        Gizmos.matrix = Matrix4x4.TRS(c, rot, Vector3.one);
        Gizmos.color = Color.cyan;
        if (m_ignoreAngle)
            Gizmos.DrawWireSphere(Vector3.zero, Mathf.Max(m_scaledWidth, m_scaledLength));
        Gizmos.DrawWireCube(Vector3.zero, new Vector3(m_scaledWidth * 2, 1, m_scaledLength * 2));
        
        if (m_baseCollider != null)
        {
            Gizmos.matrix = m_baseCollider.transform.localToWorldMatrix;
            Gizmos.color = Color.red;
            Gizmos.DrawWireCube(m_baseCollider.center, m_baseCollider.size);
            Gizmos.matrix = Matrix4x4.TRS(m_bounds2D.m_center.V3XZ().GroundPosition(), Quaternion.Euler(0, -m_bounds2D.m_angle, 0), Vector3.one);
            Gizmos.color = Color.white;
            Gizmos.DrawWireCube(Vector3.zero, m_bounds2D.m_extents.V3XZ().NewY(1) * 2);
        }
    }
}
