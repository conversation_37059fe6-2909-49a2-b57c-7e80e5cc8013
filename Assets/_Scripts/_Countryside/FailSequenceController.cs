using System.Collections;
using UnityEngine;

public class FailSequenceController : Mon<PERSON><PERSON><PERSON><PERSON><FailSequenceController>
{
	public float m_initialPanTime = 2;
	public float m_focusDistance = 15f;
	public float m_focusUp = 7.5f;
	public float m_pauseAfterInitialPan = .5f;
	public float m_zoomToDoorTime = .5f;
	public float m_zoomToDoorDistance = 14;
	public float m_zoomToThroneTime = 1.5f;
	public float m_pauseBeforeBackOut = 1;
	public float m_backOutSpeedMultiplier = 2;
	public float m_backOffDistance = 80;
	public float m_backOffSeconds = 5;
	
	const string c_failSequencePriorityLabel = "FailSequence";

	[Header("Switch must be MusicName")]
	public AkSwitchHolder m_failSwitch;
	public AkSwitchHolder m_retrySwitch;
	
	private static DebugConsole.Command s_failcmd = new ("fail", _s => {
		foreach (var building in NGManager.Me.m_maBuildings)
		{
			if (building.m_componentsDict.TryGetValue(typeof(BCTardisCrypt), out var tardisCrypts) &&
				tardisCrypts != null && tardisCrypts.Count > 0)
			{
				building.HealthNorm = 0;
			}
		}
	});

	public enum EFailType
	{
		None,
		Crypt,
		Tasks,
	}
	
	public Coroutine m_failSequence = null;
	public bool IsActive => m_failSequence != null;
	public EFailType CurrentFailType => (EFailType)GameManager.Me.m_state.m_gameInfo.m_inFailState;
	
	private static DebugConsole.Command s_startFailCmd = new ("fail", _s => Me.StartFailSequence(MASpecialMABuilding.Crypt()?.transform));

	private void PlayFailAudio() => m_failSwitch?.SetAsOverride();//AudioClipManager.Me.PlaySound("PlaySound_DayFAILED", GameManager.Me.gameObject);
	private void PlayRetryAudio() => m_retrySwitch?.SetAsOverride();//AudioClipManager.Me.PlaySound("PlaySound_DayRESTART", GameManager.Me.gameObject);
	
	public void LoadFailState()
	{
		switch (GameManager.Me.m_state.m_gameInfo.m_inFailState)
		{
			case 1: StartFailSequence(MASpecialMABuilding.Crypt()?.transform, true); break;
			case 2: StartDayNotCompleteSequence(true); break;
		}
	}

	public void StartFailSequence(Transform _whoFailed, bool _isLoad = false)
	{
		GameManager.Me.m_state.m_gameInfo.m_failedThisDay = true;
		if (m_failSequence == null)
		{
			m_failSequence = StartCoroutine(Co_FailSequence(_whoFailed, _isLoad));
		}
	}

	public void StartDayNotCompleteSequence(bool _isLoad = false)
	{
		GameManager.Me.m_state.m_gameInfo.m_failedThisDay = true;
		if (m_failSequence == null)
		{
			m_failSequence = StartCoroutine(Co_DayNotCompleteSequence(_isLoad));
		}
	}

	public void StartNightFailedSequence(bool _isLoad = false)
	{
		GameManager.Me.m_state.m_gameInfo.m_failedThisDay = true;
		if (m_failSequence == null)
		{
			m_failSequence = StartCoroutine(Co_DayNotCompleteSequence(_isLoad));
		}
	}

	public enum EBlendType
	{
		None,
		Start,
		End,
		Both,
	}

	public static float TimeBlend(float _t, EBlendType _type)
	{
		_t = Mathf.Clamp01(_t);
		if (_type == EBlendType.None) return _t;
		var smoothT = _t * _t * (3 - _t - _t);
		switch (_type)
		{
			case EBlendType.Start:
				return Mathf.LerpUnclamped(smoothT, _t, _t);
			case EBlendType.End:
				return Mathf.LerpUnclamped(_t, smoothT, _t);
			default:
				return smoothT;
		}
	}

	IEnumerator Pan(Transform _camXform, Vector3 _finalPos, Vector3 _finalFwd, float _time, EBlendType _blend, CanvasGroup _cg = null, bool _reverseCG = false)
	{
		var initialPos = _camXform.position;
		var initialFwd = _camXform.forward;
		for (float t = 0; t <= _time; t += Time.deltaTime)
		{
			var f = TimeBlend(t / _time, _blend);
			if (_cg != null)
			{
				var alpha = f;
				if (_reverseCG) alpha = 1 - alpha;
				alpha = Mathf.Max(0, alpha * 4 - 3);
				_cg.alpha = alpha;
			}
			var thisPos = Vector3.Lerp(initialPos, _finalPos, f);
			var thisFwd = Vector3.Slerp(initialFwd, _finalFwd, f);
			GameManager.Me.UpdateCameraPanSequence(thisPos, thisFwd, "", c_failSequencePriorityLabel);
			yield return null;
		}
		if (_cg != null) _cg.alpha = _reverseCG ? 0 : 1;
	}

	private bool WasHandledExternally => MAParserManager.Me.FailDayCheck();
	public bool HasExternalHandlingFinished = false;

	IEnumerator Co_DayNotCompleteSequence(bool _isLoad)
	{
		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.FailedDay);
		if (_isLoad == false) PlayFailAudio();
		if (_isLoad == false) ++GameManager.Me.m_state.m_gameInfo.m_totalTaskFails;
		GameManager.Me.m_state.m_gameInfo.m_inFailState = 2;
		yield return GameManager.Me.Co_ExitToTown();
		while (GameManager.Me.LoadComplete == false) yield return null;

		float timeAtStartOfSequence = Time.unscaledTime;

		CanvasGroup failCG = null;
		var wasHandledExternally = WasHandledExternally;
		if (wasHandledExternally == false)
		{
			bool readyToRetry = false;
			failCG = ShowDialog(() => readyToRetry = true);

			for (float t = 0; t < .5f; t += Time.deltaTime)
			{
				var f = t / .5f;
				failCG.alpha = f;
				yield return null;
			}

			while (readyToRetry == false) yield return null;
		}
		else
		{
			while (HasExternalHandlingFinished == false) yield return null;
		}

		while (Time.unscaledTime - timeAtStartOfSequence < c_minPauseBeforeBackOut) yield return null;

		PlayRetryAudio();
		CalendarUIManager.Me.m_focusMode = true;
		VignetteManager.Me.BlendCrackLevel(0, 4.5f);
		DayNight.Me.RewindTime();
		for (float t = 0; t < .5f; t += Time.deltaTime)
		{
			var f = t / .5f;
			if (failCG != null)
				failCG.alpha = 1 - f;
			yield return null;
		}
		while (DayNight.Me.IsTimeRewinding) yield return null;
		CalendarUIManager.Me.m_focusMode = false;

		MASpecialMABuilding.Crypt().HealthNorm = 1;
		HideDialog();
		m_failSequence = null;
		GameManager.Me.m_state.m_gameInfo.m_inFailState = 0;
		KeyboardShortcutManager.Me.PopShortcuts();

		GameManager.Me.CheckRePossess();
	}

	IEnumerator FadeInOut(float _duration, bool _fadeIn)
	{
		for (float f = 0; f < _duration; f += Time.deltaTime)
		{
			var t = f / _duration;
			if (_fadeIn) t = 1 - t;
			Crossfade.Me.SetFade(t);
			yield return null;
		}
		Crossfade.Me.SetFade(_fadeIn ? 0 : 1);
	}

	private CanvasGroup ShowDialog(System.Action _cb)
	{
		var failUI = UIManager.Me.m_failUI;

		var retryButton = failUI.GetComponentInChildren<UnityEngine.UI.Button>();
		retryButton.onClick.RemoveAllListeners();
		retryButton.onClick.AddListener(() => _cb());

		var failCG = failUI.GetComponent<CanvasGroup>();
		failCG.alpha = 0;
		failUI.gameObject.SetActive(true);
		
		return failCG;
	}

	private void HideDialog()
	{
		var failUI = UIManager.Me.m_failUI;
		failUI.gameObject.SetActive(false);
	}

	const float c_minPauseBeforeBackOut = 6f;

	IEnumerator Co_FailSequence(Transform _whoFailed, bool _isLoad)
	{
		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.FailedDay);
		if (_isLoad == false) PlayFailAudio();
		if (_isLoad == false) ++GameManager.Me.m_state.m_gameInfo.m_totalCryptFails;
		GameManager.Me.m_state.m_gameInfo.m_inFailState = 1;
		yield return GameManager.Me.Co_ExitToTown();
		while (GameManager.Me.LoadComplete == false) yield return null;

		CanvasGroup failCG = null;
		bool readyToRetry = true;
		var wasHandledExternally = WasHandledExternally;
		if (wasHandledExternally == false)
		{
			readyToRetry = false;
			failCG = ShowDialog(() => readyToRetry = true);
		}

		float timeAtStartOfSequence = Time.unscaledTime;
		
		GameManager.Me.StartCameraPanSequence(c_failSequencePriorityLabel);
		var forward = _whoFailed.forward * m_focusDistance + Vector3.up * -m_focusUp;
		var position = _whoFailed.position + Vector3.up * 2 - forward;
		forward.Normalize();
		var atDoorPos = position + forward * m_zoomToDoorDistance;
		var cryptStart = CryptManager.Me.m_failSequenceStart;
		var cryptEnd = CryptManager.Me.m_failSequenceTarget;
		var finalPosition = atDoorPos - forward * m_backOffDistance;

		var camXform = GameManager.Me.m_camera.transform;
		
		var resetPos = camXform.position;
		var resetFwd = camXform.forward;
		
		void SetCam(Vector3 _pos, Vector3 _fwd) { camXform.position = _pos; camXform.forward = _fwd; }
		
		yield return FadeInOut(.15f, false);
		camXform.position = position - forward * 10 + Vector3.up * 50;
		camXform.LookAt(position, Vector3.up);
		yield return FadeInOut(.15f, true);
		
		yield return Pan(camXform, position, forward, m_initialPanTime, EBlendType.Both);
		CryptManager.Me.EnterNonFresco();
		CryptManager.Me.ShowCharacterOnThrone();
		yield return new WaitForSeconds(m_pauseAfterInitialPan);
		yield return Pan(camXform, atDoorPos, forward, m_zoomToDoorTime, EBlendType.Start);
		SetCam(cryptStart.position, cryptStart.forward);
		yield return Pan(camXform, cryptEnd.position, cryptEnd.forward, m_zoomToThroneTime, EBlendType.End, failCG);
		if (wasHandledExternally == false)
		{
			while (readyToRetry == false) yield return null;
		}
		else
		{
			while (HasExternalHandlingFinished == false) yield return null;
		}

		while (Time.unscaledTime - timeAtStartOfSequence < c_minPauseBeforeBackOut) yield return null;

		PlayRetryAudio();
		CalendarUIManager.Me.m_focusMode = true;
		VignetteManager.Me.BlendCrackLevel(0, 4.5f);
		yield return Pan(camXform, cryptEnd.position, cryptEnd.forward, .2f, EBlendType.End, failCG, true);
		yield return new WaitForSeconds(m_pauseBeforeBackOut - .2f);
		yield return Pan(camXform, cryptStart.position, cryptStart.forward, m_zoomToThroneTime / m_backOutSpeedMultiplier, EBlendType.Start);
		SetCam(atDoorPos, forward);
		DayNight.Me.RewindTime();
		CryptManager.Me.ExitNonFresco();
		yield return Pan(camXform, finalPosition, forward, m_backOffSeconds, EBlendType.End);
		CalendarUIManager.Me.m_focusMode = false;
		GameManager.Me.EndCameraPanSequence(c_failSequencePriorityLabel);

		yield return FadeInOut(.15f, false);
		camXform.position = resetPos;
		camXform.LookAt(resetPos + resetFwd, Vector3.up);
		yield return FadeInOut(.15f, true);
		
		MASpecialMABuilding.Crypt().HealthNorm = 1;
		HideDialog();
		m_failSequence = null;
		GameManager.Me.m_state.m_gameInfo.m_inFailState = 0;
		KeyboardShortcutManager.Me.PopShortcuts();

		GameManager.Me.CheckRePossess();
	}
}
