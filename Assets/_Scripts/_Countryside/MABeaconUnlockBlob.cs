using UnityEngine;

public class MABeaconUnlockBlob : Mon<PERSON><PERSON><PERSON><PERSON><PERSON>, ICharacterObjectInteract
{
    public AkEventHolder m_unlockSound;
    
    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr)
    {
        return MAUnlocks.Me.m_unlockBeacons && GameManager.Me.IsPossessed(_chr) && _chr is MAHeroBase && m_owner.IsInRange(_chr.transform.position) &&
               NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);
    }

    public string GetInteractLabel(NGMovingObject _chr)
    {
        return "Activate";
    }

    public void DoInteract(NGMovingObject _chr)
    {
        m_unlockSound?.Play(gameObject);
        m_owner.SmashUnlockBlob(_chr as MACharacterBase);
        Destroy(gameObject);
    }

    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    
    public float AutoInteractTime => 0;

    //

    private SoundTrigger m_attachedSoundTrigger;
    private BCBeacon m_owner;

    void Awake()
    {
        m_attachedSoundTrigger = GetComponent<SoundTrigger>();
    }

    const float c_disabledScale = .01f;
    const float c_baseScale = 4.5f;
    void Update()
    {
        var targetScale = MAUnlocks.Me.m_unlockBeacons ? c_baseScale : c_disabledScale;
        if (m_attachedSoundTrigger != null && m_attachedSoundTrigger.enabled != MAUnlocks.Me.m_unlockBeacons)
            m_attachedSoundTrigger.enabled = MAUnlocks.Me.m_unlockBeacons;
        transform.localScale = Vector3.one * Mathf.Lerp(transform.localScale.x, targetScale, .1f);
    }

    private void Activate(BCBeacon _owner)
    {
        var building = _owner.Building;
        transform.SetParent(building.transform);
        transform.localPosition = Vector3.up * 0.5f;
        transform.localRotation = Quaternion.identity;
        transform.localScale = Vector3.one * c_disabledScale;
        m_owner = _owner;
    }
    
    public static MABeaconUnlockBlob Create(BCBeacon _owner)
    {
        var bub = Instantiate(Resources.Load<MABeaconUnlockBlob>("PowerEffects/MABeaconUnlockBlob"));
        bub.Activate(_owner);
        return bub;
    }
}
