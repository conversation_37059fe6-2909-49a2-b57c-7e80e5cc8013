using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class PathBreak : Mono<PERSON>ehaviour, IPointerClickHandler
{
    public static List<PathBreak> s_allBreaks = new();

    private static void RefreshAllBalloons()
    {
        foreach (var pathBreak in s_allBreaks)
        {
            pathBreak.RefreshBalloon();
        }
    }
    
    private static int GetNextRepairOrder()
    {
        int maxOrder = 0;
        foreach (var pathBreak in s_allBreaks)
        {
            maxOrder = Mathf.Max(pathBreak.m_state.m_repairOrder, maxOrder);
        }
        return Mathf.Max(1, maxOrder + 1);
    }
    
    private static void Register(PathBreak _this)
    {
        s_allBreaks.Add(_this);
        PathManager.s_blockDataDirty = true;
    }
    
    private static void Unregister(PathBreak _this)
    {
        s_allBreaks.Remove(_this);
        PathManager.s_blockDataDirty = true;

        _this.StopRepair();
    }

    public void MakePriority()
    {
        // Already a priority
        if(RepairOrder == 1) return;
        
        int ignoreBreaksEqualToOrAbove = RepairOrder == 0 ? int.MaxValue : RepairOrder;
        
        foreach (var pathBreak in s_allBreaks)
        {
            if(pathBreak.RepairOrder == 0 || pathBreak.RepairOrder >= ignoreBreaksEqualToOrAbove) continue;
            
            pathBreak.m_state.m_repairOrder++; // Shift up
        }
        
        m_state.m_repairOrder = 1;
        RefreshAllBalloons();
    }
    
    public void ResumeRepair()
    {
        if(m_state.m_repairOrder > 0) return;
        
        m_state.m_repairOrder = GetNextRepairOrder();
        RefreshAllBalloons();
    }
    
    public void StopRepair()
    {
        if(m_state.m_repairOrder <= 0) return;
        
        foreach (var pathBreak in s_allBreaks)
        {
            if(pathBreak.RepairOrder <= RepairOrder) continue;
            
            // Shift down
            pathBreak.m_state.m_repairOrder--;
        }
        
        m_state.m_repairOrder = 0;

        RefreshAllBalloons();
    }
    
    public int ActiveBreaks => s_allBreaks.FindAll(b => b.m_state.m_repairOrder != 0)?.Count ?? 0;
    
    [SerializeField]
    private GameState_PathBreak m_state;
    private GameObject m_debris;
    private Balloon m_balloon;
    private GameObject m_effectPrefab;
    private bool m_initial = true;
    private bool m_isDestroying = false; public bool IsDestroyed => m_isDestroying;
    
    private Rigidbody[] m_debrisBodies;

    private PathBreakExplode m_explodeParts;

    public bool IsOpen => m_state.m_repairLevel <= 0;
    public bool IsDamaged => m_state.m_repairLevel < 1f;

    bool m_hasVisualsDisabled = false;
    bool m_hasNavDisabled = false;
    
    
    public GameState_PathBreak State => m_state;
    
    private PathManager.Path m_path;
    private RoadSet m_roadSet;
    private float m_repairCostMultiplier;
    private float m_defenseMultiplier;
    
    private BCResourceNeededRepair resourceNeededRepair = null;

    public PathManager.Path Path => m_path;

    public int RepairOrder => m_state.m_repairOrder;

    public event Action<PathBreak> DidRepair;
    public event Action<PathBreak> DidDestroy;
    
    void OnDestroy()
    {
        if (m_fakeBuilding != null)
        {
            m_fakeBuilding.DestroyMe();
            m_fakeBuilding = null;
        }
        Unregister(this);
        if (gameObject.scene.isLoaded && RoadManager.Me != null && GameManager.Me != null) // not shutting down
        {
            Refresh(m_state.m_position);
            GameManager.Me.m_state.m_pathBreaks.Remove(m_state);
        }

        DidDestroy?.Invoke(this);
    }

    void CheckDisplayRepair()
    {
        if (m_balloon != null && m_balloon.isActiveAndEnabled != RoadManager.Me.RoadBuildMode)
            DisplayRepair();
    }
    
    void DisplayRepair()
    {
        m_balloon.gameObject.SetActive(RoadManager.Me.RoadBuildMode);
        //m_repairCollider.enabled = RoadManager.Me.RoadBuildMode;
    }

    void Update()
    {
        if (RoadManager.Me == null || GameManager.Me == null) return; // shutting down
        if (GameManager.Me.PathsLoaded == false) return;
        CheckDisplayRepair();
        if (m_initial || (m_state.m_position - transform.position).xzSqrMagnitude() > .5f * .5f)
        {
            if (!m_initial && m_state.m_position.sqrMagnitude > 0)
                Refresh(m_state.m_position, transform.position);
            else
                Refresh(transform.position);
            m_initial = false;
        }
    }

    public void FlagUpdated()
    {
        m_initial = false;
        m_state.m_position = transform.position;
    }

    //private SphereCollider m_repairCollider = null;
    public void GeneratePathDebris(bool _colliderOnly = false)
    {
        /*var clickCll = gameObject.AddComponent<SphereCollider>();
        clickCll.center = Vector3.zero;
        clickCll.radius = 4;
        clickCll.isTrigger = true;
        m_repairCollider = clickCll;*/
        
        if (_colliderOnly) return;
        
        RoadManager.Me.m_pathSet.GetClosestPathToPoint(null, true, transform.position, 4, out var path, out var pathT, (p) => p.Set.m_creatureBreakable);
        if (path != null)
        {
            m_path = path;
            m_roadSet = path.Set;
            SetupRepair();
            
            var prefab = path.Set.m_prefabExplode ?? path.Set.m_prefabStraight;
            if(prefab == null)
            {
                Debug.LogError($"{GetType().Name} - Wall Section -  Prefab Explode Reference is null, and Prefab Straight Reference is null for set {path.Set.name}");
            }
            else if(Camera.main == null)
            {
                Debug.LogError($"{GetType().Name} - Wall Section - Camera.main is null");
            }
            else
            {
                m_debris = Instantiate(prefab);
                m_explodeParts = m_debris.GetComponent<PathBreakExplode>();

                Transform tr = transform;
                Transform trDebris = m_debris.transform;
                trDebris.SetParent(tr, false);
                var (pos, fwd, side) = path.GetPosFwdSide(pathT);
                tr.position = pos.GroundPosition();
                m_state.m_position = tr.position;
                tr.LookAt(tr.position + ((Vector3)side).GetXZ(), Vector3.up);
                trDebris.localPosition = Vector3.zero;
                trDebris.localRotation = Quaternion.identity;
            }
        }
        Refresh();
    }

    public float RepairLevel => m_state.m_repairLevel;
    public float RepairRequired => 1f - RepairLevel;
    private string RepairResourceName => m_roadSet.m_breakRepairMaterial;
    private NGCarriableResource RepairResource => NGCarriableResource.GetInfo(RepairResourceName);
    private MABuilding m_fakeBuilding;

    void SetupRepair()
    {
        m_fakeBuilding = CreateFakeBuildingWithRepair(transform.position, transform, RepairResource, out resourceNeededRepair);
        resourceNeededRepair.SetPathBreak(this);
        var details = RoadDetails.GetInfo(m_roadSet.name);
        m_repairCostMultiplier = details.m_repairCostMultiplier;
    }
    
    public static MABuilding CreateFakeBuildingWithRepair(Vector3 _position, Transform _owner, NGCarriableResource _repairResource, out BCResourceNeededRepair resourceNeededRepairOut, List<Vector3> _previousPositions = null)
    {
        var fakeBuilding = MABuilding.CreateFake("ResourceNeededDestination", _position, _owner, Vector3.zero, _previousPositions:_previousPositions);
        var resourceNeededRepair = fakeBuilding.gameObject.AddComponent<BCResourceNeededRepair>();
        fakeBuilding.AddComponent(resourceNeededRepair);
        fakeBuilding.StartupComponents();
        fakeBuilding.m_fuzzyDistrictCheck = true; // allow a slightly fuzzy district check in case the repait site is slightly outside districts
        
        GlobalData.Me.DoPostLoad(() => {
            resourceNeededRepair.SetupInputStock(_owner, _repairResource);
        });
        resourceNeededRepairOut = resourceNeededRepair;
        
        return fakeBuilding;
    }
    
    void Refresh(Vector3 _pos)
    {
        m_state.m_position = _pos;
        Refresh();
    }

    void Refresh()
    {
        bool visualsDisable = m_state.m_repairLevel < 1;
        bool navDisable = m_state.m_repairLevel <= 0;
        bool visualsChanged = visualsDisable != m_hasVisualsDisabled;
        bool navChanged = navDisable != m_hasNavDisabled;
        m_hasVisualsDisabled = visualsDisable;
        m_hasNavDisabled = navDisable;
        SetBlockerCollider(navDisable == false);
        Vector3 dirtyMin = m_state.m_position - Vector3.one * 8, dirtyMax = m_state.m_position + Vector3.one * 8;
        if (visualsChanged || navChanged) PathManager.s_blockDataDirty = true;
        if (visualsChanged) RoadManager.Me.m_pathSet.CreateVisuals(dirtyMin, dirtyMax, null, true);
        if (navChanged) RoadManager.Me.m_pathSet.UpdateNav(dirtyMin, dirtyMax);
    }
    
    public string Status => RepairOrder == 0 ? "Paused" : $"{RepairOrder} of {ActiveBreaks}";
    
    void RefreshBalloon()
    {
        if(m_balloon == null && IsDamaged)
        {
            m_balloon = NGBalloonManager.Me.CreateBalloon(NGBalloonManager.BalloonType.Red, transform, "", null,
                null, b =>
                {
                    WallRepairingInfoGUI.Create(this);
                });
            if(m_balloon)
                m_balloon.transform.position += Vector3.up * 3f;
            var notWanted = m_balloon?.GetComponent<NGColliderPasser>();
            Destroy(notWanted);
            DisplayRepair();
        }

        if (m_balloon != null)
            m_balloon.SetText($"{RepairLevel:P0}\nHealth\n{Status}");
    }

    void Refresh(Vector3 _old, Vector3 _pos)
    {
        m_state.m_position = _pos;
        var min = Vector3.Min(_old, _pos);
        var max = Vector3.Max(_old, _pos);
        RoadManager.Me.m_pathSet.CreateVisuals(min - Vector3.one * 8, max + Vector3.one * 8, null, false);
    }

    IEnumerator Co_DestroyAfter(float _seconds)
    {
        yield return new WaitForSeconds(_seconds);
        Destroy(gameObject);
    }

    private static bool s_debugBreakAndRepair = false;
    private static DebugConsole.Command s_debugBreakAndRepairCmd = new ("debugbreaks", _s => Utility.SetOrToggle(ref s_debugBreakAndRepair, _s));
    
    const float c_damageStep = .05f; 
    public void OnPointerClick(PointerEventData eventData)
    {
        if (s_debugBreakAndRepair)
        {
            float damageStep =  c_damageStep * 20;
            if (Input.GetKey(KeyCode.K))
            {
                DoDamage(ref damageStep, Camera.main.transform.position);
            }
            else
                DoRepair(damageStep);
        }

        if (m_balloon != null && m_balloon.isActiveAndEnabled)
            m_balloon.BalloonClicked();
    }

    private static bool s_debugBreakDamage = false;
    private static DebugConsole.Command s_debugBreakDamageCmd = new ("breakdamage", _s => Utility.SetOrToggle(ref s_debugBreakDamage, _s));
    public void DoDamage(ref float _damageDone, Vector3 _origin)
    {
        if (m_state.m_repairLevel <= 0)
        {
            if (s_debugBreakDamage) Debug.LogError($"Damage {_damageDone} to destroyed break {m_state.m_repairLevel}", gameObject);
            SetBlockerCollider(false);
            _damageDone = 0;
            return; // already destroyed
        }
        
        if (m_defenseMultiplier <= 0f)
        {
            if (s_debugBreakDamage) Debug.LogError($"Damage {_damageDone} to break with outlying defense multiplier {m_defenseMultiplier} - {m_state.m_repairLevel}", gameObject);
            _damageDone = 0;
            return;
        }

        float repairLevelBefore = State.m_repairLevel;
        float realHealth = repairLevelBefore * m_defenseMultiplier;
        float newHealth = Mathf.Clamp(realHealth - _damageDone, 0, Single.MaxValue);
        if (s_debugBreakDamage) Debug.LogError($"Damage {_damageDone} to normal break with defense multiplier {m_defenseMultiplier} - {m_state.m_repairLevel} - rh {realHealth} nh {newHealth}", gameObject);
        _damageDone = realHealth - newHealth;
        m_state.m_repairLevel = newHealth / m_defenseMultiplier;

        if (m_state.m_repairLevel > 0.999f)
        {
            m_state.m_repairLevel = 1f;
        }
        else if (m_state.m_repairLevel < 0.001f)
        {
            m_state.m_repairLevel = 0f;
        }
        
        RefreshBalloon();
        m_explodeParts?.SetExplosionLevel(1 - m_state.m_repairLevel, _origin);
        Refresh();
    }

    void SetBlockerCollider(bool _enabled)
    {
        BoxCollider[] brokenPalisadeColliders = GetComponentsInChildren<BoxCollider>();
        if (brokenPalisadeColliders != null)
        {
            foreach (BoxCollider brokenPalisadeCollider in brokenPalisadeColliders)
            {
                if (brokenPalisadeCollider.isTrigger == false)
                {
                    brokenPalisadeCollider.enabled = _enabled;
                }
            }
        }
    }

    public void DoRepair(float _repair)
    {
        if (m_isDestroying) return; // already fixed, just waiting for effects to complete

        m_state.m_repairLevel += _repair * m_repairCostMultiplier;
        
        RefreshBalloon();
        DidRepair?.Invoke(this);
        
        m_path.PlayConstructAudio(gameObject);
        var rb = m_balloon.GetComponentInChildren<Rigidbody>();
        if (rb != null)
            rb.AddForce(Vector3.up * -8, ForceMode.VelocityChange);
        m_explodeParts?.SetExplosionLevel(1 - m_state.m_repairLevel, Camera.main.transform.position);
        
        if (m_state.m_repairLevel >= 1)
        {
            m_isDestroying = true;
            if (m_effectPrefab != null)
            {
                var rootPos = transform.position;
                var effect = Instantiate(m_effectPrefab);
                effect.transform.position = rootPos;
                effect.gameObject.AddComponent<ParticleSystemDestroyOnFinish>();
            }
            Destroy(gameObject);
        }
        Refresh();
    }
    
    public void Activate(GameState_PathBreak _state, bool _isLoad)
    {
        transform.position = _state.m_position;
        m_state = _state;
        
        Register(this);
        
        GeneratePathDebris(false);

        if (m_roadSet != null)
        {
            var details = RoadDetails.GetInfo(m_roadSet.name);
            m_defenseMultiplier = details.m_defenseMultiplier;
        }

        m_explodeParts?.SetExplosionLevel(1 - m_state.m_repairLevel, Camera.main.transform.position, _isLoad);
        
        RefreshAllBalloons();
    }

    public void GetTotalAndRequiredResources(out float total, out float required)
    {
        float rpd = resourceNeededRepair.m_repairPerDelivery;
        if ((rpd <= 0f) || (m_repairCostMultiplier <= 0f))
        {
            total = 0f;
            required = 0f;
            
            return;
        }

        total = 1f / (rpd * m_repairCostMultiplier);
        required = total * (1f - RepairLevel);
    }

    static DebugConsole.Command s_genpathbreak = new ("pathbreak", _s => {
        if (float.TryParse(_s, out float f) == false) f = 1f;
        Create(PlayerHandManager.Me.Fingertip.position, Camera.main.transform.position, f);
    });
    
    public static void LoadAll()
    {
        var pbs = GameManager.Me.m_state.m_pathBreaks;
        for (int i = pbs.Count - 1; i >= 0; --i)
        {
            var pb = pbs[i];
            if (pb.m_repairLevel >= 1)
                pbs.RemoveAt(i);
            else
                Create(pb, true);
        }
    }

    const float c_closeBreakDistance = 12f;
    public static PathBreak FindAt(Vector3 _pos, float withinRange = c_closeBreakDistance)
    {
        var bestD2 = withinRange * withinRange;
        PathBreak best = null;
        foreach (var pb in s_allBreaks)
        {
            var d2 = (pb.m_state.m_position - _pos).xzSqrMagnitude();
            if (d2 < bestD2)
            {
                best = pb;
                bestD2 = d2;
            }
        }
        return best;
    }

    public static PathBreak Create(Vector3 _pos, Vector3 _origin, float _damage = c_damageStep, float withinRange = c_closeBreakDistance)
    {
        var pb = FindAt(_pos, withinRange);
        if (pb == null)
        {
            var state = new GameState_PathBreak() {m_position = _pos, m_repairLevel = 1};
            GameManager.Me.m_state.m_pathBreaks.Add(state);
            state.m_repairOrder = GetNextRepairOrder();
            pb = Create(state);
        }
        pb.DoDamage(ref _damage, _origin);
        return pb;
    }
    public static PathBreak Create(GameState_PathBreak _state, bool _isLoad = false)
    {
        var go = new GameObject("PathBreak");
        go.transform.SetParent(RoadManager.Me.m_pathHolder);
        var pathBreak = go.AddComponent<PathBreak>();
        pathBreak.Activate(_state, _isLoad);
        return pathBreak;
    }
}
