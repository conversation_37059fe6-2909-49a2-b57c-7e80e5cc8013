using UnityEngine;

public class CryptDoorsInteraction : Mono<PERSON><PERSON><PERSON><PERSON>, ICharacterObjectInteract
{
    public string InteractType => null;
    public bool CanInteract(NGMovingObject _chr)
    {
        return IntroControl.Me.IsPossessedCharacter(_chr) &&
               NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);
    }

    public string GetInteractLabel(NGMovingObject _chr)
    {
        return IntroControl.Me.InCrypt ? "Leave" : "Enter";
    }

    public void DoInteract(NGMovingObject _chr)
    {
        if (IntroControl.Me.InCrypt)
            IntroControl.Me.LeaveCryptIn3rdPerson();
        else
            IntroControl.Me.ProceedIntoCrypt();
    }

    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    
    public float AutoInteractTime => 0;
}
