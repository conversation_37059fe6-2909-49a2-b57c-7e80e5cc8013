using System.Collections;
using System.Collections.Generic;
using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Serialization;
using Random = UnityEngine.Random;

public class BlightManager : MonoSingleton<BlightManager>
{
    public int m_minimumSteps = 11;
    public int m_maximumSteps = 15;
    public float m_angleVariance = 60;
    public float m_minimumStepLength = .7f;
    public float m_maximumStepLength = 1.5f;
    private Texture2D m_blightTexture;
    private NativeArray<byte> m_blightData;

    void CheckTexture()
    {
        if (m_blightTexture == null)
        {
            m_blightTexture = new Texture2D(GlobalData.c_heightmapW, GlobalData.c_heightmapH, TextureFormat.RGBA32, false, true);
            m_blightData = m_blightTexture.GetRawTextureData<byte>();
            ResetTexture();
            m_blightTexture.Apply();
            Shader.SetGlobalTexture("_BlightMap", m_blightTexture);
        }
    }

    void ResetTexture()
    {
        var job = new FillBlightJob(0);
        job.Schedule().Complete();
        m_blightTexture.Apply();
    }

    void ResetBlightProgress()
    {
        foreach (var kvp in m_blightDataLookup)
            kvp.Value.m_current = 0;
    }
    
    static DebugConsole.Command s_resetblight = new ("resetblight", _s => Me.RestartAll(false));
    static DebugConsole.Command s_restartblight = new ("restartblight", _s => Me.RestartAll(true));

    void OnValidate()
    {
        RestartAll(true);
    }
    public void RestartAll(bool _recreate)
    {
        if (m_blightTexture == null) return;
        ResetTexture();
        ResetBlightProgress();
        if (_recreate)
            m_blightDataLookup.Clear();
    }

    bool m_isActive = false;
    void Update()
    {
        CheckTexture();

        if (CameraRenderSettings.Me._BlightEnabled == false)
        {
            if (m_isActive)
            {
                ResetTexture();
                ResetBlightProgress();
                m_isActive = false;
            }
            return;
        }

        Complete();
        m_blightTexture.Apply();

        m_isActive = true;
        foreach (var c in NGManager.Me.m_maBuildings)
        {
            if (c.HasBuildingComponent<BCFactory>())// GetProduct() != null)
                foreach (var b in c.GetComponentsInChildren<Block>())
                    Generate(b.transform);
            //Drop(c.transform.position);
        }
    }

    public class BlightData
    {
        public NativeArray<float3> m_points;
        public float m_current;
    }
    Dictionary<Transform, BlightData> m_blightDataLookup = new Dictionary<Transform, BlightData>();

    Vector3 RandomStep(float _angle)
    {
        float angleVariance = m_angleVariance;
        float minStep = m_minimumStepLength, maxStep = m_maximumStepLength; 
        var finalAngle = Mathf.Deg2Rad * (_angle + Random.Range(-angleVariance, angleVariance));
        var step = Random.Range(minStep, maxStep);
        return new Vector3(Mathf.Cos(finalAngle) * step, 0, Mathf.Sin(finalAngle) * step);
    }

    BlightData GetBlightData(Transform _t)
    {
        BlightData points;
        if (!m_blightDataLookup.TryGetValue(_t, out points))
        {
            int count = Random.Range(m_minimumSteps, m_maximumSteps);
            var pointList = new NativeArray<float3>(count * 2, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
            var root = Vector3.zero;
            var angle = Random.Range(0, 360);
            for (int i = 0; i < count; ++i)
            {
                pointList[i * 2 + 0] = root;
                pointList[i * 2 + 1] = root + RandomStep(angle);
                int numPoints = i + 1;
                int nextStart = numPoints - 1 - Random.Range(0, numPoints) * Random.Range(0, numPoints) * Random.Range(0, numPoints) / (numPoints * numPoints); // bias towards end of list
                root = pointList[nextStart * 2 + 1];
            }
            points = new BlightData() { m_points = pointList, m_current = 0 };
            m_blightDataLookup[_t] = points;
        }
        return points;
    }
    void Generate(Transform _t)
    {
        var data = GetBlightData(_t);
        data.m_current += Time.deltaTime;
        if (data.m_current > (int)(data.m_points.Length / 2))
            data.m_current = (int)(data.m_points.Length / 2);
        var job = new BlightJob(_t.position, data.m_points, data.m_current);
        m_jobs.Add(job.Schedule((int)math.ceil(data.m_current), 1));
        /*int pointsToShow = Mathf.CeilToInt(data.m_current);
        for (int i = 0; i < pointsToShow; ++i)
        {
            float frac = i == pointsToShow - 1 ? data.m_current - i : 1;
            StampLine(_t.position + data.m_points[i * 2], _t.position + Vector3.Lerp(data.m_points[i * 2 + 0], data.m_points[i * 2 + 1], frac));
        }*/
    }

    void StampLine(Vector3 _start, Vector3 _end)
    {
        int x0 = GlobalData.TerrainX(_start.x), z0 = GlobalData.TerrainZ(_start.z);
        int x1 = GlobalData.TerrainX(_end.x), z1 = GlobalData.TerrainZ(_end.z);
        //StampLine(x0, z0, x1, z1, 1, 0);
        //StampLine(x0, z0, x1, z1, c_radius * 2, 1);
        //return;
        StampLine(x0, z0, x1, z1);
    }
    List<JobHandle> m_jobs = new List<JobHandle>();

    void Complete()
    {
        for (int i = 0; i < m_jobs.Count; ++i)
            m_jobs[i].Complete();
        m_jobs.Clear();
    }

    void StampLine(int x0, int z0, int x1, int z1)
    {
        int dx = Mathf.Abs(x1 - x0), dz = Mathf.Abs(z1 - z0);
        int sx = x0 < x1 ? 1 : -1, sz = z0 < z1 ? 1 : -1;
        int err = (dx > dz ? dx : -dz) / 2;
        while (x0 != x1  || z0 != z1)
        {
            int index = (x0 + z0 * GlobalData.c_heightmapW) * 4;
            m_blightData[index + 0] = 255;
            StampGreen(x0, z0, 255);
            int errBase = err;
            if (errBase > -dx) { err -= dz; x0 += sx; }
            if (errBase < dz) { err += dx; z0 += sz; }
        }
    }

    void setPixel(int _x, int _y, int _value, int _channel)
    {
        int index = (_x + _y * GlobalData.c_heightmapW) * 4 + _channel;
        if (_value > m_blightData[index])
            m_blightData[index] = (byte) _value;
    }
    void StampLine(int x0, int y0, int x1, int y1, int th, int channel)
    {
        int dx = math.abs(x1 - x0), sx = x0 < x1 ? 1 : -1;
        int dy = math.abs(y1 - y0), sy = y0 < y1 ? 1 : -1;
        int err, e2 = (int) math.sqrt(dx * dx + dy * dy); /* length */

        if (e2 == 0) return; // zero length line
        
        dx = dx * 255 / e2;
        dy = dy * 255 / e2;
        th = 255 * (th - 1); /* scale values */

        if (dx < dy)
        { /* steep line */
            x1 = (int) math.round((e2 + th / 2) / dy); /* start offset */
            err = x1 * dy - th / 2; /* shift error value to offset width */
            for (x0 -= x1 * sx;; y0 += sy)
            {
                setPixel(x1 = x0, y0, err, channel); /* aliasing pre-pixel */
                for (e2 = dy - err - th; e2 + dy < 255; e2 += dy)
                    setPixel(x1 += sx, y0, 255, channel); /* pixel on the line */
                setPixel(x1 + sx, y0, e2, channel); /* aliasing post-pixel */
                if (y0 == y1) break;
                err += dx; /* y-step */
                if (err > 255) { err -= dy; x0 += sx; } /* x-step */
            }
        }
        else
        { /* flat line */
            y1 = (int) math.round((e2 + th / 2) / dx); /* start offset */
            err = y1 * dx - th / 2; /* shift error value to offset width */
            for (y0 -= y1 * sy;; x0 += sx)
            {
                setPixel(x0, y1 = y0, err, channel); /* aliasing pre-pixel */
                for (e2 = dx - err - th; e2 + dx < 255; e2 += dx)
                    setPixel(x0, y1 += sy, 255, channel); /* pixel on the line */
                setPixel(x0, y1 + sy, e2, channel); /* aliasing post-pixel */
                if (x0 == x1) break;
                err += dy; /* x-step */
                if (err > 255) { err -= dx; y0 += sy; } /* y-step */
            }
        }
    }

    void OnDrawGizmos()
    {
        Gizmos.color = new Color(1, 0, .4f, 1);
        foreach (var kvp in m_blightDataLookup)
        {
            Gizmos.DrawSphere(kvp.Key.position, .5f);
            for (int i = 0; i < kvp.Value.m_points.Length; i += 2)
            {
                Gizmos.DrawLine(kvp.Key.position + (Vector3)kvp.Value.m_points[i], kvp.Key.position + (Vector3)kvp.Value.m_points[i + 1]);
            }
        }
    }

    void Drop(Vector3 _pos)
    {
        var x = GlobalData.TerrainX(_pos.x);
        var z = GlobalData.TerrainZ(_pos.z);
        Drop(-1, x, z, x, z, 32, new HashSet<int>());
    }

    static readonly int[] c_directions = { -1,0, 1,0, 0,-1, 0,1, -1,-1, 1,-1, -1,1, 1,1 };
    int[] m_neighbourValues = new int[8];
    void Drop(int _fromDirection, int _rootX, int _rootZ, int _x, int _z, int _v, HashSet<int> _visited)
    {
        const int c_maxDistance = 4 * 12;
        int rdx = _x - _rootX, rdz = _z - _rootZ;
        if (rdx * rdx + rdz * rdz > c_maxDistance * c_maxDistance) return;
        
        int index = (_x + _z * GlobalData.c_heightmapW) * 4;
        _visited.Add(index);
        int current = m_blightData[index + 0];
        if (current < 255)
        {
            current += _v;
            if (current > 255)
            {
                _v -= (current - 255);
                current = 255;
            }
            else
                _v = 0;
            m_blightData[index + 0] = (byte) current;
            StampGreen(_x, _z, current);
        }
        if (_v > 0)
        {
            int total = 0;
            for (int i = 0; i < 8; ++i)
            {
                int weight = 0;
                int dirChange = (_fromDirection - i) & 7;
                if (_fromDirection == -1 || (dirChange <= 1 || dirChange >= 7))
                {
                    int dx = c_directions[i * 2 + 0];
                    int dz = c_directions[i * 2 + 1];
                    int nx = _x + dx;
                    int nz = _z + dz;
                    if (nx >= 0 && nx < GlobalData.c_heightmapW && nz >= 0 && nz < GlobalData.c_heightmapH)
                    {
                        int nindex = (nx + nz * GlobalData.c_heightmapW) * 4;
                        if (!_visited.Contains(nindex))
                        {
                            weight = m_blightData[nindex + 0];
                            weight = weight * weight + 1;
                        }
                    }
                }
                m_neighbourValues[i] = weight;
                total += weight;
            }
            if (total == 0) return;
            int r = UnityEngine.Random.Range(0, total);
            for (int i = 0; i < 8; ++i)
            {
                r -= m_neighbourValues[i];
                if (r < 0)
                {
                    int dx = c_directions[i * 2 + 0];
                    int dz = c_directions[i * 2 + 1];
                    int nx = _x + dx;
                    int nz = _z + dz;
                    if (_fromDirection == -1) _fromDirection = i;
                    Drop(_fromDirection, _rootX, _rootZ, nx, nz, _v, _visited);
                    break;
                }
            }
        }
    }

    const int c_radius = 12;

    void StampGreen(int _x, int _z, int _value)
    {
        int index = (_x + _z * GlobalData.c_heightmapW) * 4;
        byte current = m_blightData[index + 1];
        if (_value > current) m_blightData[index + 1] = (byte) _value;
        for (int dz = -c_radius; dz <= c_radius; ++dz)
        {
            for (int dx = -c_radius; dx <= c_radius; ++dx)
            {
                int nx = _x + dx, nz = _z + dz;
                if (nx >= 0 && nx < GlobalData.c_heightmapW && nz >= 0 && nz < GlobalData.c_heightmapH)
                {
                    int weight = 256 - (dx * dx + dz * dz) * 256 / (c_radius * c_radius);
                    if (weight > 0)
                    {
                        int nvalue = _value * weight / 256;
                        int nindex = (nx + nz * GlobalData.c_heightmapW) * 4;
                        byte ncurrent = m_blightData[nindex + 1];
                        if (nvalue > ncurrent) m_blightData[nindex + 1] = (byte) nvalue;
                    }
                }
            }
        }
    }

    [BurstCompile]
    public struct FillBlightJob : IJob
    {
        NativeArray<byte> m_blightData;
        [ReadOnly] byte m_fill;
        public FillBlightJob(byte _fill)
        {
            m_blightData = Me.m_blightData;
            m_fill = _fill;
        }
        public void Execute()
        {
            for (int i = 0; i < m_blightData.Length; ++i)
            {
                m_blightData[i] = (i & 3) == 3 ? (byte) 255 : (byte) 0;
            }
        }
    }
    
    [BurstCompile]
    public struct BlightJob : IJobParallelFor
    {
        [NativeDisableContainerSafetyRestriction] NativeArray<byte> m_blightData;
        [ReadOnly] NativeArray<float3> m_points;
        [ReadOnly] float3 m_pos;
        [ReadOnly] float m_current;
        public BlightJob(float3 _pos, NativeArray<float3> _points, float _current)
        {
            m_blightData = Me.m_blightData;
            m_pos = _pos;
            m_points = _points;
            m_current = _current;
        }

        
        public void Execute(int _index)
        {
            int pointsToShow = (int)math.ceil(m_current);
            float frac = _index == pointsToShow - 1 ? m_current - _index : 1;
            var from = m_pos + m_points[_index * 2];
            var to = m_pos + math.lerp(m_points[_index * 2 + 0], m_points[_index * 2 + 1], frac);
            int x0 = GlobalData.TerrainX(from.x), z0 = GlobalData.TerrainZ(from.z);
            int x1 = GlobalData.TerrainX(to.x), z1 = GlobalData.TerrainZ(to.z);
            int dx = math.abs(x1 - x0), dz = math.abs(z1 - z0);
            int sx = x0 < x1 ? 1 : -1, sz = z0 < z1 ? 1 : -1;
            int err = (dx > dz ? dx : -dz) / 2;
            while (x0 != x1 || z0 != z1)
            {
                int index = (x0 + z0 * GlobalData.c_heightmapW) * 4;
                m_blightData[index + 0] = 255;
                StampGreen(x0, z0, 255);
                int errBase = err;
                if (errBase > -dx)
                {
                    err -= dz;
                    x0 += sx;
                }
                if (errBase < dz)
                {
                    err += dx;
                    z0 += sz;
                }
            }            
        }

        void StampGreen(int _x, int _z, int _value)
        {
            int index = (_x + _z * GlobalData.c_heightmapW) * 4;
            byte current = m_blightData[index + 1];
            if (_value > current) m_blightData[index + 1] = (byte) _value;
            for (int dz = -c_radius; dz <= c_radius; ++dz)
            {
                for (int dx = -c_radius; dx <= c_radius; ++dx)
                {
                    int nx = _x + dx, nz = _z + dz;
                    if (nx >= 0 && nx < GlobalData.c_heightmapW && nz >= 0 && nz < GlobalData.c_heightmapH)
                    {
                        int weight = 256 - (dx * dx + dz * dz) * 256 / (c_radius * c_radius);
                        if (weight > 0)
                        {
                            int nvalue = _value * weight / 256;
                            int nindex = (nx + nz * GlobalData.c_heightmapW) * 4 + 1;
                            if (nvalue > m_blightData[nindex])
                                m_blightData[nindex] = (byte) nvalue;
                        }
                    }
                }
            }
        }        
    }
}
