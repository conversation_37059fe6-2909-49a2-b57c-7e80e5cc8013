using System.Collections.Generic;
using UnityEngine;

public class MeterVisual : Mono<PERSON><PERSON><PERSON>our, IBatchPartitioner
{
    public Renderer[] m_renderers;
    public void SetFill(float _level, Vector3 _origin, Vector3 _axis)
    {
        foreach (var renderer in m_renderers)
        {
            if (renderer == null) continue;
            foreach (var material in renderer.materials)
            {
                material.SetVector("_MeterOrigin", _origin);
                material.SetVector("_MeterAxis", _axis);
                material.SetFloat("_MeterFill", _level);
            }
        }
    }

    public void SetColour(Color _colour)
    {
        foreach (var renderer in m_renderers)
        {
            if (renderer == null) continue;
            foreach (var material in renderer.materials)
            {
                material.SetColor("_MeterColour", _colour);
            }
        }
    }

    public Component Component() => this;

    public List<List<Transform>> GetExcludedTransforms() => new();

    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced)
    {
        var newRends = new HashSet<Renderer>();
        foreach (var rend in m_renderers)
        {
            if (rend is MeshRenderer mR && _replaced.TryGetValue(mR, out var newRenderer))
                newRends.Add(newRenderer);
            else
                newRends.Add(rend);
        }
        m_renderers = new List<Renderer>(newRends).ToArray();
    }
}
