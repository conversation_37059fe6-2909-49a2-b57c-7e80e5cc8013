using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(GenericSubSceneExitInteraction))]
public class GenericSubSceneExitInteractionEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        GenericSubSceneEditor.DrawGUI();
    }
}
#endif

public class GenericSubSceneExitInteraction : MonoBehaviour, ICharacterObjectInteract
{
    public static Dictionary<string, Transform> s_linkTransforms = new();

    public static void Register(string _id, Transform _transform)
    {
        s_linkTransforms[_id] = _transform;
    }

    public static void Unregister(string _id)
    {
        s_linkTransforms.Remove(_id);
    }

    public static Transform FindLinkTransform(string _id)
    {
        if (s_linkTransforms.TryGetValue(_id, out var transform))
            return transform;
        return null;
    }
    
    void OnEnable() => Register(m_linkLabel, m_spawnTransform);
    void OnDisable() => Unregister(m_linkLabel);

    public string m_linkLabel;
    public Transform m_spawnTransform;
    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr)
    {
        return GameManager.Me.IsPossessed(_chr);
    }

    public string GetInteractLabel(NGMovingObject _chr)
    {
        return "Leave";
    }

    public void DoInteract(NGMovingObject _chr)
    {
        GenericSubScene.Exit(m_linkLabel);
    }

    public bool RunInteractSequence(List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    
    public float m_autoInteractTime = 0;
    public float AutoInteractTime => m_autoInteractTime;
}
