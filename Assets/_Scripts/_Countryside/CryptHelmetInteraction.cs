using UnityEngine;

public class CryptHelmetInteraction : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ICharacterObjectInteract
{
    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr)
    {
        return IntroControl.Me.IsPossessedCharacter(_chr) &&
               NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);
    }

    public string GetInteractLabel(NGMovingObject _chr)
    {
        return "Inspect";
    }

    public void DoInteract(NGMovingObject _chr)
    {
        IntroControl.Me.LeaveCrypt();
    }

    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    
    public float AutoInteractTime => 0;
}
