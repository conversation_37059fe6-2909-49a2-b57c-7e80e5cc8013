using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MALockManager : Mono<PERSON>ing<PERSON><MALockManager>
{
    [System.Serializable]
    public class MALockPrefab
    {
        public string m_type;
        public string m_variant;
        public GameObject m_prefab;
    }
    
    public MALockPrefab[] m_lockPrefabs;
    public MALockPrefab[] m_keyContainerPrefabs;
    private Dictionary<string, MALockPrefab> m_lookup = new ();
    private Dictionary<string, MALockPrefab> m_lookupKeyContainer = new();

    public event System.Action<string> OnUnlock;
    public void FireUnlockCallback(string _s) => OnUnlock?.Invoke(_s);

    void Start()
    {
        CreateLookup();
    }
    
#if UNITY_EDITOR
    void OnValidate()
    {
        CreateLookup();
    }
#endif

    public string GetRandomVariant(string _type, bool _isKeyContainer)
    {
        _type = _type.ToLower();
        int count = 0;
        var list = _isKeyContainer ? m_keyContainerPrefabs : m_lockPrefabs;
        for (int i = 0; i < list.Length; ++i)
        {
            if (list[i].m_type.ToLower() == _type)
            {
                ++count;
            }
        }
        if (count == 0) return null;
        int index = Random.Range(0, count);
        for (int i = 0; i < list.Length; ++i)
        {
            if (list[i].m_type.ToLower() == _type)
            {
                if (index == 0) return list[i].m_variant;
                --index;
            }
        }
        return null;
    }

    public static string Id(string _type, string _variant) => $"{_type}_{_variant}".ToLower();
    void CreateLookup()
    {
        m_lookup.Clear();
        for (int i = 0; i < m_lockPrefabs.Length; ++i)
        {
            m_lookup.Add(Id(m_lockPrefabs[i].m_type, m_lockPrefabs[i].m_variant), m_lockPrefabs[i]);
        }
        m_lookupKeyContainer.Clear();
        for (int i = 0; i < m_keyContainerPrefabs.Length; ++i)
        {
            m_lookupKeyContainer.Add(Id(m_keyContainerPrefabs[i].m_type, m_keyContainerPrefabs[i].m_variant), m_keyContainerPrefabs[i]);
        }
    }

    const string c_boulderUnlock = "rock";
    public static bool CanUnlock(string _type)
    {
        _type = _type.ToLower();
#if UNITY_EDITOR
        if (GameManager.Me.IsSeedEditMode) return true;
#endif
        if (_type == c_boulderUnlock)
        {
            if (NGUnlocks.DemolishBolders) return true;
        }
        return false;
    }

    public GameObject Create(string _type, string _variant, Vector3 _position, Vector3 _eulers, bool _isKeyContainer)
    {
        var lookup = _isKeyContainer ? m_lookupKeyContainer : m_lookup;
        if (lookup.TryGetValue(Id(_type, _variant), out var prefab) == false) return null;
        var go = Instantiate(prefab.m_prefab, _position, Quaternion.Euler(_eulers), transform);
        go.name = Id(_type, _variant);
        return go;
    }
}

