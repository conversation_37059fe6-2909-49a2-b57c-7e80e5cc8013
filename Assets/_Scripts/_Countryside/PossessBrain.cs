using UnityEngine;
using UnityEngine.UI;

public class PossessBrain : Mono<PERSON>ingleton<PossessBrain>
{
    public RawImage m_subImage;
    public Image m_backgroundImage;
    private CanvasGroup m_canvasGroup;
    
    void Start()
    {
        m_canvasGroup = GetComponent<CanvasGroup>();
        gameObject.SetActive(false);
    }

    public void SetAlpha(float _f, bool _includeSubImage)
    {
        //if (m_subImage.enabled != _includeSubImage)
        //    m_subImage.enabled = _includeSubImage;
        // alpha the background image so it fades in more slowly than main image (i.e. it gets alpha squared instead of alpha)
        //m_backgroundImage.color = new Color(m_backgroundImage.color.r, m_backgroundImage.color.g, m_backgroundImage.color.b, _f);
        m_backgroundImage.enabled = false;
        
        _f = _f * _f * (3 - _f  - _f);
        
        var rt = (m_backgroundImage.transform as RectTransform);
        rt.localScale = Vector3.one * (_includeSubImage ? 1 + _f : 3 - _f);
        
        var active = _f > .0001f;
        if (gameObject.activeSelf != active)
            gameObject.SetActive(active);
        m_canvasGroup.alpha = _f;
    }
}
