using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public struct NativeListOfLists : System.IDisposable
{
    public NativeList<int> m_toc;
    public NativeList<int> m_list;

    public NativeListOfLists(int _capacity)
    {
        m_toc = new(_capacity, Allocator.Persistent);
        m_list = new(_capacity, Allocator.Persistent);
    }

    public int Assign()
    {
        int index = m_toc.Length;
        m_toc.Add(m_list.Length); // first
        m_toc.Add(0); // count
        return index;
    }

    public bool RemoveItem(int _index, int _value)
    {
        int first = m_toc[_index + 0];
        int count = m_toc[_index + 1];
        for (int i = first; i < first + count; ++i)
        {
            if (m_list[i] == _value)
            {
                RemoveAt(_index, i - first);
                return true;
            }
        }
        return false;
    }
    public void RemoveAt(int _index, int _itemIndex)
    {
        int first = m_toc[_index + 0];
        int count = m_toc[_index + 1];
        m_list.RemoveAt(first + _itemIndex);
        if (_index < m_toc.Length - 2)
            for (int i = _index + 2; i < m_toc.Length; i += 2)
                --m_toc[i + 0];
        --m_toc[_index + 1]; 
    }

    public void Add(int _index, int _value)
    {
        if (_index == m_toc.Length - 2)
        {
            m_list.Add(_value);
        }
        else
        {
            int first = m_toc[_index + 0];
            int count = m_toc[_index + 1];
            for (int i = first; i < first + count; ++i)
                if (m_list[i] == _value)
                    return;
            m_list.InsertRange(first + count, 1);
            m_list[first + count] = _value;
            for (int i = _index + 2; i < m_toc.Length; i += 2)
                ++m_toc[i + 0];
        }
        ++m_toc[_index + 1];
    }

    public void Add(int _index, List<int> _value)
    {
        int toAdd = _value.Count;
        if (_index == m_toc.Length - 2)
        {
            for (int i = 0; i < toAdd; ++i)
                m_list.Add(_value[i]);
        }
        else
        {
            int first = m_toc[_index + 0];
            int count = m_toc[_index + 1];
            m_list.InsertRange(first + count, toAdd);
            for (int i = 0; i < toAdd; ++i)
                m_list[first + count + i] = _value[i];
            for (int i = _index + 2; i < m_toc.Length; i += 2)
                m_toc[i + 0] += toAdd;
        }
        m_toc[_index + 1] += toAdd;
    }

    public void Clear(int _index)
    {
        int toRemove = m_toc[_index + 1];
        m_list.RemoveRange(m_toc[_index + 0], toRemove);
        m_toc[_index + 1] = 0;
        for (int i = _index + 2; i < m_toc.Length; i += 2)
            m_toc[i + 0] -= toRemove;
    }

    public void Dispose()
    {
        m_toc.Dispose();
        m_list.Dispose();
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(NavStrut)), CanEditMultipleObjects]
public class NavStrutEditor : Editor
{
    protected virtual void OnSceneGUI()
    {
        var item = (NavStrut) target;
        if (item.m_showStartEndGizmos)
        {
            EditorGUI.BeginChangeCheck();
            var newStart = Handles.PositionHandle(item.LiveWorldStart, item.transform.rotation);
            var newEnd = Handles.PositionHandle(item.LiveWorldEnd, item.transform.rotation);
            item.m_localStart = item.transform.InverseTransformPoint(newStart);
            item.m_localEnd = item.transform.InverseTransformPoint(newEnd);
            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(item, "Change Positions");
                EditorUtility.SetDirty(item);
            }
        }
    }

    public static string StrutToString(int _strut)
    {
        string desc;
        if (_strut < 0 || GlobalData.Me == null) desc = $"Strut [{_strut}]";
        else desc = $"Pos {GlobalData.Me.I2V(_strut).ToVector2().ToString("n1")} [{_strut}]";
        return desc;
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        var item = (NavStrut) target;
        var thisID = item.StrutID();
        GUILayout.Label("\nAll strut details", EditorStyles.boldLabel);
        foreach (var kvp in NavStrut.s_navPointNeighbours)
        {
            GUI.color = thisID == kvp.Key ? Color.yellow : Color.white;
            int first = NavStrut.s_navPointNeighboursList.m_toc[kvp.Value + 0];
            int count = NavStrut.s_navPointNeighboursList.m_toc[kvp.Value + 1];
            GUILayout.Label($"{StrutToString(kvp.Key)}: [{first},{count}]");
            for (int i = first; i < first + count; ++i)
            {
                int strutIndex = NavStrut.s_navPointNeighboursList.m_list[i];
                GUILayout.Label($"  > {StrutToString(strutIndex)}");
            }
        }
    }
}
#endif


public class NavStrut : MonoBehaviour
{
    //==============================================================================

    public static List<NavStrut> s_allStruts = new ();
    public static NativeHashMap<int, int> s_navPointNeighbours = new (64, Allocator.Persistent);
    public static NativeHashMap<int, int> s_navPointLowResNeighbours = new(64, Allocator.Persistent);
    public static NativeListOfLists s_navPointNeighboursList = new(64);
    public static NativeHashMap<int, int> s_navPointStrutToNavPos = new(64, Allocator.Persistent);
    public static NativeHashMap<int, float3> s_navPointStrutToWorldPos = new(64, Allocator.Persistent);


    public static NavStrut GetStrutFromY(float _y)
    {
        var index = (int) -_y - 2;
        if (index < 0 || index >= s_allStruts.Count) return null;
        return s_allStruts[index];
    }

    private static void SetOnDirty()
    {
        if (Application.isPlaying == false) return;
        GlobalData.Me.OnTerrainHeightsUpdated -= OnTerrainHeightsUpdated;
        GlobalData.Me.OnTerrainHeightsUpdated += OnTerrainHeightsUpdated;
    }

    private static void OnTerrainHeightsUpdated()
    {
        for (int i = 0; i < s_allStruts.Count; ++i)
            s_allStruts[i].Check(true);
    }

    public static void Register(NavStrut _strut)
    {
        SetOnDirty();
        s_allStruts.Add(_strut);
    }
    public static void Unregister(NavStrut _strut)
    {
        s_allStruts.Remove(_strut);
    }

    private static void FindLinks(NavStrut _new)
    {
        int newIndex = s_allStruts.IndexOf(_new);
        var nStart = _new.WorldStart;
        var nEnd = _new.WorldEnd;
        for (int i = 0; i < s_allStruts.Count; ++i)
        {
            if (newIndex == i) continue;
            var iStart = s_allStruts[i].WorldStart;
            var iEnd = s_allStruts[i].WorldEnd;
            var distSqrd = Utility.LineSegmentMinimumDistanceSquared(nStart, nEnd, iStart, iEnd, out var iPos, out var nPos);
            const float c_maxLinkDistance = 3f;
            if (distSqrd < c_maxLinkDistance * c_maxLinkDistance)
            {
                NavStrutLink linkNew = new() {m_strut = s_allStruts[i], m_strutIndex = i, m_pos = nPos};
                NavStrutLink linkI = new() {m_strut = _new, m_strutIndex = newIndex, m_pos = iPos};
                linkNew.m_other = linkI;
                linkI.m_other = linkNew;
                _new.m_links.Add(linkNew);
                s_allStruts[i].m_links.Add(linkI);
                s_allStruts[i].UpdateNeighbours();
            }
        }
    }

    public static int NavStrutAtPosition(Vector3 _pos)
    {
        for (int i = 0; i < s_allStruts.Count; ++i)
            if (s_allStruts[i].AssociatedWithPoint(_pos))
                return -2 - i;
        return 0;
    }

    //==============================================================================

    public enum ENavStrutAnimationType
    {
        Locomotion,
        Animation,
    }
    public enum ENavStrutDirectionType
    {
        StrutDirection,
        ObjectForward,
        ObjectBackward,
        ObjectRight,
        ObjectLeft,
        ObjectUp,
        ObjectDown,
    }
    public enum EGroundPosType
    {
        None,
        Start,
        End,
    }
    
    public class NavStrutLink
    {
        public NavStrut m_strut;
        public int m_strutIndex;
        public Vector3 m_pos;
        public NavStrutLink m_other;
    }
    public Vector3 m_localStart, m_localEnd;
    public ENavStrutAnimationType m_animationType = ENavStrutAnimationType.Animation;
    public ENavStrutDirectionType m_directionType = ENavStrutDirectionType.StrutDirection;
    public float m_width = 1;
    public string m_animS2EInto, m_animS2ELoop, m_animS2EOut;
    public string m_animE2SInto, m_animE2SLoop, m_animE2SOut;
    public bool m_showStartEndGizmos = false;
    
    private Vector3 m_worldStart, m_worldEnd;
    private Vector3 m_worldCenter, m_worldExtent, m_worldExtentPaddedSqrd;
    private Vector3 m_worldGroundPos;
    private EGroundPosType m_worldGroundPosType;
    private int m_worldGroundPosID;
    public Vector3 WorldStart => m_worldStart;
    public Vector3 WorldEnd => m_worldEnd;
    public Vector3 WorldCenter => m_worldCenter;
    public Vector3 LiveWorldStart => transform.TransformPoint(m_localStart);
    public Vector3 LiveWorldEnd => transform.TransformPoint(m_localEnd);
    public List<NavStrutLink> m_links = new ();
    private int m_neighboursListIndex = -1;

    void Awake()
    {
        Register(this);
        Init(false);
    }
    void Start()
    {
        FindAndProcessLinks();
    }

    void OnDestroy()
    {
        Unregister(this);
    }
    
    public int StrutID() => -2 - s_allStruts.IndexOf(this);

    void Init(bool _checkLinks)
    {
        if (Application.isPlaying == false || GlobalData.Me == null || GlobalData.Me.TerrainReady == false) return;
        int strutId = StrutID();
        m_worldStart = LiveWorldStart;
        m_worldEnd = LiveWorldEnd;
        m_worldCenter = (m_worldStart + m_worldEnd) / 2;
        m_worldExtent = m_worldStart - m_worldCenter;
        m_worldExtent.x = Mathf.Abs(m_worldExtent.x);
        m_worldExtent.y = Mathf.Abs(m_worldExtent.y);
        m_worldExtent.z = Mathf.Abs(m_worldExtent.z);
        m_worldExtentPaddedSqrd = m_worldExtent + Vector3.one * c_associationDistance;
        m_worldExtentPaddedSqrd = Vector3.Scale(m_worldExtentPaddedSqrd, m_worldExtentPaddedSqrd);
        s_navPointStrutToNavPos[strutId] = GlobalData.Me.V2I(ref m_worldCenter);
        float3 worldCenterWithIndex = m_worldCenter; worldCenterWithIndex.y = strutId;
        s_navPointStrutToWorldPos[strutId] = worldCenterWithIndex;
        var groundStartY = m_worldStart.GroundPosition().y;
        var groundStartOffs = m_worldStart.y - groundStartY;
        const float c_groundLinkDistance = 2.0f;
        m_worldGroundPosType = EGroundPosType.None;
        m_worldGroundPosID = 0;
        if (groundStartOffs < c_groundLinkDistance)
        {
            m_worldGroundPos = m_worldStart;
            m_worldGroundPos.y = groundStartY;
            m_worldGroundPosType = EGroundPosType.Start;
        }
        else
        {
            var groundEndY = m_worldEnd.GroundPosition().y;
            var groundEndOffs = m_worldEnd.y - groundEndY;
            if (groundEndOffs < c_groundLinkDistance)
            {
                m_worldGroundPos = m_worldEnd;
                m_worldGroundPos.y = groundEndY;
                m_worldGroundPosType = EGroundPosType.End;
            }
        }
        if (m_worldGroundPosType != EGroundPosType.None)
        {
            m_worldGroundPosID = GlobalData.Me.V2I(ref m_worldGroundPos);
        }
        if (_checkLinks) FindAndProcessLinks();
    }
    void FindAndProcessLinks()
    {
        FindLinks(this);
        UpdateNeighbours();
    }
    public void UpdateNeighbours()
    {
        // add neighbours
        int strutId = StrutID();
        var neighbours = GetNavNeighbours();
        if (m_neighboursListIndex < 0)
        {
            m_neighboursListIndex = s_navPointNeighboursList.Assign();
            s_navPointNeighbours[strutId] = m_neighboursListIndex;
            s_navPointLowResNeighbours[strutId] = m_neighboursListIndex;
        }
        s_navPointNeighboursList.Clear(m_neighboursListIndex);
        s_navPointNeighboursList.Add(m_neighboursListIndex, neighbours);
        // add to associated nav point list
        if (m_worldGroundPosType != EGroundPosType.None)
        {
            if (!s_navPointNeighbours.TryGetValue(m_worldGroundPosID, out var groundIndex))
            {
                groundIndex = s_navPointNeighboursList.Assign();
                s_navPointNeighbours[m_worldGroundPosID] = groundIndex;
                s_navPointLowResNeighbours[GlobalData.Me.NavToLowRes(m_worldGroundPosID)] = groundIndex;
            }
            s_navPointNeighboursList.Add(groundIndex, strutId);
        }
#if UNITY_EDITOR
        foreach (var kvp in s_navPointNeighbours)
        {
            if (kvp.Value < 0 && (s_navPointNeighbours.ContainsKey(kvp.Value) == false || s_navPointStrutToNavPos.ContainsKey(kvp.Value) == false || s_navPointStrutToWorldPos.ContainsKey(kvp.Value) == false))
            {
                Debug.LogError($"NavStrut: struct {kvp.Key} has strut neighbour {kvp.Value} that is not in the list of struts or nav points. This will crash the nav\n[npn:{s_navPointNeighbours.ContainsKey(kvp.Value)} npstnp:{s_navPointStrutToNavPos.ContainsKey(kvp.Value)} npstwp:{s_navPointStrutToWorldPos.ContainsKey(kvp.Value)}]");
            }
        }
#endif
    }

    public Vector3 GetForward(Vector3 _strutForward)
    {
        switch (m_directionType)
        {
            case ENavStrutDirectionType.ObjectForward:
                return transform.forward;
            case ENavStrutDirectionType.ObjectBackward:
                return -transform.forward;
            case ENavStrutDirectionType.ObjectRight:
                return transform.right;
            case ENavStrutDirectionType.ObjectLeft:
                return -transform.right;
            case ENavStrutDirectionType.ObjectUp:
                return transform.up;
            case ENavStrutDirectionType.ObjectDown:
                return -transform.up;
            default: case ENavStrutDirectionType.StrutDirection:
                return _strutForward;
        }
    }

    void Check(bool _force = false)
    {
        var ws = LiveWorldStart;
        var we = LiveWorldEnd;
        if (_force || (ws - m_worldStart).sqrMagnitude > .01f * .01f || (we - m_worldEnd).sqrMagnitude > .01f * .01f)
        {
            RemoveLinks();
            Init(true);
        }
    }

    void Update()
    {
        Check();
    }

    void RemoveLinks()
    {
        for (int i = 0; i < m_links.Count; ++i)
        {
            var other = m_links[i].m_strut;
            for (int j = 0; j < other.m_links.Count; ++j)
            {
                if (other.m_links[j].m_strut == this)
                {
                    other.m_links.RemoveAt(j);
                    break;
                }
            }
        }
        m_links.Clear();
        if (m_worldGroundPosType != EGroundPosType.None)
        {
            s_navPointNeighboursList.Clear(m_neighboursListIndex);

            if (s_navPointNeighbours.TryGetValue(m_worldGroundPosID, out var groundIndex))
            {
                int strutId = -2 - s_allStruts.IndexOf(this);
                s_navPointNeighboursList.RemoveItem(groundIndex, strutId);
                if (s_navPointNeighboursList.m_toc[groundIndex + 1] == 0)
                {
                    s_navPointNeighbours.Remove(m_worldGroundPosID);
                    s_navPointLowResNeighbours.Remove(GlobalData.Me.NavToLowRes(m_worldGroundPosID));
                }
            }
        }
    }

    public enum EAnimationState
    {
        Into,
        Loop,
        Out,
        Done,
    }
    static Dictionary<GameObject, EAnimationState> s_animationStates = new();

    private static EAnimationState AnimationState(GameObject _o)
    {
        if (s_animationStates.TryGetValue(_o, out var state))
            return state;
        return EAnimationState.Done;
    }
    
    public static bool IsAnimationPlaying(GameObject _o) => AnimationState(_o) != EAnimationState.Done;

    public static bool IsAnimationTransitionPlaying(GameObject _o)
    {
        var state = AnimationState(_o);
        return state == EAnimationState.Into || state == EAnimationState.Out;
    }

    public void StartAnimation(GameObject _o, Vector3 _from, Vector3 _to)
    {
        StartAnimation(_o, IsStartToEnd(_from, _to));
    }

    public void StartAnimation(GameObject _o, bool _startToEnd)
    {
        if (m_animationType == ENavStrutAnimationType.Locomotion) return;
        var ao = _o.GetComponentInChildren<AnimationHandler>();
        string animIn = _startToEnd ? m_animS2EInto : m_animE2SInto;
        string animLoop = _startToEnd ? m_animS2ELoop : m_animE2SLoop;
        string animOut = _startToEnd ? m_animS2EOut : m_animE2SOut;
        s_animationStates[_o] = EAnimationState.Into;
        ao.PlayLoopingAnimation(animIn, animLoop, animOut, 
            (b) => { s_animationStates[_o] = EAnimationState.Loop; }, (b) => {}, (b) => { s_animationStates[_o] = EAnimationState.Done; });
    }

    public void EndAnimation(GameObject _o)
    {
        if (m_animationType == ENavStrutAnimationType.Locomotion) return;
        var state = AnimationState(_o); 
        if (state != EAnimationState.Loop && state != EAnimationState.Into) return;
        s_animationStates[_o] = EAnimationState.Out;
        var ao = _o.GetComponentInChildren<AnimationHandler>();
        ao.FinishLoopingAnimation();
    }

    public Vector3 GetNavPoint(NavStrut _from)
    {
        if (_from == null)
        {
            // nav to/from ground
            if (m_worldGroundPosType == EGroundPosType.None)
                return Vector3.zero;
            return m_worldGroundPos;
        }
        // nav to/from a previous strut
        for (int i = 0; i < m_links.Count; ++i)
            if (m_links[i].m_strut == _from)
                return m_links[i].m_pos;
        return Vector3.zero;
    }

    public bool IsStartToEnd(Vector3 _from, Vector3 _to)
    {
        var wStart = WorldStart;
        var wEnd = WorldEnd;
        var tFrom = Utility.GetTInSegment(_from, WorldStart, WorldEnd);
        var tTo = Utility.GetTInSegment(_to, WorldStart, WorldEnd);
        return tTo > tFrom;
    }

    public (Vector3, Vector3) NavInStrut(NavStrut _from, Vector3 _dest)
    {
        var inStrut = ClosestContainedPoint(_dest, out var overflow);
        Vector3 fromPos = Vector3.zero;
        if (_from == null)
        {
            // nav to/from ground
            if (m_worldGroundPosType != EGroundPosType.None)
                fromPos =  m_worldGroundPos;
        }
        else
        {
            for (int i = 0; i < m_links.Count; ++i)
                if (m_links[i].m_strut == _from)
                    fromPos = m_links[i].m_other.m_pos;
        }
        return (fromPos, inStrut);
    }

    public List<int> GetNavNeighbours()
    {
        var list = new List<int>();
        for (int i = 0; i < m_links.Count; ++i)
            list.Add(-2 - m_links[i].m_strutIndex);
        if (m_worldGroundPosType != EGroundPosType.None)
            list.Add(m_worldGroundPosID);
        return list;
    }

    const float c_associationDistance = 1.5f;

    public bool PointInBounds(Vector3 _pos)
    {
        var toCenter = m_worldCenter - _pos;
        toCenter = Vector3.Scale(toCenter, toCenter);
        return toCenter.x < m_worldExtentPaddedSqrd.x &&
               toCenter.y < m_worldExtentPaddedSqrd.y &&
               toCenter.z < m_worldExtentPaddedSqrd.z;
    }

    public bool AssociatedWithPoint(Vector3 _pos)
    {
        if (PointInBounds(_pos) == false) return false;
        var wStart = WorldStart;
        var wEnd = WorldEnd;
        var t = Utility.GetTInSegment(_pos, WorldStart, WorldEnd);
        var p = Vector3.LerpUnclamped(wStart, wEnd, t);
        return (p - _pos).sqrMagnitude < c_associationDistance * c_associationDistance;
    }

    public Vector3 ClosestContainedPoint(Vector3 _pos, out bool _overflow, float _tAdjust = 0)
    {
        var wStart = WorldStart;
        var wEnd = WorldEnd;
        var t = Utility.GetTInSegment(_pos, WorldStart, WorldEnd);
        t = t + _tAdjust;
        _overflow = t < 0.0f || t > 1.0f;
        t = Mathf.Clamp01(t);
        return Vector3.LerpUnclamped(wStart, wEnd, t);
    }

    public Vector3 GetPointAtStrutHeight(Vector3 _pos)
    {
        var closest = ClosestContainedPoint(_pos, out var overflow);
        return new Vector3(_pos.x, closest.y, _pos.z);
    }

#if UNITY_EDITOR
    static void GizmosDrawThickLine(Vector3 _start, Vector3 _end, float _thickness)
    {
        var center = (_start + _end) / 2;
        var fwd = _end - _start;
        var length = fwd.magnitude;
        fwd /= length;
        var up = fwd.y * fwd.y < .9f ? Vector3.up : Vector3.right;
        var right = Vector3.Cross(fwd, up).normalized;
        up = Vector3.Cross(right, fwd).normalized;
        var mat = Gizmos.matrix;
        Gizmos.matrix = Matrix4x4.LookAt(center, _end, up);
        var size = new Vector3(_thickness, _thickness, length);
        Gizmos.DrawCube(Vector3.zero, size);
        Gizmos.matrix = mat;
    }

    void OnDrawGizmos()
    {
        Check();
        var worldStart = WorldStart;
        var worldEnd = WorldEnd;
        Gizmos.color = Color.green;
        GizmosDrawThickLine(worldStart, worldEnd, .08f);
        Gizmos.DrawLine(worldStart, worldEnd);
        Gizmos.DrawSphere(worldStart, .1f);
        Gizmos.color = Color.yellow;
        Gizmos.DrawSphere(worldEnd, .1f);
        
        var fwd = GetForward(worldEnd - worldStart);
        var side = Vector3.Cross(fwd, Vector3.up).normalized;
        Gizmos.color = Color.white;
        var hs = side * (m_width * .5f);
        var c1 = worldStart - hs;
        var c2 = worldStart + hs;
        var c3 = worldEnd - hs;
        var c4 = worldEnd + hs;
        Gizmos.DrawLine(c1, c2);
        Gizmos.DrawLine(c2, c4);
        Gizmos.DrawLine(c4, c3);
        Gizmos.DrawLine(c3, c1);

        for (int i = 0; i < m_links.Count; ++i)
        {
            var from = m_links[i].m_pos;
            var to = m_links[i].m_other.m_pos;
            Gizmos.color = Color.red;
            GizmosDrawThickLine(from, to, .06f);
            Gizmos.DrawSphere(from, .09f);
            Gizmos.DrawSphere(to, .09f);
            if (m_worldGroundPos.sqrMagnitude > 0)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawSphere(m_worldGroundPos, .2f);
            }
        }
    }
#endif
}
