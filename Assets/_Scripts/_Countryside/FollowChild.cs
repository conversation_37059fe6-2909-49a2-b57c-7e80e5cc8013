using UnityEngine;

public class FollowChild : MonoBehaviour
{
    private FollowManager m_manager;
    private Vector3 m_currentTarget, m_currentBase;
    private NavAgent m_nav;
    private NGMovingObject m_object;
    private float m_speedRandomiser;
    private float m_radialRandomiser; public float RadialRandomiser => m_radialRandomiser;
    private float m_angleRandomiser; public float AngleRandomiser => m_angleRandomiser;
    private float m_bravery = 0; // determines how far forward the child will go; 0 = always behind, 1 equally likely in front

    const float c_reNavDistance = 10; // re-nav if final target position moves by this much
    const float c_reNavTargetDistance = 4; // re-nav if target moves by this much
    const float c_driftedFromTargetDistance = 10; // re-nav if child has drifted from target by this much (e.g. pushed or gone off to attack something)
    const float c_freeWillDistance = 20;
    
    const float c_minSpeedMultiplier = .8f;
    const float c_maxSpeedMultiplier = 1.2f;
    const float c_minRadialDistance = 3.5f;
    const float c_maxRadialDistance = 8.5f;
    const float c_minAngleBrave = 45f;
    const float c_minAngleScared = 45f + 60f;
    const float c_maxAngleBrave = 315f;
    const float c_maxAngleScared = 315f - 60f;

    void Start()
    {
        m_nav = GetComponent<NavAgent>();
        m_object = GetComponent<NGMovingObject>();
        m_object.SetFollow(this);
        uint seed = (uint)(0x17273747 +  m_object.m_ID * 0x71625347);
        var minAngle = Mathf.Lerp(c_minAngleScared, c_minAngleBrave, m_bravery);
        var maxAngle = Mathf.Lerp(c_maxAngleScared, c_maxAngleBrave, m_bravery);
        m_speedRandomiser = Utility.XorShiftRange(ref seed, c_minSpeedMultiplier, c_maxSpeedMultiplier);
        m_radialRandomiser = Utility.XorShiftRange(ref seed, c_minRadialDistance, c_maxRadialDistance);
        m_angleRandomiser = Utility.XorShiftRange(ref seed, minAngle, maxAngle);
    }

    void OnDestroy()
    {
        m_object.SetFollow(null);
        DEBUG_CheckAndCleanUp();
    }

    public void SetManager(FollowManager _manager)
    {
        m_manager = _manager;
        var ng = GetComponent<NGMovingObject>();
        if (ng is MAHeroBase) m_bravery = 1;
        else if (ng is MAWorker) m_bravery = 0;
        else m_bravery = .5f;
    }

    /*void Update()
    {
        RunNav();
    }*/
    
    public bool IsInsideFreeWillRange => (m_manager.transform.position - transform.position).xzSqrMagnitude() < c_freeWillDistance * c_freeWillDistance;

    private bool m_isBeingControlled = false;
    public void SetBeingControlled(bool _on) => m_isBeingControlled = _on;
    
    public void RunNav()
    {
        if (m_isBeingControlled) return;
        var target = m_manager.GetTarget(this);
        var xzSqrDistToTarget = (target - m_currentTarget).xzSqrMagnitude();
        var xzSqrDistToManager = (m_manager.transform.position - m_currentBase).xzSqrMagnitude();
        var xzSqrDistDrift = m_nav.IsNavigating == false ? (transform.position - target).xzSqrMagnitude() : 0;
        if (xzSqrDistToTarget > c_reNavDistance * c_reNavDistance || xzSqrDistToManager > c_reNavTargetDistance * c_reNavTargetDistance || xzSqrDistDrift > c_driftedFromTargetDistance * c_driftedFromTargetDistance)
        {
            m_currentBase = m_manager.transform.position;
            m_currentTarget = target;
            m_nav.NavigateTo(target, _costSetOverride:GlobalData.s_followLeaderCosts);
            m_manager.ReserveSlot(this, target);
        }
        var speed = (transform.position - m_currentTarget).xzMagnitude() * 5;
        var desiredSpeed = m_object.GetDesiredSpeed();
        m_nav.Speed = Mathf.Clamp(speed, .4f * desiredSpeed, 3f * desiredSpeed) * m_speedRandomiser;
        DEBUG_Update();
    }

    public bool IsTargetViable(Vector3 _pos)
    {
        // does the leader allow the child to visit this position?
        if (m_manager == null) return true;
        return (m_manager.transform.position - _pos).xzSqrMagnitude() < c_freeWillDistance * c_freeWillDistance;
    }

    private static DebugConsole.Command s_debugCmd = new ("debugfollow", _s => Utility.SetOrToggle(ref s_debug, _s), "Set FollowChild debug mode", "<bool>");
    private static bool s_debug = false;
    private GameObject[] m_debugObjects;
    void DEBUG_Update()
    {
        if (s_debug)
        {
            if (m_debugObjects == null)
            {
                m_debugObjects = new GameObject[3];
                for (int i = 0; i < 3; ++i)
                {
                    m_debugObjects[i] = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                    Destroy(m_debugObjects[i].GetComponent<Collider>());
                    m_debugObjects[i].transform.localScale = Vector3.one * 0.2f;
                    m_debugObjects[i].GetComponent<MeshRenderer>().material.color = i switch
                    {
                        0 => Color.red,
                        1 => Color.green,
                        2 => Color.blue,
                        _ => Color.white
                    };
                }
            }
            m_debugObjects[0].transform.position = m_currentTarget.GroundPosition();
            m_debugObjects[1].transform.position = transform.position.GroundPosition();
            m_debugObjects[2].transform.position = m_manager.GetTarget(this).GroundPosition();
        }
        else
        {
            DEBUG_CheckAndCleanUp();
        }
    }

    private void DEBUG_CheckAndCleanUp()
    {
        if (m_debugObjects != null)
        {
            foreach (var obj in m_debugObjects)
                Destroy(obj);
            m_debugObjects = null;
        }
    }
}
