using System.Collections.Generic;
using UnityEngine;

public class ExplodeInteraction : MonoBehaviour, ICharacterObjectInteract, IImpactDataSupplier
{
    private static Dictionary<string, ExplodeInteraction> s_all = new();

    public enum ExplodeInteractionType
    {
        Default,
        Disable,
        Destroy
    }

    public ExplodeInteractionType m_explodeInteractionType = ExplodeInteractionType.Default;
    public bool m_useInteraction = true;
    public bool m_isAttackable = false;
    public string m_saveIdentifier;
    public string m_actionLabel = "Smash";
    public bool m_onlyAllowHero = true;
    public float m_explosionRadius = 5;
    public float m_explosionPower = 100;
    public string m_characterTriggerAnimation = null;
    public float m_triggerDelay = 0;
    public AkEventHolder m_triggerAudio;
    public AkEventHolder m_perImpactedCharacterAudio;
    public float m_impactedCharacterDamageBase = 0;
    public Rigidbody[] m_holdBodies;
    public bool m_canRepeat = false;
    public List<GameObject> m_explosionEffectPrefabs;
    public float m_interactionDamageDone = 0.0f;

    void Awake()
    {
        HoldBodies(true);
        s_all[m_saveIdentifier] = this;
    }

    void OnDestroy()
    {
        s_all.Remove(m_saveIdentifier);
    }

    private void HoldBodies(bool _hold)
    {
        if (m_holdBodies != null)
            foreach (var body in m_holdBodies)
                body.isKinematic = _hold;
    }

    private void CreateExplosionEffect(float _damageDone, Vector3 _damageOrigin)
    {
        foreach (var prefab in m_explosionEffectPrefabs)
        {
            GameObject go = Instantiate(prefab, transform.position, transform.rotation);

            MASpawnForceApplier sfa = go.GetComponent<MASpawnForceApplier>();

            if(sfa != null)
            {
                sfa.ApplySpawnForce(_damageDone, _damageOrigin);
            }
        }
    }

    public GameState_ExplodeInteraction SaveData()
    {
        var save = new GameState_ExplodeInteraction();
        save.m_id = m_saveIdentifier;
        for (int i = 0; i < m_holdBodies.Length; i++)
        {
            save.m_positions.Add(m_holdBodies[i].position);
            save.m_rotations.Add(m_holdBodies[i].transform.eulerAngles);
        }
        save.m_isEnabled = enabled ? 1 : 0;
        save.m_isAttackable = m_isAttackable ? 1 : 0;
        return save;
    }

    public void LoadData(GameState_ExplodeInteraction _data)
    {
        if (_data == null) return;
        int toLoad = Mathf.Min(m_holdBodies.Length, _data.m_positions.Count);
        for (int i = 0; i < toLoad; i++)
        {
            m_holdBodies[i].position = _data.m_positions[i];
            m_holdBodies[i].transform.eulerAngles = _data.m_rotations[i];
        }
        enabled = _data.m_isEnabled > 0;
        m_isAttackable = _data.m_isAttackable > 0;

        if(!enabled)
        {
            switch (m_explodeInteractionType)
            {
                case ExplodeInteractionType.Disable:
                    gameObject.SetActive(false);
                    break;
                default:
                    break;
            }
            HoldBodies(false);
        } 
    }

    public static void SaveAll()
    {
        var state = GameManager.Me.m_state;
        if (state.m_explodeInteractions == null)
            state.m_explodeInteractions = new List<GameState_ExplodeInteraction>();
        else
            state.m_explodeInteractions.Clear();
        foreach (var kvp in s_all)
        {
            var save = kvp.Value.SaveData();
            if (save != null)
                state.m_explodeInteractions.Add(save);
        }
    }

    public static void LoadAll()
    {
        var state = GameManager.Me.m_state;
        if (state.m_explodeInteractions == null) return;
        foreach (var save in state.m_explodeInteractions)
            if (s_all.TryGetValue(save.m_id, out var interaction))
                interaction.LoadData(save);
    }
    
    private bool m_interactDisabled = false;

// ICharacterObjectInteract
    public string InteractType => null;
    public bool CanInteract(NGMovingObject _chr) => m_interactDisabled == false && m_useInteraction && isActiveAndEnabled && GameManager.Me.GlobalInteractCheck(_chr) && GameManager.Me.IsPossessed(_chr) && (m_onlyAllowHero == false || _chr is MAHeroBase);
    public string GetInteractLabel(NGMovingObject _chr) => m_actionLabel;
    public void DoInteract(NGMovingObject _chr)
    {
        IDamageReceiver.DamageSource source = IDamageReceiver.DamageSource.AI;
        if(_chr is MACharacterBase)
            source = IDamageReceiver.GetSourceFromCharacter(_chr as MACharacterBase);
        
        if (!string.IsNullOrEmpty(m_characterTriggerAnimation))
        {
            _chr.PlayExplodeInteractionAnim(m_saveIdentifier, m_characterTriggerAnimation);
        }
        this.DoAfter(m_triggerDelay, () =>
        {
            Explode(source, _chr.gameObject, m_interactionDamageDone, _chr.transform.position);
        });
    }

    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => InteractionSequenceNode.AttemptInteraction(_chr, gameObject);
    public void EnableInteractionTriggers(bool _b) => m_interactDisabled = !_b;
    
    public void ApplyDamageEffect(IDamageReceiver.DamageSource _source, float _damageDone, Vector3 _damageOrigin, MAAttackInstance _attack = null, MAHandPowerInfo handPower = null)
    {
        Explode(_source, null, _damageDone, _damageOrigin);
    }

    private void Explode(IDamageReceiver.DamageSource _source, GameObject _exclude, float _damageDone, Vector3 _damageOrigin)
    {
        HoldBodies(false);
        CreateExplosionEffect(_damageDone, _damageOrigin);
        m_triggerAudio?.Play(gameObject);

        if (m_explosionRadius > 0.0f)
        {
            MAPowerEffectBase.CreateExplosionAtPoint(_source, transform.position, m_explosionRadius, m_explosionPower, this, default, _exclude);
        }
        if (m_canRepeat == false)
        {
            enabled = false;

            switch (m_explodeInteractionType)
            {
                case ExplodeInteractionType.Disable:
                    gameObject.SetActive(false);
                    break;
                case ExplodeInteractionType.Destroy:
                    Destroy(gameObject);
                    break;
                default:
                    break;
            }
        }
    }

    public float AutoInteractTime => 0;
    
    // IImpactDataSupplier
    public void PlayImpactAudioOnObject(NGMovingObject _obj)
    {
        m_perImpactedCharacterAudio?.Play(_obj.gameObject);
    }
    public void ApplyDamage(IDamageReceiver.DamageSource _source,NGMovingObject _obj, bool _isContinuous, float _multiplier = 1)
    {
        if (m_impactedCharacterDamageBase > 0)
            _obj.ApplyDamageEffect(_source, m_impactedCharacterDamageBase * _multiplier, _obj.transform.position);
    }

    public static List<ExplodeInteraction> GetAttackableExplodeInteractions()
    {
        List<ExplodeInteraction> explodeInteractions = new List<ExplodeInteraction>();

        foreach(var kvp in s_all)
        {
            var explodeInteraction = kvp.Value;

            if(explodeInteraction.isActiveAndEnabled && explodeInteraction.m_isAttackable)
            {
                explodeInteractions.Add(explodeInteraction);
            }
        }

        return explodeInteractions;
    }
}
