using System;
using System.Collections.Generic;
using UnityEngine;

public class ObjectTrackerBase<T> where T : class
{
    private Dictionary<MonoBehaviour, T> m_objects = new();
    private HashSet<MonoBehaviour> m_objectsToRemove = new();

    public virtual void UpdateObjects()
    {
    }

    public virtual void DestroyObject(T _item)
    {
    }

    public void Update()
    {
        m_objectsToRemove.Clear();
        foreach (var kvp in m_objects)
            m_objectsToRemove.Add(kvp.Key);
        UpdateObjects();
        foreach (var item in m_objectsToRemove)
        {
            if (m_objects.TryGetValue(item, out var obj) == false) continue;
            m_objects.Remove(item);
            DestroyObject(obj);
        }
    }

    public T GetExistingItem(MonoBehaviour _m)
    {
        if (m_objects.TryGetValue(_m, out var t))
        {
            m_objectsToRemove.Remove(_m);
            return t;
        }
        return null;
    }

    public void Add(MonoBehaviour _m, T _t) => m_objects[_m] = _t;

    public void Clear()
    {
        foreach (var kvp in m_objects)
            DestroyObject(kvp.Value);
        m_objects.Clear();
        m_objectsToRemove.Clear();
    }
}

public class TowerDefenseVisualsManager : MonoSingleton<TowerDefenseVisualsManager>
{
    public enum OverlayObjectType
    {
        None = 0,
        Enemy,
        NonEnemy,
        Hero
    };

    public float m_overlayDisplayHeightStart = 200;
    public float m_overlayDisplayHeightEnd = 250;
    [SerializeField]
    int m_spawnPointOverlayMinSize = 10;
    [SerializeField]
    int m_spawnPointOverlayMaxSize = 20;
    public Material m_overlayMaterial;
    private GameObject m_overlay;
    private int m_overlayDay = -999;
    private float m_overlayAlpha = 0;
    private List<Material> m_allMaterials = new ();
    void Update()
    {
        if (GameManager.Me == null) return;
        if (GameManager.Me.LoadComplete == false) return;
        
        var dayStage = DayNight.Me.m_timeStage;
        int dayAndWave = DayNight.Me.CurrentWorkingDay * 1000;// + MASpawnPointManager.GetCurrentHighestSpawnWave;
        var showOverlay = (dayStage < DayNight.c_timeStageNightEnd || dayStage > DayNight.c_timeStageNightStart) && (m_overlayDay == -999 || m_overlayDay == dayAndWave);
        var overlayAlphaTarget = showOverlay ? OverlayAlpha() : 0.0f;
        m_overlayAlpha = Mathf.Lerp(m_overlayAlpha, overlayAlphaTarget, .2f);
        if (showOverlay && m_overlay == null)
            GenerateOverlay();
        else if (showOverlay == false && m_overlay != null)
            DestroyOverlay();
        SetOverlayAlpha();
        if (m_overlay != null)
            m_overlayObjects.Update();
    }
    
    public class OverlayObjectTracker : ObjectTrackerBase<Transform>
    {
        public override void UpdateObjects()
        {
            Transform CreateObject(NGMovingObject _obj, OverlayObjectType type)
            {
                var go = GameObject.CreatePrimitive(PrimitiveType.Quad);
                Destroy(go.GetComponent<Collider>());
                var entry = go.transform;
                entry.SetParent(Me.m_overlay.transform);
                entry.localScale = Vector3.one * 4;
                var mr = go.GetComponent<MeshRenderer>();
                switch (type)
                {
                    case OverlayObjectType.Enemy:
                        mr.material = Me.EnemyMaterial();
                        break;
                    case OverlayObjectType.NonEnemy:
                        mr.material = Me.NonEnemyMaterial();
                        break;
                    case OverlayObjectType.Hero:
                        mr.material = Me.HeroMaterial();
                        break;
                }
                Add(_obj, entry);
                return entry;
            }

            void CheckObject(NGMovingObject _obj, OverlayObjectType type)
            {
                if (_obj.Health < .001f) return;
                var entry = GetExistingItem(_obj);
                if (entry == null)
                    entry = CreateObject(_obj, type);
                entry.position = _obj.transform.position + Vector3.up * 1f;
                entry.eulerAngles = new Vector3(90, _obj.transform.eulerAngles.y, 0);
            }

            foreach (var creature in NGManager.Me.m_MACreatureList)
            {
                if (creature.IsEnemy)
                    CheckObject(creature, OverlayObjectType.Enemy);
                else
                    CheckObject(creature, OverlayObjectType.NonEnemy);
            }
            foreach (var hero in NGManager.Me.m_MAHeroList)
                CheckObject(hero, OverlayObjectType.Hero);
        }
        public override void DestroyObject(Transform _t) => GameObject.Destroy(_t.gameObject);
    }

    private OverlayObjectTracker m_overlayObjects = new();

    private Material EnemyMaterial()
    {
        var material = new Material(m_overlayMaterial);
        material.SetColor("_Tint", GameManager.Me.m_state.m_spawnMarkerColour);
        material.SetFloat("_BrightnessMult", 0);
        material.SetFloat("_RotateUVsAmount", 0);
        m_allMaterials.Add(material);
        return material;
    }

    private Material NonEnemyMaterial()
    {
        var material = new Material(m_overlayMaterial);
        material.SetColor("_Tint", Color.yellow);
        material.SetFloat("_BrightnessMult", 0);
        material.SetFloat("_RotateUVsAmount", 0);
        m_allMaterials.Add(material);
        return material;
    }

    private Material HeroMaterial()
    {
        var material = new Material(m_overlayMaterial);
        material.SetColor("_Tint", Color.green);
        material.SetFloat("_BrightnessMult", 0);
        material.SetFloat("_RotateUVsAmount", 0);
        m_allMaterials.Add(material);
        return material;
    }
    
    private void SetOverlayAlpha()
    {
        const float c_brightnessBase = 4;
        var scale = Mathf.Sin(Time.time * 4) * .05f + 1;
        var offsetScale = new Vector4(scale, scale, .5f - .5f * scale, .5f - .5f * scale);
        for (int i = 0; i < m_allMaterials.Count; ++i)
        {
            m_allMaterials[i].SetFloat("_BrightnessMult", c_brightnessBase * m_overlayAlpha);
            m_allMaterials[i].SetVector("_MainTex_ST", offsetScale);
        }
    }
    
    private void GenerateOverlay()
    {
        int day = DayNight.Me.CurrentWorkingDay;
        int dayAndWave = day * 1000;// + MASpawnPointManager.GetCurrentHighestSpawnWave;
        
        Dictionary<(Vector3 pos, Vector3 dest), int> infos = new();
        foreach (MASpawnPointManager maSpawnPointManager in MASpawnPointManager.s_spawnPointManagers)
        {
            if (maSpawnPointManager.ShowOverlay())
            {
                Dictionary<(Vector3 pos, Vector3 dest), int> info = maSpawnPointManager.GetRemainingSpawns();
                foreach (var keyValuePair in info)
                {
                    if (infos.TryGetValue(keyValuePair.Key, out var outVal) == false)
                    {
                        infos.Add(keyValuePair.Key, keyValuePair.Value);
                    }
                    else
                    {
                        infos[keyValuePair.Key] = Mathf.Max(infos[keyValuePair.Key], outVal);
                    }
                }
            }
        }

        if (infos.Count == 0) return; // no data yet (or no data at all)
        
        m_overlay = new GameObject("Overlay");
        m_overlay.transform.SetParent(transform, true);
        m_overlayDay = dayAndWave;

        int totalCount = 0;
        foreach (var kvp in infos)
            totalCount += kvp.Value;
        foreach (var kvp in infos)
        {
            var (pos, targetPos) = kvp.Key;
            var size = 25 * kvp.Value / totalCount;
            size = Math.Clamp(size, m_spawnPointOverlayMinSize, m_spawnPointOverlayMaxSize);
            
            var angle = Mathf.Atan2(targetPos.z - pos.z, targetPos.x - pos.x) * Mathf.Rad2Deg - 90;

            var go = new GameObject("OverlayTarget");
            go.transform.SetParent(m_overlay.transform, true);
            var mesh = TerrainManager.Me.GetLandscapeMesh(pos, size, size, 1, 1, 1, 3);
            var mr = go.AddComponent<MeshRenderer>();
            var mf = go.AddComponent<MeshFilter>();
            mf.mesh = mesh;
            mr.material = m_overlayMaterial;
            mr.material.SetColor("_Tint", GameManager.Me.m_state.m_spawnMarkerColour);
            mr.material.SetFloat("_BrightnessMult", 0);
            mr.material.SetFloat("_RotateUVsAmount", angle / 180.0f);
            m_allMaterials.Add(mr.material);
        }
    }

    public void DestroyOverlay(bool _force = false)
    {
        if (m_overlay == null) return;			
        if (!_force && m_overlayAlpha > .004f) return;
        Destroy(m_overlay);
        m_overlay = null;
        m_allMaterials.Clear();
        m_overlayDay = -999;
        m_overlayObjects.Clear();
    }

    private float OverlayAlpha()
    {
        var camY = GameManager.Me.m_camera.transform.position.y;
        return Mathf.InverseLerp(m_overlayDisplayHeightStart, m_overlayDisplayHeightEnd, camY);
    }
}
