using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MACooldownGUI : MonoBehaviour
{
    public TMP_Text m_cooldownText;
    public Image m_progressBar;
    
    
    public void UpdateProgress(Vector3 _pos, float _currentTime, float _maxTime)
    {
        if(_maxTime <= 0)
        {
            if(gameObject.activeInHierarchy)
                gameObject.SetActive(false);
            return;
        }
        
        transform.position = _pos;
            
        m_cooldownText.text = _currentTime.ToHMSTimeString();
        
        var fillAmount = _currentTime / _maxTime;
        
        m_progressBar.fillAmount = fillAmount;
        if((fillAmount > 0f && fillAmount < 1f) && gameObject.activeInHierarchy == false)
            gameObject.SetActive(true);
            
        if((fillAmount <= 0 || fillAmount >= 1f) && gameObject.activeInHierarchy)
            gameObject.SetActive(false);
    }
}
